/* Copyright 2017 - 2025 <PERSON><PERSON>
 * Copyright 2017 - 2025 Quarkslab
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "ELF/enums.hpp"
#include "LIEF/ELF/enums.hpp"
#include "LIEF/ELF/EnumToString.hpp"
#include "enums_wrapper.hpp"

namespace LIEF::ELF::py {
void init_enums(nb::module_& m) {
  #define ENTRY(X) .value(to_string(ARCH::X), ARCH::X)
  enum_<ARCH>(m, "ARCH")
    ENTRY(NONE)
    ENTRY(M32)
    ENTRY(SPARC)
    ENTRY(I386)
    ENTRY(M68K)
    ENTRY(M88K)
    ENTRY(IAMCU)
    ENTRY(I860)
    ENTRY(MIPS)
    ENTRY(S370)
    ENTRY(MIPS_RS3_LE)
    ENTRY(PARISC)
    ENTRY(VPP500)
    ENTRY(SPARC32PLUS)
    ENTRY(I60)
    ENTRY(PPC)
    ENTRY(PPC64)
    ENTRY(S390)
    ENTRY(SPU)
    ENTRY(V800)
    ENTRY(FR20)
    ENTRY(RH32)
    ENTRY(RCE)
    ENTRY(ARM)
    ENTRY(ALPHA)
    ENTRY(SH)
    ENTRY(SPARCV9)
    ENTRY(TRICORE)
    ENTRY(ARC)
    ENTRY(H8_300)
    ENTRY(H8_300H)
    ENTRY(H8S)
    ENTRY(H8_500)
    ENTRY(IA_64)
    ENTRY(MIPS_X)
    ENTRY(COLDFIRE)
    ENTRY(M68HC12)
    ENTRY(MMA)
    ENTRY(PCP)
    ENTRY(NCPU)
    ENTRY(NDR1)
    ENTRY(STARCORE)
    ENTRY(ME16)
    ENTRY(ST100)
    ENTRY(TINYJ)
    ENTRY(X86_64)
    ENTRY(PDSP)
    ENTRY(PDP10)
    ENTRY(PDP11)
    ENTRY(FX66)
    ENTRY(ST9PLUS)
    ENTRY(ST7)
    ENTRY(M68HC16)
    ENTRY(M68HC11)
    ENTRY(M68HC08)
    ENTRY(M68HC05)
    ENTRY(SVX)
    ENTRY(ST19)
    ENTRY(VAX)
    ENTRY(CRIS)
    ENTRY(JAVELIN)
    ENTRY(FIREPATH)
    ENTRY(ZSP)
    ENTRY(MMIX)
    ENTRY(HUANY)
    ENTRY(PRISM)
    ENTRY(AVR)
    ENTRY(FR30)
    ENTRY(D10V)
    ENTRY(D30V)
    ENTRY(V850)
    ENTRY(M32R)
    ENTRY(MN10300)
    ENTRY(MN10200)
    ENTRY(PJ)
    ENTRY(OPENRISC)
    ENTRY(ARC_COMPACT)
    ENTRY(XTENSA)
    ENTRY(VIDEOCORE)
    ENTRY(TMM_GPP)
    ENTRY(NS32K)
    ENTRY(TPC)
    ENTRY(SNP1K)
    ENTRY(ST200)
    ENTRY(IP2K)
    ENTRY(MAX)
    ENTRY(CR)
    ENTRY(F2MC16)
    ENTRY(MSP430)
    ENTRY(BLACKFIN)
    ENTRY(SE_C33)
    ENTRY(SEP)
    ENTRY(ARCA)
    ENTRY(UNICORE)
    ENTRY(EXCESS)
    ENTRY(DXP)
    ENTRY(ALTERA_NIOS2)
    ENTRY(CRX)
    ENTRY(XGATE)
    ENTRY(C166)
    ENTRY(M16C)
    ENTRY(DSPIC30F)
    ENTRY(CE)
    ENTRY(M32C)
    ENTRY(TSK3000)
    ENTRY(RS08)
    ENTRY(SHARC)
    ENTRY(ECOG2)
    ENTRY(SCORE7)
    ENTRY(DSP24)
    ENTRY(VIDEOCORE3)
    ENTRY(LATTICEMICO32)
    ENTRY(SE_C17)
    ENTRY(TI_C6000)
    ENTRY(TI_C2000)
    ENTRY(TI_C5500)
    ENTRY(MMDSP_PLUS)
    ENTRY(CYPRESS_M8C)
    ENTRY(R32C)
    ENTRY(TRIMEDIA)
    ENTRY(HEXAGON)
    ENTRY(M8051)
    ENTRY(STXP7X)
    ENTRY(NDS32)
    ENTRY(ECOG1)
    /* ENTRY(ECOG1X) */
    ENTRY(MAXQ30)
    ENTRY(XIMO16)
    ENTRY(MANIK)
    ENTRY(CRAYNV2)
    ENTRY(RX)
    ENTRY(METAG)
    ENTRY(MCST_ELBRUS)
    ENTRY(ECOG16)
    ENTRY(CR16)
    ENTRY(ETPU)
    ENTRY(SLE9X)
    ENTRY(L10M)
    ENTRY(K10M)
    ENTRY(AARCH64)
    ENTRY(AVR32)
    ENTRY(STM8)
    ENTRY(TILE64)
    ENTRY(TILEPRO)
    ENTRY(CUDA)
    ENTRY(TILEGX)
    ENTRY(CLOUDSHIELD)
    ENTRY(COREA_1ST)
    ENTRY(COREA_2ND)
    ENTRY(ARC_COMPACT2)
    ENTRY(OPEN8)
    ENTRY(RL78)
    ENTRY(VIDEOCORE5)
    ENTRY(M78KOR)
    ENTRY(M56800EX)
    ENTRY(BA1)
    ENTRY(BA2)
    ENTRY(XCORE)
    ENTRY(MCHP_PIC)
    ENTRY(INTEL205)
    ENTRY(INTEL206)
    ENTRY(INTEL207)
    ENTRY(INTEL208)
    ENTRY(INTEL209)
    ENTRY(KM32)
    ENTRY(KMX32)
    ENTRY(KMX16)
    ENTRY(KMX8)
    ENTRY(KVARC)
    ENTRY(CDP)
    ENTRY(COGE)
    ENTRY(COOL)
    ENTRY(NORC)
    ENTRY(CSR_KALIMBA)
    ENTRY(AMDGPU)
    ENTRY(RISCV)
    ENTRY(BPF)
    ENTRY(CSKY)
    ENTRY(LOONGARCH)
    ENTRY(ALPHA_ALT)
  ;
  #undef ENTRY
}
}
