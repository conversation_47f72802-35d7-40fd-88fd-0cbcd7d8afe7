#include "asm/mips/init.hpp"

#include "LIEF/asm/mips/opcodes.hpp"

namespace LIEF::assembly::mips::py {
template<>
void create<LIEF::assembly::mips::OPCODE>(nb::module_& m) {
  nb::enum_<LIEF::assembly::mips::OPCODE> opcodes(m, "OPCODE");
  opcodes.value("PHI", LIEF::assembly::mips::OPCODE::PHI)
  .value("INLINEASM", LIEF::assembly::mips::OPCODE::INLINEASM)
  .value("INLINEASM_BR", LIEF::assembly::mips::OPCODE::INLINEASM_BR)
  .value("CFI_INSTRUCTION", LIEF::assembly::mips::OPCODE::CFI_INSTRUCTION)
  .value("EH_LABEL", LIEF::assembly::mips::OPCODE::EH_LABEL)
  .value("GC_LABEL", LIEF::assembly::mips::OPCODE::GC_LABEL)
  .value("ANNOTATION_LABEL", LIEF::assembly::mips::OPCODE::ANNOTATION_LABEL)
  .value("KILL", LIEF::assembly::mips::OPCODE::KILL)
  .value("EXTRACT_SUBREG", LIEF::assembly::mips::OPCODE::EXTRACT_SUBREG)
  .value("INSERT_SUBREG", LIEF::assembly::mips::OPCODE::INSERT_SUBREG)
  .value("IMPLICIT_DEF", LIEF::assembly::mips::OPCODE::IMPLICIT_DEF)
  .value("INIT_UNDEF", LIEF::assembly::mips::OPCODE::INIT_UNDEF)
  .value("SUBREG_TO_REG", LIEF::assembly::mips::OPCODE::SUBREG_TO_REG)
  .value("COPY_TO_REGCLASS", LIEF::assembly::mips::OPCODE::COPY_TO_REGCLASS)
  .value("DBG_VALUE", LIEF::assembly::mips::OPCODE::DBG_VALUE)
  .value("DBG_VALUE_LIST", LIEF::assembly::mips::OPCODE::DBG_VALUE_LIST)
  .value("DBG_INSTR_REF", LIEF::assembly::mips::OPCODE::DBG_INSTR_REF)
  .value("DBG_PHI", LIEF::assembly::mips::OPCODE::DBG_PHI)
  .value("DBG_LABEL", LIEF::assembly::mips::OPCODE::DBG_LABEL)
  .value("REG_SEQUENCE", LIEF::assembly::mips::OPCODE::REG_SEQUENCE)
  .value("COPY", LIEF::assembly::mips::OPCODE::COPY)
  .value("BUNDLE", LIEF::assembly::mips::OPCODE::BUNDLE)
  .value("LIFETIME_START", LIEF::assembly::mips::OPCODE::LIFETIME_START)
  .value("LIFETIME_END", LIEF::assembly::mips::OPCODE::LIFETIME_END)
  .value("PSEUDO_PROBE", LIEF::assembly::mips::OPCODE::PSEUDO_PROBE)
  .value("ARITH_FENCE", LIEF::assembly::mips::OPCODE::ARITH_FENCE)
  .value("STACKMAP", LIEF::assembly::mips::OPCODE::STACKMAP)
  .value("FENTRY_CALL", LIEF::assembly::mips::OPCODE::FENTRY_CALL)
  .value("PATCHPOINT", LIEF::assembly::mips::OPCODE::PATCHPOINT)
  .value("LOAD_STACK_GUARD", LIEF::assembly::mips::OPCODE::LOAD_STACK_GUARD)
  .value("PREALLOCATED_SETUP", LIEF::assembly::mips::OPCODE::PREALLOCATED_SETUP)
  .value("PREALLOCATED_ARG", LIEF::assembly::mips::OPCODE::PREALLOCATED_ARG)
  .value("STATEPOINT", LIEF::assembly::mips::OPCODE::STATEPOINT)
  .value("LOCAL_ESCAPE", LIEF::assembly::mips::OPCODE::LOCAL_ESCAPE)
  .value("FAULTING_OP", LIEF::assembly::mips::OPCODE::FAULTING_OP)
  .value("PATCHABLE_OP", LIEF::assembly::mips::OPCODE::PATCHABLE_OP)
  .value("PATCHABLE_FUNCTION_ENTER", LIEF::assembly::mips::OPCODE::PATCHABLE_FUNCTION_ENTER)
  .value("PATCHABLE_RET", LIEF::assembly::mips::OPCODE::PATCHABLE_RET)
  .value("PATCHABLE_FUNCTION_EXIT", LIEF::assembly::mips::OPCODE::PATCHABLE_FUNCTION_EXIT)
  .value("PATCHABLE_TAIL_CALL", LIEF::assembly::mips::OPCODE::PATCHABLE_TAIL_CALL)
  .value("PATCHABLE_EVENT_CALL", LIEF::assembly::mips::OPCODE::PATCHABLE_EVENT_CALL)
  .value("PATCHABLE_TYPED_EVENT_CALL", LIEF::assembly::mips::OPCODE::PATCHABLE_TYPED_EVENT_CALL)
  .value("ICALL_BRANCH_FUNNEL", LIEF::assembly::mips::OPCODE::ICALL_BRANCH_FUNNEL)
  .value("FAKE_USE", LIEF::assembly::mips::OPCODE::FAKE_USE)
  .value("MEMBARRIER", LIEF::assembly::mips::OPCODE::MEMBARRIER)
  .value("JUMP_TABLE_DEBUG_INFO", LIEF::assembly::mips::OPCODE::JUMP_TABLE_DEBUG_INFO)
  .value("CONVERGENCECTRL_ENTRY", LIEF::assembly::mips::OPCODE::CONVERGENCECTRL_ENTRY)
  .value("CONVERGENCECTRL_ANCHOR", LIEF::assembly::mips::OPCODE::CONVERGENCECTRL_ANCHOR)
  .value("CONVERGENCECTRL_LOOP", LIEF::assembly::mips::OPCODE::CONVERGENCECTRL_LOOP)
  .value("CONVERGENCECTRL_GLUE", LIEF::assembly::mips::OPCODE::CONVERGENCECTRL_GLUE)
  .value("G_ASSERT_SEXT", LIEF::assembly::mips::OPCODE::G_ASSERT_SEXT)
  .value("G_ASSERT_ZEXT", LIEF::assembly::mips::OPCODE::G_ASSERT_ZEXT)
  .value("G_ASSERT_ALIGN", LIEF::assembly::mips::OPCODE::G_ASSERT_ALIGN)
  .value("G_ADD", LIEF::assembly::mips::OPCODE::G_ADD)
  .value("G_SUB", LIEF::assembly::mips::OPCODE::G_SUB)
  .value("G_MUL", LIEF::assembly::mips::OPCODE::G_MUL)
  .value("G_SDIV", LIEF::assembly::mips::OPCODE::G_SDIV)
  .value("G_UDIV", LIEF::assembly::mips::OPCODE::G_UDIV)
  .value("G_SREM", LIEF::assembly::mips::OPCODE::G_SREM)
  .value("G_UREM", LIEF::assembly::mips::OPCODE::G_UREM)
  .value("G_SDIVREM", LIEF::assembly::mips::OPCODE::G_SDIVREM)
  .value("G_UDIVREM", LIEF::assembly::mips::OPCODE::G_UDIVREM)
  .value("G_AND", LIEF::assembly::mips::OPCODE::G_AND)
  .value("G_OR", LIEF::assembly::mips::OPCODE::G_OR)
  .value("G_XOR", LIEF::assembly::mips::OPCODE::G_XOR)
  .value("G_ABDS", LIEF::assembly::mips::OPCODE::G_ABDS)
  .value("G_ABDU", LIEF::assembly::mips::OPCODE::G_ABDU)
  .value("G_IMPLICIT_DEF", LIEF::assembly::mips::OPCODE::G_IMPLICIT_DEF)
  .value("G_PHI", LIEF::assembly::mips::OPCODE::G_PHI)
  .value("G_FRAME_INDEX", LIEF::assembly::mips::OPCODE::G_FRAME_INDEX)
  .value("G_GLOBAL_VALUE", LIEF::assembly::mips::OPCODE::G_GLOBAL_VALUE)
  .value("G_PTRAUTH_GLOBAL_VALUE", LIEF::assembly::mips::OPCODE::G_PTRAUTH_GLOBAL_VALUE)
  .value("G_CONSTANT_POOL", LIEF::assembly::mips::OPCODE::G_CONSTANT_POOL)
  .value("G_EXTRACT", LIEF::assembly::mips::OPCODE::G_EXTRACT)
  .value("G_UNMERGE_VALUES", LIEF::assembly::mips::OPCODE::G_UNMERGE_VALUES)
  .value("G_INSERT", LIEF::assembly::mips::OPCODE::G_INSERT)
  .value("G_MERGE_VALUES", LIEF::assembly::mips::OPCODE::G_MERGE_VALUES)
  .value("G_BUILD_VECTOR", LIEF::assembly::mips::OPCODE::G_BUILD_VECTOR)
  .value("G_BUILD_VECTOR_TRUNC", LIEF::assembly::mips::OPCODE::G_BUILD_VECTOR_TRUNC)
  .value("G_CONCAT_VECTORS", LIEF::assembly::mips::OPCODE::G_CONCAT_VECTORS)
  .value("G_PTRTOINT", LIEF::assembly::mips::OPCODE::G_PTRTOINT)
  .value("G_INTTOPTR", LIEF::assembly::mips::OPCODE::G_INTTOPTR)
  .value("G_BITCAST", LIEF::assembly::mips::OPCODE::G_BITCAST)
  .value("G_FREEZE", LIEF::assembly::mips::OPCODE::G_FREEZE)
  .value("G_CONSTANT_FOLD_BARRIER", LIEF::assembly::mips::OPCODE::G_CONSTANT_FOLD_BARRIER)
  .value("G_INTRINSIC_FPTRUNC_ROUND", LIEF::assembly::mips::OPCODE::G_INTRINSIC_FPTRUNC_ROUND)
  .value("G_INTRINSIC_TRUNC", LIEF::assembly::mips::OPCODE::G_INTRINSIC_TRUNC)
  .value("G_INTRINSIC_ROUND", LIEF::assembly::mips::OPCODE::G_INTRINSIC_ROUND)
  .value("G_INTRINSIC_LRINT", LIEF::assembly::mips::OPCODE::G_INTRINSIC_LRINT)
  .value("G_INTRINSIC_LLRINT", LIEF::assembly::mips::OPCODE::G_INTRINSIC_LLRINT)
  .value("G_INTRINSIC_ROUNDEVEN", LIEF::assembly::mips::OPCODE::G_INTRINSIC_ROUNDEVEN)
  .value("G_READCYCLECOUNTER", LIEF::assembly::mips::OPCODE::G_READCYCLECOUNTER)
  .value("G_READSTEADYCOUNTER", LIEF::assembly::mips::OPCODE::G_READSTEADYCOUNTER)
  .value("G_LOAD", LIEF::assembly::mips::OPCODE::G_LOAD)
  .value("G_SEXTLOAD", LIEF::assembly::mips::OPCODE::G_SEXTLOAD)
  .value("G_ZEXTLOAD", LIEF::assembly::mips::OPCODE::G_ZEXTLOAD)
  .value("G_INDEXED_LOAD", LIEF::assembly::mips::OPCODE::G_INDEXED_LOAD)
  .value("G_INDEXED_SEXTLOAD", LIEF::assembly::mips::OPCODE::G_INDEXED_SEXTLOAD)
  .value("G_INDEXED_ZEXTLOAD", LIEF::assembly::mips::OPCODE::G_INDEXED_ZEXTLOAD)
  .value("G_STORE", LIEF::assembly::mips::OPCODE::G_STORE)
  .value("G_INDEXED_STORE", LIEF::assembly::mips::OPCODE::G_INDEXED_STORE)
  .value("G_ATOMIC_CMPXCHG_WITH_SUCCESS", LIEF::assembly::mips::OPCODE::G_ATOMIC_CMPXCHG_WITH_SUCCESS)
  .value("G_ATOMIC_CMPXCHG", LIEF::assembly::mips::OPCODE::G_ATOMIC_CMPXCHG)
  .value("G_ATOMICRMW_XCHG", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_XCHG)
  .value("G_ATOMICRMW_ADD", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_ADD)
  .value("G_ATOMICRMW_SUB", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_SUB)
  .value("G_ATOMICRMW_AND", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_AND)
  .value("G_ATOMICRMW_NAND", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_NAND)
  .value("G_ATOMICRMW_OR", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_OR)
  .value("G_ATOMICRMW_XOR", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_XOR)
  .value("G_ATOMICRMW_MAX", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_MAX)
  .value("G_ATOMICRMW_MIN", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_MIN)
  .value("G_ATOMICRMW_UMAX", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_UMAX)
  .value("G_ATOMICRMW_UMIN", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_UMIN)
  .value("G_ATOMICRMW_FADD", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_FADD)
  .value("G_ATOMICRMW_FSUB", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_FSUB)
  .value("G_ATOMICRMW_FMAX", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_FMAX)
  .value("G_ATOMICRMW_FMIN", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_FMIN)
  .value("G_ATOMICRMW_UINC_WRAP", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_UINC_WRAP)
  .value("G_ATOMICRMW_UDEC_WRAP", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_UDEC_WRAP)
  .value("G_ATOMICRMW_USUB_COND", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_USUB_COND)
  .value("G_ATOMICRMW_USUB_SAT", LIEF::assembly::mips::OPCODE::G_ATOMICRMW_USUB_SAT)
  .value("G_FENCE", LIEF::assembly::mips::OPCODE::G_FENCE)
  .value("G_PREFETCH", LIEF::assembly::mips::OPCODE::G_PREFETCH)
  .value("G_BRCOND", LIEF::assembly::mips::OPCODE::G_BRCOND)
  .value("G_BRINDIRECT", LIEF::assembly::mips::OPCODE::G_BRINDIRECT)
  .value("G_INVOKE_REGION_START", LIEF::assembly::mips::OPCODE::G_INVOKE_REGION_START)
  .value("G_INTRINSIC", LIEF::assembly::mips::OPCODE::G_INTRINSIC)
  .value("G_INTRINSIC_W_SIDE_EFFECTS", LIEF::assembly::mips::OPCODE::G_INTRINSIC_W_SIDE_EFFECTS)
  .value("G_INTRINSIC_CONVERGENT", LIEF::assembly::mips::OPCODE::G_INTRINSIC_CONVERGENT)
  .value("G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS", LIEF::assembly::mips::OPCODE::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS)
  .value("G_ANYEXT", LIEF::assembly::mips::OPCODE::G_ANYEXT)
  .value("G_TRUNC", LIEF::assembly::mips::OPCODE::G_TRUNC)
  .value("G_CONSTANT", LIEF::assembly::mips::OPCODE::G_CONSTANT)
  .value("G_FCONSTANT", LIEF::assembly::mips::OPCODE::G_FCONSTANT)
  .value("G_VASTART", LIEF::assembly::mips::OPCODE::G_VASTART)
  .value("G_VAARG", LIEF::assembly::mips::OPCODE::G_VAARG)
  .value("G_SEXT", LIEF::assembly::mips::OPCODE::G_SEXT)
  .value("G_SEXT_INREG", LIEF::assembly::mips::OPCODE::G_SEXT_INREG)
  .value("G_ZEXT", LIEF::assembly::mips::OPCODE::G_ZEXT)
  .value("G_SHL", LIEF::assembly::mips::OPCODE::G_SHL)
  .value("G_LSHR", LIEF::assembly::mips::OPCODE::G_LSHR)
  .value("G_ASHR", LIEF::assembly::mips::OPCODE::G_ASHR)
  .value("G_FSHL", LIEF::assembly::mips::OPCODE::G_FSHL)
  .value("G_FSHR", LIEF::assembly::mips::OPCODE::G_FSHR)
  .value("G_ROTR", LIEF::assembly::mips::OPCODE::G_ROTR)
  .value("G_ROTL", LIEF::assembly::mips::OPCODE::G_ROTL)
  .value("G_ICMP", LIEF::assembly::mips::OPCODE::G_ICMP)
  .value("G_FCMP", LIEF::assembly::mips::OPCODE::G_FCMP)
  .value("G_SCMP", LIEF::assembly::mips::OPCODE::G_SCMP)
  .value("G_UCMP", LIEF::assembly::mips::OPCODE::G_UCMP)
  .value("G_SELECT", LIEF::assembly::mips::OPCODE::G_SELECT)
  .value("G_UADDO", LIEF::assembly::mips::OPCODE::G_UADDO)
  .value("G_UADDE", LIEF::assembly::mips::OPCODE::G_UADDE)
  .value("G_USUBO", LIEF::assembly::mips::OPCODE::G_USUBO)
  .value("G_USUBE", LIEF::assembly::mips::OPCODE::G_USUBE)
  .value("G_SADDO", LIEF::assembly::mips::OPCODE::G_SADDO)
  .value("G_SADDE", LIEF::assembly::mips::OPCODE::G_SADDE)
  .value("G_SSUBO", LIEF::assembly::mips::OPCODE::G_SSUBO)
  .value("G_SSUBE", LIEF::assembly::mips::OPCODE::G_SSUBE)
  .value("G_UMULO", LIEF::assembly::mips::OPCODE::G_UMULO)
  .value("G_SMULO", LIEF::assembly::mips::OPCODE::G_SMULO)
  .value("G_UMULH", LIEF::assembly::mips::OPCODE::G_UMULH)
  .value("G_SMULH", LIEF::assembly::mips::OPCODE::G_SMULH)
  .value("G_UADDSAT", LIEF::assembly::mips::OPCODE::G_UADDSAT)
  .value("G_SADDSAT", LIEF::assembly::mips::OPCODE::G_SADDSAT)
  .value("G_USUBSAT", LIEF::assembly::mips::OPCODE::G_USUBSAT)
  .value("G_SSUBSAT", LIEF::assembly::mips::OPCODE::G_SSUBSAT)
  .value("G_USHLSAT", LIEF::assembly::mips::OPCODE::G_USHLSAT)
  .value("G_SSHLSAT", LIEF::assembly::mips::OPCODE::G_SSHLSAT)
  .value("G_SMULFIX", LIEF::assembly::mips::OPCODE::G_SMULFIX)
  .value("G_UMULFIX", LIEF::assembly::mips::OPCODE::G_UMULFIX)
  .value("G_SMULFIXSAT", LIEF::assembly::mips::OPCODE::G_SMULFIXSAT)
  .value("G_UMULFIXSAT", LIEF::assembly::mips::OPCODE::G_UMULFIXSAT)
  .value("G_SDIVFIX", LIEF::assembly::mips::OPCODE::G_SDIVFIX)
  .value("G_UDIVFIX", LIEF::assembly::mips::OPCODE::G_UDIVFIX)
  .value("G_SDIVFIXSAT", LIEF::assembly::mips::OPCODE::G_SDIVFIXSAT)
  .value("G_UDIVFIXSAT", LIEF::assembly::mips::OPCODE::G_UDIVFIXSAT)
  .value("G_FADD", LIEF::assembly::mips::OPCODE::G_FADD)
  .value("G_FSUB", LIEF::assembly::mips::OPCODE::G_FSUB)
  .value("G_FMUL", LIEF::assembly::mips::OPCODE::G_FMUL)
  .value("G_FMA", LIEF::assembly::mips::OPCODE::G_FMA)
  .value("G_FMAD", LIEF::assembly::mips::OPCODE::G_FMAD)
  .value("G_FDIV", LIEF::assembly::mips::OPCODE::G_FDIV)
  .value("G_FREM", LIEF::assembly::mips::OPCODE::G_FREM)
  .value("G_FPOW", LIEF::assembly::mips::OPCODE::G_FPOW)
  .value("G_FPOWI", LIEF::assembly::mips::OPCODE::G_FPOWI)
  .value("G_FEXP", LIEF::assembly::mips::OPCODE::G_FEXP)
  .value("G_FEXP2", LIEF::assembly::mips::OPCODE::G_FEXP2)
  .value("G_FEXP10", LIEF::assembly::mips::OPCODE::G_FEXP10)
  .value("G_FLOG", LIEF::assembly::mips::OPCODE::G_FLOG)
  .value("G_FLOG2", LIEF::assembly::mips::OPCODE::G_FLOG2)
  .value("G_FLOG10", LIEF::assembly::mips::OPCODE::G_FLOG10)
  .value("G_FLDEXP", LIEF::assembly::mips::OPCODE::G_FLDEXP)
  .value("G_FFREXP", LIEF::assembly::mips::OPCODE::G_FFREXP)
  .value("G_FNEG", LIEF::assembly::mips::OPCODE::G_FNEG)
  .value("G_FPEXT", LIEF::assembly::mips::OPCODE::G_FPEXT)
  .value("G_FPTRUNC", LIEF::assembly::mips::OPCODE::G_FPTRUNC)
  .value("G_FPTOSI", LIEF::assembly::mips::OPCODE::G_FPTOSI)
  .value("G_FPTOUI", LIEF::assembly::mips::OPCODE::G_FPTOUI)
  .value("G_SITOFP", LIEF::assembly::mips::OPCODE::G_SITOFP)
  .value("G_UITOFP", LIEF::assembly::mips::OPCODE::G_UITOFP)
  .value("G_FPTOSI_SAT", LIEF::assembly::mips::OPCODE::G_FPTOSI_SAT)
  .value("G_FPTOUI_SAT", LIEF::assembly::mips::OPCODE::G_FPTOUI_SAT)
  .value("G_FABS", LIEF::assembly::mips::OPCODE::G_FABS)
  .value("G_FCOPYSIGN", LIEF::assembly::mips::OPCODE::G_FCOPYSIGN)
  .value("G_IS_FPCLASS", LIEF::assembly::mips::OPCODE::G_IS_FPCLASS)
  .value("G_FCANONICALIZE", LIEF::assembly::mips::OPCODE::G_FCANONICALIZE)
  .value("G_FMINNUM", LIEF::assembly::mips::OPCODE::G_FMINNUM)
  .value("G_FMAXNUM", LIEF::assembly::mips::OPCODE::G_FMAXNUM)
  .value("G_FMINNUM_IEEE", LIEF::assembly::mips::OPCODE::G_FMINNUM_IEEE)
  .value("G_FMAXNUM_IEEE", LIEF::assembly::mips::OPCODE::G_FMAXNUM_IEEE)
  .value("G_FMINIMUM", LIEF::assembly::mips::OPCODE::G_FMINIMUM)
  .value("G_FMAXIMUM", LIEF::assembly::mips::OPCODE::G_FMAXIMUM)
  .value("G_GET_FPENV", LIEF::assembly::mips::OPCODE::G_GET_FPENV)
  .value("G_SET_FPENV", LIEF::assembly::mips::OPCODE::G_SET_FPENV)
  .value("G_RESET_FPENV", LIEF::assembly::mips::OPCODE::G_RESET_FPENV)
  .value("G_GET_FPMODE", LIEF::assembly::mips::OPCODE::G_GET_FPMODE)
  .value("G_SET_FPMODE", LIEF::assembly::mips::OPCODE::G_SET_FPMODE)
  .value("G_RESET_FPMODE", LIEF::assembly::mips::OPCODE::G_RESET_FPMODE)
  .value("G_PTR_ADD", LIEF::assembly::mips::OPCODE::G_PTR_ADD)
  .value("G_PTRMASK", LIEF::assembly::mips::OPCODE::G_PTRMASK)
  .value("G_SMIN", LIEF::assembly::mips::OPCODE::G_SMIN)
  .value("G_SMAX", LIEF::assembly::mips::OPCODE::G_SMAX)
  .value("G_UMIN", LIEF::assembly::mips::OPCODE::G_UMIN)
  .value("G_UMAX", LIEF::assembly::mips::OPCODE::G_UMAX)
  .value("G_ABS", LIEF::assembly::mips::OPCODE::G_ABS)
  .value("G_LROUND", LIEF::assembly::mips::OPCODE::G_LROUND)
  .value("G_LLROUND", LIEF::assembly::mips::OPCODE::G_LLROUND)
  .value("G_BR", LIEF::assembly::mips::OPCODE::G_BR)
  .value("G_BRJT", LIEF::assembly::mips::OPCODE::G_BRJT)
  .value("G_VSCALE", LIEF::assembly::mips::OPCODE::G_VSCALE)
  .value("G_INSERT_SUBVECTOR", LIEF::assembly::mips::OPCODE::G_INSERT_SUBVECTOR)
  .value("G_EXTRACT_SUBVECTOR", LIEF::assembly::mips::OPCODE::G_EXTRACT_SUBVECTOR)
  .value("G_INSERT_VECTOR_ELT", LIEF::assembly::mips::OPCODE::G_INSERT_VECTOR_ELT)
  .value("G_EXTRACT_VECTOR_ELT", LIEF::assembly::mips::OPCODE::G_EXTRACT_VECTOR_ELT)
  .value("G_SHUFFLE_VECTOR", LIEF::assembly::mips::OPCODE::G_SHUFFLE_VECTOR)
  .value("G_SPLAT_VECTOR", LIEF::assembly::mips::OPCODE::G_SPLAT_VECTOR)
  .value("G_STEP_VECTOR", LIEF::assembly::mips::OPCODE::G_STEP_VECTOR)
  .value("G_VECTOR_COMPRESS", LIEF::assembly::mips::OPCODE::G_VECTOR_COMPRESS)
  .value("G_CTTZ", LIEF::assembly::mips::OPCODE::G_CTTZ)
  .value("G_CTTZ_ZERO_UNDEF", LIEF::assembly::mips::OPCODE::G_CTTZ_ZERO_UNDEF)
  .value("G_CTLZ", LIEF::assembly::mips::OPCODE::G_CTLZ)
  .value("G_CTLZ_ZERO_UNDEF", LIEF::assembly::mips::OPCODE::G_CTLZ_ZERO_UNDEF)
  .value("G_CTPOP", LIEF::assembly::mips::OPCODE::G_CTPOP)
  .value("G_BSWAP", LIEF::assembly::mips::OPCODE::G_BSWAP)
  .value("G_BITREVERSE", LIEF::assembly::mips::OPCODE::G_BITREVERSE)
  .value("G_FCEIL", LIEF::assembly::mips::OPCODE::G_FCEIL)
  .value("G_FCOS", LIEF::assembly::mips::OPCODE::G_FCOS)
  .value("G_FSIN", LIEF::assembly::mips::OPCODE::G_FSIN)
  .value("G_FSINCOS", LIEF::assembly::mips::OPCODE::G_FSINCOS)
  .value("G_FTAN", LIEF::assembly::mips::OPCODE::G_FTAN)
  .value("G_FACOS", LIEF::assembly::mips::OPCODE::G_FACOS)
  .value("G_FASIN", LIEF::assembly::mips::OPCODE::G_FASIN)
  .value("G_FATAN", LIEF::assembly::mips::OPCODE::G_FATAN)
  .value("G_FATAN2", LIEF::assembly::mips::OPCODE::G_FATAN2)
  .value("G_FCOSH", LIEF::assembly::mips::OPCODE::G_FCOSH)
  .value("G_FSINH", LIEF::assembly::mips::OPCODE::G_FSINH)
  .value("G_FTANH", LIEF::assembly::mips::OPCODE::G_FTANH)
  .value("G_FSQRT", LIEF::assembly::mips::OPCODE::G_FSQRT)
  .value("G_FFLOOR", LIEF::assembly::mips::OPCODE::G_FFLOOR)
  .value("G_FRINT", LIEF::assembly::mips::OPCODE::G_FRINT)
  .value("G_FNEARBYINT", LIEF::assembly::mips::OPCODE::G_FNEARBYINT)
  .value("G_ADDRSPACE_CAST", LIEF::assembly::mips::OPCODE::G_ADDRSPACE_CAST)
  .value("G_BLOCK_ADDR", LIEF::assembly::mips::OPCODE::G_BLOCK_ADDR)
  .value("G_JUMP_TABLE", LIEF::assembly::mips::OPCODE::G_JUMP_TABLE)
  .value("G_DYN_STACKALLOC", LIEF::assembly::mips::OPCODE::G_DYN_STACKALLOC)
  .value("G_STACKSAVE", LIEF::assembly::mips::OPCODE::G_STACKSAVE)
  .value("G_STACKRESTORE", LIEF::assembly::mips::OPCODE::G_STACKRESTORE)
  .value("G_STRICT_FADD", LIEF::assembly::mips::OPCODE::G_STRICT_FADD)
  .value("G_STRICT_FSUB", LIEF::assembly::mips::OPCODE::G_STRICT_FSUB)
  .value("G_STRICT_FMUL", LIEF::assembly::mips::OPCODE::G_STRICT_FMUL)
  .value("G_STRICT_FDIV", LIEF::assembly::mips::OPCODE::G_STRICT_FDIV)
  .value("G_STRICT_FREM", LIEF::assembly::mips::OPCODE::G_STRICT_FREM)
  .value("G_STRICT_FMA", LIEF::assembly::mips::OPCODE::G_STRICT_FMA)
  .value("G_STRICT_FSQRT", LIEF::assembly::mips::OPCODE::G_STRICT_FSQRT)
  .value("G_STRICT_FLDEXP", LIEF::assembly::mips::OPCODE::G_STRICT_FLDEXP)
  .value("G_READ_REGISTER", LIEF::assembly::mips::OPCODE::G_READ_REGISTER)
  .value("G_WRITE_REGISTER", LIEF::assembly::mips::OPCODE::G_WRITE_REGISTER)
  .value("G_MEMCPY", LIEF::assembly::mips::OPCODE::G_MEMCPY)
  .value("G_MEMCPY_INLINE", LIEF::assembly::mips::OPCODE::G_MEMCPY_INLINE)
  .value("G_MEMMOVE", LIEF::assembly::mips::OPCODE::G_MEMMOVE)
  .value("G_MEMSET", LIEF::assembly::mips::OPCODE::G_MEMSET)
  .value("G_BZERO", LIEF::assembly::mips::OPCODE::G_BZERO)
  .value("G_TRAP", LIEF::assembly::mips::OPCODE::G_TRAP)
  .value("G_DEBUGTRAP", LIEF::assembly::mips::OPCODE::G_DEBUGTRAP)
  .value("G_UBSANTRAP", LIEF::assembly::mips::OPCODE::G_UBSANTRAP)
  .value("G_VECREDUCE_SEQ_FADD", LIEF::assembly::mips::OPCODE::G_VECREDUCE_SEQ_FADD)
  .value("G_VECREDUCE_SEQ_FMUL", LIEF::assembly::mips::OPCODE::G_VECREDUCE_SEQ_FMUL)
  .value("G_VECREDUCE_FADD", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FADD)
  .value("G_VECREDUCE_FMUL", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FMUL)
  .value("G_VECREDUCE_FMAX", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FMAX)
  .value("G_VECREDUCE_FMIN", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FMIN)
  .value("G_VECREDUCE_FMAXIMUM", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FMAXIMUM)
  .value("G_VECREDUCE_FMINIMUM", LIEF::assembly::mips::OPCODE::G_VECREDUCE_FMINIMUM)
  .value("G_VECREDUCE_ADD", LIEF::assembly::mips::OPCODE::G_VECREDUCE_ADD)
  .value("G_VECREDUCE_MUL", LIEF::assembly::mips::OPCODE::G_VECREDUCE_MUL)
  .value("G_VECREDUCE_AND", LIEF::assembly::mips::OPCODE::G_VECREDUCE_AND)
  .value("G_VECREDUCE_OR", LIEF::assembly::mips::OPCODE::G_VECREDUCE_OR)
  .value("G_VECREDUCE_XOR", LIEF::assembly::mips::OPCODE::G_VECREDUCE_XOR);
  opcodes.value("G_VECREDUCE_SMAX", LIEF::assembly::mips::OPCODE::G_VECREDUCE_SMAX)
  .value("G_VECREDUCE_SMIN", LIEF::assembly::mips::OPCODE::G_VECREDUCE_SMIN)
  .value("G_VECREDUCE_UMAX", LIEF::assembly::mips::OPCODE::G_VECREDUCE_UMAX)
  .value("G_VECREDUCE_UMIN", LIEF::assembly::mips::OPCODE::G_VECREDUCE_UMIN)
  .value("G_SBFX", LIEF::assembly::mips::OPCODE::G_SBFX)
  .value("G_UBFX", LIEF::assembly::mips::OPCODE::G_UBFX)
  .value("ABSMacro", LIEF::assembly::mips::OPCODE::ABSMacro)
  .value("ADJCALLSTACKDOWN", LIEF::assembly::mips::OPCODE::ADJCALLSTACKDOWN)
  .value("ADJCALLSTACKUP", LIEF::assembly::mips::OPCODE::ADJCALLSTACKUP)
  .value("AND_V_D_PSEUDO", LIEF::assembly::mips::OPCODE::AND_V_D_PSEUDO)
  .value("AND_V_H_PSEUDO", LIEF::assembly::mips::OPCODE::AND_V_H_PSEUDO)
  .value("AND_V_W_PSEUDO", LIEF::assembly::mips::OPCODE::AND_V_W_PSEUDO)
  .value("ATOMIC_CMP_SWAP_I16", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I16)
  .value("ATOMIC_CMP_SWAP_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I16_POSTRA)
  .value("ATOMIC_CMP_SWAP_I32", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I32)
  .value("ATOMIC_CMP_SWAP_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I32_POSTRA)
  .value("ATOMIC_CMP_SWAP_I64", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I64)
  .value("ATOMIC_CMP_SWAP_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I64_POSTRA)
  .value("ATOMIC_CMP_SWAP_I8", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I8)
  .value("ATOMIC_CMP_SWAP_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_CMP_SWAP_I8_POSTRA)
  .value("ATOMIC_LOAD_ADD_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I16)
  .value("ATOMIC_LOAD_ADD_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I16_POSTRA)
  .value("ATOMIC_LOAD_ADD_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I32)
  .value("ATOMIC_LOAD_ADD_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I32_POSTRA)
  .value("ATOMIC_LOAD_ADD_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I64)
  .value("ATOMIC_LOAD_ADD_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I64_POSTRA)
  .value("ATOMIC_LOAD_ADD_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I8)
  .value("ATOMIC_LOAD_ADD_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_ADD_I8_POSTRA)
  .value("ATOMIC_LOAD_AND_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I16)
  .value("ATOMIC_LOAD_AND_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I16_POSTRA)
  .value("ATOMIC_LOAD_AND_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I32)
  .value("ATOMIC_LOAD_AND_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I32_POSTRA)
  .value("ATOMIC_LOAD_AND_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I64)
  .value("ATOMIC_LOAD_AND_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I64_POSTRA)
  .value("ATOMIC_LOAD_AND_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I8)
  .value("ATOMIC_LOAD_AND_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_AND_I8_POSTRA)
  .value("ATOMIC_LOAD_MAX_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I16)
  .value("ATOMIC_LOAD_MAX_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I16_POSTRA)
  .value("ATOMIC_LOAD_MAX_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I32)
  .value("ATOMIC_LOAD_MAX_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I32_POSTRA)
  .value("ATOMIC_LOAD_MAX_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I64)
  .value("ATOMIC_LOAD_MAX_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I64_POSTRA)
  .value("ATOMIC_LOAD_MAX_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I8)
  .value("ATOMIC_LOAD_MAX_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MAX_I8_POSTRA)
  .value("ATOMIC_LOAD_MIN_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I16)
  .value("ATOMIC_LOAD_MIN_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I16_POSTRA)
  .value("ATOMIC_LOAD_MIN_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I32)
  .value("ATOMIC_LOAD_MIN_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I32_POSTRA)
  .value("ATOMIC_LOAD_MIN_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I64)
  .value("ATOMIC_LOAD_MIN_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I64_POSTRA)
  .value("ATOMIC_LOAD_MIN_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I8)
  .value("ATOMIC_LOAD_MIN_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_MIN_I8_POSTRA)
  .value("ATOMIC_LOAD_NAND_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I16)
  .value("ATOMIC_LOAD_NAND_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I16_POSTRA)
  .value("ATOMIC_LOAD_NAND_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I32)
  .value("ATOMIC_LOAD_NAND_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I32_POSTRA)
  .value("ATOMIC_LOAD_NAND_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I64)
  .value("ATOMIC_LOAD_NAND_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I64_POSTRA)
  .value("ATOMIC_LOAD_NAND_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I8)
  .value("ATOMIC_LOAD_NAND_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_NAND_I8_POSTRA)
  .value("ATOMIC_LOAD_OR_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I16)
  .value("ATOMIC_LOAD_OR_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I16_POSTRA)
  .value("ATOMIC_LOAD_OR_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I32)
  .value("ATOMIC_LOAD_OR_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I32_POSTRA)
  .value("ATOMIC_LOAD_OR_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I64)
  .value("ATOMIC_LOAD_OR_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I64_POSTRA)
  .value("ATOMIC_LOAD_OR_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I8)
  .value("ATOMIC_LOAD_OR_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_OR_I8_POSTRA)
  .value("ATOMIC_LOAD_SUB_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I16)
  .value("ATOMIC_LOAD_SUB_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I16_POSTRA)
  .value("ATOMIC_LOAD_SUB_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I32)
  .value("ATOMIC_LOAD_SUB_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I32_POSTRA)
  .value("ATOMIC_LOAD_SUB_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I64)
  .value("ATOMIC_LOAD_SUB_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I64_POSTRA)
  .value("ATOMIC_LOAD_SUB_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I8)
  .value("ATOMIC_LOAD_SUB_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_SUB_I8_POSTRA)
  .value("ATOMIC_LOAD_UMAX_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I16)
  .value("ATOMIC_LOAD_UMAX_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I16_POSTRA)
  .value("ATOMIC_LOAD_UMAX_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I32)
  .value("ATOMIC_LOAD_UMAX_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I32_POSTRA)
  .value("ATOMIC_LOAD_UMAX_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I64)
  .value("ATOMIC_LOAD_UMAX_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I64_POSTRA)
  .value("ATOMIC_LOAD_UMAX_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I8)
  .value("ATOMIC_LOAD_UMAX_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMAX_I8_POSTRA)
  .value("ATOMIC_LOAD_UMIN_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I16)
  .value("ATOMIC_LOAD_UMIN_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I16_POSTRA)
  .value("ATOMIC_LOAD_UMIN_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I32)
  .value("ATOMIC_LOAD_UMIN_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I32_POSTRA)
  .value("ATOMIC_LOAD_UMIN_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I64)
  .value("ATOMIC_LOAD_UMIN_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I64_POSTRA)
  .value("ATOMIC_LOAD_UMIN_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I8)
  .value("ATOMIC_LOAD_UMIN_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_UMIN_I8_POSTRA)
  .value("ATOMIC_LOAD_XOR_I16", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I16)
  .value("ATOMIC_LOAD_XOR_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I16_POSTRA)
  .value("ATOMIC_LOAD_XOR_I32", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I32)
  .value("ATOMIC_LOAD_XOR_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I32_POSTRA)
  .value("ATOMIC_LOAD_XOR_I64", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I64)
  .value("ATOMIC_LOAD_XOR_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I64_POSTRA)
  .value("ATOMIC_LOAD_XOR_I8", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I8)
  .value("ATOMIC_LOAD_XOR_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_LOAD_XOR_I8_POSTRA)
  .value("ATOMIC_SWAP_I16", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I16)
  .value("ATOMIC_SWAP_I16_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I16_POSTRA)
  .value("ATOMIC_SWAP_I32", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I32)
  .value("ATOMIC_SWAP_I32_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I32_POSTRA)
  .value("ATOMIC_SWAP_I64", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I64)
  .value("ATOMIC_SWAP_I64_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I64_POSTRA)
  .value("ATOMIC_SWAP_I8", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I8)
  .value("ATOMIC_SWAP_I8_POSTRA", LIEF::assembly::mips::OPCODE::ATOMIC_SWAP_I8_POSTRA)
  .value("B", LIEF::assembly::mips::OPCODE::B)
  .value("BAL_BR", LIEF::assembly::mips::OPCODE::BAL_BR)
  .value("BAL_BR_MM", LIEF::assembly::mips::OPCODE::BAL_BR_MM)
  .value("BEQLImmMacro", LIEF::assembly::mips::OPCODE::BEQLImmMacro)
  .value("BGE", LIEF::assembly::mips::OPCODE::BGE)
  .value("BGEImmMacro", LIEF::assembly::mips::OPCODE::BGEImmMacro)
  .value("BGEL", LIEF::assembly::mips::OPCODE::BGEL)
  .value("BGELImmMacro", LIEF::assembly::mips::OPCODE::BGELImmMacro)
  .value("BGEU", LIEF::assembly::mips::OPCODE::BGEU)
  .value("BGEUImmMacro", LIEF::assembly::mips::OPCODE::BGEUImmMacro)
  .value("BGEUL", LIEF::assembly::mips::OPCODE::BGEUL)
  .value("BGEULImmMacro", LIEF::assembly::mips::OPCODE::BGEULImmMacro)
  .value("BGT", LIEF::assembly::mips::OPCODE::BGT)
  .value("BGTImmMacro", LIEF::assembly::mips::OPCODE::BGTImmMacro)
  .value("BGTL", LIEF::assembly::mips::OPCODE::BGTL)
  .value("BGTLImmMacro", LIEF::assembly::mips::OPCODE::BGTLImmMacro)
  .value("BGTU", LIEF::assembly::mips::OPCODE::BGTU)
  .value("BGTUImmMacro", LIEF::assembly::mips::OPCODE::BGTUImmMacro)
  .value("BGTUL", LIEF::assembly::mips::OPCODE::BGTUL)
  .value("BGTULImmMacro", LIEF::assembly::mips::OPCODE::BGTULImmMacro)
  .value("BLE", LIEF::assembly::mips::OPCODE::BLE)
  .value("BLEImmMacro", LIEF::assembly::mips::OPCODE::BLEImmMacro)
  .value("BLEL", LIEF::assembly::mips::OPCODE::BLEL)
  .value("BLELImmMacro", LIEF::assembly::mips::OPCODE::BLELImmMacro)
  .value("BLEU", LIEF::assembly::mips::OPCODE::BLEU)
  .value("BLEUImmMacro", LIEF::assembly::mips::OPCODE::BLEUImmMacro)
  .value("BLEUL", LIEF::assembly::mips::OPCODE::BLEUL)
  .value("BLEULImmMacro", LIEF::assembly::mips::OPCODE::BLEULImmMacro)
  .value("BLT", LIEF::assembly::mips::OPCODE::BLT)
  .value("BLTImmMacro", LIEF::assembly::mips::OPCODE::BLTImmMacro)
  .value("BLTL", LIEF::assembly::mips::OPCODE::BLTL)
  .value("BLTLImmMacro", LIEF::assembly::mips::OPCODE::BLTLImmMacro)
  .value("BLTU", LIEF::assembly::mips::OPCODE::BLTU)
  .value("BLTUImmMacro", LIEF::assembly::mips::OPCODE::BLTUImmMacro)
  .value("BLTUL", LIEF::assembly::mips::OPCODE::BLTUL)
  .value("BLTULImmMacro", LIEF::assembly::mips::OPCODE::BLTULImmMacro)
  .value("BNELImmMacro", LIEF::assembly::mips::OPCODE::BNELImmMacro)
  .value("BPOSGE32_PSEUDO", LIEF::assembly::mips::OPCODE::BPOSGE32_PSEUDO)
  .value("BSEL_D_PSEUDO", LIEF::assembly::mips::OPCODE::BSEL_D_PSEUDO)
  .value("BSEL_FD_PSEUDO", LIEF::assembly::mips::OPCODE::BSEL_FD_PSEUDO)
  .value("BSEL_FW_PSEUDO", LIEF::assembly::mips::OPCODE::BSEL_FW_PSEUDO)
  .value("BSEL_H_PSEUDO", LIEF::assembly::mips::OPCODE::BSEL_H_PSEUDO)
  .value("BSEL_W_PSEUDO", LIEF::assembly::mips::OPCODE::BSEL_W_PSEUDO)
  .value("B_MM", LIEF::assembly::mips::OPCODE::B_MM)
  .value("B_MMR6_Pseudo", LIEF::assembly::mips::OPCODE::B_MMR6_Pseudo)
  .value("B_MM_Pseudo", LIEF::assembly::mips::OPCODE::B_MM_Pseudo)
  .value("BeqImm", LIEF::assembly::mips::OPCODE::BeqImm)
  .value("BneImm", LIEF::assembly::mips::OPCODE::BneImm)
  .value("BteqzT8CmpX16", LIEF::assembly::mips::OPCODE::BteqzT8CmpX16)
  .value("BteqzT8CmpiX16", LIEF::assembly::mips::OPCODE::BteqzT8CmpiX16)
  .value("BteqzT8SltX16", LIEF::assembly::mips::OPCODE::BteqzT8SltX16)
  .value("BteqzT8SltiX16", LIEF::assembly::mips::OPCODE::BteqzT8SltiX16)
  .value("BteqzT8SltiuX16", LIEF::assembly::mips::OPCODE::BteqzT8SltiuX16)
  .value("BteqzT8SltuX16", LIEF::assembly::mips::OPCODE::BteqzT8SltuX16)
  .value("BtnezT8CmpX16", LIEF::assembly::mips::OPCODE::BtnezT8CmpX16)
  .value("BtnezT8CmpiX16", LIEF::assembly::mips::OPCODE::BtnezT8CmpiX16)
  .value("BtnezT8SltX16", LIEF::assembly::mips::OPCODE::BtnezT8SltX16)
  .value("BtnezT8SltiX16", LIEF::assembly::mips::OPCODE::BtnezT8SltiX16)
  .value("BtnezT8SltiuX16", LIEF::assembly::mips::OPCODE::BtnezT8SltiuX16)
  .value("BtnezT8SltuX16", LIEF::assembly::mips::OPCODE::BtnezT8SltuX16)
  .value("BuildPairF64", LIEF::assembly::mips::OPCODE::BuildPairF64)
  .value("BuildPairF64_64", LIEF::assembly::mips::OPCODE::BuildPairF64_64)
  .value("CFTC1", LIEF::assembly::mips::OPCODE::CFTC1)
  .value("CONSTPOOL_ENTRY", LIEF::assembly::mips::OPCODE::CONSTPOOL_ENTRY)
  .value("COPY_FD_PSEUDO", LIEF::assembly::mips::OPCODE::COPY_FD_PSEUDO)
  .value("COPY_FW_PSEUDO", LIEF::assembly::mips::OPCODE::COPY_FW_PSEUDO)
  .value("CTTC1", LIEF::assembly::mips::OPCODE::CTTC1)
  .value("Constant32", LIEF::assembly::mips::OPCODE::Constant32)
  .value("DMULImmMacro", LIEF::assembly::mips::OPCODE::DMULImmMacro)
  .value("DMULMacro", LIEF::assembly::mips::OPCODE::DMULMacro)
  .value("DMULOMacro", LIEF::assembly::mips::OPCODE::DMULOMacro)
  .value("DMULOUMacro", LIEF::assembly::mips::OPCODE::DMULOUMacro)
  .value("DROL", LIEF::assembly::mips::OPCODE::DROL)
  .value("DROLImm", LIEF::assembly::mips::OPCODE::DROLImm)
  .value("DROR", LIEF::assembly::mips::OPCODE::DROR)
  .value("DRORImm", LIEF::assembly::mips::OPCODE::DRORImm)
  .value("DSDivIMacro", LIEF::assembly::mips::OPCODE::DSDivIMacro)
  .value("DSDivMacro", LIEF::assembly::mips::OPCODE::DSDivMacro)
  .value("DSRemIMacro", LIEF::assembly::mips::OPCODE::DSRemIMacro)
  .value("DSRemMacro", LIEF::assembly::mips::OPCODE::DSRemMacro)
  .value("DUDivIMacro", LIEF::assembly::mips::OPCODE::DUDivIMacro)
  .value("DUDivMacro", LIEF::assembly::mips::OPCODE::DUDivMacro)
  .value("DURemIMacro", LIEF::assembly::mips::OPCODE::DURemIMacro)
  .value("DURemMacro", LIEF::assembly::mips::OPCODE::DURemMacro)
  .value("ERet", LIEF::assembly::mips::OPCODE::ERet)
  .value("ExtractElementF64", LIEF::assembly::mips::OPCODE::ExtractElementF64)
  .value("ExtractElementF64_64", LIEF::assembly::mips::OPCODE::ExtractElementF64_64)
  .value("FABS_D", LIEF::assembly::mips::OPCODE::FABS_D)
  .value("FABS_W", LIEF::assembly::mips::OPCODE::FABS_W)
  .value("FEXP2_D_1_PSEUDO", LIEF::assembly::mips::OPCODE::FEXP2_D_1_PSEUDO)
  .value("FEXP2_W_1_PSEUDO", LIEF::assembly::mips::OPCODE::FEXP2_W_1_PSEUDO)
  .value("FILL_FD_PSEUDO", LIEF::assembly::mips::OPCODE::FILL_FD_PSEUDO)
  .value("FILL_FW_PSEUDO", LIEF::assembly::mips::OPCODE::FILL_FW_PSEUDO)
  .value("GotPrologue16", LIEF::assembly::mips::OPCODE::GotPrologue16)
  .value("INSERT_B_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_B_VIDX64_PSEUDO)
  .value("INSERT_B_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_B_VIDX_PSEUDO)
  .value("INSERT_D_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_D_VIDX64_PSEUDO)
  .value("INSERT_D_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_D_VIDX_PSEUDO)
  .value("INSERT_FD_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FD_PSEUDO)
  .value("INSERT_FD_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FD_VIDX64_PSEUDO)
  .value("INSERT_FD_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FD_VIDX_PSEUDO)
  .value("INSERT_FW_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FW_PSEUDO)
  .value("INSERT_FW_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FW_VIDX64_PSEUDO)
  .value("INSERT_FW_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_FW_VIDX_PSEUDO)
  .value("INSERT_H_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_H_VIDX64_PSEUDO)
  .value("INSERT_H_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_H_VIDX_PSEUDO)
  .value("INSERT_W_VIDX64_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_W_VIDX64_PSEUDO)
  .value("INSERT_W_VIDX_PSEUDO", LIEF::assembly::mips::OPCODE::INSERT_W_VIDX_PSEUDO)
  .value("JALR64Pseudo", LIEF::assembly::mips::OPCODE::JALR64Pseudo)
  .value("JALRHB64Pseudo", LIEF::assembly::mips::OPCODE::JALRHB64Pseudo)
  .value("JALRHBPseudo", LIEF::assembly::mips::OPCODE::JALRHBPseudo)
  .value("JALRPseudo", LIEF::assembly::mips::OPCODE::JALRPseudo)
  .value("JAL_MMR6", LIEF::assembly::mips::OPCODE::JAL_MMR6)
  .value("JalOneReg", LIEF::assembly::mips::OPCODE::JalOneReg)
  .value("JalTwoReg", LIEF::assembly::mips::OPCODE::JalTwoReg)
  .value("LDMacro", LIEF::assembly::mips::OPCODE::LDMacro)
  .value("LDR_D", LIEF::assembly::mips::OPCODE::LDR_D)
  .value("LDR_W", LIEF::assembly::mips::OPCODE::LDR_W)
  .value("LD_F16", LIEF::assembly::mips::OPCODE::LD_F16)
  .value("LOAD_ACC128", LIEF::assembly::mips::OPCODE::LOAD_ACC128)
  .value("LOAD_ACC64", LIEF::assembly::mips::OPCODE::LOAD_ACC64)
  .value("LOAD_ACC64DSP", LIEF::assembly::mips::OPCODE::LOAD_ACC64DSP)
  .value("LOAD_CCOND_DSP", LIEF::assembly::mips::OPCODE::LOAD_CCOND_DSP)
  .value("LONG_BRANCH_ADDiu", LIEF::assembly::mips::OPCODE::LONG_BRANCH_ADDiu)
  .value("LONG_BRANCH_ADDiu2Op", LIEF::assembly::mips::OPCODE::LONG_BRANCH_ADDiu2Op)
  .value("LONG_BRANCH_DADDiu", LIEF::assembly::mips::OPCODE::LONG_BRANCH_DADDiu)
  .value("LONG_BRANCH_DADDiu2Op", LIEF::assembly::mips::OPCODE::LONG_BRANCH_DADDiu2Op)
  .value("LONG_BRANCH_LUi", LIEF::assembly::mips::OPCODE::LONG_BRANCH_LUi)
  .value("LONG_BRANCH_LUi2Op", LIEF::assembly::mips::OPCODE::LONG_BRANCH_LUi2Op)
  .value("LONG_BRANCH_LUi2Op_64", LIEF::assembly::mips::OPCODE::LONG_BRANCH_LUi2Op_64)
  .value("LWM_MM", LIEF::assembly::mips::OPCODE::LWM_MM)
  .value("LoadAddrImm32", LIEF::assembly::mips::OPCODE::LoadAddrImm32)
  .value("LoadAddrImm64", LIEF::assembly::mips::OPCODE::LoadAddrImm64)
  .value("LoadAddrReg32", LIEF::assembly::mips::OPCODE::LoadAddrReg32)
  .value("LoadAddrReg64", LIEF::assembly::mips::OPCODE::LoadAddrReg64)
  .value("LoadImm32", LIEF::assembly::mips::OPCODE::LoadImm32)
  .value("LoadImm64", LIEF::assembly::mips::OPCODE::LoadImm64)
  .value("LoadImmDoubleFGR", LIEF::assembly::mips::OPCODE::LoadImmDoubleFGR)
  .value("LoadImmDoubleFGR_32", LIEF::assembly::mips::OPCODE::LoadImmDoubleFGR_32)
  .value("LoadImmDoubleGPR", LIEF::assembly::mips::OPCODE::LoadImmDoubleGPR)
  .value("LoadImmSingleFGR", LIEF::assembly::mips::OPCODE::LoadImmSingleFGR)
  .value("LoadImmSingleGPR", LIEF::assembly::mips::OPCODE::LoadImmSingleGPR)
  .value("LwConstant32", LIEF::assembly::mips::OPCODE::LwConstant32)
  .value("MFTACX", LIEF::assembly::mips::OPCODE::MFTACX)
  .value("MFTC0", LIEF::assembly::mips::OPCODE::MFTC0)
  .value("MFTC1", LIEF::assembly::mips::OPCODE::MFTC1)
  .value("MFTDSP", LIEF::assembly::mips::OPCODE::MFTDSP)
  .value("MFTGPR", LIEF::assembly::mips::OPCODE::MFTGPR)
  .value("MFTHC1", LIEF::assembly::mips::OPCODE::MFTHC1)
  .value("MFTHI", LIEF::assembly::mips::OPCODE::MFTHI)
  .value("MFTLO", LIEF::assembly::mips::OPCODE::MFTLO)
  .value("MIPSeh_return32", LIEF::assembly::mips::OPCODE::MIPSeh_return32)
  .value("MIPSeh_return64", LIEF::assembly::mips::OPCODE::MIPSeh_return64)
  .value("MSA_FP_EXTEND_D_PSEUDO", LIEF::assembly::mips::OPCODE::MSA_FP_EXTEND_D_PSEUDO)
  .value("MSA_FP_EXTEND_W_PSEUDO", LIEF::assembly::mips::OPCODE::MSA_FP_EXTEND_W_PSEUDO)
  .value("MSA_FP_ROUND_D_PSEUDO", LIEF::assembly::mips::OPCODE::MSA_FP_ROUND_D_PSEUDO)
  .value("MSA_FP_ROUND_W_PSEUDO", LIEF::assembly::mips::OPCODE::MSA_FP_ROUND_W_PSEUDO)
  .value("MTTACX", LIEF::assembly::mips::OPCODE::MTTACX)
  .value("MTTC0", LIEF::assembly::mips::OPCODE::MTTC0)
  .value("MTTC1", LIEF::assembly::mips::OPCODE::MTTC1)
  .value("MTTDSP", LIEF::assembly::mips::OPCODE::MTTDSP)
  .value("MTTGPR", LIEF::assembly::mips::OPCODE::MTTGPR)
  .value("MTTHC1", LIEF::assembly::mips::OPCODE::MTTHC1)
  .value("MTTHI", LIEF::assembly::mips::OPCODE::MTTHI)
  .value("MTTLO", LIEF::assembly::mips::OPCODE::MTTLO)
  .value("MULImmMacro", LIEF::assembly::mips::OPCODE::MULImmMacro)
  .value("MULOMacro", LIEF::assembly::mips::OPCODE::MULOMacro)
  .value("MULOUMacro", LIEF::assembly::mips::OPCODE::MULOUMacro)
  .value("MultRxRy16", LIEF::assembly::mips::OPCODE::MultRxRy16)
  .value("MultRxRyRz16", LIEF::assembly::mips::OPCODE::MultRxRyRz16)
  .value("MultuRxRy16", LIEF::assembly::mips::OPCODE::MultuRxRy16)
  .value("MultuRxRyRz16", LIEF::assembly::mips::OPCODE::MultuRxRyRz16)
  .value("NOP", LIEF::assembly::mips::OPCODE::NOP)
  .value("NORImm", LIEF::assembly::mips::OPCODE::NORImm)
  .value("NORImm64", LIEF::assembly::mips::OPCODE::NORImm64)
  .value("NOR_V_D_PSEUDO", LIEF::assembly::mips::OPCODE::NOR_V_D_PSEUDO)
  .value("NOR_V_H_PSEUDO", LIEF::assembly::mips::OPCODE::NOR_V_H_PSEUDO)
  .value("NOR_V_W_PSEUDO", LIEF::assembly::mips::OPCODE::NOR_V_W_PSEUDO)
  .value("OR_V_D_PSEUDO", LIEF::assembly::mips::OPCODE::OR_V_D_PSEUDO)
  .value("OR_V_H_PSEUDO", LIEF::assembly::mips::OPCODE::OR_V_H_PSEUDO)
  .value("OR_V_W_PSEUDO", LIEF::assembly::mips::OPCODE::OR_V_W_PSEUDO)
  .value("PseudoCMPU_EQ_QB", LIEF::assembly::mips::OPCODE::PseudoCMPU_EQ_QB)
  .value("PseudoCMPU_LE_QB", LIEF::assembly::mips::OPCODE::PseudoCMPU_LE_QB)
  .value("PseudoCMPU_LT_QB", LIEF::assembly::mips::OPCODE::PseudoCMPU_LT_QB)
  .value("PseudoCMP_EQ_PH", LIEF::assembly::mips::OPCODE::PseudoCMP_EQ_PH)
  .value("PseudoCMP_LE_PH", LIEF::assembly::mips::OPCODE::PseudoCMP_LE_PH)
  .value("PseudoCMP_LT_PH", LIEF::assembly::mips::OPCODE::PseudoCMP_LT_PH)
  .value("PseudoCVT_D32_W", LIEF::assembly::mips::OPCODE::PseudoCVT_D32_W)
  .value("PseudoCVT_D64_L", LIEF::assembly::mips::OPCODE::PseudoCVT_D64_L)
  .value("PseudoCVT_D64_W", LIEF::assembly::mips::OPCODE::PseudoCVT_D64_W)
  .value("PseudoCVT_S_L", LIEF::assembly::mips::OPCODE::PseudoCVT_S_L)
  .value("PseudoCVT_S_W", LIEF::assembly::mips::OPCODE::PseudoCVT_S_W);
  opcodes.value("PseudoDMULT", LIEF::assembly::mips::OPCODE::PseudoDMULT)
  .value("PseudoDMULTu", LIEF::assembly::mips::OPCODE::PseudoDMULTu)
  .value("PseudoDSDIV", LIEF::assembly::mips::OPCODE::PseudoDSDIV)
  .value("PseudoDUDIV", LIEF::assembly::mips::OPCODE::PseudoDUDIV)
  .value("PseudoD_SELECT_I", LIEF::assembly::mips::OPCODE::PseudoD_SELECT_I)
  .value("PseudoD_SELECT_I64", LIEF::assembly::mips::OPCODE::PseudoD_SELECT_I64)
  .value("PseudoIndirectBranch", LIEF::assembly::mips::OPCODE::PseudoIndirectBranch)
  .value("PseudoIndirectBranch64", LIEF::assembly::mips::OPCODE::PseudoIndirectBranch64)
  .value("PseudoIndirectBranch64R6", LIEF::assembly::mips::OPCODE::PseudoIndirectBranch64R6)
  .value("PseudoIndirectBranchR6", LIEF::assembly::mips::OPCODE::PseudoIndirectBranchR6)
  .value("PseudoIndirectBranch_MM", LIEF::assembly::mips::OPCODE::PseudoIndirectBranch_MM)
  .value("PseudoIndirectBranch_MMR6", LIEF::assembly::mips::OPCODE::PseudoIndirectBranch_MMR6)
  .value("PseudoIndirectHazardBranch", LIEF::assembly::mips::OPCODE::PseudoIndirectHazardBranch)
  .value("PseudoIndirectHazardBranch64", LIEF::assembly::mips::OPCODE::PseudoIndirectHazardBranch64)
  .value("PseudoIndrectHazardBranch64R6", LIEF::assembly::mips::OPCODE::PseudoIndrectHazardBranch64R6)
  .value("PseudoIndrectHazardBranchR6", LIEF::assembly::mips::OPCODE::PseudoIndrectHazardBranchR6)
  .value("PseudoMADD", LIEF::assembly::mips::OPCODE::PseudoMADD)
  .value("PseudoMADDU", LIEF::assembly::mips::OPCODE::PseudoMADDU)
  .value("PseudoMADDU_MM", LIEF::assembly::mips::OPCODE::PseudoMADDU_MM)
  .value("PseudoMADD_MM", LIEF::assembly::mips::OPCODE::PseudoMADD_MM)
  .value("PseudoMFHI", LIEF::assembly::mips::OPCODE::PseudoMFHI)
  .value("PseudoMFHI64", LIEF::assembly::mips::OPCODE::PseudoMFHI64)
  .value("PseudoMFHI_MM", LIEF::assembly::mips::OPCODE::PseudoMFHI_MM)
  .value("PseudoMFLO", LIEF::assembly::mips::OPCODE::PseudoMFLO)
  .value("PseudoMFLO64", LIEF::assembly::mips::OPCODE::PseudoMFLO64)
  .value("PseudoMFLO_MM", LIEF::assembly::mips::OPCODE::PseudoMFLO_MM)
  .value("PseudoMSUB", LIEF::assembly::mips::OPCODE::PseudoMSUB)
  .value("PseudoMSUBU", LIEF::assembly::mips::OPCODE::PseudoMSUBU)
  .value("PseudoMSUBU_MM", LIEF::assembly::mips::OPCODE::PseudoMSUBU_MM)
  .value("PseudoMSUB_MM", LIEF::assembly::mips::OPCODE::PseudoMSUB_MM)
  .value("PseudoMTLOHI", LIEF::assembly::mips::OPCODE::PseudoMTLOHI)
  .value("PseudoMTLOHI64", LIEF::assembly::mips::OPCODE::PseudoMTLOHI64)
  .value("PseudoMTLOHI_DSP", LIEF::assembly::mips::OPCODE::PseudoMTLOHI_DSP)
  .value("PseudoMTLOHI_MM", LIEF::assembly::mips::OPCODE::PseudoMTLOHI_MM)
  .value("PseudoMULT", LIEF::assembly::mips::OPCODE::PseudoMULT)
  .value("PseudoMULT_MM", LIEF::assembly::mips::OPCODE::PseudoMULT_MM)
  .value("PseudoMULTu", LIEF::assembly::mips::OPCODE::PseudoMULTu)
  .value("PseudoMULTu_MM", LIEF::assembly::mips::OPCODE::PseudoMULTu_MM)
  .value("PseudoPICK_PH", LIEF::assembly::mips::OPCODE::PseudoPICK_PH)
  .value("PseudoPICK_QB", LIEF::assembly::mips::OPCODE::PseudoPICK_QB)
  .value("PseudoReturn", LIEF::assembly::mips::OPCODE::PseudoReturn)
  .value("PseudoReturn64", LIEF::assembly::mips::OPCODE::PseudoReturn64)
  .value("PseudoSDIV", LIEF::assembly::mips::OPCODE::PseudoSDIV)
  .value("PseudoSELECTFP_F_D32", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_F_D32)
  .value("PseudoSELECTFP_F_D64", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_F_D64)
  .value("PseudoSELECTFP_F_I", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_F_I)
  .value("PseudoSELECTFP_F_I64", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_F_I64)
  .value("PseudoSELECTFP_F_S", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_F_S)
  .value("PseudoSELECTFP_T_D32", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_T_D32)
  .value("PseudoSELECTFP_T_D64", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_T_D64)
  .value("PseudoSELECTFP_T_I", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_T_I)
  .value("PseudoSELECTFP_T_I64", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_T_I64)
  .value("PseudoSELECTFP_T_S", LIEF::assembly::mips::OPCODE::PseudoSELECTFP_T_S)
  .value("PseudoSELECT_D32", LIEF::assembly::mips::OPCODE::PseudoSELECT_D32)
  .value("PseudoSELECT_D64", LIEF::assembly::mips::OPCODE::PseudoSELECT_D64)
  .value("PseudoSELECT_I", LIEF::assembly::mips::OPCODE::PseudoSELECT_I)
  .value("PseudoSELECT_I64", LIEF::assembly::mips::OPCODE::PseudoSELECT_I64)
  .value("PseudoSELECT_S", LIEF::assembly::mips::OPCODE::PseudoSELECT_S)
  .value("PseudoTRUNC_W_D", LIEF::assembly::mips::OPCODE::PseudoTRUNC_W_D)
  .value("PseudoTRUNC_W_D32", LIEF::assembly::mips::OPCODE::PseudoTRUNC_W_D32)
  .value("PseudoTRUNC_W_S", LIEF::assembly::mips::OPCODE::PseudoTRUNC_W_S)
  .value("PseudoUDIV", LIEF::assembly::mips::OPCODE::PseudoUDIV)
  .value("ROL", LIEF::assembly::mips::OPCODE::ROL)
  .value("ROLImm", LIEF::assembly::mips::OPCODE::ROLImm)
  .value("ROR", LIEF::assembly::mips::OPCODE::ROR)
  .value("RORImm", LIEF::assembly::mips::OPCODE::RORImm)
  .value("RetRA", LIEF::assembly::mips::OPCODE::RetRA)
  .value("RetRA16", LIEF::assembly::mips::OPCODE::RetRA16)
  .value("SDC1_M1", LIEF::assembly::mips::OPCODE::SDC1_M1)
  .value("SDIV_MM_Pseudo", LIEF::assembly::mips::OPCODE::SDIV_MM_Pseudo)
  .value("SDMacro", LIEF::assembly::mips::OPCODE::SDMacro)
  .value("SDivIMacro", LIEF::assembly::mips::OPCODE::SDivIMacro)
  .value("SDivMacro", LIEF::assembly::mips::OPCODE::SDivMacro)
  .value("SEQIMacro", LIEF::assembly::mips::OPCODE::SEQIMacro)
  .value("SEQMacro", LIEF::assembly::mips::OPCODE::SEQMacro)
  .value("SGE", LIEF::assembly::mips::OPCODE::SGE)
  .value("SGEImm", LIEF::assembly::mips::OPCODE::SGEImm)
  .value("SGEImm64", LIEF::assembly::mips::OPCODE::SGEImm64)
  .value("SGEU", LIEF::assembly::mips::OPCODE::SGEU)
  .value("SGEUImm", LIEF::assembly::mips::OPCODE::SGEUImm)
  .value("SGEUImm64", LIEF::assembly::mips::OPCODE::SGEUImm64)
  .value("SGTImm", LIEF::assembly::mips::OPCODE::SGTImm)
  .value("SGTImm64", LIEF::assembly::mips::OPCODE::SGTImm64)
  .value("SGTUImm", LIEF::assembly::mips::OPCODE::SGTUImm)
  .value("SGTUImm64", LIEF::assembly::mips::OPCODE::SGTUImm64)
  .value("SLE", LIEF::assembly::mips::OPCODE::SLE)
  .value("SLEImm", LIEF::assembly::mips::OPCODE::SLEImm)
  .value("SLEImm64", LIEF::assembly::mips::OPCODE::SLEImm64)
  .value("SLEU", LIEF::assembly::mips::OPCODE::SLEU)
  .value("SLEUImm", LIEF::assembly::mips::OPCODE::SLEUImm)
  .value("SLEUImm64", LIEF::assembly::mips::OPCODE::SLEUImm64)
  .value("SLTImm64", LIEF::assembly::mips::OPCODE::SLTImm64)
  .value("SLTUImm64", LIEF::assembly::mips::OPCODE::SLTUImm64)
  .value("SNEIMacro", LIEF::assembly::mips::OPCODE::SNEIMacro)
  .value("SNEMacro", LIEF::assembly::mips::OPCODE::SNEMacro)
  .value("SNZ_B_PSEUDO", LIEF::assembly::mips::OPCODE::SNZ_B_PSEUDO)
  .value("SNZ_D_PSEUDO", LIEF::assembly::mips::OPCODE::SNZ_D_PSEUDO)
  .value("SNZ_H_PSEUDO", LIEF::assembly::mips::OPCODE::SNZ_H_PSEUDO)
  .value("SNZ_V_PSEUDO", LIEF::assembly::mips::OPCODE::SNZ_V_PSEUDO)
  .value("SNZ_W_PSEUDO", LIEF::assembly::mips::OPCODE::SNZ_W_PSEUDO)
  .value("SRemIMacro", LIEF::assembly::mips::OPCODE::SRemIMacro)
  .value("SRemMacro", LIEF::assembly::mips::OPCODE::SRemMacro)
  .value("STORE_ACC128", LIEF::assembly::mips::OPCODE::STORE_ACC128)
  .value("STORE_ACC64", LIEF::assembly::mips::OPCODE::STORE_ACC64)
  .value("STORE_ACC64DSP", LIEF::assembly::mips::OPCODE::STORE_ACC64DSP)
  .value("STORE_CCOND_DSP", LIEF::assembly::mips::OPCODE::STORE_CCOND_DSP)
  .value("STR_D", LIEF::assembly::mips::OPCODE::STR_D)
  .value("STR_W", LIEF::assembly::mips::OPCODE::STR_W)
  .value("ST_F16", LIEF::assembly::mips::OPCODE::ST_F16)
  .value("SWM_MM", LIEF::assembly::mips::OPCODE::SWM_MM)
  .value("SZ_B_PSEUDO", LIEF::assembly::mips::OPCODE::SZ_B_PSEUDO)
  .value("SZ_D_PSEUDO", LIEF::assembly::mips::OPCODE::SZ_D_PSEUDO)
  .value("SZ_H_PSEUDO", LIEF::assembly::mips::OPCODE::SZ_H_PSEUDO)
  .value("SZ_V_PSEUDO", LIEF::assembly::mips::OPCODE::SZ_V_PSEUDO)
  .value("SZ_W_PSEUDO", LIEF::assembly::mips::OPCODE::SZ_W_PSEUDO)
  .value("SaaAddr", LIEF::assembly::mips::OPCODE::SaaAddr)
  .value("SaadAddr", LIEF::assembly::mips::OPCODE::SaadAddr)
  .value("SelBeqZ", LIEF::assembly::mips::OPCODE::SelBeqZ)
  .value("SelBneZ", LIEF::assembly::mips::OPCODE::SelBneZ)
  .value("SelTBteqZCmp", LIEF::assembly::mips::OPCODE::SelTBteqZCmp)
  .value("SelTBteqZCmpi", LIEF::assembly::mips::OPCODE::SelTBteqZCmpi)
  .value("SelTBteqZSlt", LIEF::assembly::mips::OPCODE::SelTBteqZSlt)
  .value("SelTBteqZSlti", LIEF::assembly::mips::OPCODE::SelTBteqZSlti)
  .value("SelTBteqZSltiu", LIEF::assembly::mips::OPCODE::SelTBteqZSltiu)
  .value("SelTBteqZSltu", LIEF::assembly::mips::OPCODE::SelTBteqZSltu)
  .value("SelTBtneZCmp", LIEF::assembly::mips::OPCODE::SelTBtneZCmp)
  .value("SelTBtneZCmpi", LIEF::assembly::mips::OPCODE::SelTBtneZCmpi)
  .value("SelTBtneZSlt", LIEF::assembly::mips::OPCODE::SelTBtneZSlt)
  .value("SelTBtneZSlti", LIEF::assembly::mips::OPCODE::SelTBtneZSlti)
  .value("SelTBtneZSltiu", LIEF::assembly::mips::OPCODE::SelTBtneZSltiu)
  .value("SelTBtneZSltu", LIEF::assembly::mips::OPCODE::SelTBtneZSltu)
  .value("SltCCRxRy16", LIEF::assembly::mips::OPCODE::SltCCRxRy16)
  .value("SltiCCRxImmX16", LIEF::assembly::mips::OPCODE::SltiCCRxImmX16)
  .value("SltiuCCRxImmX16", LIEF::assembly::mips::OPCODE::SltiuCCRxImmX16)
  .value("SltuCCRxRy16", LIEF::assembly::mips::OPCODE::SltuCCRxRy16)
  .value("SltuRxRyRz16", LIEF::assembly::mips::OPCODE::SltuRxRyRz16)
  .value("TAILCALL", LIEF::assembly::mips::OPCODE::TAILCALL)
  .value("TAILCALL64R6REG", LIEF::assembly::mips::OPCODE::TAILCALL64R6REG)
  .value("TAILCALLHB64R6REG", LIEF::assembly::mips::OPCODE::TAILCALLHB64R6REG)
  .value("TAILCALLHBR6REG", LIEF::assembly::mips::OPCODE::TAILCALLHBR6REG)
  .value("TAILCALLR6REG", LIEF::assembly::mips::OPCODE::TAILCALLR6REG)
  .value("TAILCALLREG", LIEF::assembly::mips::OPCODE::TAILCALLREG)
  .value("TAILCALLREG64", LIEF::assembly::mips::OPCODE::TAILCALLREG64)
  .value("TAILCALLREGHB", LIEF::assembly::mips::OPCODE::TAILCALLREGHB)
  .value("TAILCALLREGHB64", LIEF::assembly::mips::OPCODE::TAILCALLREGHB64)
  .value("TAILCALLREG_MM", LIEF::assembly::mips::OPCODE::TAILCALLREG_MM)
  .value("TAILCALLREG_MMR6", LIEF::assembly::mips::OPCODE::TAILCALLREG_MMR6)
  .value("TAILCALL_MM", LIEF::assembly::mips::OPCODE::TAILCALL_MM)
  .value("TAILCALL_MMR6", LIEF::assembly::mips::OPCODE::TAILCALL_MMR6)
  .value("TRAP", LIEF::assembly::mips::OPCODE::TRAP)
  .value("TRAP_MM", LIEF::assembly::mips::OPCODE::TRAP_MM)
  .value("UDIV_MM_Pseudo", LIEF::assembly::mips::OPCODE::UDIV_MM_Pseudo)
  .value("UDivIMacro", LIEF::assembly::mips::OPCODE::UDivIMacro)
  .value("UDivMacro", LIEF::assembly::mips::OPCODE::UDivMacro)
  .value("URemIMacro", LIEF::assembly::mips::OPCODE::URemIMacro)
  .value("URemMacro", LIEF::assembly::mips::OPCODE::URemMacro)
  .value("Ulh", LIEF::assembly::mips::OPCODE::Ulh)
  .value("Ulhu", LIEF::assembly::mips::OPCODE::Ulhu)
  .value("Ulw", LIEF::assembly::mips::OPCODE::Ulw)
  .value("Ush", LIEF::assembly::mips::OPCODE::Ush)
  .value("Usw", LIEF::assembly::mips::OPCODE::Usw)
  .value("XOR_V_D_PSEUDO", LIEF::assembly::mips::OPCODE::XOR_V_D_PSEUDO)
  .value("XOR_V_H_PSEUDO", LIEF::assembly::mips::OPCODE::XOR_V_H_PSEUDO)
  .value("XOR_V_W_PSEUDO", LIEF::assembly::mips::OPCODE::XOR_V_W_PSEUDO)
  .value("ABSQ_S_PH", LIEF::assembly::mips::OPCODE::ABSQ_S_PH)
  .value("ABSQ_S_PH_MM", LIEF::assembly::mips::OPCODE::ABSQ_S_PH_MM)
  .value("ABSQ_S_QB", LIEF::assembly::mips::OPCODE::ABSQ_S_QB)
  .value("ABSQ_S_QB_MMR2", LIEF::assembly::mips::OPCODE::ABSQ_S_QB_MMR2)
  .value("ABSQ_S_W", LIEF::assembly::mips::OPCODE::ABSQ_S_W)
  .value("ABSQ_S_W_MM", LIEF::assembly::mips::OPCODE::ABSQ_S_W_MM)
  .value("ADD", LIEF::assembly::mips::OPCODE::ADD)
  .value("ADDIUPC", LIEF::assembly::mips::OPCODE::ADDIUPC)
  .value("ADDIUPC_MM", LIEF::assembly::mips::OPCODE::ADDIUPC_MM)
  .value("ADDIUPC_MMR6", LIEF::assembly::mips::OPCODE::ADDIUPC_MMR6)
  .value("ADDIUR1SP_MM", LIEF::assembly::mips::OPCODE::ADDIUR1SP_MM)
  .value("ADDIUR2_MM", LIEF::assembly::mips::OPCODE::ADDIUR2_MM)
  .value("ADDIUS5_MM", LIEF::assembly::mips::OPCODE::ADDIUS5_MM)
  .value("ADDIUSP_MM", LIEF::assembly::mips::OPCODE::ADDIUSP_MM)
  .value("ADDIU_MMR6", LIEF::assembly::mips::OPCODE::ADDIU_MMR6)
  .value("ADDQH_PH", LIEF::assembly::mips::OPCODE::ADDQH_PH)
  .value("ADDQH_PH_MMR2", LIEF::assembly::mips::OPCODE::ADDQH_PH_MMR2)
  .value("ADDQH_R_PH", LIEF::assembly::mips::OPCODE::ADDQH_R_PH)
  .value("ADDQH_R_PH_MMR2", LIEF::assembly::mips::OPCODE::ADDQH_R_PH_MMR2)
  .value("ADDQH_R_W", LIEF::assembly::mips::OPCODE::ADDQH_R_W)
  .value("ADDQH_R_W_MMR2", LIEF::assembly::mips::OPCODE::ADDQH_R_W_MMR2)
  .value("ADDQH_W", LIEF::assembly::mips::OPCODE::ADDQH_W)
  .value("ADDQH_W_MMR2", LIEF::assembly::mips::OPCODE::ADDQH_W_MMR2)
  .value("ADDQ_PH", LIEF::assembly::mips::OPCODE::ADDQ_PH)
  .value("ADDQ_PH_MM", LIEF::assembly::mips::OPCODE::ADDQ_PH_MM)
  .value("ADDQ_S_PH", LIEF::assembly::mips::OPCODE::ADDQ_S_PH)
  .value("ADDQ_S_PH_MM", LIEF::assembly::mips::OPCODE::ADDQ_S_PH_MM)
  .value("ADDQ_S_W", LIEF::assembly::mips::OPCODE::ADDQ_S_W)
  .value("ADDQ_S_W_MM", LIEF::assembly::mips::OPCODE::ADDQ_S_W_MM)
  .value("ADDR_PS64", LIEF::assembly::mips::OPCODE::ADDR_PS64)
  .value("ADDSC", LIEF::assembly::mips::OPCODE::ADDSC)
  .value("ADDSC_MM", LIEF::assembly::mips::OPCODE::ADDSC_MM)
  .value("ADDS_A_B", LIEF::assembly::mips::OPCODE::ADDS_A_B)
  .value("ADDS_A_D", LIEF::assembly::mips::OPCODE::ADDS_A_D)
  .value("ADDS_A_H", LIEF::assembly::mips::OPCODE::ADDS_A_H)
  .value("ADDS_A_W", LIEF::assembly::mips::OPCODE::ADDS_A_W)
  .value("ADDS_S_B", LIEF::assembly::mips::OPCODE::ADDS_S_B)
  .value("ADDS_S_D", LIEF::assembly::mips::OPCODE::ADDS_S_D)
  .value("ADDS_S_H", LIEF::assembly::mips::OPCODE::ADDS_S_H)
  .value("ADDS_S_W", LIEF::assembly::mips::OPCODE::ADDS_S_W)
  .value("ADDS_U_B", LIEF::assembly::mips::OPCODE::ADDS_U_B)
  .value("ADDS_U_D", LIEF::assembly::mips::OPCODE::ADDS_U_D)
  .value("ADDS_U_H", LIEF::assembly::mips::OPCODE::ADDS_U_H)
  .value("ADDS_U_W", LIEF::assembly::mips::OPCODE::ADDS_U_W)
  .value("ADDU16_MM", LIEF::assembly::mips::OPCODE::ADDU16_MM)
  .value("ADDU16_MMR6", LIEF::assembly::mips::OPCODE::ADDU16_MMR6)
  .value("ADDUH_QB", LIEF::assembly::mips::OPCODE::ADDUH_QB)
  .value("ADDUH_QB_MMR2", LIEF::assembly::mips::OPCODE::ADDUH_QB_MMR2)
  .value("ADDUH_R_QB", LIEF::assembly::mips::OPCODE::ADDUH_R_QB)
  .value("ADDUH_R_QB_MMR2", LIEF::assembly::mips::OPCODE::ADDUH_R_QB_MMR2)
  .value("ADDU_MMR6", LIEF::assembly::mips::OPCODE::ADDU_MMR6)
  .value("ADDU_PH", LIEF::assembly::mips::OPCODE::ADDU_PH)
  .value("ADDU_PH_MMR2", LIEF::assembly::mips::OPCODE::ADDU_PH_MMR2)
  .value("ADDU_QB", LIEF::assembly::mips::OPCODE::ADDU_QB)
  .value("ADDU_QB_MM", LIEF::assembly::mips::OPCODE::ADDU_QB_MM)
  .value("ADDU_S_PH", LIEF::assembly::mips::OPCODE::ADDU_S_PH)
  .value("ADDU_S_PH_MMR2", LIEF::assembly::mips::OPCODE::ADDU_S_PH_MMR2)
  .value("ADDU_S_QB", LIEF::assembly::mips::OPCODE::ADDU_S_QB)
  .value("ADDU_S_QB_MM", LIEF::assembly::mips::OPCODE::ADDU_S_QB_MM)
  .value("ADDVI_B", LIEF::assembly::mips::OPCODE::ADDVI_B)
  .value("ADDVI_D", LIEF::assembly::mips::OPCODE::ADDVI_D)
  .value("ADDVI_H", LIEF::assembly::mips::OPCODE::ADDVI_H)
  .value("ADDVI_W", LIEF::assembly::mips::OPCODE::ADDVI_W)
  .value("ADDV_B", LIEF::assembly::mips::OPCODE::ADDV_B)
  .value("ADDV_D", LIEF::assembly::mips::OPCODE::ADDV_D)
  .value("ADDV_H", LIEF::assembly::mips::OPCODE::ADDV_H)
  .value("ADDV_W", LIEF::assembly::mips::OPCODE::ADDV_W)
  .value("ADDWC", LIEF::assembly::mips::OPCODE::ADDWC)
  .value("ADDWC_MM", LIEF::assembly::mips::OPCODE::ADDWC_MM)
  .value("ADD_A_B", LIEF::assembly::mips::OPCODE::ADD_A_B)
  .value("ADD_A_D", LIEF::assembly::mips::OPCODE::ADD_A_D)
  .value("ADD_A_H", LIEF::assembly::mips::OPCODE::ADD_A_H)
  .value("ADD_A_W", LIEF::assembly::mips::OPCODE::ADD_A_W)
  .value("ADD_MM", LIEF::assembly::mips::OPCODE::ADD_MM)
  .value("ADD_MMR6", LIEF::assembly::mips::OPCODE::ADD_MMR6)
  .value("ADDi", LIEF::assembly::mips::OPCODE::ADDi)
  .value("ADDi_MM", LIEF::assembly::mips::OPCODE::ADDi_MM)
  .value("ADDiu", LIEF::assembly::mips::OPCODE::ADDiu)
  .value("ADDiu_MM", LIEF::assembly::mips::OPCODE::ADDiu_MM)
  .value("ADDu", LIEF::assembly::mips::OPCODE::ADDu)
  .value("ADDu_MM", LIEF::assembly::mips::OPCODE::ADDu_MM)
  .value("ALIGN", LIEF::assembly::mips::OPCODE::ALIGN)
  .value("ALIGN_MMR6", LIEF::assembly::mips::OPCODE::ALIGN_MMR6)
  .value("ALUIPC", LIEF::assembly::mips::OPCODE::ALUIPC)
  .value("ALUIPC_MMR6", LIEF::assembly::mips::OPCODE::ALUIPC_MMR6)
  .value("AND", LIEF::assembly::mips::OPCODE::AND)
  .value("AND16_MM", LIEF::assembly::mips::OPCODE::AND16_MM)
  .value("AND16_MMR6", LIEF::assembly::mips::OPCODE::AND16_MMR6)
  .value("AND64", LIEF::assembly::mips::OPCODE::AND64)
  .value("ANDI16_MM", LIEF::assembly::mips::OPCODE::ANDI16_MM)
  .value("ANDI16_MMR6", LIEF::assembly::mips::OPCODE::ANDI16_MMR6)
  .value("ANDI_B", LIEF::assembly::mips::OPCODE::ANDI_B)
  .value("ANDI_MMR6", LIEF::assembly::mips::OPCODE::ANDI_MMR6)
  .value("AND_MM", LIEF::assembly::mips::OPCODE::AND_MM)
  .value("AND_MMR6", LIEF::assembly::mips::OPCODE::AND_MMR6)
  .value("AND_V", LIEF::assembly::mips::OPCODE::AND_V)
  .value("ANDi", LIEF::assembly::mips::OPCODE::ANDi)
  .value("ANDi64", LIEF::assembly::mips::OPCODE::ANDi64)
  .value("ANDi_MM", LIEF::assembly::mips::OPCODE::ANDi_MM)
  .value("APPEND", LIEF::assembly::mips::OPCODE::APPEND)
  .value("APPEND_MMR2", LIEF::assembly::mips::OPCODE::APPEND_MMR2)
  .value("ASUB_S_B", LIEF::assembly::mips::OPCODE::ASUB_S_B)
  .value("ASUB_S_D", LIEF::assembly::mips::OPCODE::ASUB_S_D)
  .value("ASUB_S_H", LIEF::assembly::mips::OPCODE::ASUB_S_H)
  .value("ASUB_S_W", LIEF::assembly::mips::OPCODE::ASUB_S_W)
  .value("ASUB_U_B", LIEF::assembly::mips::OPCODE::ASUB_U_B)
  .value("ASUB_U_D", LIEF::assembly::mips::OPCODE::ASUB_U_D)
  .value("ASUB_U_H", LIEF::assembly::mips::OPCODE::ASUB_U_H)
  .value("ASUB_U_W", LIEF::assembly::mips::OPCODE::ASUB_U_W)
  .value("AUI", LIEF::assembly::mips::OPCODE::AUI)
  .value("AUIPC", LIEF::assembly::mips::OPCODE::AUIPC)
  .value("AUIPC_MMR6", LIEF::assembly::mips::OPCODE::AUIPC_MMR6)
  .value("AUI_MMR6", LIEF::assembly::mips::OPCODE::AUI_MMR6)
  .value("AVER_S_B", LIEF::assembly::mips::OPCODE::AVER_S_B)
  .value("AVER_S_D", LIEF::assembly::mips::OPCODE::AVER_S_D)
  .value("AVER_S_H", LIEF::assembly::mips::OPCODE::AVER_S_H)
  .value("AVER_S_W", LIEF::assembly::mips::OPCODE::AVER_S_W)
  .value("AVER_U_B", LIEF::assembly::mips::OPCODE::AVER_U_B)
  .value("AVER_U_D", LIEF::assembly::mips::OPCODE::AVER_U_D)
  .value("AVER_U_H", LIEF::assembly::mips::OPCODE::AVER_U_H)
  .value("AVER_U_W", LIEF::assembly::mips::OPCODE::AVER_U_W)
  .value("AVE_S_B", LIEF::assembly::mips::OPCODE::AVE_S_B)
  .value("AVE_S_D", LIEF::assembly::mips::OPCODE::AVE_S_D)
  .value("AVE_S_H", LIEF::assembly::mips::OPCODE::AVE_S_H)
  .value("AVE_S_W", LIEF::assembly::mips::OPCODE::AVE_S_W)
  .value("AVE_U_B", LIEF::assembly::mips::OPCODE::AVE_U_B)
  .value("AVE_U_D", LIEF::assembly::mips::OPCODE::AVE_U_D)
  .value("AVE_U_H", LIEF::assembly::mips::OPCODE::AVE_U_H)
  .value("AVE_U_W", LIEF::assembly::mips::OPCODE::AVE_U_W)
  .value("AddiuRxImmX16", LIEF::assembly::mips::OPCODE::AddiuRxImmX16)
  .value("AddiuRxPcImmX16", LIEF::assembly::mips::OPCODE::AddiuRxPcImmX16)
  .value("AddiuRxRxImm16", LIEF::assembly::mips::OPCODE::AddiuRxRxImm16)
  .value("AddiuRxRxImmX16", LIEF::assembly::mips::OPCODE::AddiuRxRxImmX16)
  .value("AddiuRxRyOffMemX16", LIEF::assembly::mips::OPCODE::AddiuRxRyOffMemX16)
  .value("AddiuSpImm16", LIEF::assembly::mips::OPCODE::AddiuSpImm16)
  .value("AddiuSpImmX16", LIEF::assembly::mips::OPCODE::AddiuSpImmX16);
  opcodes.value("AdduRxRyRz16", LIEF::assembly::mips::OPCODE::AdduRxRyRz16)
  .value("AndRxRxRy16", LIEF::assembly::mips::OPCODE::AndRxRxRy16)
  .value("B16_MM", LIEF::assembly::mips::OPCODE::B16_MM)
  .value("BADDu", LIEF::assembly::mips::OPCODE::BADDu)
  .value("BAL", LIEF::assembly::mips::OPCODE::BAL)
  .value("BALC", LIEF::assembly::mips::OPCODE::BALC)
  .value("BALC_MMR6", LIEF::assembly::mips::OPCODE::BALC_MMR6)
  .value("BALIGN", LIEF::assembly::mips::OPCODE::BALIGN)
  .value("BALIGN_MMR2", LIEF::assembly::mips::OPCODE::BALIGN_MMR2)
  .value("BBIT0", LIEF::assembly::mips::OPCODE::BBIT0)
  .value("BBIT032", LIEF::assembly::mips::OPCODE::BBIT032)
  .value("BBIT1", LIEF::assembly::mips::OPCODE::BBIT1)
  .value("BBIT132", LIEF::assembly::mips::OPCODE::BBIT132)
  .value("BC", LIEF::assembly::mips::OPCODE::BC)
  .value("BC16_MMR6", LIEF::assembly::mips::OPCODE::BC16_MMR6)
  .value("BC1EQZ", LIEF::assembly::mips::OPCODE::BC1EQZ)
  .value("BC1EQZC_MMR6", LIEF::assembly::mips::OPCODE::BC1EQZC_MMR6)
  .value("BC1F", LIEF::assembly::mips::OPCODE::BC1F)
  .value("BC1FL", LIEF::assembly::mips::OPCODE::BC1FL)
  .value("BC1F_MM", LIEF::assembly::mips::OPCODE::BC1F_MM)
  .value("BC1NEZ", LIEF::assembly::mips::OPCODE::BC1NEZ)
  .value("BC1NEZC_MMR6", LIEF::assembly::mips::OPCODE::BC1NEZC_MMR6)
  .value("BC1T", LIEF::assembly::mips::OPCODE::BC1T)
  .value("BC1TL", LIEF::assembly::mips::OPCODE::BC1TL)
  .value("BC1T_MM", LIEF::assembly::mips::OPCODE::BC1T_MM)
  .value("BC2EQZ", LIEF::assembly::mips::OPCODE::BC2EQZ)
  .value("BC2EQZC_MMR6", LIEF::assembly::mips::OPCODE::BC2EQZC_MMR6)
  .value("BC2NEZ", LIEF::assembly::mips::OPCODE::BC2NEZ)
  .value("BC2NEZC_MMR6", LIEF::assembly::mips::OPCODE::BC2NEZC_MMR6)
  .value("BCLRI_B", LIEF::assembly::mips::OPCODE::BCLRI_B)
  .value("BCLRI_D", LIEF::assembly::mips::OPCODE::BCLRI_D)
  .value("BCLRI_H", LIEF::assembly::mips::OPCODE::BCLRI_H)
  .value("BCLRI_W", LIEF::assembly::mips::OPCODE::BCLRI_W)
  .value("BCLR_B", LIEF::assembly::mips::OPCODE::BCLR_B)
  .value("BCLR_D", LIEF::assembly::mips::OPCODE::BCLR_D)
  .value("BCLR_H", LIEF::assembly::mips::OPCODE::BCLR_H)
  .value("BCLR_W", LIEF::assembly::mips::OPCODE::BCLR_W)
  .value("BC_MMR6", LIEF::assembly::mips::OPCODE::BC_MMR6)
  .value("BEQ", LIEF::assembly::mips::OPCODE::BEQ)
  .value("BEQ64", LIEF::assembly::mips::OPCODE::BEQ64)
  .value("BEQC", LIEF::assembly::mips::OPCODE::BEQC)
  .value("BEQC64", LIEF::assembly::mips::OPCODE::BEQC64)
  .value("BEQC_MMR6", LIEF::assembly::mips::OPCODE::BEQC_MMR6)
  .value("BEQL", LIEF::assembly::mips::OPCODE::BEQL)
  .value("BEQZ16_MM", LIEF::assembly::mips::OPCODE::BEQZ16_MM)
  .value("BEQZALC", LIEF::assembly::mips::OPCODE::BEQZALC)
  .value("BEQZALC_MMR6", LIEF::assembly::mips::OPCODE::BEQZALC_MMR6)
  .value("BEQZC", LIEF::assembly::mips::OPCODE::BEQZC)
  .value("BEQZC16_MMR6", LIEF::assembly::mips::OPCODE::BEQZC16_MMR6)
  .value("BEQZC64", LIEF::assembly::mips::OPCODE::BEQZC64)
  .value("BEQZC_MM", LIEF::assembly::mips::OPCODE::BEQZC_MM)
  .value("BEQZC_MMR6", LIEF::assembly::mips::OPCODE::BEQZC_MMR6)
  .value("BEQ_MM", LIEF::assembly::mips::OPCODE::BEQ_MM)
  .value("BGEC", LIEF::assembly::mips::OPCODE::BGEC)
  .value("BGEC64", LIEF::assembly::mips::OPCODE::BGEC64)
  .value("BGEC_MMR6", LIEF::assembly::mips::OPCODE::BGEC_MMR6)
  .value("BGEUC", LIEF::assembly::mips::OPCODE::BGEUC)
  .value("BGEUC64", LIEF::assembly::mips::OPCODE::BGEUC64)
  .value("BGEUC_MMR6", LIEF::assembly::mips::OPCODE::BGEUC_MMR6)
  .value("BGEZ", LIEF::assembly::mips::OPCODE::BGEZ)
  .value("BGEZ64", LIEF::assembly::mips::OPCODE::BGEZ64)
  .value("BGEZAL", LIEF::assembly::mips::OPCODE::BGEZAL)
  .value("BGEZALC", LIEF::assembly::mips::OPCODE::BGEZALC)
  .value("BGEZALC_MMR6", LIEF::assembly::mips::OPCODE::BGEZALC_MMR6)
  .value("BGEZALL", LIEF::assembly::mips::OPCODE::BGEZALL)
  .value("BGEZALS_MM", LIEF::assembly::mips::OPCODE::BGEZALS_MM)
  .value("BGEZAL_MM", LIEF::assembly::mips::OPCODE::BGEZAL_MM)
  .value("BGEZC", LIEF::assembly::mips::OPCODE::BGEZC)
  .value("BGEZC64", LIEF::assembly::mips::OPCODE::BGEZC64)
  .value("BGEZC_MMR6", LIEF::assembly::mips::OPCODE::BGEZC_MMR6)
  .value("BGEZL", LIEF::assembly::mips::OPCODE::BGEZL)
  .value("BGEZ_MM", LIEF::assembly::mips::OPCODE::BGEZ_MM)
  .value("BGTZ", LIEF::assembly::mips::OPCODE::BGTZ)
  .value("BGTZ64", LIEF::assembly::mips::OPCODE::BGTZ64)
  .value("BGTZALC", LIEF::assembly::mips::OPCODE::BGTZALC)
  .value("BGTZALC_MMR6", LIEF::assembly::mips::OPCODE::BGTZALC_MMR6)
  .value("BGTZC", LIEF::assembly::mips::OPCODE::BGTZC)
  .value("BGTZC64", LIEF::assembly::mips::OPCODE::BGTZC64)
  .value("BGTZC_MMR6", LIEF::assembly::mips::OPCODE::BGTZC_MMR6)
  .value("BGTZL", LIEF::assembly::mips::OPCODE::BGTZL)
  .value("BGTZ_MM", LIEF::assembly::mips::OPCODE::BGTZ_MM)
  .value("BINSLI_B", LIEF::assembly::mips::OPCODE::BINSLI_B)
  .value("BINSLI_D", LIEF::assembly::mips::OPCODE::BINSLI_D)
  .value("BINSLI_H", LIEF::assembly::mips::OPCODE::BINSLI_H)
  .value("BINSLI_W", LIEF::assembly::mips::OPCODE::BINSLI_W)
  .value("BINSL_B", LIEF::assembly::mips::OPCODE::BINSL_B)
  .value("BINSL_D", LIEF::assembly::mips::OPCODE::BINSL_D)
  .value("BINSL_H", LIEF::assembly::mips::OPCODE::BINSL_H)
  .value("BINSL_W", LIEF::assembly::mips::OPCODE::BINSL_W)
  .value("BINSRI_B", LIEF::assembly::mips::OPCODE::BINSRI_B)
  .value("BINSRI_D", LIEF::assembly::mips::OPCODE::BINSRI_D)
  .value("BINSRI_H", LIEF::assembly::mips::OPCODE::BINSRI_H)
  .value("BINSRI_W", LIEF::assembly::mips::OPCODE::BINSRI_W)
  .value("BINSR_B", LIEF::assembly::mips::OPCODE::BINSR_B)
  .value("BINSR_D", LIEF::assembly::mips::OPCODE::BINSR_D)
  .value("BINSR_H", LIEF::assembly::mips::OPCODE::BINSR_H)
  .value("BINSR_W", LIEF::assembly::mips::OPCODE::BINSR_W)
  .value("BITREV", LIEF::assembly::mips::OPCODE::BITREV)
  .value("BITREV_MM", LIEF::assembly::mips::OPCODE::BITREV_MM)
  .value("BITSWAP", LIEF::assembly::mips::OPCODE::BITSWAP)
  .value("BITSWAP_MMR6", LIEF::assembly::mips::OPCODE::BITSWAP_MMR6)
  .value("BLEZ", LIEF::assembly::mips::OPCODE::BLEZ)
  .value("BLEZ64", LIEF::assembly::mips::OPCODE::BLEZ64)
  .value("BLEZALC", LIEF::assembly::mips::OPCODE::BLEZALC)
  .value("BLEZALC_MMR6", LIEF::assembly::mips::OPCODE::BLEZALC_MMR6)
  .value("BLEZC", LIEF::assembly::mips::OPCODE::BLEZC)
  .value("BLEZC64", LIEF::assembly::mips::OPCODE::BLEZC64)
  .value("BLEZC_MMR6", LIEF::assembly::mips::OPCODE::BLEZC_MMR6)
  .value("BLEZL", LIEF::assembly::mips::OPCODE::BLEZL)
  .value("BLEZ_MM", LIEF::assembly::mips::OPCODE::BLEZ_MM)
  .value("BLTC", LIEF::assembly::mips::OPCODE::BLTC)
  .value("BLTC64", LIEF::assembly::mips::OPCODE::BLTC64)
  .value("BLTC_MMR6", LIEF::assembly::mips::OPCODE::BLTC_MMR6)
  .value("BLTUC", LIEF::assembly::mips::OPCODE::BLTUC)
  .value("BLTUC64", LIEF::assembly::mips::OPCODE::BLTUC64)
  .value("BLTUC_MMR6", LIEF::assembly::mips::OPCODE::BLTUC_MMR6)
  .value("BLTZ", LIEF::assembly::mips::OPCODE::BLTZ)
  .value("BLTZ64", LIEF::assembly::mips::OPCODE::BLTZ64)
  .value("BLTZAL", LIEF::assembly::mips::OPCODE::BLTZAL)
  .value("BLTZALC", LIEF::assembly::mips::OPCODE::BLTZALC)
  .value("BLTZALC_MMR6", LIEF::assembly::mips::OPCODE::BLTZALC_MMR6)
  .value("BLTZALL", LIEF::assembly::mips::OPCODE::BLTZALL)
  .value("BLTZALS_MM", LIEF::assembly::mips::OPCODE::BLTZALS_MM)
  .value("BLTZAL_MM", LIEF::assembly::mips::OPCODE::BLTZAL_MM)
  .value("BLTZC", LIEF::assembly::mips::OPCODE::BLTZC)
  .value("BLTZC64", LIEF::assembly::mips::OPCODE::BLTZC64)
  .value("BLTZC_MMR6", LIEF::assembly::mips::OPCODE::BLTZC_MMR6)
  .value("BLTZL", LIEF::assembly::mips::OPCODE::BLTZL)
  .value("BLTZ_MM", LIEF::assembly::mips::OPCODE::BLTZ_MM)
  .value("BMNZI_B", LIEF::assembly::mips::OPCODE::BMNZI_B)
  .value("BMNZ_V", LIEF::assembly::mips::OPCODE::BMNZ_V)
  .value("BMZI_B", LIEF::assembly::mips::OPCODE::BMZI_B)
  .value("BMZ_V", LIEF::assembly::mips::OPCODE::BMZ_V)
  .value("BNE", LIEF::assembly::mips::OPCODE::BNE)
  .value("BNE64", LIEF::assembly::mips::OPCODE::BNE64)
  .value("BNEC", LIEF::assembly::mips::OPCODE::BNEC)
  .value("BNEC64", LIEF::assembly::mips::OPCODE::BNEC64)
  .value("BNEC_MMR6", LIEF::assembly::mips::OPCODE::BNEC_MMR6)
  .value("BNEGI_B", LIEF::assembly::mips::OPCODE::BNEGI_B)
  .value("BNEGI_D", LIEF::assembly::mips::OPCODE::BNEGI_D)
  .value("BNEGI_H", LIEF::assembly::mips::OPCODE::BNEGI_H)
  .value("BNEGI_W", LIEF::assembly::mips::OPCODE::BNEGI_W)
  .value("BNEG_B", LIEF::assembly::mips::OPCODE::BNEG_B)
  .value("BNEG_D", LIEF::assembly::mips::OPCODE::BNEG_D)
  .value("BNEG_H", LIEF::assembly::mips::OPCODE::BNEG_H)
  .value("BNEG_W", LIEF::assembly::mips::OPCODE::BNEG_W)
  .value("BNEL", LIEF::assembly::mips::OPCODE::BNEL)
  .value("BNEZ16_MM", LIEF::assembly::mips::OPCODE::BNEZ16_MM)
  .value("BNEZALC", LIEF::assembly::mips::OPCODE::BNEZALC)
  .value("BNEZALC_MMR6", LIEF::assembly::mips::OPCODE::BNEZALC_MMR6)
  .value("BNEZC", LIEF::assembly::mips::OPCODE::BNEZC)
  .value("BNEZC16_MMR6", LIEF::assembly::mips::OPCODE::BNEZC16_MMR6)
  .value("BNEZC64", LIEF::assembly::mips::OPCODE::BNEZC64)
  .value("BNEZC_MM", LIEF::assembly::mips::OPCODE::BNEZC_MM)
  .value("BNEZC_MMR6", LIEF::assembly::mips::OPCODE::BNEZC_MMR6)
  .value("BNE_MM", LIEF::assembly::mips::OPCODE::BNE_MM)
  .value("BNVC", LIEF::assembly::mips::OPCODE::BNVC)
  .value("BNVC_MMR6", LIEF::assembly::mips::OPCODE::BNVC_MMR6)
  .value("BNZ_B", LIEF::assembly::mips::OPCODE::BNZ_B)
  .value("BNZ_D", LIEF::assembly::mips::OPCODE::BNZ_D)
  .value("BNZ_H", LIEF::assembly::mips::OPCODE::BNZ_H)
  .value("BNZ_V", LIEF::assembly::mips::OPCODE::BNZ_V)
  .value("BNZ_W", LIEF::assembly::mips::OPCODE::BNZ_W)
  .value("BOVC", LIEF::assembly::mips::OPCODE::BOVC)
  .value("BOVC_MMR6", LIEF::assembly::mips::OPCODE::BOVC_MMR6)
  .value("BPOSGE32", LIEF::assembly::mips::OPCODE::BPOSGE32)
  .value("BPOSGE32C_MMR3", LIEF::assembly::mips::OPCODE::BPOSGE32C_MMR3)
  .value("BPOSGE32_MM", LIEF::assembly::mips::OPCODE::BPOSGE32_MM)
  .value("BREAK", LIEF::assembly::mips::OPCODE::BREAK)
  .value("BREAK16_MM", LIEF::assembly::mips::OPCODE::BREAK16_MM)
  .value("BREAK16_MMR6", LIEF::assembly::mips::OPCODE::BREAK16_MMR6)
  .value("BREAK_MM", LIEF::assembly::mips::OPCODE::BREAK_MM)
  .value("BREAK_MMR6", LIEF::assembly::mips::OPCODE::BREAK_MMR6)
  .value("BSELI_B", LIEF::assembly::mips::OPCODE::BSELI_B)
  .value("BSEL_V", LIEF::assembly::mips::OPCODE::BSEL_V)
  .value("BSETI_B", LIEF::assembly::mips::OPCODE::BSETI_B)
  .value("BSETI_D", LIEF::assembly::mips::OPCODE::BSETI_D)
  .value("BSETI_H", LIEF::assembly::mips::OPCODE::BSETI_H)
  .value("BSETI_W", LIEF::assembly::mips::OPCODE::BSETI_W)
  .value("BSET_B", LIEF::assembly::mips::OPCODE::BSET_B)
  .value("BSET_D", LIEF::assembly::mips::OPCODE::BSET_D)
  .value("BSET_H", LIEF::assembly::mips::OPCODE::BSET_H)
  .value("BSET_W", LIEF::assembly::mips::OPCODE::BSET_W)
  .value("BZ_B", LIEF::assembly::mips::OPCODE::BZ_B)
  .value("BZ_D", LIEF::assembly::mips::OPCODE::BZ_D)
  .value("BZ_H", LIEF::assembly::mips::OPCODE::BZ_H)
  .value("BZ_V", LIEF::assembly::mips::OPCODE::BZ_V)
  .value("BZ_W", LIEF::assembly::mips::OPCODE::BZ_W)
  .value("BeqzRxImm16", LIEF::assembly::mips::OPCODE::BeqzRxImm16)
  .value("BeqzRxImmX16", LIEF::assembly::mips::OPCODE::BeqzRxImmX16)
  .value("Bimm16", LIEF::assembly::mips::OPCODE::Bimm16)
  .value("BimmX16", LIEF::assembly::mips::OPCODE::BimmX16)
  .value("BnezRxImm16", LIEF::assembly::mips::OPCODE::BnezRxImm16)
  .value("BnezRxImmX16", LIEF::assembly::mips::OPCODE::BnezRxImmX16)
  .value("Break16", LIEF::assembly::mips::OPCODE::Break16)
  .value("Bteqz16", LIEF::assembly::mips::OPCODE::Bteqz16)
  .value("BteqzX16", LIEF::assembly::mips::OPCODE::BteqzX16)
  .value("Btnez16", LIEF::assembly::mips::OPCODE::Btnez16)
  .value("BtnezX16", LIEF::assembly::mips::OPCODE::BtnezX16)
  .value("CACHE", LIEF::assembly::mips::OPCODE::CACHE)
  .value("CACHEE", LIEF::assembly::mips::OPCODE::CACHEE)
  .value("CACHEE_MM", LIEF::assembly::mips::OPCODE::CACHEE_MM)
  .value("CACHE_MM", LIEF::assembly::mips::OPCODE::CACHE_MM)
  .value("CACHE_MMR6", LIEF::assembly::mips::OPCODE::CACHE_MMR6)
  .value("CACHE_R6", LIEF::assembly::mips::OPCODE::CACHE_R6)
  .value("CEIL_L_D64", LIEF::assembly::mips::OPCODE::CEIL_L_D64)
  .value("CEIL_L_D_MMR6", LIEF::assembly::mips::OPCODE::CEIL_L_D_MMR6)
  .value("CEIL_L_S", LIEF::assembly::mips::OPCODE::CEIL_L_S)
  .value("CEIL_L_S_MMR6", LIEF::assembly::mips::OPCODE::CEIL_L_S_MMR6)
  .value("CEIL_W_D32", LIEF::assembly::mips::OPCODE::CEIL_W_D32)
  .value("CEIL_W_D64", LIEF::assembly::mips::OPCODE::CEIL_W_D64)
  .value("CEIL_W_D_MMR6", LIEF::assembly::mips::OPCODE::CEIL_W_D_MMR6)
  .value("CEIL_W_MM", LIEF::assembly::mips::OPCODE::CEIL_W_MM)
  .value("CEIL_W_S", LIEF::assembly::mips::OPCODE::CEIL_W_S)
  .value("CEIL_W_S_MM", LIEF::assembly::mips::OPCODE::CEIL_W_S_MM)
  .value("CEIL_W_S_MMR6", LIEF::assembly::mips::OPCODE::CEIL_W_S_MMR6)
  .value("CEQI_B", LIEF::assembly::mips::OPCODE::CEQI_B)
  .value("CEQI_D", LIEF::assembly::mips::OPCODE::CEQI_D)
  .value("CEQI_H", LIEF::assembly::mips::OPCODE::CEQI_H)
  .value("CEQI_W", LIEF::assembly::mips::OPCODE::CEQI_W)
  .value("CEQ_B", LIEF::assembly::mips::OPCODE::CEQ_B)
  .value("CEQ_D", LIEF::assembly::mips::OPCODE::CEQ_D)
  .value("CEQ_H", LIEF::assembly::mips::OPCODE::CEQ_H)
  .value("CEQ_W", LIEF::assembly::mips::OPCODE::CEQ_W)
  .value("CFC1", LIEF::assembly::mips::OPCODE::CFC1)
  .value("CFC1_MM", LIEF::assembly::mips::OPCODE::CFC1_MM)
  .value("CFC2_MM", LIEF::assembly::mips::OPCODE::CFC2_MM)
  .value("CFCMSA", LIEF::assembly::mips::OPCODE::CFCMSA)
  .value("CINS", LIEF::assembly::mips::OPCODE::CINS)
  .value("CINS32", LIEF::assembly::mips::OPCODE::CINS32)
  .value("CINS64_32", LIEF::assembly::mips::OPCODE::CINS64_32)
  .value("CINS_i32", LIEF::assembly::mips::OPCODE::CINS_i32)
  .value("CLASS_D", LIEF::assembly::mips::OPCODE::CLASS_D)
  .value("CLASS_D_MMR6", LIEF::assembly::mips::OPCODE::CLASS_D_MMR6)
  .value("CLASS_S", LIEF::assembly::mips::OPCODE::CLASS_S)
  .value("CLASS_S_MMR6", LIEF::assembly::mips::OPCODE::CLASS_S_MMR6)
  .value("CLEI_S_B", LIEF::assembly::mips::OPCODE::CLEI_S_B)
  .value("CLEI_S_D", LIEF::assembly::mips::OPCODE::CLEI_S_D)
  .value("CLEI_S_H", LIEF::assembly::mips::OPCODE::CLEI_S_H)
  .value("CLEI_S_W", LIEF::assembly::mips::OPCODE::CLEI_S_W)
  .value("CLEI_U_B", LIEF::assembly::mips::OPCODE::CLEI_U_B)
  .value("CLEI_U_D", LIEF::assembly::mips::OPCODE::CLEI_U_D)
  .value("CLEI_U_H", LIEF::assembly::mips::OPCODE::CLEI_U_H)
  .value("CLEI_U_W", LIEF::assembly::mips::OPCODE::CLEI_U_W)
  .value("CLE_S_B", LIEF::assembly::mips::OPCODE::CLE_S_B)
  .value("CLE_S_D", LIEF::assembly::mips::OPCODE::CLE_S_D)
  .value("CLE_S_H", LIEF::assembly::mips::OPCODE::CLE_S_H)
  .value("CLE_S_W", LIEF::assembly::mips::OPCODE::CLE_S_W)
  .value("CLE_U_B", LIEF::assembly::mips::OPCODE::CLE_U_B)
  .value("CLE_U_D", LIEF::assembly::mips::OPCODE::CLE_U_D)
  .value("CLE_U_H", LIEF::assembly::mips::OPCODE::CLE_U_H)
  .value("CLE_U_W", LIEF::assembly::mips::OPCODE::CLE_U_W)
  .value("CLO", LIEF::assembly::mips::OPCODE::CLO)
  .value("CLO_MM", LIEF::assembly::mips::OPCODE::CLO_MM)
  .value("CLO_MMR6", LIEF::assembly::mips::OPCODE::CLO_MMR6)
  .value("CLO_R6", LIEF::assembly::mips::OPCODE::CLO_R6)
  .value("CLTI_S_B", LIEF::assembly::mips::OPCODE::CLTI_S_B)
  .value("CLTI_S_D", LIEF::assembly::mips::OPCODE::CLTI_S_D)
  .value("CLTI_S_H", LIEF::assembly::mips::OPCODE::CLTI_S_H)
  .value("CLTI_S_W", LIEF::assembly::mips::OPCODE::CLTI_S_W)
  .value("CLTI_U_B", LIEF::assembly::mips::OPCODE::CLTI_U_B)
  .value("CLTI_U_D", LIEF::assembly::mips::OPCODE::CLTI_U_D)
  .value("CLTI_U_H", LIEF::assembly::mips::OPCODE::CLTI_U_H)
  .value("CLTI_U_W", LIEF::assembly::mips::OPCODE::CLTI_U_W)
  .value("CLT_S_B", LIEF::assembly::mips::OPCODE::CLT_S_B)
  .value("CLT_S_D", LIEF::assembly::mips::OPCODE::CLT_S_D)
  .value("CLT_S_H", LIEF::assembly::mips::OPCODE::CLT_S_H)
  .value("CLT_S_W", LIEF::assembly::mips::OPCODE::CLT_S_W)
  .value("CLT_U_B", LIEF::assembly::mips::OPCODE::CLT_U_B)
  .value("CLT_U_D", LIEF::assembly::mips::OPCODE::CLT_U_D)
  .value("CLT_U_H", LIEF::assembly::mips::OPCODE::CLT_U_H)
  .value("CLT_U_W", LIEF::assembly::mips::OPCODE::CLT_U_W)
  .value("CLZ", LIEF::assembly::mips::OPCODE::CLZ)
  .value("CLZ_MM", LIEF::assembly::mips::OPCODE::CLZ_MM)
  .value("CLZ_MMR6", LIEF::assembly::mips::OPCODE::CLZ_MMR6)
  .value("CLZ_R6", LIEF::assembly::mips::OPCODE::CLZ_R6)
  .value("CMPGDU_EQ_QB", LIEF::assembly::mips::OPCODE::CMPGDU_EQ_QB)
  .value("CMPGDU_EQ_QB_MMR2", LIEF::assembly::mips::OPCODE::CMPGDU_EQ_QB_MMR2)
  .value("CMPGDU_LE_QB", LIEF::assembly::mips::OPCODE::CMPGDU_LE_QB)
  .value("CMPGDU_LE_QB_MMR2", LIEF::assembly::mips::OPCODE::CMPGDU_LE_QB_MMR2)
  .value("CMPGDU_LT_QB", LIEF::assembly::mips::OPCODE::CMPGDU_LT_QB)
  .value("CMPGDU_LT_QB_MMR2", LIEF::assembly::mips::OPCODE::CMPGDU_LT_QB_MMR2)
  .value("CMPGU_EQ_QB", LIEF::assembly::mips::OPCODE::CMPGU_EQ_QB)
  .value("CMPGU_EQ_QB_MM", LIEF::assembly::mips::OPCODE::CMPGU_EQ_QB_MM)
  .value("CMPGU_LE_QB", LIEF::assembly::mips::OPCODE::CMPGU_LE_QB)
  .value("CMPGU_LE_QB_MM", LIEF::assembly::mips::OPCODE::CMPGU_LE_QB_MM)
  .value("CMPGU_LT_QB", LIEF::assembly::mips::OPCODE::CMPGU_LT_QB)
  .value("CMPGU_LT_QB_MM", LIEF::assembly::mips::OPCODE::CMPGU_LT_QB_MM)
  .value("CMPU_EQ_QB", LIEF::assembly::mips::OPCODE::CMPU_EQ_QB)
  .value("CMPU_EQ_QB_MM", LIEF::assembly::mips::OPCODE::CMPU_EQ_QB_MM)
  .value("CMPU_LE_QB", LIEF::assembly::mips::OPCODE::CMPU_LE_QB)
  .value("CMPU_LE_QB_MM", LIEF::assembly::mips::OPCODE::CMPU_LE_QB_MM)
  .value("CMPU_LT_QB", LIEF::assembly::mips::OPCODE::CMPU_LT_QB)
  .value("CMPU_LT_QB_MM", LIEF::assembly::mips::OPCODE::CMPU_LT_QB_MM)
  .value("CMP_AF_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_AF_D_MMR6)
  .value("CMP_AF_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_AF_S_MMR6)
  .value("CMP_EQ_D", LIEF::assembly::mips::OPCODE::CMP_EQ_D)
  .value("CMP_EQ_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_EQ_D_MMR6)
  .value("CMP_EQ_PH", LIEF::assembly::mips::OPCODE::CMP_EQ_PH)
  .value("CMP_EQ_PH_MM", LIEF::assembly::mips::OPCODE::CMP_EQ_PH_MM);
  opcodes.value("CMP_EQ_S", LIEF::assembly::mips::OPCODE::CMP_EQ_S)
  .value("CMP_EQ_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_EQ_S_MMR6)
  .value("CMP_F_D", LIEF::assembly::mips::OPCODE::CMP_F_D)
  .value("CMP_F_S", LIEF::assembly::mips::OPCODE::CMP_F_S)
  .value("CMP_LE_D", LIEF::assembly::mips::OPCODE::CMP_LE_D)
  .value("CMP_LE_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_LE_D_MMR6)
  .value("CMP_LE_PH", LIEF::assembly::mips::OPCODE::CMP_LE_PH)
  .value("CMP_LE_PH_MM", LIEF::assembly::mips::OPCODE::CMP_LE_PH_MM)
  .value("CMP_LE_S", LIEF::assembly::mips::OPCODE::CMP_LE_S)
  .value("CMP_LE_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_LE_S_MMR6)
  .value("CMP_LT_D", LIEF::assembly::mips::OPCODE::CMP_LT_D)
  .value("CMP_LT_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_LT_D_MMR6)
  .value("CMP_LT_PH", LIEF::assembly::mips::OPCODE::CMP_LT_PH)
  .value("CMP_LT_PH_MM", LIEF::assembly::mips::OPCODE::CMP_LT_PH_MM)
  .value("CMP_LT_S", LIEF::assembly::mips::OPCODE::CMP_LT_S)
  .value("CMP_LT_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_LT_S_MMR6)
  .value("CMP_SAF_D", LIEF::assembly::mips::OPCODE::CMP_SAF_D)
  .value("CMP_SAF_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SAF_D_MMR6)
  .value("CMP_SAF_S", LIEF::assembly::mips::OPCODE::CMP_SAF_S)
  .value("CMP_SAF_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SAF_S_MMR6)
  .value("CMP_SEQ_D", LIEF::assembly::mips::OPCODE::CMP_SEQ_D)
  .value("CMP_SEQ_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SEQ_D_MMR6)
  .value("CMP_SEQ_S", LIEF::assembly::mips::OPCODE::CMP_SEQ_S)
  .value("CMP_SEQ_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SEQ_S_MMR6)
  .value("CMP_SLE_D", LIEF::assembly::mips::OPCODE::CMP_SLE_D)
  .value("CMP_SLE_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SLE_D_MMR6)
  .value("CMP_SLE_S", LIEF::assembly::mips::OPCODE::CMP_SLE_S)
  .value("CMP_SLE_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SLE_S_MMR6)
  .value("CMP_SLT_D", LIEF::assembly::mips::OPCODE::CMP_SLT_D)
  .value("CMP_SLT_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SLT_D_MMR6)
  .value("CMP_SLT_S", LIEF::assembly::mips::OPCODE::CMP_SLT_S)
  .value("CMP_SLT_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SLT_S_MMR6)
  .value("CMP_SUEQ_D", LIEF::assembly::mips::OPCODE::CMP_SUEQ_D)
  .value("CMP_SUEQ_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SUEQ_D_MMR6)
  .value("CMP_SUEQ_S", LIEF::assembly::mips::OPCODE::CMP_SUEQ_S)
  .value("CMP_SUEQ_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SUEQ_S_MMR6)
  .value("CMP_SULE_D", LIEF::assembly::mips::OPCODE::CMP_SULE_D)
  .value("CMP_SULE_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SULE_D_MMR6)
  .value("CMP_SULE_S", LIEF::assembly::mips::OPCODE::CMP_SULE_S)
  .value("CMP_SULE_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SULE_S_MMR6)
  .value("CMP_SULT_D", LIEF::assembly::mips::OPCODE::CMP_SULT_D)
  .value("CMP_SULT_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SULT_D_MMR6)
  .value("CMP_SULT_S", LIEF::assembly::mips::OPCODE::CMP_SULT_S)
  .value("CMP_SULT_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SULT_S_MMR6)
  .value("CMP_SUN_D", LIEF::assembly::mips::OPCODE::CMP_SUN_D)
  .value("CMP_SUN_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_SUN_D_MMR6)
  .value("CMP_SUN_S", LIEF::assembly::mips::OPCODE::CMP_SUN_S)
  .value("CMP_SUN_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_SUN_S_MMR6)
  .value("CMP_UEQ_D", LIEF::assembly::mips::OPCODE::CMP_UEQ_D)
  .value("CMP_UEQ_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_UEQ_D_MMR6)
  .value("CMP_UEQ_S", LIEF::assembly::mips::OPCODE::CMP_UEQ_S)
  .value("CMP_UEQ_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_UEQ_S_MMR6)
  .value("CMP_ULE_D", LIEF::assembly::mips::OPCODE::CMP_ULE_D)
  .value("CMP_ULE_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_ULE_D_MMR6)
  .value("CMP_ULE_S", LIEF::assembly::mips::OPCODE::CMP_ULE_S)
  .value("CMP_ULE_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_ULE_S_MMR6)
  .value("CMP_ULT_D", LIEF::assembly::mips::OPCODE::CMP_ULT_D)
  .value("CMP_ULT_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_ULT_D_MMR6)
  .value("CMP_ULT_S", LIEF::assembly::mips::OPCODE::CMP_ULT_S)
  .value("CMP_ULT_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_ULT_S_MMR6)
  .value("CMP_UN_D", LIEF::assembly::mips::OPCODE::CMP_UN_D)
  .value("CMP_UN_D_MMR6", LIEF::assembly::mips::OPCODE::CMP_UN_D_MMR6)
  .value("CMP_UN_S", LIEF::assembly::mips::OPCODE::CMP_UN_S)
  .value("CMP_UN_S_MMR6", LIEF::assembly::mips::OPCODE::CMP_UN_S_MMR6)
  .value("COPY_S_B", LIEF::assembly::mips::OPCODE::COPY_S_B)
  .value("COPY_S_D", LIEF::assembly::mips::OPCODE::COPY_S_D)
  .value("COPY_S_H", LIEF::assembly::mips::OPCODE::COPY_S_H)
  .value("COPY_S_W", LIEF::assembly::mips::OPCODE::COPY_S_W)
  .value("COPY_U_B", LIEF::assembly::mips::OPCODE::COPY_U_B)
  .value("COPY_U_H", LIEF::assembly::mips::OPCODE::COPY_U_H)
  .value("COPY_U_W", LIEF::assembly::mips::OPCODE::COPY_U_W)
  .value("CRC32B", LIEF::assembly::mips::OPCODE::CRC32B)
  .value("CRC32CB", LIEF::assembly::mips::OPCODE::CRC32CB)
  .value("CRC32CD", LIEF::assembly::mips::OPCODE::CRC32CD)
  .value("CRC32CH", LIEF::assembly::mips::OPCODE::CRC32CH)
  .value("CRC32CW", LIEF::assembly::mips::OPCODE::CRC32CW)
  .value("CRC32D", LIEF::assembly::mips::OPCODE::CRC32D)
  .value("CRC32H", LIEF::assembly::mips::OPCODE::CRC32H)
  .value("CRC32W", LIEF::assembly::mips::OPCODE::CRC32W)
  .value("CTC1", LIEF::assembly::mips::OPCODE::CTC1)
  .value("CTC1_MM", LIEF::assembly::mips::OPCODE::CTC1_MM)
  .value("CTC2_MM", LIEF::assembly::mips::OPCODE::CTC2_MM)
  .value("CTCMSA", LIEF::assembly::mips::OPCODE::CTCMSA)
  .value("CVT_D32_S", LIEF::assembly::mips::OPCODE::CVT_D32_S)
  .value("CVT_D32_S_MM", LIEF::assembly::mips::OPCODE::CVT_D32_S_MM)
  .value("CVT_D32_W", LIEF::assembly::mips::OPCODE::CVT_D32_W)
  .value("CVT_D32_W_MM", LIEF::assembly::mips::OPCODE::CVT_D32_W_MM)
  .value("CVT_D64_L", LIEF::assembly::mips::OPCODE::CVT_D64_L)
  .value("CVT_D64_S", LIEF::assembly::mips::OPCODE::CVT_D64_S)
  .value("CVT_D64_S_MM", LIEF::assembly::mips::OPCODE::CVT_D64_S_MM)
  .value("CVT_D64_W", LIEF::assembly::mips::OPCODE::CVT_D64_W)
  .value("CVT_D64_W_MM", LIEF::assembly::mips::OPCODE::CVT_D64_W_MM)
  .value("CVT_D_L_MMR6", LIEF::assembly::mips::OPCODE::CVT_D_L_MMR6)
  .value("CVT_L_D64", LIEF::assembly::mips::OPCODE::CVT_L_D64)
  .value("CVT_L_D64_MM", LIEF::assembly::mips::OPCODE::CVT_L_D64_MM)
  .value("CVT_L_D_MMR6", LIEF::assembly::mips::OPCODE::CVT_L_D_MMR6)
  .value("CVT_L_S", LIEF::assembly::mips::OPCODE::CVT_L_S)
  .value("CVT_L_S_MM", LIEF::assembly::mips::OPCODE::CVT_L_S_MM)
  .value("CVT_L_S_MMR6", LIEF::assembly::mips::OPCODE::CVT_L_S_MMR6)
  .value("CVT_PS_PW64", LIEF::assembly::mips::OPCODE::CVT_PS_PW64)
  .value("CVT_PS_S64", LIEF::assembly::mips::OPCODE::CVT_PS_S64)
  .value("CVT_PW_PS64", LIEF::assembly::mips::OPCODE::CVT_PW_PS64)
  .value("CVT_S_D32", LIEF::assembly::mips::OPCODE::CVT_S_D32)
  .value("CVT_S_D32_MM", LIEF::assembly::mips::OPCODE::CVT_S_D32_MM)
  .value("CVT_S_D64", LIEF::assembly::mips::OPCODE::CVT_S_D64)
  .value("CVT_S_D64_MM", LIEF::assembly::mips::OPCODE::CVT_S_D64_MM)
  .value("CVT_S_L", LIEF::assembly::mips::OPCODE::CVT_S_L)
  .value("CVT_S_L_MMR6", LIEF::assembly::mips::OPCODE::CVT_S_L_MMR6)
  .value("CVT_S_PL64", LIEF::assembly::mips::OPCODE::CVT_S_PL64)
  .value("CVT_S_PU64", LIEF::assembly::mips::OPCODE::CVT_S_PU64)
  .value("CVT_S_W", LIEF::assembly::mips::OPCODE::CVT_S_W)
  .value("CVT_S_W_MM", LIEF::assembly::mips::OPCODE::CVT_S_W_MM)
  .value("CVT_S_W_MMR6", LIEF::assembly::mips::OPCODE::CVT_S_W_MMR6)
  .value("CVT_W_D32", LIEF::assembly::mips::OPCODE::CVT_W_D32)
  .value("CVT_W_D32_MM", LIEF::assembly::mips::OPCODE::CVT_W_D32_MM)
  .value("CVT_W_D64", LIEF::assembly::mips::OPCODE::CVT_W_D64)
  .value("CVT_W_D64_MM", LIEF::assembly::mips::OPCODE::CVT_W_D64_MM)
  .value("CVT_W_S", LIEF::assembly::mips::OPCODE::CVT_W_S)
  .value("CVT_W_S_MM", LIEF::assembly::mips::OPCODE::CVT_W_S_MM)
  .value("CVT_W_S_MMR6", LIEF::assembly::mips::OPCODE::CVT_W_S_MMR6)
  .value("C_EQ_D32", LIEF::assembly::mips::OPCODE::C_EQ_D32)
  .value("C_EQ_D32_MM", LIEF::assembly::mips::OPCODE::C_EQ_D32_MM)
  .value("C_EQ_D64", LIEF::assembly::mips::OPCODE::C_EQ_D64)
  .value("C_EQ_D64_MM", LIEF::assembly::mips::OPCODE::C_EQ_D64_MM)
  .value("C_EQ_S", LIEF::assembly::mips::OPCODE::C_EQ_S)
  .value("C_EQ_S_MM", LIEF::assembly::mips::OPCODE::C_EQ_S_MM)
  .value("C_F_D32", LIEF::assembly::mips::OPCODE::C_F_D32)
  .value("C_F_D32_MM", LIEF::assembly::mips::OPCODE::C_F_D32_MM)
  .value("C_F_D64", LIEF::assembly::mips::OPCODE::C_F_D64)
  .value("C_F_D64_MM", LIEF::assembly::mips::OPCODE::C_F_D64_MM)
  .value("C_F_S", LIEF::assembly::mips::OPCODE::C_F_S)
  .value("C_F_S_MM", LIEF::assembly::mips::OPCODE::C_F_S_MM)
  .value("C_LE_D32", LIEF::assembly::mips::OPCODE::C_LE_D32)
  .value("C_LE_D32_MM", LIEF::assembly::mips::OPCODE::C_LE_D32_MM)
  .value("C_LE_D64", LIEF::assembly::mips::OPCODE::C_LE_D64)
  .value("C_LE_D64_MM", LIEF::assembly::mips::OPCODE::C_LE_D64_MM)
  .value("C_LE_S", LIEF::assembly::mips::OPCODE::C_LE_S)
  .value("C_LE_S_MM", LIEF::assembly::mips::OPCODE::C_LE_S_MM)
  .value("C_LT_D32", LIEF::assembly::mips::OPCODE::C_LT_D32)
  .value("C_LT_D32_MM", LIEF::assembly::mips::OPCODE::C_LT_D32_MM)
  .value("C_LT_D64", LIEF::assembly::mips::OPCODE::C_LT_D64)
  .value("C_LT_D64_MM", LIEF::assembly::mips::OPCODE::C_LT_D64_MM)
  .value("C_LT_S", LIEF::assembly::mips::OPCODE::C_LT_S)
  .value("C_LT_S_MM", LIEF::assembly::mips::OPCODE::C_LT_S_MM)
  .value("C_NGE_D32", LIEF::assembly::mips::OPCODE::C_NGE_D32)
  .value("C_NGE_D32_MM", LIEF::assembly::mips::OPCODE::C_NGE_D32_MM)
  .value("C_NGE_D64", LIEF::assembly::mips::OPCODE::C_NGE_D64)
  .value("C_NGE_D64_MM", LIEF::assembly::mips::OPCODE::C_NGE_D64_MM)
  .value("C_NGE_S", LIEF::assembly::mips::OPCODE::C_NGE_S)
  .value("C_NGE_S_MM", LIEF::assembly::mips::OPCODE::C_NGE_S_MM)
  .value("C_NGLE_D32", LIEF::assembly::mips::OPCODE::C_NGLE_D32)
  .value("C_NGLE_D32_MM", LIEF::assembly::mips::OPCODE::C_NGLE_D32_MM)
  .value("C_NGLE_D64", LIEF::assembly::mips::OPCODE::C_NGLE_D64)
  .value("C_NGLE_D64_MM", LIEF::assembly::mips::OPCODE::C_NGLE_D64_MM)
  .value("C_NGLE_S", LIEF::assembly::mips::OPCODE::C_NGLE_S)
  .value("C_NGLE_S_MM", LIEF::assembly::mips::OPCODE::C_NGLE_S_MM)
  .value("C_NGL_D32", LIEF::assembly::mips::OPCODE::C_NGL_D32)
  .value("C_NGL_D32_MM", LIEF::assembly::mips::OPCODE::C_NGL_D32_MM)
  .value("C_NGL_D64", LIEF::assembly::mips::OPCODE::C_NGL_D64)
  .value("C_NGL_D64_MM", LIEF::assembly::mips::OPCODE::C_NGL_D64_MM)
  .value("C_NGL_S", LIEF::assembly::mips::OPCODE::C_NGL_S)
  .value("C_NGL_S_MM", LIEF::assembly::mips::OPCODE::C_NGL_S_MM)
  .value("C_NGT_D32", LIEF::assembly::mips::OPCODE::C_NGT_D32)
  .value("C_NGT_D32_MM", LIEF::assembly::mips::OPCODE::C_NGT_D32_MM)
  .value("C_NGT_D64", LIEF::assembly::mips::OPCODE::C_NGT_D64)
  .value("C_NGT_D64_MM", LIEF::assembly::mips::OPCODE::C_NGT_D64_MM)
  .value("C_NGT_S", LIEF::assembly::mips::OPCODE::C_NGT_S)
  .value("C_NGT_S_MM", LIEF::assembly::mips::OPCODE::C_NGT_S_MM)
  .value("C_OLE_D32", LIEF::assembly::mips::OPCODE::C_OLE_D32)
  .value("C_OLE_D32_MM", LIEF::assembly::mips::OPCODE::C_OLE_D32_MM)
  .value("C_OLE_D64", LIEF::assembly::mips::OPCODE::C_OLE_D64)
  .value("C_OLE_D64_MM", LIEF::assembly::mips::OPCODE::C_OLE_D64_MM)
  .value("C_OLE_S", LIEF::assembly::mips::OPCODE::C_OLE_S)
  .value("C_OLE_S_MM", LIEF::assembly::mips::OPCODE::C_OLE_S_MM)
  .value("C_OLT_D32", LIEF::assembly::mips::OPCODE::C_OLT_D32)
  .value("C_OLT_D32_MM", LIEF::assembly::mips::OPCODE::C_OLT_D32_MM)
  .value("C_OLT_D64", LIEF::assembly::mips::OPCODE::C_OLT_D64)
  .value("C_OLT_D64_MM", LIEF::assembly::mips::OPCODE::C_OLT_D64_MM)
  .value("C_OLT_S", LIEF::assembly::mips::OPCODE::C_OLT_S)
  .value("C_OLT_S_MM", LIEF::assembly::mips::OPCODE::C_OLT_S_MM)
  .value("C_SEQ_D32", LIEF::assembly::mips::OPCODE::C_SEQ_D32)
  .value("C_SEQ_D32_MM", LIEF::assembly::mips::OPCODE::C_SEQ_D32_MM)
  .value("C_SEQ_D64", LIEF::assembly::mips::OPCODE::C_SEQ_D64)
  .value("C_SEQ_D64_MM", LIEF::assembly::mips::OPCODE::C_SEQ_D64_MM)
  .value("C_SEQ_S", LIEF::assembly::mips::OPCODE::C_SEQ_S)
  .value("C_SEQ_S_MM", LIEF::assembly::mips::OPCODE::C_SEQ_S_MM)
  .value("C_SF_D32", LIEF::assembly::mips::OPCODE::C_SF_D32)
  .value("C_SF_D32_MM", LIEF::assembly::mips::OPCODE::C_SF_D32_MM)
  .value("C_SF_D64", LIEF::assembly::mips::OPCODE::C_SF_D64)
  .value("C_SF_D64_MM", LIEF::assembly::mips::OPCODE::C_SF_D64_MM)
  .value("C_SF_S", LIEF::assembly::mips::OPCODE::C_SF_S)
  .value("C_SF_S_MM", LIEF::assembly::mips::OPCODE::C_SF_S_MM)
  .value("C_UEQ_D32", LIEF::assembly::mips::OPCODE::C_UEQ_D32)
  .value("C_UEQ_D32_MM", LIEF::assembly::mips::OPCODE::C_UEQ_D32_MM)
  .value("C_UEQ_D64", LIEF::assembly::mips::OPCODE::C_UEQ_D64)
  .value("C_UEQ_D64_MM", LIEF::assembly::mips::OPCODE::C_UEQ_D64_MM)
  .value("C_UEQ_S", LIEF::assembly::mips::OPCODE::C_UEQ_S)
  .value("C_UEQ_S_MM", LIEF::assembly::mips::OPCODE::C_UEQ_S_MM)
  .value("C_ULE_D32", LIEF::assembly::mips::OPCODE::C_ULE_D32)
  .value("C_ULE_D32_MM", LIEF::assembly::mips::OPCODE::C_ULE_D32_MM)
  .value("C_ULE_D64", LIEF::assembly::mips::OPCODE::C_ULE_D64)
  .value("C_ULE_D64_MM", LIEF::assembly::mips::OPCODE::C_ULE_D64_MM)
  .value("C_ULE_S", LIEF::assembly::mips::OPCODE::C_ULE_S)
  .value("C_ULE_S_MM", LIEF::assembly::mips::OPCODE::C_ULE_S_MM)
  .value("C_ULT_D32", LIEF::assembly::mips::OPCODE::C_ULT_D32)
  .value("C_ULT_D32_MM", LIEF::assembly::mips::OPCODE::C_ULT_D32_MM)
  .value("C_ULT_D64", LIEF::assembly::mips::OPCODE::C_ULT_D64)
  .value("C_ULT_D64_MM", LIEF::assembly::mips::OPCODE::C_ULT_D64_MM)
  .value("C_ULT_S", LIEF::assembly::mips::OPCODE::C_ULT_S)
  .value("C_ULT_S_MM", LIEF::assembly::mips::OPCODE::C_ULT_S_MM)
  .value("C_UN_D32", LIEF::assembly::mips::OPCODE::C_UN_D32)
  .value("C_UN_D32_MM", LIEF::assembly::mips::OPCODE::C_UN_D32_MM)
  .value("C_UN_D64", LIEF::assembly::mips::OPCODE::C_UN_D64)
  .value("C_UN_D64_MM", LIEF::assembly::mips::OPCODE::C_UN_D64_MM)
  .value("C_UN_S", LIEF::assembly::mips::OPCODE::C_UN_S)
  .value("C_UN_S_MM", LIEF::assembly::mips::OPCODE::C_UN_S_MM)
  .value("CmpRxRy16", LIEF::assembly::mips::OPCODE::CmpRxRy16)
  .value("CmpiRxImm16", LIEF::assembly::mips::OPCODE::CmpiRxImm16)
  .value("CmpiRxImmX16", LIEF::assembly::mips::OPCODE::CmpiRxImmX16)
  .value("DADD", LIEF::assembly::mips::OPCODE::DADD)
  .value("DADDi", LIEF::assembly::mips::OPCODE::DADDi)
  .value("DADDiu", LIEF::assembly::mips::OPCODE::DADDiu)
  .value("DADDu", LIEF::assembly::mips::OPCODE::DADDu)
  .value("DAHI", LIEF::assembly::mips::OPCODE::DAHI)
  .value("DALIGN", LIEF::assembly::mips::OPCODE::DALIGN)
  .value("DATI", LIEF::assembly::mips::OPCODE::DATI)
  .value("DAUI", LIEF::assembly::mips::OPCODE::DAUI)
  .value("DBITSWAP", LIEF::assembly::mips::OPCODE::DBITSWAP)
  .value("DCLO", LIEF::assembly::mips::OPCODE::DCLO)
  .value("DCLO_R6", LIEF::assembly::mips::OPCODE::DCLO_R6)
  .value("DCLZ", LIEF::assembly::mips::OPCODE::DCLZ)
  .value("DCLZ_R6", LIEF::assembly::mips::OPCODE::DCLZ_R6)
  .value("DDIV", LIEF::assembly::mips::OPCODE::DDIV)
  .value("DDIVU", LIEF::assembly::mips::OPCODE::DDIVU)
  .value("DERET", LIEF::assembly::mips::OPCODE::DERET)
  .value("DERET_MM", LIEF::assembly::mips::OPCODE::DERET_MM)
  .value("DERET_MMR6", LIEF::assembly::mips::OPCODE::DERET_MMR6)
  .value("DEXT", LIEF::assembly::mips::OPCODE::DEXT)
  .value("DEXT64_32", LIEF::assembly::mips::OPCODE::DEXT64_32)
  .value("DEXTM", LIEF::assembly::mips::OPCODE::DEXTM)
  .value("DEXTU", LIEF::assembly::mips::OPCODE::DEXTU)
  .value("DI", LIEF::assembly::mips::OPCODE::DI)
  .value("DINS", LIEF::assembly::mips::OPCODE::DINS)
  .value("DINSM", LIEF::assembly::mips::OPCODE::DINSM)
  .value("DINSU", LIEF::assembly::mips::OPCODE::DINSU)
  .value("DIV", LIEF::assembly::mips::OPCODE::DIV)
  .value("DIVU", LIEF::assembly::mips::OPCODE::DIVU)
  .value("DIVU_MMR6", LIEF::assembly::mips::OPCODE::DIVU_MMR6)
  .value("DIV_MMR6", LIEF::assembly::mips::OPCODE::DIV_MMR6)
  .value("DIV_S_B", LIEF::assembly::mips::OPCODE::DIV_S_B)
  .value("DIV_S_D", LIEF::assembly::mips::OPCODE::DIV_S_D)
  .value("DIV_S_H", LIEF::assembly::mips::OPCODE::DIV_S_H)
  .value("DIV_S_W", LIEF::assembly::mips::OPCODE::DIV_S_W)
  .value("DIV_U_B", LIEF::assembly::mips::OPCODE::DIV_U_B)
  .value("DIV_U_D", LIEF::assembly::mips::OPCODE::DIV_U_D)
  .value("DIV_U_H", LIEF::assembly::mips::OPCODE::DIV_U_H)
  .value("DIV_U_W", LIEF::assembly::mips::OPCODE::DIV_U_W)
  .value("DI_MM", LIEF::assembly::mips::OPCODE::DI_MM)
  .value("DI_MMR6", LIEF::assembly::mips::OPCODE::DI_MMR6)
  .value("DLSA", LIEF::assembly::mips::OPCODE::DLSA)
  .value("DLSA_R6", LIEF::assembly::mips::OPCODE::DLSA_R6)
  .value("DMFC0", LIEF::assembly::mips::OPCODE::DMFC0)
  .value("DMFC1", LIEF::assembly::mips::OPCODE::DMFC1)
  .value("DMFC2", LIEF::assembly::mips::OPCODE::DMFC2)
  .value("DMFC2_OCTEON", LIEF::assembly::mips::OPCODE::DMFC2_OCTEON)
  .value("DMFGC0", LIEF::assembly::mips::OPCODE::DMFGC0)
  .value("DMOD", LIEF::assembly::mips::OPCODE::DMOD)
  .value("DMODU", LIEF::assembly::mips::OPCODE::DMODU)
  .value("DMT", LIEF::assembly::mips::OPCODE::DMT)
  .value("DMTC0", LIEF::assembly::mips::OPCODE::DMTC0)
  .value("DMTC1", LIEF::assembly::mips::OPCODE::DMTC1)
  .value("DMTC2", LIEF::assembly::mips::OPCODE::DMTC2)
  .value("DMTC2_OCTEON", LIEF::assembly::mips::OPCODE::DMTC2_OCTEON)
  .value("DMTGC0", LIEF::assembly::mips::OPCODE::DMTGC0)
  .value("DMUH", LIEF::assembly::mips::OPCODE::DMUH)
  .value("DMUHU", LIEF::assembly::mips::OPCODE::DMUHU)
  .value("DMUL", LIEF::assembly::mips::OPCODE::DMUL)
  .value("DMULT", LIEF::assembly::mips::OPCODE::DMULT)
  .value("DMULTu", LIEF::assembly::mips::OPCODE::DMULTu)
  .value("DMULU", LIEF::assembly::mips::OPCODE::DMULU)
  .value("DMUL_R6", LIEF::assembly::mips::OPCODE::DMUL_R6)
  .value("DOTP_S_D", LIEF::assembly::mips::OPCODE::DOTP_S_D)
  .value("DOTP_S_H", LIEF::assembly::mips::OPCODE::DOTP_S_H)
  .value("DOTP_S_W", LIEF::assembly::mips::OPCODE::DOTP_S_W)
  .value("DOTP_U_D", LIEF::assembly::mips::OPCODE::DOTP_U_D)
  .value("DOTP_U_H", LIEF::assembly::mips::OPCODE::DOTP_U_H)
  .value("DOTP_U_W", LIEF::assembly::mips::OPCODE::DOTP_U_W)
  .value("DPADD_S_D", LIEF::assembly::mips::OPCODE::DPADD_S_D)
  .value("DPADD_S_H", LIEF::assembly::mips::OPCODE::DPADD_S_H)
  .value("DPADD_S_W", LIEF::assembly::mips::OPCODE::DPADD_S_W)
  .value("DPADD_U_D", LIEF::assembly::mips::OPCODE::DPADD_U_D)
  .value("DPADD_U_H", LIEF::assembly::mips::OPCODE::DPADD_U_H)
  .value("DPADD_U_W", LIEF::assembly::mips::OPCODE::DPADD_U_W)
  .value("DPAQX_SA_W_PH", LIEF::assembly::mips::OPCODE::DPAQX_SA_W_PH)
  .value("DPAQX_SA_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPAQX_SA_W_PH_MMR2)
  .value("DPAQX_S_W_PH", LIEF::assembly::mips::OPCODE::DPAQX_S_W_PH)
  .value("DPAQX_S_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPAQX_S_W_PH_MMR2)
  .value("DPAQ_SA_L_W", LIEF::assembly::mips::OPCODE::DPAQ_SA_L_W)
  .value("DPAQ_SA_L_W_MM", LIEF::assembly::mips::OPCODE::DPAQ_SA_L_W_MM)
  .value("DPAQ_S_W_PH", LIEF::assembly::mips::OPCODE::DPAQ_S_W_PH);
  opcodes.value("DPAQ_S_W_PH_MM", LIEF::assembly::mips::OPCODE::DPAQ_S_W_PH_MM)
  .value("DPAU_H_QBL", LIEF::assembly::mips::OPCODE::DPAU_H_QBL)
  .value("DPAU_H_QBL_MM", LIEF::assembly::mips::OPCODE::DPAU_H_QBL_MM)
  .value("DPAU_H_QBR", LIEF::assembly::mips::OPCODE::DPAU_H_QBR)
  .value("DPAU_H_QBR_MM", LIEF::assembly::mips::OPCODE::DPAU_H_QBR_MM)
  .value("DPAX_W_PH", LIEF::assembly::mips::OPCODE::DPAX_W_PH)
  .value("DPAX_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPAX_W_PH_MMR2)
  .value("DPA_W_PH", LIEF::assembly::mips::OPCODE::DPA_W_PH)
  .value("DPA_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPA_W_PH_MMR2)
  .value("DPOP", LIEF::assembly::mips::OPCODE::DPOP)
  .value("DPSQX_SA_W_PH", LIEF::assembly::mips::OPCODE::DPSQX_SA_W_PH)
  .value("DPSQX_SA_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPSQX_SA_W_PH_MMR2)
  .value("DPSQX_S_W_PH", LIEF::assembly::mips::OPCODE::DPSQX_S_W_PH)
  .value("DPSQX_S_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPSQX_S_W_PH_MMR2)
  .value("DPSQ_SA_L_W", LIEF::assembly::mips::OPCODE::DPSQ_SA_L_W)
  .value("DPSQ_SA_L_W_MM", LIEF::assembly::mips::OPCODE::DPSQ_SA_L_W_MM)
  .value("DPSQ_S_W_PH", LIEF::assembly::mips::OPCODE::DPSQ_S_W_PH)
  .value("DPSQ_S_W_PH_MM", LIEF::assembly::mips::OPCODE::DPSQ_S_W_PH_MM)
  .value("DPSUB_S_D", LIEF::assembly::mips::OPCODE::DPSUB_S_D)
  .value("DPSUB_S_H", LIEF::assembly::mips::OPCODE::DPSUB_S_H)
  .value("DPSUB_S_W", LIEF::assembly::mips::OPCODE::DPSUB_S_W)
  .value("DPSUB_U_D", LIEF::assembly::mips::OPCODE::DPSUB_U_D)
  .value("DPSUB_U_H", LIEF::assembly::mips::OPCODE::DPSUB_U_H)
  .value("DPSUB_U_W", LIEF::assembly::mips::OPCODE::DPSUB_U_W)
  .value("DPSU_H_QBL", LIEF::assembly::mips::OPCODE::DPSU_H_QBL)
  .value("DPSU_H_QBL_MM", LIEF::assembly::mips::OPCODE::DPSU_H_QBL_MM)
  .value("DPSU_H_QBR", LIEF::assembly::mips::OPCODE::DPSU_H_QBR)
  .value("DPSU_H_QBR_MM", LIEF::assembly::mips::OPCODE::DPSU_H_QBR_MM)
  .value("DPSX_W_PH", LIEF::assembly::mips::OPCODE::DPSX_W_PH)
  .value("DPSX_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPSX_W_PH_MMR2)
  .value("DPS_W_PH", LIEF::assembly::mips::OPCODE::DPS_W_PH)
  .value("DPS_W_PH_MMR2", LIEF::assembly::mips::OPCODE::DPS_W_PH_MMR2)
  .value("DROTR", LIEF::assembly::mips::OPCODE::DROTR)
  .value("DROTR32", LIEF::assembly::mips::OPCODE::DROTR32)
  .value("DROTRV", LIEF::assembly::mips::OPCODE::DROTRV)
  .value("DSBH", LIEF::assembly::mips::OPCODE::DSBH)
  .value("DSDIV", LIEF::assembly::mips::OPCODE::DSDIV)
  .value("DSHD", LIEF::assembly::mips::OPCODE::DSHD)
  .value("DSLL", LIEF::assembly::mips::OPCODE::DSLL)
  .value("DSLL32", LIEF::assembly::mips::OPCODE::DSLL32)
  .value("DSLL64_32", LIEF::assembly::mips::OPCODE::DSLL64_32)
  .value("DSLLV", LIEF::assembly::mips::OPCODE::DSLLV)
  .value("DSRA", LIEF::assembly::mips::OPCODE::DSRA)
  .value("DSRA32", LIEF::assembly::mips::OPCODE::DSRA32)
  .value("DSRAV", LIEF::assembly::mips::OPCODE::DSRAV)
  .value("DSRL", LIEF::assembly::mips::OPCODE::DSRL)
  .value("DSRL32", LIEF::assembly::mips::OPCODE::DSRL32)
  .value("DSRLV", LIEF::assembly::mips::OPCODE::DSRLV)
  .value("DSUB", LIEF::assembly::mips::OPCODE::DSUB)
  .value("DSUBu", LIEF::assembly::mips::OPCODE::DSUBu)
  .value("DUDIV", LIEF::assembly::mips::OPCODE::DUDIV)
  .value("DVP", LIEF::assembly::mips::OPCODE::DVP)
  .value("DVPE", LIEF::assembly::mips::OPCODE::DVPE)
  .value("DVP_MMR6", LIEF::assembly::mips::OPCODE::DVP_MMR6)
  .value("DivRxRy16", LIEF::assembly::mips::OPCODE::DivRxRy16)
  .value("DivuRxRy16", LIEF::assembly::mips::OPCODE::DivuRxRy16)
  .value("EHB", LIEF::assembly::mips::OPCODE::EHB)
  .value("EHB_MM", LIEF::assembly::mips::OPCODE::EHB_MM)
  .value("EHB_MMR6", LIEF::assembly::mips::OPCODE::EHB_MMR6)
  .value("EI", LIEF::assembly::mips::OPCODE::EI)
  .value("EI_MM", LIEF::assembly::mips::OPCODE::EI_MM)
  .value("EI_MMR6", LIEF::assembly::mips::OPCODE::EI_MMR6)
  .value("EMT", LIEF::assembly::mips::OPCODE::EMT)
  .value("ERET", LIEF::assembly::mips::OPCODE::ERET)
  .value("ERETNC", LIEF::assembly::mips::OPCODE::ERETNC)
  .value("ERETNC_MMR6", LIEF::assembly::mips::OPCODE::ERETNC_MMR6)
  .value("ERET_MM", LIEF::assembly::mips::OPCODE::ERET_MM)
  .value("ERET_MMR6", LIEF::assembly::mips::OPCODE::ERET_MMR6)
  .value("EVP", LIEF::assembly::mips::OPCODE::EVP)
  .value("EVPE", LIEF::assembly::mips::OPCODE::EVPE)
  .value("EVP_MMR6", LIEF::assembly::mips::OPCODE::EVP_MMR6)
  .value("EXT", LIEF::assembly::mips::OPCODE::EXT)
  .value("EXTP", LIEF::assembly::mips::OPCODE::EXTP)
  .value("EXTPDP", LIEF::assembly::mips::OPCODE::EXTPDP)
  .value("EXTPDPV", LIEF::assembly::mips::OPCODE::EXTPDPV)
  .value("EXTPDPV_MM", LIEF::assembly::mips::OPCODE::EXTPDPV_MM)
  .value("EXTPDP_MM", LIEF::assembly::mips::OPCODE::EXTPDP_MM)
  .value("EXTPV", LIEF::assembly::mips::OPCODE::EXTPV)
  .value("EXTPV_MM", LIEF::assembly::mips::OPCODE::EXTPV_MM)
  .value("EXTP_MM", LIEF::assembly::mips::OPCODE::EXTP_MM)
  .value("EXTRV_RS_W", LIEF::assembly::mips::OPCODE::EXTRV_RS_W)
  .value("EXTRV_RS_W_MM", LIEF::assembly::mips::OPCODE::EXTRV_RS_W_MM)
  .value("EXTRV_R_W", LIEF::assembly::mips::OPCODE::EXTRV_R_W)
  .value("EXTRV_R_W_MM", LIEF::assembly::mips::OPCODE::EXTRV_R_W_MM)
  .value("EXTRV_S_H", LIEF::assembly::mips::OPCODE::EXTRV_S_H)
  .value("EXTRV_S_H_MM", LIEF::assembly::mips::OPCODE::EXTRV_S_H_MM)
  .value("EXTRV_W", LIEF::assembly::mips::OPCODE::EXTRV_W)
  .value("EXTRV_W_MM", LIEF::assembly::mips::OPCODE::EXTRV_W_MM)
  .value("EXTR_RS_W", LIEF::assembly::mips::OPCODE::EXTR_RS_W)
  .value("EXTR_RS_W_MM", LIEF::assembly::mips::OPCODE::EXTR_RS_W_MM)
  .value("EXTR_R_W", LIEF::assembly::mips::OPCODE::EXTR_R_W)
  .value("EXTR_R_W_MM", LIEF::assembly::mips::OPCODE::EXTR_R_W_MM)
  .value("EXTR_S_H", LIEF::assembly::mips::OPCODE::EXTR_S_H)
  .value("EXTR_S_H_MM", LIEF::assembly::mips::OPCODE::EXTR_S_H_MM)
  .value("EXTR_W", LIEF::assembly::mips::OPCODE::EXTR_W)
  .value("EXTR_W_MM", LIEF::assembly::mips::OPCODE::EXTR_W_MM)
  .value("EXTS", LIEF::assembly::mips::OPCODE::EXTS)
  .value("EXTS32", LIEF::assembly::mips::OPCODE::EXTS32)
  .value("EXT_MM", LIEF::assembly::mips::OPCODE::EXT_MM)
  .value("EXT_MMR6", LIEF::assembly::mips::OPCODE::EXT_MMR6)
  .value("FABS_D32", LIEF::assembly::mips::OPCODE::FABS_D32)
  .value("FABS_D32_MM", LIEF::assembly::mips::OPCODE::FABS_D32_MM)
  .value("FABS_D64", LIEF::assembly::mips::OPCODE::FABS_D64)
  .value("FABS_D64_MM", LIEF::assembly::mips::OPCODE::FABS_D64_MM)
  .value("FABS_S", LIEF::assembly::mips::OPCODE::FABS_S)
  .value("FABS_S_MM", LIEF::assembly::mips::OPCODE::FABS_S_MM)
  .value("FADD_D", LIEF::assembly::mips::OPCODE::FADD_D)
  .value("FADD_D32", LIEF::assembly::mips::OPCODE::FADD_D32)
  .value("FADD_D32_MM", LIEF::assembly::mips::OPCODE::FADD_D32_MM)
  .value("FADD_D64", LIEF::assembly::mips::OPCODE::FADD_D64)
  .value("FADD_D64_MM", LIEF::assembly::mips::OPCODE::FADD_D64_MM)
  .value("FADD_PS64", LIEF::assembly::mips::OPCODE::FADD_PS64)
  .value("FADD_S", LIEF::assembly::mips::OPCODE::FADD_S)
  .value("FADD_S_MM", LIEF::assembly::mips::OPCODE::FADD_S_MM)
  .value("FADD_S_MMR6", LIEF::assembly::mips::OPCODE::FADD_S_MMR6)
  .value("FADD_W", LIEF::assembly::mips::OPCODE::FADD_W)
  .value("FCAF_D", LIEF::assembly::mips::OPCODE::FCAF_D)
  .value("FCAF_W", LIEF::assembly::mips::OPCODE::FCAF_W)
  .value("FCEQ_D", LIEF::assembly::mips::OPCODE::FCEQ_D)
  .value("FCEQ_W", LIEF::assembly::mips::OPCODE::FCEQ_W)
  .value("FCLASS_D", LIEF::assembly::mips::OPCODE::FCLASS_D)
  .value("FCLASS_W", LIEF::assembly::mips::OPCODE::FCLASS_W)
  .value("FCLE_D", LIEF::assembly::mips::OPCODE::FCLE_D)
  .value("FCLE_W", LIEF::assembly::mips::OPCODE::FCLE_W)
  .value("FCLT_D", LIEF::assembly::mips::OPCODE::FCLT_D)
  .value("FCLT_W", LIEF::assembly::mips::OPCODE::FCLT_W)
  .value("FCMP_D32", LIEF::assembly::mips::OPCODE::FCMP_D32)
  .value("FCMP_D32_MM", LIEF::assembly::mips::OPCODE::FCMP_D32_MM)
  .value("FCMP_D64", LIEF::assembly::mips::OPCODE::FCMP_D64)
  .value("FCMP_S32", LIEF::assembly::mips::OPCODE::FCMP_S32)
  .value("FCMP_S32_MM", LIEF::assembly::mips::OPCODE::FCMP_S32_MM)
  .value("FCNE_D", LIEF::assembly::mips::OPCODE::FCNE_D)
  .value("FCNE_W", LIEF::assembly::mips::OPCODE::FCNE_W)
  .value("FCOR_D", LIEF::assembly::mips::OPCODE::FCOR_D)
  .value("FCOR_W", LIEF::assembly::mips::OPCODE::FCOR_W)
  .value("FCUEQ_D", LIEF::assembly::mips::OPCODE::FCUEQ_D)
  .value("FCUEQ_W", LIEF::assembly::mips::OPCODE::FCUEQ_W)
  .value("FCULE_D", LIEF::assembly::mips::OPCODE::FCULE_D)
  .value("FCULE_W", LIEF::assembly::mips::OPCODE::FCULE_W)
  .value("FCULT_D", LIEF::assembly::mips::OPCODE::FCULT_D)
  .value("FCULT_W", LIEF::assembly::mips::OPCODE::FCULT_W)
  .value("FCUNE_D", LIEF::assembly::mips::OPCODE::FCUNE_D)
  .value("FCUNE_W", LIEF::assembly::mips::OPCODE::FCUNE_W)
  .value("FCUN_D", LIEF::assembly::mips::OPCODE::FCUN_D)
  .value("FCUN_W", LIEF::assembly::mips::OPCODE::FCUN_W)
  .value("FDIV_D", LIEF::assembly::mips::OPCODE::FDIV_D)
  .value("FDIV_D32", LIEF::assembly::mips::OPCODE::FDIV_D32)
  .value("FDIV_D32_MM", LIEF::assembly::mips::OPCODE::FDIV_D32_MM)
  .value("FDIV_D64", LIEF::assembly::mips::OPCODE::FDIV_D64)
  .value("FDIV_D64_MM", LIEF::assembly::mips::OPCODE::FDIV_D64_MM)
  .value("FDIV_S", LIEF::assembly::mips::OPCODE::FDIV_S)
  .value("FDIV_S_MM", LIEF::assembly::mips::OPCODE::FDIV_S_MM)
  .value("FDIV_S_MMR6", LIEF::assembly::mips::OPCODE::FDIV_S_MMR6)
  .value("FDIV_W", LIEF::assembly::mips::OPCODE::FDIV_W)
  .value("FEXDO_H", LIEF::assembly::mips::OPCODE::FEXDO_H)
  .value("FEXDO_W", LIEF::assembly::mips::OPCODE::FEXDO_W)
  .value("FEXP2_D", LIEF::assembly::mips::OPCODE::FEXP2_D)
  .value("FEXP2_W", LIEF::assembly::mips::OPCODE::FEXP2_W)
  .value("FEXUPL_D", LIEF::assembly::mips::OPCODE::FEXUPL_D)
  .value("FEXUPL_W", LIEF::assembly::mips::OPCODE::FEXUPL_W)
  .value("FEXUPR_D", LIEF::assembly::mips::OPCODE::FEXUPR_D)
  .value("FEXUPR_W", LIEF::assembly::mips::OPCODE::FEXUPR_W)
  .value("FFINT_S_D", LIEF::assembly::mips::OPCODE::FFINT_S_D)
  .value("FFINT_S_W", LIEF::assembly::mips::OPCODE::FFINT_S_W)
  .value("FFINT_U_D", LIEF::assembly::mips::OPCODE::FFINT_U_D)
  .value("FFINT_U_W", LIEF::assembly::mips::OPCODE::FFINT_U_W)
  .value("FFQL_D", LIEF::assembly::mips::OPCODE::FFQL_D)
  .value("FFQL_W", LIEF::assembly::mips::OPCODE::FFQL_W)
  .value("FFQR_D", LIEF::assembly::mips::OPCODE::FFQR_D)
  .value("FFQR_W", LIEF::assembly::mips::OPCODE::FFQR_W)
  .value("FILL_B", LIEF::assembly::mips::OPCODE::FILL_B)
  .value("FILL_D", LIEF::assembly::mips::OPCODE::FILL_D)
  .value("FILL_H", LIEF::assembly::mips::OPCODE::FILL_H)
  .value("FILL_W", LIEF::assembly::mips::OPCODE::FILL_W)
  .value("FLOG2_D", LIEF::assembly::mips::OPCODE::FLOG2_D)
  .value("FLOG2_W", LIEF::assembly::mips::OPCODE::FLOG2_W)
  .value("FLOOR_L_D64", LIEF::assembly::mips::OPCODE::FLOOR_L_D64)
  .value("FLOOR_L_D_MMR6", LIEF::assembly::mips::OPCODE::FLOOR_L_D_MMR6)
  .value("FLOOR_L_S", LIEF::assembly::mips::OPCODE::FLOOR_L_S)
  .value("FLOOR_L_S_MMR6", LIEF::assembly::mips::OPCODE::FLOOR_L_S_MMR6)
  .value("FLOOR_W_D32", LIEF::assembly::mips::OPCODE::FLOOR_W_D32)
  .value("FLOOR_W_D64", LIEF::assembly::mips::OPCODE::FLOOR_W_D64)
  .value("FLOOR_W_D_MMR6", LIEF::assembly::mips::OPCODE::FLOOR_W_D_MMR6)
  .value("FLOOR_W_MM", LIEF::assembly::mips::OPCODE::FLOOR_W_MM)
  .value("FLOOR_W_S", LIEF::assembly::mips::OPCODE::FLOOR_W_S)
  .value("FLOOR_W_S_MM", LIEF::assembly::mips::OPCODE::FLOOR_W_S_MM)
  .value("FLOOR_W_S_MMR6", LIEF::assembly::mips::OPCODE::FLOOR_W_S_MMR6)
  .value("FMADD_D", LIEF::assembly::mips::OPCODE::FMADD_D)
  .value("FMADD_W", LIEF::assembly::mips::OPCODE::FMADD_W)
  .value("FMAX_A_D", LIEF::assembly::mips::OPCODE::FMAX_A_D)
  .value("FMAX_A_W", LIEF::assembly::mips::OPCODE::FMAX_A_W)
  .value("FMAX_D", LIEF::assembly::mips::OPCODE::FMAX_D)
  .value("FMAX_W", LIEF::assembly::mips::OPCODE::FMAX_W)
  .value("FMIN_A_D", LIEF::assembly::mips::OPCODE::FMIN_A_D)
  .value("FMIN_A_W", LIEF::assembly::mips::OPCODE::FMIN_A_W)
  .value("FMIN_D", LIEF::assembly::mips::OPCODE::FMIN_D)
  .value("FMIN_W", LIEF::assembly::mips::OPCODE::FMIN_W)
  .value("FMOV_D32", LIEF::assembly::mips::OPCODE::FMOV_D32)
  .value("FMOV_D32_MM", LIEF::assembly::mips::OPCODE::FMOV_D32_MM)
  .value("FMOV_D64", LIEF::assembly::mips::OPCODE::FMOV_D64)
  .value("FMOV_D64_MM", LIEF::assembly::mips::OPCODE::FMOV_D64_MM)
  .value("FMOV_D_MMR6", LIEF::assembly::mips::OPCODE::FMOV_D_MMR6)
  .value("FMOV_S", LIEF::assembly::mips::OPCODE::FMOV_S)
  .value("FMOV_S_MM", LIEF::assembly::mips::OPCODE::FMOV_S_MM)
  .value("FMOV_S_MMR6", LIEF::assembly::mips::OPCODE::FMOV_S_MMR6)
  .value("FMSUB_D", LIEF::assembly::mips::OPCODE::FMSUB_D)
  .value("FMSUB_W", LIEF::assembly::mips::OPCODE::FMSUB_W)
  .value("FMUL_D", LIEF::assembly::mips::OPCODE::FMUL_D)
  .value("FMUL_D32", LIEF::assembly::mips::OPCODE::FMUL_D32)
  .value("FMUL_D32_MM", LIEF::assembly::mips::OPCODE::FMUL_D32_MM)
  .value("FMUL_D64", LIEF::assembly::mips::OPCODE::FMUL_D64)
  .value("FMUL_D64_MM", LIEF::assembly::mips::OPCODE::FMUL_D64_MM)
  .value("FMUL_PS64", LIEF::assembly::mips::OPCODE::FMUL_PS64)
  .value("FMUL_S", LIEF::assembly::mips::OPCODE::FMUL_S)
  .value("FMUL_S_MM", LIEF::assembly::mips::OPCODE::FMUL_S_MM)
  .value("FMUL_S_MMR6", LIEF::assembly::mips::OPCODE::FMUL_S_MMR6)
  .value("FMUL_W", LIEF::assembly::mips::OPCODE::FMUL_W)
  .value("FNEG_D32", LIEF::assembly::mips::OPCODE::FNEG_D32)
  .value("FNEG_D32_MM", LIEF::assembly::mips::OPCODE::FNEG_D32_MM)
  .value("FNEG_D64", LIEF::assembly::mips::OPCODE::FNEG_D64)
  .value("FNEG_D64_MM", LIEF::assembly::mips::OPCODE::FNEG_D64_MM)
  .value("FNEG_S", LIEF::assembly::mips::OPCODE::FNEG_S)
  .value("FNEG_S_MM", LIEF::assembly::mips::OPCODE::FNEG_S_MM)
  .value("FNEG_S_MMR6", LIEF::assembly::mips::OPCODE::FNEG_S_MMR6)
  .value("FORK", LIEF::assembly::mips::OPCODE::FORK)
  .value("FRCP_D", LIEF::assembly::mips::OPCODE::FRCP_D)
  .value("FRCP_W", LIEF::assembly::mips::OPCODE::FRCP_W)
  .value("FRINT_D", LIEF::assembly::mips::OPCODE::FRINT_D)
  .value("FRINT_W", LIEF::assembly::mips::OPCODE::FRINT_W)
  .value("FRSQRT_D", LIEF::assembly::mips::OPCODE::FRSQRT_D)
  .value("FRSQRT_W", LIEF::assembly::mips::OPCODE::FRSQRT_W)
  .value("FSAF_D", LIEF::assembly::mips::OPCODE::FSAF_D)
  .value("FSAF_W", LIEF::assembly::mips::OPCODE::FSAF_W)
  .value("FSEQ_D", LIEF::assembly::mips::OPCODE::FSEQ_D)
  .value("FSEQ_W", LIEF::assembly::mips::OPCODE::FSEQ_W)
  .value("FSLE_D", LIEF::assembly::mips::OPCODE::FSLE_D)
  .value("FSLE_W", LIEF::assembly::mips::OPCODE::FSLE_W)
  .value("FSLT_D", LIEF::assembly::mips::OPCODE::FSLT_D)
  .value("FSLT_W", LIEF::assembly::mips::OPCODE::FSLT_W)
  .value("FSNE_D", LIEF::assembly::mips::OPCODE::FSNE_D)
  .value("FSNE_W", LIEF::assembly::mips::OPCODE::FSNE_W)
  .value("FSOR_D", LIEF::assembly::mips::OPCODE::FSOR_D)
  .value("FSOR_W", LIEF::assembly::mips::OPCODE::FSOR_W)
  .value("FSQRT_D", LIEF::assembly::mips::OPCODE::FSQRT_D)
  .value("FSQRT_D32", LIEF::assembly::mips::OPCODE::FSQRT_D32)
  .value("FSQRT_D32_MM", LIEF::assembly::mips::OPCODE::FSQRT_D32_MM)
  .value("FSQRT_D64", LIEF::assembly::mips::OPCODE::FSQRT_D64)
  .value("FSQRT_D64_MM", LIEF::assembly::mips::OPCODE::FSQRT_D64_MM)
  .value("FSQRT_S", LIEF::assembly::mips::OPCODE::FSQRT_S)
  .value("FSQRT_S_MM", LIEF::assembly::mips::OPCODE::FSQRT_S_MM)
  .value("FSQRT_W", LIEF::assembly::mips::OPCODE::FSQRT_W)
  .value("FSUB_D", LIEF::assembly::mips::OPCODE::FSUB_D)
  .value("FSUB_D32", LIEF::assembly::mips::OPCODE::FSUB_D32)
  .value("FSUB_D32_MM", LIEF::assembly::mips::OPCODE::FSUB_D32_MM)
  .value("FSUB_D64", LIEF::assembly::mips::OPCODE::FSUB_D64)
  .value("FSUB_D64_MM", LIEF::assembly::mips::OPCODE::FSUB_D64_MM)
  .value("FSUB_PS64", LIEF::assembly::mips::OPCODE::FSUB_PS64)
  .value("FSUB_S", LIEF::assembly::mips::OPCODE::FSUB_S)
  .value("FSUB_S_MM", LIEF::assembly::mips::OPCODE::FSUB_S_MM)
  .value("FSUB_S_MMR6", LIEF::assembly::mips::OPCODE::FSUB_S_MMR6)
  .value("FSUB_W", LIEF::assembly::mips::OPCODE::FSUB_W)
  .value("FSUEQ_D", LIEF::assembly::mips::OPCODE::FSUEQ_D)
  .value("FSUEQ_W", LIEF::assembly::mips::OPCODE::FSUEQ_W)
  .value("FSULE_D", LIEF::assembly::mips::OPCODE::FSULE_D)
  .value("FSULE_W", LIEF::assembly::mips::OPCODE::FSULE_W)
  .value("FSULT_D", LIEF::assembly::mips::OPCODE::FSULT_D)
  .value("FSULT_W", LIEF::assembly::mips::OPCODE::FSULT_W)
  .value("FSUNE_D", LIEF::assembly::mips::OPCODE::FSUNE_D)
  .value("FSUNE_W", LIEF::assembly::mips::OPCODE::FSUNE_W)
  .value("FSUN_D", LIEF::assembly::mips::OPCODE::FSUN_D)
  .value("FSUN_W", LIEF::assembly::mips::OPCODE::FSUN_W)
  .value("FTINT_S_D", LIEF::assembly::mips::OPCODE::FTINT_S_D)
  .value("FTINT_S_W", LIEF::assembly::mips::OPCODE::FTINT_S_W)
  .value("FTINT_U_D", LIEF::assembly::mips::OPCODE::FTINT_U_D)
  .value("FTINT_U_W", LIEF::assembly::mips::OPCODE::FTINT_U_W)
  .value("FTQ_H", LIEF::assembly::mips::OPCODE::FTQ_H)
  .value("FTQ_W", LIEF::assembly::mips::OPCODE::FTQ_W)
  .value("FTRUNC_S_D", LIEF::assembly::mips::OPCODE::FTRUNC_S_D)
  .value("FTRUNC_S_W", LIEF::assembly::mips::OPCODE::FTRUNC_S_W)
  .value("FTRUNC_U_D", LIEF::assembly::mips::OPCODE::FTRUNC_U_D)
  .value("FTRUNC_U_W", LIEF::assembly::mips::OPCODE::FTRUNC_U_W)
  .value("GINVI", LIEF::assembly::mips::OPCODE::GINVI)
  .value("GINVI_MMR6", LIEF::assembly::mips::OPCODE::GINVI_MMR6)
  .value("GINVT", LIEF::assembly::mips::OPCODE::GINVT)
  .value("GINVT_MMR6", LIEF::assembly::mips::OPCODE::GINVT_MMR6)
  .value("HADD_S_D", LIEF::assembly::mips::OPCODE::HADD_S_D)
  .value("HADD_S_H", LIEF::assembly::mips::OPCODE::HADD_S_H)
  .value("HADD_S_W", LIEF::assembly::mips::OPCODE::HADD_S_W)
  .value("HADD_U_D", LIEF::assembly::mips::OPCODE::HADD_U_D)
  .value("HADD_U_H", LIEF::assembly::mips::OPCODE::HADD_U_H)
  .value("HADD_U_W", LIEF::assembly::mips::OPCODE::HADD_U_W)
  .value("HSUB_S_D", LIEF::assembly::mips::OPCODE::HSUB_S_D)
  .value("HSUB_S_H", LIEF::assembly::mips::OPCODE::HSUB_S_H)
  .value("HSUB_S_W", LIEF::assembly::mips::OPCODE::HSUB_S_W)
  .value("HSUB_U_D", LIEF::assembly::mips::OPCODE::HSUB_U_D)
  .value("HSUB_U_H", LIEF::assembly::mips::OPCODE::HSUB_U_H)
  .value("HSUB_U_W", LIEF::assembly::mips::OPCODE::HSUB_U_W)
  .value("HYPCALL", LIEF::assembly::mips::OPCODE::HYPCALL)
  .value("HYPCALL_MM", LIEF::assembly::mips::OPCODE::HYPCALL_MM)
  .value("ILVEV_B", LIEF::assembly::mips::OPCODE::ILVEV_B);
  opcodes.value("ILVEV_D", LIEF::assembly::mips::OPCODE::ILVEV_D)
  .value("ILVEV_H", LIEF::assembly::mips::OPCODE::ILVEV_H)
  .value("ILVEV_W", LIEF::assembly::mips::OPCODE::ILVEV_W)
  .value("ILVL_B", LIEF::assembly::mips::OPCODE::ILVL_B)
  .value("ILVL_D", LIEF::assembly::mips::OPCODE::ILVL_D)
  .value("ILVL_H", LIEF::assembly::mips::OPCODE::ILVL_H)
  .value("ILVL_W", LIEF::assembly::mips::OPCODE::ILVL_W)
  .value("ILVOD_B", LIEF::assembly::mips::OPCODE::ILVOD_B)
  .value("ILVOD_D", LIEF::assembly::mips::OPCODE::ILVOD_D)
  .value("ILVOD_H", LIEF::assembly::mips::OPCODE::ILVOD_H)
  .value("ILVOD_W", LIEF::assembly::mips::OPCODE::ILVOD_W)
  .value("ILVR_B", LIEF::assembly::mips::OPCODE::ILVR_B)
  .value("ILVR_D", LIEF::assembly::mips::OPCODE::ILVR_D)
  .value("ILVR_H", LIEF::assembly::mips::OPCODE::ILVR_H)
  .value("ILVR_W", LIEF::assembly::mips::OPCODE::ILVR_W)
  .value("INS", LIEF::assembly::mips::OPCODE::INS)
  .value("INSERT_B", LIEF::assembly::mips::OPCODE::INSERT_B)
  .value("INSERT_D", LIEF::assembly::mips::OPCODE::INSERT_D)
  .value("INSERT_H", LIEF::assembly::mips::OPCODE::INSERT_H)
  .value("INSERT_W", LIEF::assembly::mips::OPCODE::INSERT_W)
  .value("INSV", LIEF::assembly::mips::OPCODE::INSV)
  .value("INSVE_B", LIEF::assembly::mips::OPCODE::INSVE_B)
  .value("INSVE_D", LIEF::assembly::mips::OPCODE::INSVE_D)
  .value("INSVE_H", LIEF::assembly::mips::OPCODE::INSVE_H)
  .value("INSVE_W", LIEF::assembly::mips::OPCODE::INSVE_W)
  .value("INSV_MM", LIEF::assembly::mips::OPCODE::INSV_MM)
  .value("INS_MM", LIEF::assembly::mips::OPCODE::INS_MM)
  .value("INS_MMR6", LIEF::assembly::mips::OPCODE::INS_MMR6)
  .value("J", LIEF::assembly::mips::OPCODE::J)
  .value("JAL", LIEF::assembly::mips::OPCODE::JAL)
  .value("JALR", LIEF::assembly::mips::OPCODE::JALR)
  .value("JALR16_MM", LIEF::assembly::mips::OPCODE::JALR16_MM)
  .value("JALR64", LIEF::assembly::mips::OPCODE::JALR64)
  .value("JALRC16_MMR6", LIEF::assembly::mips::OPCODE::JALRC16_MMR6)
  .value("JALRC_HB_MMR6", LIEF::assembly::mips::OPCODE::JALRC_HB_MMR6)
  .value("JALRC_MMR6", LIEF::assembly::mips::OPCODE::JALRC_MMR6)
  .value("JALRS16_MM", LIEF::assembly::mips::OPCODE::JALRS16_MM)
  .value("JALRS_MM", LIEF::assembly::mips::OPCODE::JALRS_MM)
  .value("JALR_HB", LIEF::assembly::mips::OPCODE::JALR_HB)
  .value("JALR_HB64", LIEF::assembly::mips::OPCODE::JALR_HB64)
  .value("JALR_MM", LIEF::assembly::mips::OPCODE::JALR_MM)
  .value("JALS_MM", LIEF::assembly::mips::OPCODE::JALS_MM)
  .value("JALX", LIEF::assembly::mips::OPCODE::JALX)
  .value("JALX_MM", LIEF::assembly::mips::OPCODE::JALX_MM)
  .value("JAL_MM", LIEF::assembly::mips::OPCODE::JAL_MM)
  .value("JIALC", LIEF::assembly::mips::OPCODE::JIALC)
  .value("JIALC64", LIEF::assembly::mips::OPCODE::JIALC64)
  .value("JIALC_MMR6", LIEF::assembly::mips::OPCODE::JIALC_MMR6)
  .value("JIC", LIEF::assembly::mips::OPCODE::JIC)
  .value("JIC64", LIEF::assembly::mips::OPCODE::JIC64)
  .value("JIC_MMR6", LIEF::assembly::mips::OPCODE::JIC_MMR6)
  .value("JR", LIEF::assembly::mips::OPCODE::JR)
  .value("JR16_MM", LIEF::assembly::mips::OPCODE::JR16_MM)
  .value("JR64", LIEF::assembly::mips::OPCODE::JR64)
  .value("JRADDIUSP", LIEF::assembly::mips::OPCODE::JRADDIUSP)
  .value("JRC16_MM", LIEF::assembly::mips::OPCODE::JRC16_MM)
  .value("JRC16_MMR6", LIEF::assembly::mips::OPCODE::JRC16_MMR6)
  .value("JRCADDIUSP_MMR6", LIEF::assembly::mips::OPCODE::JRCADDIUSP_MMR6)
  .value("JR_HB", LIEF::assembly::mips::OPCODE::JR_HB)
  .value("JR_HB64", LIEF::assembly::mips::OPCODE::JR_HB64)
  .value("JR_HB64_R6", LIEF::assembly::mips::OPCODE::JR_HB64_R6)
  .value("JR_HB_R6", LIEF::assembly::mips::OPCODE::JR_HB_R6)
  .value("JR_MM", LIEF::assembly::mips::OPCODE::JR_MM)
  .value("J_MM", LIEF::assembly::mips::OPCODE::J_MM)
  .value("Jal16", LIEF::assembly::mips::OPCODE::Jal16)
  .value("JalB16", LIEF::assembly::mips::OPCODE::JalB16)
  .value("JrRa16", LIEF::assembly::mips::OPCODE::JrRa16)
  .value("JrcRa16", LIEF::assembly::mips::OPCODE::JrcRa16)
  .value("JrcRx16", LIEF::assembly::mips::OPCODE::JrcRx16)
  .value("JumpLinkReg16", LIEF::assembly::mips::OPCODE::JumpLinkReg16)
  .value("LB", LIEF::assembly::mips::OPCODE::LB)
  .value("LB64", LIEF::assembly::mips::OPCODE::LB64)
  .value("LBE", LIEF::assembly::mips::OPCODE::LBE)
  .value("LBE_MM", LIEF::assembly::mips::OPCODE::LBE_MM)
  .value("LBU16_MM", LIEF::assembly::mips::OPCODE::LBU16_MM)
  .value("LBUX", LIEF::assembly::mips::OPCODE::LBUX)
  .value("LBUX_MM", LIEF::assembly::mips::OPCODE::LBUX_MM)
  .value("LBU_MMR6", LIEF::assembly::mips::OPCODE::LBU_MMR6)
  .value("LB_MM", LIEF::assembly::mips::OPCODE::LB_MM)
  .value("LB_MMR6", LIEF::assembly::mips::OPCODE::LB_MMR6)
  .value("LBu", LIEF::assembly::mips::OPCODE::LBu)
  .value("LBu64", LIEF::assembly::mips::OPCODE::LBu64)
  .value("LBuE", LIEF::assembly::mips::OPCODE::LBuE)
  .value("LBuE_MM", LIEF::assembly::mips::OPCODE::LBuE_MM)
  .value("LBu_MM", LIEF::assembly::mips::OPCODE::LBu_MM)
  .value("LD", LIEF::assembly::mips::OPCODE::LD)
  .value("LDC1", LIEF::assembly::mips::OPCODE::LDC1)
  .value("LDC164", LIEF::assembly::mips::OPCODE::LDC164)
  .value("LDC1_D64_MMR6", LIEF::assembly::mips::OPCODE::LDC1_D64_MMR6)
  .value("LDC1_MM_D32", LIEF::assembly::mips::OPCODE::LDC1_MM_D32)
  .value("LDC1_MM_D64", LIEF::assembly::mips::OPCODE::LDC1_MM_D64)
  .value("LDC2", LIEF::assembly::mips::OPCODE::LDC2)
  .value("LDC2_MMR6", LIEF::assembly::mips::OPCODE::LDC2_MMR6)
  .value("LDC2_R6", LIEF::assembly::mips::OPCODE::LDC2_R6)
  .value("LDC3", LIEF::assembly::mips::OPCODE::LDC3)
  .value("LDI_B", LIEF::assembly::mips::OPCODE::LDI_B)
  .value("LDI_D", LIEF::assembly::mips::OPCODE::LDI_D)
  .value("LDI_H", LIEF::assembly::mips::OPCODE::LDI_H)
  .value("LDI_W", LIEF::assembly::mips::OPCODE::LDI_W)
  .value("LDL", LIEF::assembly::mips::OPCODE::LDL)
  .value("LDPC", LIEF::assembly::mips::OPCODE::LDPC)
  .value("LDR", LIEF::assembly::mips::OPCODE::LDR)
  .value("LDXC1", LIEF::assembly::mips::OPCODE::LDXC1)
  .value("LDXC164", LIEF::assembly::mips::OPCODE::LDXC164)
  .value("LD_B", LIEF::assembly::mips::OPCODE::LD_B)
  .value("LD_D", LIEF::assembly::mips::OPCODE::LD_D)
  .value("LD_H", LIEF::assembly::mips::OPCODE::LD_H)
  .value("LD_W", LIEF::assembly::mips::OPCODE::LD_W)
  .value("LEA_ADDiu", LIEF::assembly::mips::OPCODE::LEA_ADDiu)
  .value("LEA_ADDiu64", LIEF::assembly::mips::OPCODE::LEA_ADDiu64)
  .value("LEA_ADDiu_MM", LIEF::assembly::mips::OPCODE::LEA_ADDiu_MM)
  .value("LH", LIEF::assembly::mips::OPCODE::LH)
  .value("LH64", LIEF::assembly::mips::OPCODE::LH64)
  .value("LHE", LIEF::assembly::mips::OPCODE::LHE)
  .value("LHE_MM", LIEF::assembly::mips::OPCODE::LHE_MM)
  .value("LHU16_MM", LIEF::assembly::mips::OPCODE::LHU16_MM)
  .value("LHX", LIEF::assembly::mips::OPCODE::LHX)
  .value("LHX_MM", LIEF::assembly::mips::OPCODE::LHX_MM)
  .value("LH_MM", LIEF::assembly::mips::OPCODE::LH_MM)
  .value("LHu", LIEF::assembly::mips::OPCODE::LHu)
  .value("LHu64", LIEF::assembly::mips::OPCODE::LHu64)
  .value("LHuE", LIEF::assembly::mips::OPCODE::LHuE)
  .value("LHuE_MM", LIEF::assembly::mips::OPCODE::LHuE_MM)
  .value("LHu_MM", LIEF::assembly::mips::OPCODE::LHu_MM)
  .value("LI16_MM", LIEF::assembly::mips::OPCODE::LI16_MM)
  .value("LI16_MMR6", LIEF::assembly::mips::OPCODE::LI16_MMR6)
  .value("LL", LIEF::assembly::mips::OPCODE::LL)
  .value("LL64", LIEF::assembly::mips::OPCODE::LL64)
  .value("LL64_R6", LIEF::assembly::mips::OPCODE::LL64_R6)
  .value("LLD", LIEF::assembly::mips::OPCODE::LLD)
  .value("LLD_R6", LIEF::assembly::mips::OPCODE::LLD_R6)
  .value("LLE", LIEF::assembly::mips::OPCODE::LLE)
  .value("LLE_MM", LIEF::assembly::mips::OPCODE::LLE_MM)
  .value("LL_MM", LIEF::assembly::mips::OPCODE::LL_MM)
  .value("LL_MMR6", LIEF::assembly::mips::OPCODE::LL_MMR6)
  .value("LL_R6", LIEF::assembly::mips::OPCODE::LL_R6)
  .value("LSA", LIEF::assembly::mips::OPCODE::LSA)
  .value("LSA_MMR6", LIEF::assembly::mips::OPCODE::LSA_MMR6)
  .value("LSA_R6", LIEF::assembly::mips::OPCODE::LSA_R6)
  .value("LUI_MMR6", LIEF::assembly::mips::OPCODE::LUI_MMR6)
  .value("LUXC1", LIEF::assembly::mips::OPCODE::LUXC1)
  .value("LUXC164", LIEF::assembly::mips::OPCODE::LUXC164)
  .value("LUXC1_MM", LIEF::assembly::mips::OPCODE::LUXC1_MM)
  .value("LUi", LIEF::assembly::mips::OPCODE::LUi)
  .value("LUi64", LIEF::assembly::mips::OPCODE::LUi64)
  .value("LUi_MM", LIEF::assembly::mips::OPCODE::LUi_MM)
  .value("LW", LIEF::assembly::mips::OPCODE::LW)
  .value("LW16_MM", LIEF::assembly::mips::OPCODE::LW16_MM)
  .value("LW64", LIEF::assembly::mips::OPCODE::LW64)
  .value("LWC1", LIEF::assembly::mips::OPCODE::LWC1)
  .value("LWC1_MM", LIEF::assembly::mips::OPCODE::LWC1_MM)
  .value("LWC2", LIEF::assembly::mips::OPCODE::LWC2)
  .value("LWC2_MMR6", LIEF::assembly::mips::OPCODE::LWC2_MMR6)
  .value("LWC2_R6", LIEF::assembly::mips::OPCODE::LWC2_R6)
  .value("LWC3", LIEF::assembly::mips::OPCODE::LWC3)
  .value("LWDSP", LIEF::assembly::mips::OPCODE::LWDSP)
  .value("LWDSP_MM", LIEF::assembly::mips::OPCODE::LWDSP_MM)
  .value("LWE", LIEF::assembly::mips::OPCODE::LWE)
  .value("LWE_MM", LIEF::assembly::mips::OPCODE::LWE_MM)
  .value("LWGP_MM", LIEF::assembly::mips::OPCODE::LWGP_MM)
  .value("LWL", LIEF::assembly::mips::OPCODE::LWL)
  .value("LWL64", LIEF::assembly::mips::OPCODE::LWL64)
  .value("LWLE", LIEF::assembly::mips::OPCODE::LWLE)
  .value("LWLE_MM", LIEF::assembly::mips::OPCODE::LWLE_MM)
  .value("LWL_MM", LIEF::assembly::mips::OPCODE::LWL_MM)
  .value("LWM16_MM", LIEF::assembly::mips::OPCODE::LWM16_MM)
  .value("LWM16_MMR6", LIEF::assembly::mips::OPCODE::LWM16_MMR6)
  .value("LWM32_MM", LIEF::assembly::mips::OPCODE::LWM32_MM)
  .value("LWPC", LIEF::assembly::mips::OPCODE::LWPC)
  .value("LWPC_MMR6", LIEF::assembly::mips::OPCODE::LWPC_MMR6)
  .value("LWP_MM", LIEF::assembly::mips::OPCODE::LWP_MM)
  .value("LWR", LIEF::assembly::mips::OPCODE::LWR)
  .value("LWR64", LIEF::assembly::mips::OPCODE::LWR64)
  .value("LWRE", LIEF::assembly::mips::OPCODE::LWRE)
  .value("LWRE_MM", LIEF::assembly::mips::OPCODE::LWRE_MM)
  .value("LWR_MM", LIEF::assembly::mips::OPCODE::LWR_MM)
  .value("LWSP_MM", LIEF::assembly::mips::OPCODE::LWSP_MM)
  .value("LWUPC", LIEF::assembly::mips::OPCODE::LWUPC)
  .value("LWU_MM", LIEF::assembly::mips::OPCODE::LWU_MM)
  .value("LWX", LIEF::assembly::mips::OPCODE::LWX)
  .value("LWXC1", LIEF::assembly::mips::OPCODE::LWXC1)
  .value("LWXC1_MM", LIEF::assembly::mips::OPCODE::LWXC1_MM)
  .value("LWXS_MM", LIEF::assembly::mips::OPCODE::LWXS_MM)
  .value("LWX_MM", LIEF::assembly::mips::OPCODE::LWX_MM)
  .value("LW_MM", LIEF::assembly::mips::OPCODE::LW_MM)
  .value("LW_MMR6", LIEF::assembly::mips::OPCODE::LW_MMR6)
  .value("LWu", LIEF::assembly::mips::OPCODE::LWu)
  .value("LbRxRyOffMemX16", LIEF::assembly::mips::OPCODE::LbRxRyOffMemX16)
  .value("LbuRxRyOffMemX16", LIEF::assembly::mips::OPCODE::LbuRxRyOffMemX16)
  .value("LhRxRyOffMemX16", LIEF::assembly::mips::OPCODE::LhRxRyOffMemX16)
  .value("LhuRxRyOffMemX16", LIEF::assembly::mips::OPCODE::LhuRxRyOffMemX16)
  .value("LiRxImm16", LIEF::assembly::mips::OPCODE::LiRxImm16)
  .value("LiRxImmAlignX16", LIEF::assembly::mips::OPCODE::LiRxImmAlignX16)
  .value("LiRxImmX16", LIEF::assembly::mips::OPCODE::LiRxImmX16)
  .value("LwRxPcTcp16", LIEF::assembly::mips::OPCODE::LwRxPcTcp16)
  .value("LwRxPcTcpX16", LIEF::assembly::mips::OPCODE::LwRxPcTcpX16)
  .value("LwRxRyOffMemX16", LIEF::assembly::mips::OPCODE::LwRxRyOffMemX16)
  .value("LwRxSpImmX16", LIEF::assembly::mips::OPCODE::LwRxSpImmX16)
  .value("MADD", LIEF::assembly::mips::OPCODE::MADD)
  .value("MADDF_D", LIEF::assembly::mips::OPCODE::MADDF_D)
  .value("MADDF_D_MMR6", LIEF::assembly::mips::OPCODE::MADDF_D_MMR6)
  .value("MADDF_S", LIEF::assembly::mips::OPCODE::MADDF_S)
  .value("MADDF_S_MMR6", LIEF::assembly::mips::OPCODE::MADDF_S_MMR6)
  .value("MADDR_Q_H", LIEF::assembly::mips::OPCODE::MADDR_Q_H)
  .value("MADDR_Q_W", LIEF::assembly::mips::OPCODE::MADDR_Q_W)
  .value("MADDU", LIEF::assembly::mips::OPCODE::MADDU)
  .value("MADDU_DSP", LIEF::assembly::mips::OPCODE::MADDU_DSP)
  .value("MADDU_DSP_MM", LIEF::assembly::mips::OPCODE::MADDU_DSP_MM)
  .value("MADDU_MM", LIEF::assembly::mips::OPCODE::MADDU_MM)
  .value("MADDV_B", LIEF::assembly::mips::OPCODE::MADDV_B)
  .value("MADDV_D", LIEF::assembly::mips::OPCODE::MADDV_D)
  .value("MADDV_H", LIEF::assembly::mips::OPCODE::MADDV_H)
  .value("MADDV_W", LIEF::assembly::mips::OPCODE::MADDV_W)
  .value("MADD_D32", LIEF::assembly::mips::OPCODE::MADD_D32)
  .value("MADD_D32_MM", LIEF::assembly::mips::OPCODE::MADD_D32_MM)
  .value("MADD_D64", LIEF::assembly::mips::OPCODE::MADD_D64)
  .value("MADD_DSP", LIEF::assembly::mips::OPCODE::MADD_DSP)
  .value("MADD_DSP_MM", LIEF::assembly::mips::OPCODE::MADD_DSP_MM)
  .value("MADD_MM", LIEF::assembly::mips::OPCODE::MADD_MM)
  .value("MADD_Q_H", LIEF::assembly::mips::OPCODE::MADD_Q_H)
  .value("MADD_Q_W", LIEF::assembly::mips::OPCODE::MADD_Q_W)
  .value("MADD_S", LIEF::assembly::mips::OPCODE::MADD_S)
  .value("MADD_S_MM", LIEF::assembly::mips::OPCODE::MADD_S_MM)
  .value("MAQ_SA_W_PHL", LIEF::assembly::mips::OPCODE::MAQ_SA_W_PHL)
  .value("MAQ_SA_W_PHL_MM", LIEF::assembly::mips::OPCODE::MAQ_SA_W_PHL_MM)
  .value("MAQ_SA_W_PHR", LIEF::assembly::mips::OPCODE::MAQ_SA_W_PHR)
  .value("MAQ_SA_W_PHR_MM", LIEF::assembly::mips::OPCODE::MAQ_SA_W_PHR_MM)
  .value("MAQ_S_W_PHL", LIEF::assembly::mips::OPCODE::MAQ_S_W_PHL)
  .value("MAQ_S_W_PHL_MM", LIEF::assembly::mips::OPCODE::MAQ_S_W_PHL_MM)
  .value("MAQ_S_W_PHR", LIEF::assembly::mips::OPCODE::MAQ_S_W_PHR)
  .value("MAQ_S_W_PHR_MM", LIEF::assembly::mips::OPCODE::MAQ_S_W_PHR_MM)
  .value("MAXA_D", LIEF::assembly::mips::OPCODE::MAXA_D)
  .value("MAXA_D_MMR6", LIEF::assembly::mips::OPCODE::MAXA_D_MMR6)
  .value("MAXA_S", LIEF::assembly::mips::OPCODE::MAXA_S)
  .value("MAXA_S_MMR6", LIEF::assembly::mips::OPCODE::MAXA_S_MMR6)
  .value("MAXI_S_B", LIEF::assembly::mips::OPCODE::MAXI_S_B)
  .value("MAXI_S_D", LIEF::assembly::mips::OPCODE::MAXI_S_D)
  .value("MAXI_S_H", LIEF::assembly::mips::OPCODE::MAXI_S_H)
  .value("MAXI_S_W", LIEF::assembly::mips::OPCODE::MAXI_S_W)
  .value("MAXI_U_B", LIEF::assembly::mips::OPCODE::MAXI_U_B)
  .value("MAXI_U_D", LIEF::assembly::mips::OPCODE::MAXI_U_D)
  .value("MAXI_U_H", LIEF::assembly::mips::OPCODE::MAXI_U_H)
  .value("MAXI_U_W", LIEF::assembly::mips::OPCODE::MAXI_U_W)
  .value("MAX_A_B", LIEF::assembly::mips::OPCODE::MAX_A_B)
  .value("MAX_A_D", LIEF::assembly::mips::OPCODE::MAX_A_D)
  .value("MAX_A_H", LIEF::assembly::mips::OPCODE::MAX_A_H)
  .value("MAX_A_W", LIEF::assembly::mips::OPCODE::MAX_A_W)
  .value("MAX_D", LIEF::assembly::mips::OPCODE::MAX_D)
  .value("MAX_D_MMR6", LIEF::assembly::mips::OPCODE::MAX_D_MMR6)
  .value("MAX_S", LIEF::assembly::mips::OPCODE::MAX_S)
  .value("MAX_S_B", LIEF::assembly::mips::OPCODE::MAX_S_B)
  .value("MAX_S_D", LIEF::assembly::mips::OPCODE::MAX_S_D)
  .value("MAX_S_H", LIEF::assembly::mips::OPCODE::MAX_S_H)
  .value("MAX_S_MMR6", LIEF::assembly::mips::OPCODE::MAX_S_MMR6)
  .value("MAX_S_W", LIEF::assembly::mips::OPCODE::MAX_S_W)
  .value("MAX_U_B", LIEF::assembly::mips::OPCODE::MAX_U_B)
  .value("MAX_U_D", LIEF::assembly::mips::OPCODE::MAX_U_D)
  .value("MAX_U_H", LIEF::assembly::mips::OPCODE::MAX_U_H)
  .value("MAX_U_W", LIEF::assembly::mips::OPCODE::MAX_U_W)
  .value("MFC0", LIEF::assembly::mips::OPCODE::MFC0)
  .value("MFC0_MMR6", LIEF::assembly::mips::OPCODE::MFC0_MMR6)
  .value("MFC1", LIEF::assembly::mips::OPCODE::MFC1)
  .value("MFC1_D64", LIEF::assembly::mips::OPCODE::MFC1_D64)
  .value("MFC1_MM", LIEF::assembly::mips::OPCODE::MFC1_MM)
  .value("MFC1_MMR6", LIEF::assembly::mips::OPCODE::MFC1_MMR6)
  .value("MFC2", LIEF::assembly::mips::OPCODE::MFC2)
  .value("MFC2_MMR6", LIEF::assembly::mips::OPCODE::MFC2_MMR6)
  .value("MFGC0", LIEF::assembly::mips::OPCODE::MFGC0)
  .value("MFGC0_MM", LIEF::assembly::mips::OPCODE::MFGC0_MM)
  .value("MFHC0_MMR6", LIEF::assembly::mips::OPCODE::MFHC0_MMR6)
  .value("MFHC1_D32", LIEF::assembly::mips::OPCODE::MFHC1_D32)
  .value("MFHC1_D32_MM", LIEF::assembly::mips::OPCODE::MFHC1_D32_MM)
  .value("MFHC1_D64", LIEF::assembly::mips::OPCODE::MFHC1_D64)
  .value("MFHC1_D64_MM", LIEF::assembly::mips::OPCODE::MFHC1_D64_MM)
  .value("MFHC2_MMR6", LIEF::assembly::mips::OPCODE::MFHC2_MMR6)
  .value("MFHGC0", LIEF::assembly::mips::OPCODE::MFHGC0)
  .value("MFHGC0_MM", LIEF::assembly::mips::OPCODE::MFHGC0_MM)
  .value("MFHI", LIEF::assembly::mips::OPCODE::MFHI)
  .value("MFHI16_MM", LIEF::assembly::mips::OPCODE::MFHI16_MM)
  .value("MFHI64", LIEF::assembly::mips::OPCODE::MFHI64)
  .value("MFHI_DSP", LIEF::assembly::mips::OPCODE::MFHI_DSP)
  .value("MFHI_DSP_MM", LIEF::assembly::mips::OPCODE::MFHI_DSP_MM)
  .value("MFHI_MM", LIEF::assembly::mips::OPCODE::MFHI_MM)
  .value("MFLO", LIEF::assembly::mips::OPCODE::MFLO)
  .value("MFLO16_MM", LIEF::assembly::mips::OPCODE::MFLO16_MM)
  .value("MFLO64", LIEF::assembly::mips::OPCODE::MFLO64)
  .value("MFLO_DSP", LIEF::assembly::mips::OPCODE::MFLO_DSP)
  .value("MFLO_DSP_MM", LIEF::assembly::mips::OPCODE::MFLO_DSP_MM)
  .value("MFLO_MM", LIEF::assembly::mips::OPCODE::MFLO_MM)
  .value("MFTR", LIEF::assembly::mips::OPCODE::MFTR)
  .value("MINA_D", LIEF::assembly::mips::OPCODE::MINA_D)
  .value("MINA_D_MMR6", LIEF::assembly::mips::OPCODE::MINA_D_MMR6)
  .value("MINA_S", LIEF::assembly::mips::OPCODE::MINA_S)
  .value("MINA_S_MMR6", LIEF::assembly::mips::OPCODE::MINA_S_MMR6)
  .value("MINI_S_B", LIEF::assembly::mips::OPCODE::MINI_S_B)
  .value("MINI_S_D", LIEF::assembly::mips::OPCODE::MINI_S_D)
  .value("MINI_S_H", LIEF::assembly::mips::OPCODE::MINI_S_H)
  .value("MINI_S_W", LIEF::assembly::mips::OPCODE::MINI_S_W)
  .value("MINI_U_B", LIEF::assembly::mips::OPCODE::MINI_U_B)
  .value("MINI_U_D", LIEF::assembly::mips::OPCODE::MINI_U_D);
  opcodes.value("MINI_U_H", LIEF::assembly::mips::OPCODE::MINI_U_H)
  .value("MINI_U_W", LIEF::assembly::mips::OPCODE::MINI_U_W)
  .value("MIN_A_B", LIEF::assembly::mips::OPCODE::MIN_A_B)
  .value("MIN_A_D", LIEF::assembly::mips::OPCODE::MIN_A_D)
  .value("MIN_A_H", LIEF::assembly::mips::OPCODE::MIN_A_H)
  .value("MIN_A_W", LIEF::assembly::mips::OPCODE::MIN_A_W)
  .value("MIN_D", LIEF::assembly::mips::OPCODE::MIN_D)
  .value("MIN_D_MMR6", LIEF::assembly::mips::OPCODE::MIN_D_MMR6)
  .value("MIN_S", LIEF::assembly::mips::OPCODE::MIN_S)
  .value("MIN_S_B", LIEF::assembly::mips::OPCODE::MIN_S_B)
  .value("MIN_S_D", LIEF::assembly::mips::OPCODE::MIN_S_D)
  .value("MIN_S_H", LIEF::assembly::mips::OPCODE::MIN_S_H)
  .value("MIN_S_MMR6", LIEF::assembly::mips::OPCODE::MIN_S_MMR6)
  .value("MIN_S_W", LIEF::assembly::mips::OPCODE::MIN_S_W)
  .value("MIN_U_B", LIEF::assembly::mips::OPCODE::MIN_U_B)
  .value("MIN_U_D", LIEF::assembly::mips::OPCODE::MIN_U_D)
  .value("MIN_U_H", LIEF::assembly::mips::OPCODE::MIN_U_H)
  .value("MIN_U_W", LIEF::assembly::mips::OPCODE::MIN_U_W)
  .value("MOD", LIEF::assembly::mips::OPCODE::MOD)
  .value("MODSUB", LIEF::assembly::mips::OPCODE::MODSUB)
  .value("MODSUB_MM", LIEF::assembly::mips::OPCODE::MODSUB_MM)
  .value("MODU", LIEF::assembly::mips::OPCODE::MODU)
  .value("MODU_MMR6", LIEF::assembly::mips::OPCODE::MODU_MMR6)
  .value("MOD_MMR6", LIEF::assembly::mips::OPCODE::MOD_MMR6)
  .value("MOD_S_B", LIEF::assembly::mips::OPCODE::MOD_S_B)
  .value("MOD_S_D", LIEF::assembly::mips::OPCODE::MOD_S_D)
  .value("MOD_S_H", LIEF::assembly::mips::OPCODE::MOD_S_H)
  .value("MOD_S_W", LIEF::assembly::mips::OPCODE::MOD_S_W)
  .value("MOD_U_B", LIEF::assembly::mips::OPCODE::MOD_U_B)
  .value("MOD_U_D", LIEF::assembly::mips::OPCODE::MOD_U_D)
  .value("MOD_U_H", LIEF::assembly::mips::OPCODE::MOD_U_H)
  .value("MOD_U_W", LIEF::assembly::mips::OPCODE::MOD_U_W)
  .value("MOVE16_MM", LIEF::assembly::mips::OPCODE::MOVE16_MM)
  .value("MOVE16_MMR6", LIEF::assembly::mips::OPCODE::MOVE16_MMR6)
  .value("MOVEP_MM", LIEF::assembly::mips::OPCODE::MOVEP_MM)
  .value("MOVEP_MMR6", LIEF::assembly::mips::OPCODE::MOVEP_MMR6)
  .value("MOVE_V", LIEF::assembly::mips::OPCODE::MOVE_V)
  .value("MOVF_D32", LIEF::assembly::mips::OPCODE::MOVF_D32)
  .value("MOVF_D32_MM", LIEF::assembly::mips::OPCODE::MOVF_D32_MM)
  .value("MOVF_D64", LIEF::assembly::mips::OPCODE::MOVF_D64)
  .value("MOVF_I", LIEF::assembly::mips::OPCODE::MOVF_I)
  .value("MOVF_I64", LIEF::assembly::mips::OPCODE::MOVF_I64)
  .value("MOVF_I_MM", LIEF::assembly::mips::OPCODE::MOVF_I_MM)
  .value("MOVF_S", LIEF::assembly::mips::OPCODE::MOVF_S)
  .value("MOVF_S_MM", LIEF::assembly::mips::OPCODE::MOVF_S_MM)
  .value("MOVN_I64_D64", LIEF::assembly::mips::OPCODE::MOVN_I64_D64)
  .value("MOVN_I64_I", LIEF::assembly::mips::OPCODE::MOVN_I64_I)
  .value("MOVN_I64_I64", LIEF::assembly::mips::OPCODE::MOVN_I64_I64)
  .value("MOVN_I64_S", LIEF::assembly::mips::OPCODE::MOVN_I64_S)
  .value("MOVN_I_D32", LIEF::assembly::mips::OPCODE::MOVN_I_D32)
  .value("MOVN_I_D32_MM", LIEF::assembly::mips::OPCODE::MOVN_I_D32_MM)
  .value("MOVN_I_D64", LIEF::assembly::mips::OPCODE::MOVN_I_D64)
  .value("MOVN_I_I", LIEF::assembly::mips::OPCODE::MOVN_I_I)
  .value("MOVN_I_I64", LIEF::assembly::mips::OPCODE::MOVN_I_I64)
  .value("MOVN_I_MM", LIEF::assembly::mips::OPCODE::MOVN_I_MM)
  .value("MOVN_I_S", LIEF::assembly::mips::OPCODE::MOVN_I_S)
  .value("MOVN_I_S_MM", LIEF::assembly::mips::OPCODE::MOVN_I_S_MM)
  .value("MOVT_D32", LIEF::assembly::mips::OPCODE::MOVT_D32)
  .value("MOVT_D32_MM", LIEF::assembly::mips::OPCODE::MOVT_D32_MM)
  .value("MOVT_D64", LIEF::assembly::mips::OPCODE::MOVT_D64)
  .value("MOVT_I", LIEF::assembly::mips::OPCODE::MOVT_I)
  .value("MOVT_I64", LIEF::assembly::mips::OPCODE::MOVT_I64)
  .value("MOVT_I_MM", LIEF::assembly::mips::OPCODE::MOVT_I_MM)
  .value("MOVT_S", LIEF::assembly::mips::OPCODE::MOVT_S)
  .value("MOVT_S_MM", LIEF::assembly::mips::OPCODE::MOVT_S_MM)
  .value("MOVZ_I64_D64", LIEF::assembly::mips::OPCODE::MOVZ_I64_D64)
  .value("MOVZ_I64_I", LIEF::assembly::mips::OPCODE::MOVZ_I64_I)
  .value("MOVZ_I64_I64", LIEF::assembly::mips::OPCODE::MOVZ_I64_I64)
  .value("MOVZ_I64_S", LIEF::assembly::mips::OPCODE::MOVZ_I64_S)
  .value("MOVZ_I_D32", LIEF::assembly::mips::OPCODE::MOVZ_I_D32)
  .value("MOVZ_I_D32_MM", LIEF::assembly::mips::OPCODE::MOVZ_I_D32_MM)
  .value("MOVZ_I_D64", LIEF::assembly::mips::OPCODE::MOVZ_I_D64)
  .value("MOVZ_I_I", LIEF::assembly::mips::OPCODE::MOVZ_I_I)
  .value("MOVZ_I_I64", LIEF::assembly::mips::OPCODE::MOVZ_I_I64)
  .value("MOVZ_I_MM", LIEF::assembly::mips::OPCODE::MOVZ_I_MM)
  .value("MOVZ_I_S", LIEF::assembly::mips::OPCODE::MOVZ_I_S)
  .value("MOVZ_I_S_MM", LIEF::assembly::mips::OPCODE::MOVZ_I_S_MM)
  .value("MSUB", LIEF::assembly::mips::OPCODE::MSUB)
  .value("MSUBF_D", LIEF::assembly::mips::OPCODE::MSUBF_D)
  .value("MSUBF_D_MMR6", LIEF::assembly::mips::OPCODE::MSUBF_D_MMR6)
  .value("MSUBF_S", LIEF::assembly::mips::OPCODE::MSUBF_S)
  .value("MSUBF_S_MMR6", LIEF::assembly::mips::OPCODE::MSUBF_S_MMR6)
  .value("MSUBR_Q_H", LIEF::assembly::mips::OPCODE::MSUBR_Q_H)
  .value("MSUBR_Q_W", LIEF::assembly::mips::OPCODE::MSUBR_Q_W)
  .value("MSUBU", LIEF::assembly::mips::OPCODE::MSUBU)
  .value("MSUBU_DSP", LIEF::assembly::mips::OPCODE::MSUBU_DSP)
  .value("MSUBU_DSP_MM", LIEF::assembly::mips::OPCODE::MSUBU_DSP_MM)
  .value("MSUBU_MM", LIEF::assembly::mips::OPCODE::MSUBU_MM)
  .value("MSUBV_B", LIEF::assembly::mips::OPCODE::MSUBV_B)
  .value("MSUBV_D", LIEF::assembly::mips::OPCODE::MSUBV_D)
  .value("MSUBV_H", LIEF::assembly::mips::OPCODE::MSUBV_H)
  .value("MSUBV_W", LIEF::assembly::mips::OPCODE::MSUBV_W)
  .value("MSUB_D32", LIEF::assembly::mips::OPCODE::MSUB_D32)
  .value("MSUB_D32_MM", LIEF::assembly::mips::OPCODE::MSUB_D32_MM)
  .value("MSUB_D64", LIEF::assembly::mips::OPCODE::MSUB_D64)
  .value("MSUB_DSP", LIEF::assembly::mips::OPCODE::MSUB_DSP)
  .value("MSUB_DSP_MM", LIEF::assembly::mips::OPCODE::MSUB_DSP_MM)
  .value("MSUB_MM", LIEF::assembly::mips::OPCODE::MSUB_MM)
  .value("MSUB_Q_H", LIEF::assembly::mips::OPCODE::MSUB_Q_H)
  .value("MSUB_Q_W", LIEF::assembly::mips::OPCODE::MSUB_Q_W)
  .value("MSUB_S", LIEF::assembly::mips::OPCODE::MSUB_S)
  .value("MSUB_S_MM", LIEF::assembly::mips::OPCODE::MSUB_S_MM)
  .value("MTC0", LIEF::assembly::mips::OPCODE::MTC0)
  .value("MTC0_MMR6", LIEF::assembly::mips::OPCODE::MTC0_MMR6)
  .value("MTC1", LIEF::assembly::mips::OPCODE::MTC1)
  .value("MTC1_D64", LIEF::assembly::mips::OPCODE::MTC1_D64)
  .value("MTC1_D64_MM", LIEF::assembly::mips::OPCODE::MTC1_D64_MM)
  .value("MTC1_MM", LIEF::assembly::mips::OPCODE::MTC1_MM)
  .value("MTC1_MMR6", LIEF::assembly::mips::OPCODE::MTC1_MMR6)
  .value("MTC2", LIEF::assembly::mips::OPCODE::MTC2)
  .value("MTC2_MMR6", LIEF::assembly::mips::OPCODE::MTC2_MMR6)
  .value("MTGC0", LIEF::assembly::mips::OPCODE::MTGC0)
  .value("MTGC0_MM", LIEF::assembly::mips::OPCODE::MTGC0_MM)
  .value("MTHC0_MMR6", LIEF::assembly::mips::OPCODE::MTHC0_MMR6)
  .value("MTHC1_D32", LIEF::assembly::mips::OPCODE::MTHC1_D32)
  .value("MTHC1_D32_MM", LIEF::assembly::mips::OPCODE::MTHC1_D32_MM)
  .value("MTHC1_D64", LIEF::assembly::mips::OPCODE::MTHC1_D64)
  .value("MTHC1_D64_MM", LIEF::assembly::mips::OPCODE::MTHC1_D64_MM)
  .value("MTHC2_MMR6", LIEF::assembly::mips::OPCODE::MTHC2_MMR6)
  .value("MTHGC0", LIEF::assembly::mips::OPCODE::MTHGC0)
  .value("MTHGC0_MM", LIEF::assembly::mips::OPCODE::MTHGC0_MM)
  .value("MTHI", LIEF::assembly::mips::OPCODE::MTHI)
  .value("MTHI64", LIEF::assembly::mips::OPCODE::MTHI64)
  .value("MTHI_DSP", LIEF::assembly::mips::OPCODE::MTHI_DSP)
  .value("MTHI_DSP_MM", LIEF::assembly::mips::OPCODE::MTHI_DSP_MM)
  .value("MTHI_MM", LIEF::assembly::mips::OPCODE::MTHI_MM)
  .value("MTHLIP", LIEF::assembly::mips::OPCODE::MTHLIP)
  .value("MTHLIP_MM", LIEF::assembly::mips::OPCODE::MTHLIP_MM)
  .value("MTLO", LIEF::assembly::mips::OPCODE::MTLO)
  .value("MTLO64", LIEF::assembly::mips::OPCODE::MTLO64)
  .value("MTLO_DSP", LIEF::assembly::mips::OPCODE::MTLO_DSP)
  .value("MTLO_DSP_MM", LIEF::assembly::mips::OPCODE::MTLO_DSP_MM)
  .value("MTLO_MM", LIEF::assembly::mips::OPCODE::MTLO_MM)
  .value("MTM0", LIEF::assembly::mips::OPCODE::MTM0)
  .value("MTM1", LIEF::assembly::mips::OPCODE::MTM1)
  .value("MTM2", LIEF::assembly::mips::OPCODE::MTM2)
  .value("MTP0", LIEF::assembly::mips::OPCODE::MTP0)
  .value("MTP1", LIEF::assembly::mips::OPCODE::MTP1)
  .value("MTP2", LIEF::assembly::mips::OPCODE::MTP2)
  .value("MTTR", LIEF::assembly::mips::OPCODE::MTTR)
  .value("MUH", LIEF::assembly::mips::OPCODE::MUH)
  .value("MUHU", LIEF::assembly::mips::OPCODE::MUHU)
  .value("MUHU_MMR6", LIEF::assembly::mips::OPCODE::MUHU_MMR6)
  .value("MUH_MMR6", LIEF::assembly::mips::OPCODE::MUH_MMR6)
  .value("MUL", LIEF::assembly::mips::OPCODE::MUL)
  .value("MULEQ_S_W_PHL", LIEF::assembly::mips::OPCODE::MULEQ_S_W_PHL)
  .value("MULEQ_S_W_PHL_MM", LIEF::assembly::mips::OPCODE::MULEQ_S_W_PHL_MM)
  .value("MULEQ_S_W_PHR", LIEF::assembly::mips::OPCODE::MULEQ_S_W_PHR)
  .value("MULEQ_S_W_PHR_MM", LIEF::assembly::mips::OPCODE::MULEQ_S_W_PHR_MM)
  .value("MULEU_S_PH_QBL", LIEF::assembly::mips::OPCODE::MULEU_S_PH_QBL)
  .value("MULEU_S_PH_QBL_MM", LIEF::assembly::mips::OPCODE::MULEU_S_PH_QBL_MM)
  .value("MULEU_S_PH_QBR", LIEF::assembly::mips::OPCODE::MULEU_S_PH_QBR)
  .value("MULEU_S_PH_QBR_MM", LIEF::assembly::mips::OPCODE::MULEU_S_PH_QBR_MM)
  .value("MULQ_RS_PH", LIEF::assembly::mips::OPCODE::MULQ_RS_PH)
  .value("MULQ_RS_PH_MM", LIEF::assembly::mips::OPCODE::MULQ_RS_PH_MM)
  .value("MULQ_RS_W", LIEF::assembly::mips::OPCODE::MULQ_RS_W)
  .value("MULQ_RS_W_MMR2", LIEF::assembly::mips::OPCODE::MULQ_RS_W_MMR2)
  .value("MULQ_S_PH", LIEF::assembly::mips::OPCODE::MULQ_S_PH)
  .value("MULQ_S_PH_MMR2", LIEF::assembly::mips::OPCODE::MULQ_S_PH_MMR2)
  .value("MULQ_S_W", LIEF::assembly::mips::OPCODE::MULQ_S_W)
  .value("MULQ_S_W_MMR2", LIEF::assembly::mips::OPCODE::MULQ_S_W_MMR2)
  .value("MULR_PS64", LIEF::assembly::mips::OPCODE::MULR_PS64)
  .value("MULR_Q_H", LIEF::assembly::mips::OPCODE::MULR_Q_H)
  .value("MULR_Q_W", LIEF::assembly::mips::OPCODE::MULR_Q_W)
  .value("MULSAQ_S_W_PH", LIEF::assembly::mips::OPCODE::MULSAQ_S_W_PH)
  .value("MULSAQ_S_W_PH_MM", LIEF::assembly::mips::OPCODE::MULSAQ_S_W_PH_MM)
  .value("MULSA_W_PH", LIEF::assembly::mips::OPCODE::MULSA_W_PH)
  .value("MULSA_W_PH_MMR2", LIEF::assembly::mips::OPCODE::MULSA_W_PH_MMR2)
  .value("MULT", LIEF::assembly::mips::OPCODE::MULT)
  .value("MULTU_DSP", LIEF::assembly::mips::OPCODE::MULTU_DSP)
  .value("MULTU_DSP_MM", LIEF::assembly::mips::OPCODE::MULTU_DSP_MM)
  .value("MULT_DSP", LIEF::assembly::mips::OPCODE::MULT_DSP)
  .value("MULT_DSP_MM", LIEF::assembly::mips::OPCODE::MULT_DSP_MM)
  .value("MULT_MM", LIEF::assembly::mips::OPCODE::MULT_MM)
  .value("MULTu", LIEF::assembly::mips::OPCODE::MULTu)
  .value("MULTu_MM", LIEF::assembly::mips::OPCODE::MULTu_MM)
  .value("MULU", LIEF::assembly::mips::OPCODE::MULU)
  .value("MULU_MMR6", LIEF::assembly::mips::OPCODE::MULU_MMR6)
  .value("MULV_B", LIEF::assembly::mips::OPCODE::MULV_B)
  .value("MULV_D", LIEF::assembly::mips::OPCODE::MULV_D)
  .value("MULV_H", LIEF::assembly::mips::OPCODE::MULV_H)
  .value("MULV_W", LIEF::assembly::mips::OPCODE::MULV_W)
  .value("MUL_MM", LIEF::assembly::mips::OPCODE::MUL_MM)
  .value("MUL_MMR6", LIEF::assembly::mips::OPCODE::MUL_MMR6)
  .value("MUL_PH", LIEF::assembly::mips::OPCODE::MUL_PH)
  .value("MUL_PH_MMR2", LIEF::assembly::mips::OPCODE::MUL_PH_MMR2)
  .value("MUL_Q_H", LIEF::assembly::mips::OPCODE::MUL_Q_H)
  .value("MUL_Q_W", LIEF::assembly::mips::OPCODE::MUL_Q_W)
  .value("MUL_R6", LIEF::assembly::mips::OPCODE::MUL_R6)
  .value("MUL_S_PH", LIEF::assembly::mips::OPCODE::MUL_S_PH)
  .value("MUL_S_PH_MMR2", LIEF::assembly::mips::OPCODE::MUL_S_PH_MMR2)
  .value("Mfhi16", LIEF::assembly::mips::OPCODE::Mfhi16)
  .value("Mflo16", LIEF::assembly::mips::OPCODE::Mflo16)
  .value("Move32R16", LIEF::assembly::mips::OPCODE::Move32R16)
  .value("MoveR3216", LIEF::assembly::mips::OPCODE::MoveR3216)
  .value("NAL", LIEF::assembly::mips::OPCODE::NAL)
  .value("NLOC_B", LIEF::assembly::mips::OPCODE::NLOC_B)
  .value("NLOC_D", LIEF::assembly::mips::OPCODE::NLOC_D)
  .value("NLOC_H", LIEF::assembly::mips::OPCODE::NLOC_H)
  .value("NLOC_W", LIEF::assembly::mips::OPCODE::NLOC_W)
  .value("NLZC_B", LIEF::assembly::mips::OPCODE::NLZC_B)
  .value("NLZC_D", LIEF::assembly::mips::OPCODE::NLZC_D)
  .value("NLZC_H", LIEF::assembly::mips::OPCODE::NLZC_H)
  .value("NLZC_W", LIEF::assembly::mips::OPCODE::NLZC_W)
  .value("NMADD_D32", LIEF::assembly::mips::OPCODE::NMADD_D32)
  .value("NMADD_D32_MM", LIEF::assembly::mips::OPCODE::NMADD_D32_MM)
  .value("NMADD_D64", LIEF::assembly::mips::OPCODE::NMADD_D64)
  .value("NMADD_S", LIEF::assembly::mips::OPCODE::NMADD_S)
  .value("NMADD_S_MM", LIEF::assembly::mips::OPCODE::NMADD_S_MM)
  .value("NMSUB_D32", LIEF::assembly::mips::OPCODE::NMSUB_D32)
  .value("NMSUB_D32_MM", LIEF::assembly::mips::OPCODE::NMSUB_D32_MM)
  .value("NMSUB_D64", LIEF::assembly::mips::OPCODE::NMSUB_D64)
  .value("NMSUB_S", LIEF::assembly::mips::OPCODE::NMSUB_S)
  .value("NMSUB_S_MM", LIEF::assembly::mips::OPCODE::NMSUB_S_MM)
  .value("NOR", LIEF::assembly::mips::OPCODE::NOR)
  .value("NOR64", LIEF::assembly::mips::OPCODE::NOR64)
  .value("NORI_B", LIEF::assembly::mips::OPCODE::NORI_B)
  .value("NOR_MM", LIEF::assembly::mips::OPCODE::NOR_MM)
  .value("NOR_MMR6", LIEF::assembly::mips::OPCODE::NOR_MMR6)
  .value("NOR_V", LIEF::assembly::mips::OPCODE::NOR_V)
  .value("NOT16_MM", LIEF::assembly::mips::OPCODE::NOT16_MM)
  .value("NOT16_MMR6", LIEF::assembly::mips::OPCODE::NOT16_MMR6)
  .value("NegRxRy16", LIEF::assembly::mips::OPCODE::NegRxRy16)
  .value("NotRxRy16", LIEF::assembly::mips::OPCODE::NotRxRy16)
  .value("OR", LIEF::assembly::mips::OPCODE::OR)
  .value("OR16_MM", LIEF::assembly::mips::OPCODE::OR16_MM)
  .value("OR16_MMR6", LIEF::assembly::mips::OPCODE::OR16_MMR6)
  .value("OR64", LIEF::assembly::mips::OPCODE::OR64)
  .value("ORI_B", LIEF::assembly::mips::OPCODE::ORI_B)
  .value("ORI_MMR6", LIEF::assembly::mips::OPCODE::ORI_MMR6)
  .value("OR_MM", LIEF::assembly::mips::OPCODE::OR_MM)
  .value("OR_MMR6", LIEF::assembly::mips::OPCODE::OR_MMR6)
  .value("OR_V", LIEF::assembly::mips::OPCODE::OR_V)
  .value("ORi", LIEF::assembly::mips::OPCODE::ORi)
  .value("ORi64", LIEF::assembly::mips::OPCODE::ORi64)
  .value("ORi_MM", LIEF::assembly::mips::OPCODE::ORi_MM)
  .value("OrRxRxRy16", LIEF::assembly::mips::OPCODE::OrRxRxRy16)
  .value("PACKRL_PH", LIEF::assembly::mips::OPCODE::PACKRL_PH)
  .value("PACKRL_PH_MM", LIEF::assembly::mips::OPCODE::PACKRL_PH_MM)
  .value("PAUSE", LIEF::assembly::mips::OPCODE::PAUSE)
  .value("PAUSE_MM", LIEF::assembly::mips::OPCODE::PAUSE_MM)
  .value("PAUSE_MMR6", LIEF::assembly::mips::OPCODE::PAUSE_MMR6)
  .value("PCKEV_B", LIEF::assembly::mips::OPCODE::PCKEV_B)
  .value("PCKEV_D", LIEF::assembly::mips::OPCODE::PCKEV_D)
  .value("PCKEV_H", LIEF::assembly::mips::OPCODE::PCKEV_H)
  .value("PCKEV_W", LIEF::assembly::mips::OPCODE::PCKEV_W)
  .value("PCKOD_B", LIEF::assembly::mips::OPCODE::PCKOD_B)
  .value("PCKOD_D", LIEF::assembly::mips::OPCODE::PCKOD_D)
  .value("PCKOD_H", LIEF::assembly::mips::OPCODE::PCKOD_H)
  .value("PCKOD_W", LIEF::assembly::mips::OPCODE::PCKOD_W)
  .value("PCNT_B", LIEF::assembly::mips::OPCODE::PCNT_B)
  .value("PCNT_D", LIEF::assembly::mips::OPCODE::PCNT_D)
  .value("PCNT_H", LIEF::assembly::mips::OPCODE::PCNT_H)
  .value("PCNT_W", LIEF::assembly::mips::OPCODE::PCNT_W)
  .value("PICK_PH", LIEF::assembly::mips::OPCODE::PICK_PH)
  .value("PICK_PH_MM", LIEF::assembly::mips::OPCODE::PICK_PH_MM)
  .value("PICK_QB", LIEF::assembly::mips::OPCODE::PICK_QB)
  .value("PICK_QB_MM", LIEF::assembly::mips::OPCODE::PICK_QB_MM)
  .value("PLL_PS64", LIEF::assembly::mips::OPCODE::PLL_PS64)
  .value("PLU_PS64", LIEF::assembly::mips::OPCODE::PLU_PS64)
  .value("POP", LIEF::assembly::mips::OPCODE::POP)
  .value("PRECEQU_PH_QBL", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBL)
  .value("PRECEQU_PH_QBLA", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBLA)
  .value("PRECEQU_PH_QBLA_MM", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBLA_MM)
  .value("PRECEQU_PH_QBL_MM", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBL_MM)
  .value("PRECEQU_PH_QBR", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBR)
  .value("PRECEQU_PH_QBRA", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBRA)
  .value("PRECEQU_PH_QBRA_MM", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBRA_MM)
  .value("PRECEQU_PH_QBR_MM", LIEF::assembly::mips::OPCODE::PRECEQU_PH_QBR_MM)
  .value("PRECEQ_W_PHL", LIEF::assembly::mips::OPCODE::PRECEQ_W_PHL)
  .value("PRECEQ_W_PHL_MM", LIEF::assembly::mips::OPCODE::PRECEQ_W_PHL_MM)
  .value("PRECEQ_W_PHR", LIEF::assembly::mips::OPCODE::PRECEQ_W_PHR)
  .value("PRECEQ_W_PHR_MM", LIEF::assembly::mips::OPCODE::PRECEQ_W_PHR_MM)
  .value("PRECEU_PH_QBL", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBL)
  .value("PRECEU_PH_QBLA", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBLA)
  .value("PRECEU_PH_QBLA_MM", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBLA_MM)
  .value("PRECEU_PH_QBL_MM", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBL_MM)
  .value("PRECEU_PH_QBR", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBR)
  .value("PRECEU_PH_QBRA", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBRA)
  .value("PRECEU_PH_QBRA_MM", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBRA_MM)
  .value("PRECEU_PH_QBR_MM", LIEF::assembly::mips::OPCODE::PRECEU_PH_QBR_MM)
  .value("PRECRQU_S_QB_PH", LIEF::assembly::mips::OPCODE::PRECRQU_S_QB_PH)
  .value("PRECRQU_S_QB_PH_MM", LIEF::assembly::mips::OPCODE::PRECRQU_S_QB_PH_MM)
  .value("PRECRQ_PH_W", LIEF::assembly::mips::OPCODE::PRECRQ_PH_W)
  .value("PRECRQ_PH_W_MM", LIEF::assembly::mips::OPCODE::PRECRQ_PH_W_MM)
  .value("PRECRQ_QB_PH", LIEF::assembly::mips::OPCODE::PRECRQ_QB_PH)
  .value("PRECRQ_QB_PH_MM", LIEF::assembly::mips::OPCODE::PRECRQ_QB_PH_MM)
  .value("PRECRQ_RS_PH_W", LIEF::assembly::mips::OPCODE::PRECRQ_RS_PH_W)
  .value("PRECRQ_RS_PH_W_MM", LIEF::assembly::mips::OPCODE::PRECRQ_RS_PH_W_MM)
  .value("PRECR_QB_PH", LIEF::assembly::mips::OPCODE::PRECR_QB_PH)
  .value("PRECR_QB_PH_MMR2", LIEF::assembly::mips::OPCODE::PRECR_QB_PH_MMR2)
  .value("PRECR_SRA_PH_W", LIEF::assembly::mips::OPCODE::PRECR_SRA_PH_W)
  .value("PRECR_SRA_PH_W_MMR2", LIEF::assembly::mips::OPCODE::PRECR_SRA_PH_W_MMR2)
  .value("PRECR_SRA_R_PH_W", LIEF::assembly::mips::OPCODE::PRECR_SRA_R_PH_W)
  .value("PRECR_SRA_R_PH_W_MMR2", LIEF::assembly::mips::OPCODE::PRECR_SRA_R_PH_W_MMR2)
  .value("PREF", LIEF::assembly::mips::OPCODE::PREF)
  .value("PREFE", LIEF::assembly::mips::OPCODE::PREFE)
  .value("PREFE_MM", LIEF::assembly::mips::OPCODE::PREFE_MM)
  .value("PREFX_MM", LIEF::assembly::mips::OPCODE::PREFX_MM)
  .value("PREF_MM", LIEF::assembly::mips::OPCODE::PREF_MM);
  opcodes.value("PREF_MMR6", LIEF::assembly::mips::OPCODE::PREF_MMR6)
  .value("PREF_R6", LIEF::assembly::mips::OPCODE::PREF_R6)
  .value("PREPEND", LIEF::assembly::mips::OPCODE::PREPEND)
  .value("PREPEND_MMR2", LIEF::assembly::mips::OPCODE::PREPEND_MMR2)
  .value("PUL_PS64", LIEF::assembly::mips::OPCODE::PUL_PS64)
  .value("PUU_PS64", LIEF::assembly::mips::OPCODE::PUU_PS64)
  .value("RADDU_W_QB", LIEF::assembly::mips::OPCODE::RADDU_W_QB)
  .value("RADDU_W_QB_MM", LIEF::assembly::mips::OPCODE::RADDU_W_QB_MM)
  .value("RDDSP", LIEF::assembly::mips::OPCODE::RDDSP)
  .value("RDDSP_MM", LIEF::assembly::mips::OPCODE::RDDSP_MM)
  .value("RDHWR", LIEF::assembly::mips::OPCODE::RDHWR)
  .value("RDHWR64", LIEF::assembly::mips::OPCODE::RDHWR64)
  .value("RDHWR_MM", LIEF::assembly::mips::OPCODE::RDHWR_MM)
  .value("RDHWR_MMR6", LIEF::assembly::mips::OPCODE::RDHWR_MMR6)
  .value("RDPGPR_MMR6", LIEF::assembly::mips::OPCODE::RDPGPR_MMR6)
  .value("RECIP_D32", LIEF::assembly::mips::OPCODE::RECIP_D32)
  .value("RECIP_D32_MM", LIEF::assembly::mips::OPCODE::RECIP_D32_MM)
  .value("RECIP_D64", LIEF::assembly::mips::OPCODE::RECIP_D64)
  .value("RECIP_D64_MM", LIEF::assembly::mips::OPCODE::RECIP_D64_MM)
  .value("RECIP_S", LIEF::assembly::mips::OPCODE::RECIP_S)
  .value("RECIP_S_MM", LIEF::assembly::mips::OPCODE::RECIP_S_MM)
  .value("REPLV_PH", LIEF::assembly::mips::OPCODE::REPLV_PH)
  .value("REPLV_PH_MM", LIEF::assembly::mips::OPCODE::REPLV_PH_MM)
  .value("REPLV_QB", LIEF::assembly::mips::OPCODE::REPLV_QB)
  .value("REPLV_QB_MM", LIEF::assembly::mips::OPCODE::REPLV_QB_MM)
  .value("REPL_PH", LIEF::assembly::mips::OPCODE::REPL_PH)
  .value("REPL_PH_MM", LIEF::assembly::mips::OPCODE::REPL_PH_MM)
  .value("REPL_QB", LIEF::assembly::mips::OPCODE::REPL_QB)
  .value("REPL_QB_MM", LIEF::assembly::mips::OPCODE::REPL_QB_MM)
  .value("RINT_D", LIEF::assembly::mips::OPCODE::RINT_D)
  .value("RINT_D_MMR6", LIEF::assembly::mips::OPCODE::RINT_D_MMR6)
  .value("RINT_S", LIEF::assembly::mips::OPCODE::RINT_S)
  .value("RINT_S_MMR6", LIEF::assembly::mips::OPCODE::RINT_S_MMR6)
  .value("ROTR", LIEF::assembly::mips::OPCODE::ROTR)
  .value("ROTRV", LIEF::assembly::mips::OPCODE::ROTRV)
  .value("ROTRV_MM", LIEF::assembly::mips::OPCODE::ROTRV_MM)
  .value("ROTR_MM", LIEF::assembly::mips::OPCODE::ROTR_MM)
  .value("ROUND_L_D64", LIEF::assembly::mips::OPCODE::ROUND_L_D64)
  .value("ROUND_L_D_MMR6", LIEF::assembly::mips::OPCODE::ROUND_L_D_MMR6)
  .value("ROUND_L_S", LIEF::assembly::mips::OPCODE::ROUND_L_S)
  .value("ROUND_L_S_MMR6", LIEF::assembly::mips::OPCODE::ROUND_L_S_MMR6)
  .value("ROUND_W_D32", LIEF::assembly::mips::OPCODE::ROUND_W_D32)
  .value("ROUND_W_D64", LIEF::assembly::mips::OPCODE::ROUND_W_D64)
  .value("ROUND_W_D_MMR6", LIEF::assembly::mips::OPCODE::ROUND_W_D_MMR6)
  .value("ROUND_W_MM", LIEF::assembly::mips::OPCODE::ROUND_W_MM)
  .value("ROUND_W_S", LIEF::assembly::mips::OPCODE::ROUND_W_S)
  .value("ROUND_W_S_MM", LIEF::assembly::mips::OPCODE::ROUND_W_S_MM)
  .value("ROUND_W_S_MMR6", LIEF::assembly::mips::OPCODE::ROUND_W_S_MMR6)
  .value("RSQRT_D32", LIEF::assembly::mips::OPCODE::RSQRT_D32)
  .value("RSQRT_D32_MM", LIEF::assembly::mips::OPCODE::RSQRT_D32_MM)
  .value("RSQRT_D64", LIEF::assembly::mips::OPCODE::RSQRT_D64)
  .value("RSQRT_D64_MM", LIEF::assembly::mips::OPCODE::RSQRT_D64_MM)
  .value("RSQRT_S", LIEF::assembly::mips::OPCODE::RSQRT_S)
  .value("RSQRT_S_MM", LIEF::assembly::mips::OPCODE::RSQRT_S_MM)
  .value("Restore16", LIEF::assembly::mips::OPCODE::Restore16)
  .value("RestoreX16", LIEF::assembly::mips::OPCODE::RestoreX16)
  .value("SAA", LIEF::assembly::mips::OPCODE::SAA)
  .value("SAAD", LIEF::assembly::mips::OPCODE::SAAD)
  .value("SAT_S_B", LIEF::assembly::mips::OPCODE::SAT_S_B)
  .value("SAT_S_D", LIEF::assembly::mips::OPCODE::SAT_S_D)
  .value("SAT_S_H", LIEF::assembly::mips::OPCODE::SAT_S_H)
  .value("SAT_S_W", LIEF::assembly::mips::OPCODE::SAT_S_W)
  .value("SAT_U_B", LIEF::assembly::mips::OPCODE::SAT_U_B)
  .value("SAT_U_D", LIEF::assembly::mips::OPCODE::SAT_U_D)
  .value("SAT_U_H", LIEF::assembly::mips::OPCODE::SAT_U_H)
  .value("SAT_U_W", LIEF::assembly::mips::OPCODE::SAT_U_W)
  .value("SB", LIEF::assembly::mips::OPCODE::SB)
  .value("SB16_MM", LIEF::assembly::mips::OPCODE::SB16_MM)
  .value("SB16_MMR6", LIEF::assembly::mips::OPCODE::SB16_MMR6)
  .value("SB64", LIEF::assembly::mips::OPCODE::SB64)
  .value("SBE", LIEF::assembly::mips::OPCODE::SBE)
  .value("SBE_MM", LIEF::assembly::mips::OPCODE::SBE_MM)
  .value("SB_MM", LIEF::assembly::mips::OPCODE::SB_MM)
  .value("SB_MMR6", LIEF::assembly::mips::OPCODE::SB_MMR6)
  .value("SC", LIEF::assembly::mips::OPCODE::SC)
  .value("SC64", LIEF::assembly::mips::OPCODE::SC64)
  .value("SC64_R6", LIEF::assembly::mips::OPCODE::SC64_R6)
  .value("SCD", LIEF::assembly::mips::OPCODE::SCD)
  .value("SCD_R6", LIEF::assembly::mips::OPCODE::SCD_R6)
  .value("SCE", LIEF::assembly::mips::OPCODE::SCE)
  .value("SCE_MM", LIEF::assembly::mips::OPCODE::SCE_MM)
  .value("SC_MM", LIEF::assembly::mips::OPCODE::SC_MM)
  .value("SC_MMR6", LIEF::assembly::mips::OPCODE::SC_MMR6)
  .value("SC_R6", LIEF::assembly::mips::OPCODE::SC_R6)
  .value("SD", LIEF::assembly::mips::OPCODE::SD)
  .value("SDBBP", LIEF::assembly::mips::OPCODE::SDBBP)
  .value("SDBBP16_MM", LIEF::assembly::mips::OPCODE::SDBBP16_MM)
  .value("SDBBP16_MMR6", LIEF::assembly::mips::OPCODE::SDBBP16_MMR6)
  .value("SDBBP_MM", LIEF::assembly::mips::OPCODE::SDBBP_MM)
  .value("SDBBP_MMR6", LIEF::assembly::mips::OPCODE::SDBBP_MMR6)
  .value("SDBBP_R6", LIEF::assembly::mips::OPCODE::SDBBP_R6)
  .value("SDC1", LIEF::assembly::mips::OPCODE::SDC1)
  .value("SDC164", LIEF::assembly::mips::OPCODE::SDC164)
  .value("SDC1_D64_MMR6", LIEF::assembly::mips::OPCODE::SDC1_D64_MMR6)
  .value("SDC1_MM_D32", LIEF::assembly::mips::OPCODE::SDC1_MM_D32)
  .value("SDC1_MM_D64", LIEF::assembly::mips::OPCODE::SDC1_MM_D64)
  .value("SDC2", LIEF::assembly::mips::OPCODE::SDC2)
  .value("SDC2_MMR6", LIEF::assembly::mips::OPCODE::SDC2_MMR6)
  .value("SDC2_R6", LIEF::assembly::mips::OPCODE::SDC2_R6)
  .value("SDC3", LIEF::assembly::mips::OPCODE::SDC3)
  .value("SDIV", LIEF::assembly::mips::OPCODE::SDIV)
  .value("SDIV_MM", LIEF::assembly::mips::OPCODE::SDIV_MM)
  .value("SDL", LIEF::assembly::mips::OPCODE::SDL)
  .value("SDR", LIEF::assembly::mips::OPCODE::SDR)
  .value("SDXC1", LIEF::assembly::mips::OPCODE::SDXC1)
  .value("SDXC164", LIEF::assembly::mips::OPCODE::SDXC164)
  .value("SEB", LIEF::assembly::mips::OPCODE::SEB)
  .value("SEB64", LIEF::assembly::mips::OPCODE::SEB64)
  .value("SEB_MM", LIEF::assembly::mips::OPCODE::SEB_MM)
  .value("SEH", LIEF::assembly::mips::OPCODE::SEH)
  .value("SEH64", LIEF::assembly::mips::OPCODE::SEH64)
  .value("SEH_MM", LIEF::assembly::mips::OPCODE::SEH_MM)
  .value("SELEQZ", LIEF::assembly::mips::OPCODE::SELEQZ)
  .value("SELEQZ64", LIEF::assembly::mips::OPCODE::SELEQZ64)
  .value("SELEQZ_D", LIEF::assembly::mips::OPCODE::SELEQZ_D)
  .value("SELEQZ_D_MMR6", LIEF::assembly::mips::OPCODE::SELEQZ_D_MMR6)
  .value("SELEQZ_MMR6", LIEF::assembly::mips::OPCODE::SELEQZ_MMR6)
  .value("SELEQZ_S", LIEF::assembly::mips::OPCODE::SELEQZ_S)
  .value("SELEQZ_S_MMR6", LIEF::assembly::mips::OPCODE::SELEQZ_S_MMR6)
  .value("SELNEZ", LIEF::assembly::mips::OPCODE::SELNEZ)
  .value("SELNEZ64", LIEF::assembly::mips::OPCODE::SELNEZ64)
  .value("SELNEZ_D", LIEF::assembly::mips::OPCODE::SELNEZ_D)
  .value("SELNEZ_D_MMR6", LIEF::assembly::mips::OPCODE::SELNEZ_D_MMR6)
  .value("SELNEZ_MMR6", LIEF::assembly::mips::OPCODE::SELNEZ_MMR6)
  .value("SELNEZ_S", LIEF::assembly::mips::OPCODE::SELNEZ_S)
  .value("SELNEZ_S_MMR6", LIEF::assembly::mips::OPCODE::SELNEZ_S_MMR6)
  .value("SEL_D", LIEF::assembly::mips::OPCODE::SEL_D)
  .value("SEL_D_MMR6", LIEF::assembly::mips::OPCODE::SEL_D_MMR6)
  .value("SEL_S", LIEF::assembly::mips::OPCODE::SEL_S)
  .value("SEL_S_MMR6", LIEF::assembly::mips::OPCODE::SEL_S_MMR6)
  .value("SEQ", LIEF::assembly::mips::OPCODE::SEQ)
  .value("SEQi", LIEF::assembly::mips::OPCODE::SEQi)
  .value("SH", LIEF::assembly::mips::OPCODE::SH)
  .value("SH16_MM", LIEF::assembly::mips::OPCODE::SH16_MM)
  .value("SH16_MMR6", LIEF::assembly::mips::OPCODE::SH16_MMR6)
  .value("SH64", LIEF::assembly::mips::OPCODE::SH64)
  .value("SHE", LIEF::assembly::mips::OPCODE::SHE)
  .value("SHE_MM", LIEF::assembly::mips::OPCODE::SHE_MM)
  .value("SHF_B", LIEF::assembly::mips::OPCODE::SHF_B)
  .value("SHF_H", LIEF::assembly::mips::OPCODE::SHF_H)
  .value("SHF_W", LIEF::assembly::mips::OPCODE::SHF_W)
  .value("SHILO", LIEF::assembly::mips::OPCODE::SHILO)
  .value("SHILOV", LIEF::assembly::mips::OPCODE::SHILOV)
  .value("SHILOV_MM", LIEF::assembly::mips::OPCODE::SHILOV_MM)
  .value("SHILO_MM", LIEF::assembly::mips::OPCODE::SHILO_MM)
  .value("SHLLV_PH", LIEF::assembly::mips::OPCODE::SHLLV_PH)
  .value("SHLLV_PH_MM", LIEF::assembly::mips::OPCODE::SHLLV_PH_MM)
  .value("SHLLV_QB", LIEF::assembly::mips::OPCODE::SHLLV_QB)
  .value("SHLLV_QB_MM", LIEF::assembly::mips::OPCODE::SHLLV_QB_MM)
  .value("SHLLV_S_PH", LIEF::assembly::mips::OPCODE::SHLLV_S_PH)
  .value("SHLLV_S_PH_MM", LIEF::assembly::mips::OPCODE::SHLLV_S_PH_MM)
  .value("SHLLV_S_W", LIEF::assembly::mips::OPCODE::SHLLV_S_W)
  .value("SHLLV_S_W_MM", LIEF::assembly::mips::OPCODE::SHLLV_S_W_MM)
  .value("SHLL_PH", LIEF::assembly::mips::OPCODE::SHLL_PH)
  .value("SHLL_PH_MM", LIEF::assembly::mips::OPCODE::SHLL_PH_MM)
  .value("SHLL_QB", LIEF::assembly::mips::OPCODE::SHLL_QB)
  .value("SHLL_QB_MM", LIEF::assembly::mips::OPCODE::SHLL_QB_MM)
  .value("SHLL_S_PH", LIEF::assembly::mips::OPCODE::SHLL_S_PH)
  .value("SHLL_S_PH_MM", LIEF::assembly::mips::OPCODE::SHLL_S_PH_MM)
  .value("SHLL_S_W", LIEF::assembly::mips::OPCODE::SHLL_S_W)
  .value("SHLL_S_W_MM", LIEF::assembly::mips::OPCODE::SHLL_S_W_MM)
  .value("SHRAV_PH", LIEF::assembly::mips::OPCODE::SHRAV_PH)
  .value("SHRAV_PH_MM", LIEF::assembly::mips::OPCODE::SHRAV_PH_MM)
  .value("SHRAV_QB", LIEF::assembly::mips::OPCODE::SHRAV_QB)
  .value("SHRAV_QB_MMR2", LIEF::assembly::mips::OPCODE::SHRAV_QB_MMR2)
  .value("SHRAV_R_PH", LIEF::assembly::mips::OPCODE::SHRAV_R_PH)
  .value("SHRAV_R_PH_MM", LIEF::assembly::mips::OPCODE::SHRAV_R_PH_MM)
  .value("SHRAV_R_QB", LIEF::assembly::mips::OPCODE::SHRAV_R_QB)
  .value("SHRAV_R_QB_MMR2", LIEF::assembly::mips::OPCODE::SHRAV_R_QB_MMR2)
  .value("SHRAV_R_W", LIEF::assembly::mips::OPCODE::SHRAV_R_W)
  .value("SHRAV_R_W_MM", LIEF::assembly::mips::OPCODE::SHRAV_R_W_MM)
  .value("SHRA_PH", LIEF::assembly::mips::OPCODE::SHRA_PH)
  .value("SHRA_PH_MM", LIEF::assembly::mips::OPCODE::SHRA_PH_MM)
  .value("SHRA_QB", LIEF::assembly::mips::OPCODE::SHRA_QB)
  .value("SHRA_QB_MMR2", LIEF::assembly::mips::OPCODE::SHRA_QB_MMR2)
  .value("SHRA_R_PH", LIEF::assembly::mips::OPCODE::SHRA_R_PH)
  .value("SHRA_R_PH_MM", LIEF::assembly::mips::OPCODE::SHRA_R_PH_MM)
  .value("SHRA_R_QB", LIEF::assembly::mips::OPCODE::SHRA_R_QB)
  .value("SHRA_R_QB_MMR2", LIEF::assembly::mips::OPCODE::SHRA_R_QB_MMR2)
  .value("SHRA_R_W", LIEF::assembly::mips::OPCODE::SHRA_R_W)
  .value("SHRA_R_W_MM", LIEF::assembly::mips::OPCODE::SHRA_R_W_MM)
  .value("SHRLV_PH", LIEF::assembly::mips::OPCODE::SHRLV_PH)
  .value("SHRLV_PH_MMR2", LIEF::assembly::mips::OPCODE::SHRLV_PH_MMR2)
  .value("SHRLV_QB", LIEF::assembly::mips::OPCODE::SHRLV_QB)
  .value("SHRLV_QB_MM", LIEF::assembly::mips::OPCODE::SHRLV_QB_MM)
  .value("SHRL_PH", LIEF::assembly::mips::OPCODE::SHRL_PH)
  .value("SHRL_PH_MMR2", LIEF::assembly::mips::OPCODE::SHRL_PH_MMR2)
  .value("SHRL_QB", LIEF::assembly::mips::OPCODE::SHRL_QB)
  .value("SHRL_QB_MM", LIEF::assembly::mips::OPCODE::SHRL_QB_MM)
  .value("SH_MM", LIEF::assembly::mips::OPCODE::SH_MM)
  .value("SH_MMR6", LIEF::assembly::mips::OPCODE::SH_MMR6)
  .value("SIGRIE", LIEF::assembly::mips::OPCODE::SIGRIE)
  .value("SIGRIE_MMR6", LIEF::assembly::mips::OPCODE::SIGRIE_MMR6)
  .value("SLDI_B", LIEF::assembly::mips::OPCODE::SLDI_B)
  .value("SLDI_D", LIEF::assembly::mips::OPCODE::SLDI_D)
  .value("SLDI_H", LIEF::assembly::mips::OPCODE::SLDI_H)
  .value("SLDI_W", LIEF::assembly::mips::OPCODE::SLDI_W)
  .value("SLD_B", LIEF::assembly::mips::OPCODE::SLD_B)
  .value("SLD_D", LIEF::assembly::mips::OPCODE::SLD_D)
  .value("SLD_H", LIEF::assembly::mips::OPCODE::SLD_H)
  .value("SLD_W", LIEF::assembly::mips::OPCODE::SLD_W)
  .value("SLL", LIEF::assembly::mips::OPCODE::SLL)
  .value("SLL16_MM", LIEF::assembly::mips::OPCODE::SLL16_MM)
  .value("SLL16_MMR6", LIEF::assembly::mips::OPCODE::SLL16_MMR6)
  .value("SLL64_32", LIEF::assembly::mips::OPCODE::SLL64_32)
  .value("SLL64_64", LIEF::assembly::mips::OPCODE::SLL64_64)
  .value("SLLI_B", LIEF::assembly::mips::OPCODE::SLLI_B)
  .value("SLLI_D", LIEF::assembly::mips::OPCODE::SLLI_D)
  .value("SLLI_H", LIEF::assembly::mips::OPCODE::SLLI_H)
  .value("SLLI_W", LIEF::assembly::mips::OPCODE::SLLI_W)
  .value("SLLV", LIEF::assembly::mips::OPCODE::SLLV)
  .value("SLLV_MM", LIEF::assembly::mips::OPCODE::SLLV_MM)
  .value("SLL_B", LIEF::assembly::mips::OPCODE::SLL_B)
  .value("SLL_D", LIEF::assembly::mips::OPCODE::SLL_D)
  .value("SLL_H", LIEF::assembly::mips::OPCODE::SLL_H)
  .value("SLL_MM", LIEF::assembly::mips::OPCODE::SLL_MM)
  .value("SLL_MMR6", LIEF::assembly::mips::OPCODE::SLL_MMR6)
  .value("SLL_W", LIEF::assembly::mips::OPCODE::SLL_W)
  .value("SLT", LIEF::assembly::mips::OPCODE::SLT)
  .value("SLT64", LIEF::assembly::mips::OPCODE::SLT64)
  .value("SLT_MM", LIEF::assembly::mips::OPCODE::SLT_MM)
  .value("SLTi", LIEF::assembly::mips::OPCODE::SLTi)
  .value("SLTi64", LIEF::assembly::mips::OPCODE::SLTi64)
  .value("SLTi_MM", LIEF::assembly::mips::OPCODE::SLTi_MM)
  .value("SLTiu", LIEF::assembly::mips::OPCODE::SLTiu)
  .value("SLTiu64", LIEF::assembly::mips::OPCODE::SLTiu64)
  .value("SLTiu_MM", LIEF::assembly::mips::OPCODE::SLTiu_MM)
  .value("SLTu", LIEF::assembly::mips::OPCODE::SLTu)
  .value("SLTu64", LIEF::assembly::mips::OPCODE::SLTu64)
  .value("SLTu_MM", LIEF::assembly::mips::OPCODE::SLTu_MM)
  .value("SNE", LIEF::assembly::mips::OPCODE::SNE)
  .value("SNEi", LIEF::assembly::mips::OPCODE::SNEi)
  .value("SPLATI_B", LIEF::assembly::mips::OPCODE::SPLATI_B)
  .value("SPLATI_D", LIEF::assembly::mips::OPCODE::SPLATI_D)
  .value("SPLATI_H", LIEF::assembly::mips::OPCODE::SPLATI_H)
  .value("SPLATI_W", LIEF::assembly::mips::OPCODE::SPLATI_W)
  .value("SPLAT_B", LIEF::assembly::mips::OPCODE::SPLAT_B)
  .value("SPLAT_D", LIEF::assembly::mips::OPCODE::SPLAT_D)
  .value("SPLAT_H", LIEF::assembly::mips::OPCODE::SPLAT_H)
  .value("SPLAT_W", LIEF::assembly::mips::OPCODE::SPLAT_W)
  .value("SRA", LIEF::assembly::mips::OPCODE::SRA)
  .value("SRAI_B", LIEF::assembly::mips::OPCODE::SRAI_B)
  .value("SRAI_D", LIEF::assembly::mips::OPCODE::SRAI_D)
  .value("SRAI_H", LIEF::assembly::mips::OPCODE::SRAI_H)
  .value("SRAI_W", LIEF::assembly::mips::OPCODE::SRAI_W)
  .value("SRARI_B", LIEF::assembly::mips::OPCODE::SRARI_B)
  .value("SRARI_D", LIEF::assembly::mips::OPCODE::SRARI_D)
  .value("SRARI_H", LIEF::assembly::mips::OPCODE::SRARI_H)
  .value("SRARI_W", LIEF::assembly::mips::OPCODE::SRARI_W)
  .value("SRAR_B", LIEF::assembly::mips::OPCODE::SRAR_B)
  .value("SRAR_D", LIEF::assembly::mips::OPCODE::SRAR_D)
  .value("SRAR_H", LIEF::assembly::mips::OPCODE::SRAR_H)
  .value("SRAR_W", LIEF::assembly::mips::OPCODE::SRAR_W)
  .value("SRAV", LIEF::assembly::mips::OPCODE::SRAV)
  .value("SRAV_MM", LIEF::assembly::mips::OPCODE::SRAV_MM)
  .value("SRA_B", LIEF::assembly::mips::OPCODE::SRA_B)
  .value("SRA_D", LIEF::assembly::mips::OPCODE::SRA_D)
  .value("SRA_H", LIEF::assembly::mips::OPCODE::SRA_H)
  .value("SRA_MM", LIEF::assembly::mips::OPCODE::SRA_MM)
  .value("SRA_W", LIEF::assembly::mips::OPCODE::SRA_W)
  .value("SRL", LIEF::assembly::mips::OPCODE::SRL)
  .value("SRL16_MM", LIEF::assembly::mips::OPCODE::SRL16_MM)
  .value("SRL16_MMR6", LIEF::assembly::mips::OPCODE::SRL16_MMR6)
  .value("SRLI_B", LIEF::assembly::mips::OPCODE::SRLI_B)
  .value("SRLI_D", LIEF::assembly::mips::OPCODE::SRLI_D)
  .value("SRLI_H", LIEF::assembly::mips::OPCODE::SRLI_H)
  .value("SRLI_W", LIEF::assembly::mips::OPCODE::SRLI_W)
  .value("SRLRI_B", LIEF::assembly::mips::OPCODE::SRLRI_B)
  .value("SRLRI_D", LIEF::assembly::mips::OPCODE::SRLRI_D)
  .value("SRLRI_H", LIEF::assembly::mips::OPCODE::SRLRI_H)
  .value("SRLRI_W", LIEF::assembly::mips::OPCODE::SRLRI_W)
  .value("SRLR_B", LIEF::assembly::mips::OPCODE::SRLR_B)
  .value("SRLR_D", LIEF::assembly::mips::OPCODE::SRLR_D)
  .value("SRLR_H", LIEF::assembly::mips::OPCODE::SRLR_H)
  .value("SRLR_W", LIEF::assembly::mips::OPCODE::SRLR_W)
  .value("SRLV", LIEF::assembly::mips::OPCODE::SRLV)
  .value("SRLV_MM", LIEF::assembly::mips::OPCODE::SRLV_MM)
  .value("SRL_B", LIEF::assembly::mips::OPCODE::SRL_B)
  .value("SRL_D", LIEF::assembly::mips::OPCODE::SRL_D)
  .value("SRL_H", LIEF::assembly::mips::OPCODE::SRL_H)
  .value("SRL_MM", LIEF::assembly::mips::OPCODE::SRL_MM)
  .value("SRL_W", LIEF::assembly::mips::OPCODE::SRL_W)
  .value("SSNOP", LIEF::assembly::mips::OPCODE::SSNOP)
  .value("SSNOP_MM", LIEF::assembly::mips::OPCODE::SSNOP_MM)
  .value("SSNOP_MMR6", LIEF::assembly::mips::OPCODE::SSNOP_MMR6)
  .value("ST_B", LIEF::assembly::mips::OPCODE::ST_B)
  .value("ST_D", LIEF::assembly::mips::OPCODE::ST_D)
  .value("ST_H", LIEF::assembly::mips::OPCODE::ST_H)
  .value("ST_W", LIEF::assembly::mips::OPCODE::ST_W)
  .value("SUB", LIEF::assembly::mips::OPCODE::SUB)
  .value("SUBQH_PH", LIEF::assembly::mips::OPCODE::SUBQH_PH)
  .value("SUBQH_PH_MMR2", LIEF::assembly::mips::OPCODE::SUBQH_PH_MMR2)
  .value("SUBQH_R_PH", LIEF::assembly::mips::OPCODE::SUBQH_R_PH)
  .value("SUBQH_R_PH_MMR2", LIEF::assembly::mips::OPCODE::SUBQH_R_PH_MMR2)
  .value("SUBQH_R_W", LIEF::assembly::mips::OPCODE::SUBQH_R_W)
  .value("SUBQH_R_W_MMR2", LIEF::assembly::mips::OPCODE::SUBQH_R_W_MMR2)
  .value("SUBQH_W", LIEF::assembly::mips::OPCODE::SUBQH_W)
  .value("SUBQH_W_MMR2", LIEF::assembly::mips::OPCODE::SUBQH_W_MMR2)
  .value("SUBQ_PH", LIEF::assembly::mips::OPCODE::SUBQ_PH)
  .value("SUBQ_PH_MM", LIEF::assembly::mips::OPCODE::SUBQ_PH_MM);
  opcodes.value("SUBQ_S_PH", LIEF::assembly::mips::OPCODE::SUBQ_S_PH)
  .value("SUBQ_S_PH_MM", LIEF::assembly::mips::OPCODE::SUBQ_S_PH_MM)
  .value("SUBQ_S_W", LIEF::assembly::mips::OPCODE::SUBQ_S_W)
  .value("SUBQ_S_W_MM", LIEF::assembly::mips::OPCODE::SUBQ_S_W_MM)
  .value("SUBSUS_U_B", LIEF::assembly::mips::OPCODE::SUBSUS_U_B)
  .value("SUBSUS_U_D", LIEF::assembly::mips::OPCODE::SUBSUS_U_D)
  .value("SUBSUS_U_H", LIEF::assembly::mips::OPCODE::SUBSUS_U_H)
  .value("SUBSUS_U_W", LIEF::assembly::mips::OPCODE::SUBSUS_U_W)
  .value("SUBSUU_S_B", LIEF::assembly::mips::OPCODE::SUBSUU_S_B)
  .value("SUBSUU_S_D", LIEF::assembly::mips::OPCODE::SUBSUU_S_D)
  .value("SUBSUU_S_H", LIEF::assembly::mips::OPCODE::SUBSUU_S_H)
  .value("SUBSUU_S_W", LIEF::assembly::mips::OPCODE::SUBSUU_S_W)
  .value("SUBS_S_B", LIEF::assembly::mips::OPCODE::SUBS_S_B)
  .value("SUBS_S_D", LIEF::assembly::mips::OPCODE::SUBS_S_D)
  .value("SUBS_S_H", LIEF::assembly::mips::OPCODE::SUBS_S_H)
  .value("SUBS_S_W", LIEF::assembly::mips::OPCODE::SUBS_S_W)
  .value("SUBS_U_B", LIEF::assembly::mips::OPCODE::SUBS_U_B)
  .value("SUBS_U_D", LIEF::assembly::mips::OPCODE::SUBS_U_D)
  .value("SUBS_U_H", LIEF::assembly::mips::OPCODE::SUBS_U_H)
  .value("SUBS_U_W", LIEF::assembly::mips::OPCODE::SUBS_U_W)
  .value("SUBU16_MM", LIEF::assembly::mips::OPCODE::SUBU16_MM)
  .value("SUBU16_MMR6", LIEF::assembly::mips::OPCODE::SUBU16_MMR6)
  .value("SUBUH_QB", LIEF::assembly::mips::OPCODE::SUBUH_QB)
  .value("SUBUH_QB_MMR2", LIEF::assembly::mips::OPCODE::SUBUH_QB_MMR2)
  .value("SUBUH_R_QB", LIEF::assembly::mips::OPCODE::SUBUH_R_QB)
  .value("SUBUH_R_QB_MMR2", LIEF::assembly::mips::OPCODE::SUBUH_R_QB_MMR2)
  .value("SUBU_MMR6", LIEF::assembly::mips::OPCODE::SUBU_MMR6)
  .value("SUBU_PH", LIEF::assembly::mips::OPCODE::SUBU_PH)
  .value("SUBU_PH_MMR2", LIEF::assembly::mips::OPCODE::SUBU_PH_MMR2)
  .value("SUBU_QB", LIEF::assembly::mips::OPCODE::SUBU_QB)
  .value("SUBU_QB_MM", LIEF::assembly::mips::OPCODE::SUBU_QB_MM)
  .value("SUBU_S_PH", LIEF::assembly::mips::OPCODE::SUBU_S_PH)
  .value("SUBU_S_PH_MMR2", LIEF::assembly::mips::OPCODE::SUBU_S_PH_MMR2)
  .value("SUBU_S_QB", LIEF::assembly::mips::OPCODE::SUBU_S_QB)
  .value("SUBU_S_QB_MM", LIEF::assembly::mips::OPCODE::SUBU_S_QB_MM)
  .value("SUBVI_B", LIEF::assembly::mips::OPCODE::SUBVI_B)
  .value("SUBVI_D", LIEF::assembly::mips::OPCODE::SUBVI_D)
  .value("SUBVI_H", LIEF::assembly::mips::OPCODE::SUBVI_H)
  .value("SUBVI_W", LIEF::assembly::mips::OPCODE::SUBVI_W)
  .value("SUBV_B", LIEF::assembly::mips::OPCODE::SUBV_B)
  .value("SUBV_D", LIEF::assembly::mips::OPCODE::SUBV_D)
  .value("SUBV_H", LIEF::assembly::mips::OPCODE::SUBV_H)
  .value("SUBV_W", LIEF::assembly::mips::OPCODE::SUBV_W)
  .value("SUB_MM", LIEF::assembly::mips::OPCODE::SUB_MM)
  .value("SUB_MMR6", LIEF::assembly::mips::OPCODE::SUB_MMR6)
  .value("SUBu", LIEF::assembly::mips::OPCODE::SUBu)
  .value("SUBu_MM", LIEF::assembly::mips::OPCODE::SUBu_MM)
  .value("SUXC1", LIEF::assembly::mips::OPCODE::SUXC1)
  .value("SUXC164", LIEF::assembly::mips::OPCODE::SUXC164)
  .value("SUXC1_MM", LIEF::assembly::mips::OPCODE::SUXC1_MM)
  .value("SW", LIEF::assembly::mips::OPCODE::SW)
  .value("SW16_MM", LIEF::assembly::mips::OPCODE::SW16_MM)
  .value("SW16_MMR6", LIEF::assembly::mips::OPCODE::SW16_MMR6)
  .value("SW64", LIEF::assembly::mips::OPCODE::SW64)
  .value("SWC1", LIEF::assembly::mips::OPCODE::SWC1)
  .value("SWC1_MM", LIEF::assembly::mips::OPCODE::SWC1_MM)
  .value("SWC2", LIEF::assembly::mips::OPCODE::SWC2)
  .value("SWC2_MMR6", LIEF::assembly::mips::OPCODE::SWC2_MMR6)
  .value("SWC2_R6", LIEF::assembly::mips::OPCODE::SWC2_R6)
  .value("SWC3", LIEF::assembly::mips::OPCODE::SWC3)
  .value("SWDSP", LIEF::assembly::mips::OPCODE::SWDSP)
  .value("SWDSP_MM", LIEF::assembly::mips::OPCODE::SWDSP_MM)
  .value("SWE", LIEF::assembly::mips::OPCODE::SWE)
  .value("SWE_MM", LIEF::assembly::mips::OPCODE::SWE_MM)
  .value("SWL", LIEF::assembly::mips::OPCODE::SWL)
  .value("SWL64", LIEF::assembly::mips::OPCODE::SWL64)
  .value("SWLE", LIEF::assembly::mips::OPCODE::SWLE)
  .value("SWLE_MM", LIEF::assembly::mips::OPCODE::SWLE_MM)
  .value("SWL_MM", LIEF::assembly::mips::OPCODE::SWL_MM)
  .value("SWM16_MM", LIEF::assembly::mips::OPCODE::SWM16_MM)
  .value("SWM16_MMR6", LIEF::assembly::mips::OPCODE::SWM16_MMR6)
  .value("SWM32_MM", LIEF::assembly::mips::OPCODE::SWM32_MM)
  .value("SWP_MM", LIEF::assembly::mips::OPCODE::SWP_MM)
  .value("SWR", LIEF::assembly::mips::OPCODE::SWR)
  .value("SWR64", LIEF::assembly::mips::OPCODE::SWR64)
  .value("SWRE", LIEF::assembly::mips::OPCODE::SWRE)
  .value("SWRE_MM", LIEF::assembly::mips::OPCODE::SWRE_MM)
  .value("SWR_MM", LIEF::assembly::mips::OPCODE::SWR_MM)
  .value("SWSP_MM", LIEF::assembly::mips::OPCODE::SWSP_MM)
  .value("SWSP_MMR6", LIEF::assembly::mips::OPCODE::SWSP_MMR6)
  .value("SWXC1", LIEF::assembly::mips::OPCODE::SWXC1)
  .value("SWXC1_MM", LIEF::assembly::mips::OPCODE::SWXC1_MM)
  .value("SW_MM", LIEF::assembly::mips::OPCODE::SW_MM)
  .value("SW_MMR6", LIEF::assembly::mips::OPCODE::SW_MMR6)
  .value("SYNC", LIEF::assembly::mips::OPCODE::SYNC)
  .value("SYNCI", LIEF::assembly::mips::OPCODE::SYNCI)
  .value("SYNCI_MM", LIEF::assembly::mips::OPCODE::SYNCI_MM)
  .value("SYNCI_MMR6", LIEF::assembly::mips::OPCODE::SYNCI_MMR6)
  .value("SYNC_MM", LIEF::assembly::mips::OPCODE::SYNC_MM)
  .value("SYNC_MMR6", LIEF::assembly::mips::OPCODE::SYNC_MMR6)
  .value("SYSCALL", LIEF::assembly::mips::OPCODE::SYSCALL)
  .value("SYSCALL_MM", LIEF::assembly::mips::OPCODE::SYSCALL_MM)
  .value("Save16", LIEF::assembly::mips::OPCODE::Save16)
  .value("SaveX16", LIEF::assembly::mips::OPCODE::SaveX16)
  .value("SbRxRyOffMemX16", LIEF::assembly::mips::OPCODE::SbRxRyOffMemX16)
  .value("SebRx16", LIEF::assembly::mips::OPCODE::SebRx16)
  .value("SehRx16", LIEF::assembly::mips::OPCODE::SehRx16)
  .value("ShRxRyOffMemX16", LIEF::assembly::mips::OPCODE::ShRxRyOffMemX16)
  .value("SllX16", LIEF::assembly::mips::OPCODE::SllX16)
  .value("SllvRxRy16", LIEF::assembly::mips::OPCODE::SllvRxRy16)
  .value("SltRxRy16", LIEF::assembly::mips::OPCODE::SltRxRy16)
  .value("SltiRxImm16", LIEF::assembly::mips::OPCODE::SltiRxImm16)
  .value("SltiRxImmX16", LIEF::assembly::mips::OPCODE::SltiRxImmX16)
  .value("SltiuRxImm16", LIEF::assembly::mips::OPCODE::SltiuRxImm16)
  .value("SltiuRxImmX16", LIEF::assembly::mips::OPCODE::SltiuRxImmX16)
  .value("SltuRxRy16", LIEF::assembly::mips::OPCODE::SltuRxRy16)
  .value("SraX16", LIEF::assembly::mips::OPCODE::SraX16)
  .value("SravRxRy16", LIEF::assembly::mips::OPCODE::SravRxRy16)
  .value("SrlX16", LIEF::assembly::mips::OPCODE::SrlX16)
  .value("SrlvRxRy16", LIEF::assembly::mips::OPCODE::SrlvRxRy16)
  .value("SubuRxRyRz16", LIEF::assembly::mips::OPCODE::SubuRxRyRz16)
  .value("SwRxRyOffMemX16", LIEF::assembly::mips::OPCODE::SwRxRyOffMemX16)
  .value("SwRxSpImmX16", LIEF::assembly::mips::OPCODE::SwRxSpImmX16)
  .value("TEQ", LIEF::assembly::mips::OPCODE::TEQ)
  .value("TEQI", LIEF::assembly::mips::OPCODE::TEQI)
  .value("TEQI_MM", LIEF::assembly::mips::OPCODE::TEQI_MM)
  .value("TEQ_MM", LIEF::assembly::mips::OPCODE::TEQ_MM)
  .value("TGE", LIEF::assembly::mips::OPCODE::TGE)
  .value("TGEI", LIEF::assembly::mips::OPCODE::TGEI)
  .value("TGEIU", LIEF::assembly::mips::OPCODE::TGEIU)
  .value("TGEIU_MM", LIEF::assembly::mips::OPCODE::TGEIU_MM)
  .value("TGEI_MM", LIEF::assembly::mips::OPCODE::TGEI_MM)
  .value("TGEU", LIEF::assembly::mips::OPCODE::TGEU)
  .value("TGEU_MM", LIEF::assembly::mips::OPCODE::TGEU_MM)
  .value("TGE_MM", LIEF::assembly::mips::OPCODE::TGE_MM)
  .value("TLBGINV", LIEF::assembly::mips::OPCODE::TLBGINV)
  .value("TLBGINVF", LIEF::assembly::mips::OPCODE::TLBGINVF)
  .value("TLBGINVF_MM", LIEF::assembly::mips::OPCODE::TLBGINVF_MM)
  .value("TLBGINV_MM", LIEF::assembly::mips::OPCODE::TLBGINV_MM)
  .value("TLBGP", LIEF::assembly::mips::OPCODE::TLBGP)
  .value("TLBGP_MM", LIEF::assembly::mips::OPCODE::TLBGP_MM)
  .value("TLBGR", LIEF::assembly::mips::OPCODE::TLBGR)
  .value("TLBGR_MM", LIEF::assembly::mips::OPCODE::TLBGR_MM)
  .value("TLBGWI", LIEF::assembly::mips::OPCODE::TLBGWI)
  .value("TLBGWI_MM", LIEF::assembly::mips::OPCODE::TLBGWI_MM)
  .value("TLBGWR", LIEF::assembly::mips::OPCODE::TLBGWR)
  .value("TLBGWR_MM", LIEF::assembly::mips::OPCODE::TLBGWR_MM)
  .value("TLBINV", LIEF::assembly::mips::OPCODE::TLBINV)
  .value("TLBINVF", LIEF::assembly::mips::OPCODE::TLBINVF)
  .value("TLBINVF_MMR6", LIEF::assembly::mips::OPCODE::TLBINVF_MMR6)
  .value("TLBINV_MMR6", LIEF::assembly::mips::OPCODE::TLBINV_MMR6)
  .value("TLBP", LIEF::assembly::mips::OPCODE::TLBP)
  .value("TLBP_MM", LIEF::assembly::mips::OPCODE::TLBP_MM)
  .value("TLBR", LIEF::assembly::mips::OPCODE::TLBR)
  .value("TLBR_MM", LIEF::assembly::mips::OPCODE::TLBR_MM)
  .value("TLBWI", LIEF::assembly::mips::OPCODE::TLBWI)
  .value("TLBWI_MM", LIEF::assembly::mips::OPCODE::TLBWI_MM)
  .value("TLBWR", LIEF::assembly::mips::OPCODE::TLBWR)
  .value("TLBWR_MM", LIEF::assembly::mips::OPCODE::TLBWR_MM)
  .value("TLT", LIEF::assembly::mips::OPCODE::TLT)
  .value("TLTI", LIEF::assembly::mips::OPCODE::TLTI)
  .value("TLTIU_MM", LIEF::assembly::mips::OPCODE::TLTIU_MM)
  .value("TLTI_MM", LIEF::assembly::mips::OPCODE::TLTI_MM)
  .value("TLTU", LIEF::assembly::mips::OPCODE::TLTU)
  .value("TLTU_MM", LIEF::assembly::mips::OPCODE::TLTU_MM)
  .value("TLT_MM", LIEF::assembly::mips::OPCODE::TLT_MM)
  .value("TNE", LIEF::assembly::mips::OPCODE::TNE)
  .value("TNEI", LIEF::assembly::mips::OPCODE::TNEI)
  .value("TNEI_MM", LIEF::assembly::mips::OPCODE::TNEI_MM)
  .value("TNE_MM", LIEF::assembly::mips::OPCODE::TNE_MM)
  .value("TRUNC_L_D64", LIEF::assembly::mips::OPCODE::TRUNC_L_D64)
  .value("TRUNC_L_D_MMR6", LIEF::assembly::mips::OPCODE::TRUNC_L_D_MMR6)
  .value("TRUNC_L_S", LIEF::assembly::mips::OPCODE::TRUNC_L_S)
  .value("TRUNC_L_S_MMR6", LIEF::assembly::mips::OPCODE::TRUNC_L_S_MMR6)
  .value("TRUNC_W_D32", LIEF::assembly::mips::OPCODE::TRUNC_W_D32)
  .value("TRUNC_W_D64", LIEF::assembly::mips::OPCODE::TRUNC_W_D64)
  .value("TRUNC_W_D_MMR6", LIEF::assembly::mips::OPCODE::TRUNC_W_D_MMR6)
  .value("TRUNC_W_MM", LIEF::assembly::mips::OPCODE::TRUNC_W_MM)
  .value("TRUNC_W_S", LIEF::assembly::mips::OPCODE::TRUNC_W_S)
  .value("TRUNC_W_S_MM", LIEF::assembly::mips::OPCODE::TRUNC_W_S_MM)
  .value("TRUNC_W_S_MMR6", LIEF::assembly::mips::OPCODE::TRUNC_W_S_MMR6)
  .value("TTLTIU", LIEF::assembly::mips::OPCODE::TTLTIU)
  .value("UDIV", LIEF::assembly::mips::OPCODE::UDIV)
  .value("UDIV_MM", LIEF::assembly::mips::OPCODE::UDIV_MM)
  .value("V3MULU", LIEF::assembly::mips::OPCODE::V3MULU)
  .value("VMM0", LIEF::assembly::mips::OPCODE::VMM0)
  .value("VMULU", LIEF::assembly::mips::OPCODE::VMULU)
  .value("VSHF_B", LIEF::assembly::mips::OPCODE::VSHF_B)
  .value("VSHF_D", LIEF::assembly::mips::OPCODE::VSHF_D)
  .value("VSHF_H", LIEF::assembly::mips::OPCODE::VSHF_H)
  .value("VSHF_W", LIEF::assembly::mips::OPCODE::VSHF_W)
  .value("WAIT", LIEF::assembly::mips::OPCODE::WAIT)
  .value("WAIT_MM", LIEF::assembly::mips::OPCODE::WAIT_MM)
  .value("WAIT_MMR6", LIEF::assembly::mips::OPCODE::WAIT_MMR6)
  .value("WRDSP", LIEF::assembly::mips::OPCODE::WRDSP)
  .value("WRDSP_MM", LIEF::assembly::mips::OPCODE::WRDSP_MM)
  .value("WRPGPR_MMR6", LIEF::assembly::mips::OPCODE::WRPGPR_MMR6)
  .value("WSBH", LIEF::assembly::mips::OPCODE::WSBH)
  .value("WSBH_MM", LIEF::assembly::mips::OPCODE::WSBH_MM)
  .value("WSBH_MMR6", LIEF::assembly::mips::OPCODE::WSBH_MMR6)
  .value("XOR", LIEF::assembly::mips::OPCODE::XOR)
  .value("XOR16_MM", LIEF::assembly::mips::OPCODE::XOR16_MM)
  .value("XOR16_MMR6", LIEF::assembly::mips::OPCODE::XOR16_MMR6)
  .value("XOR64", LIEF::assembly::mips::OPCODE::XOR64)
  .value("XORI_B", LIEF::assembly::mips::OPCODE::XORI_B)
  .value("XORI_MMR6", LIEF::assembly::mips::OPCODE::XORI_MMR6)
  .value("XOR_MM", LIEF::assembly::mips::OPCODE::XOR_MM)
  .value("XOR_MMR6", LIEF::assembly::mips::OPCODE::XOR_MMR6)
  .value("XOR_V", LIEF::assembly::mips::OPCODE::XOR_V)
  .value("XORi", LIEF::assembly::mips::OPCODE::XORi)
  .value("XORi64", LIEF::assembly::mips::OPCODE::XORi64)
  .value("XORi_MM", LIEF::assembly::mips::OPCODE::XORi_MM)
  .value("XorRxRxRy16", LIEF::assembly::mips::OPCODE::XorRxRxRy16)
  .value("YIELD", LIEF::assembly::mips::OPCODE::YIELD)
  .value("INSTRUCTION_LIST_END", LIEF::assembly::mips::OPCODE::INSTRUCTION_LIST_END)
  ;
}
}
