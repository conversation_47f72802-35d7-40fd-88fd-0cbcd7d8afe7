[build-system]
requires = []
build-backend = "setup"
backend-path = ["backend"]

[project]
name = "lief"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
description = "Library to instrument executable formats"
requires-python = ">=3.8"
keywords = ["parser", "elf", "pe", "macho", "reverse-engineering"]
license = {text = "Apache License 2.0"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python :: 3",
    "Programming Language :: C++",
    "Topic :: Software Development :: Libraries",
]
dynamic = ["version", "readme"]

[tool.scikit-build]
experimental = true
metadata.version.provider = "dynamic_provider"
metadata.readme.provider = "dynamic_provider"

[project.urls]
homepage = "https://lief-project.github.io/"
documentation = "https://lief-project.github.io/doc/latest/"
repository = "https://github.com/lief-project/LIEF"
changelog = "https://lief-project.github.io/doc/latest/changelog.html"
Funding = "https://github.com/sponsors/lief-project"
Tracker = "https://github.com/lief-project/LIEF/issues"
