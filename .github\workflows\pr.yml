name: LIEF PR
on: [pull_request, workflow_dispatch]

env:
  python-osx-version: '3.10'
  python-win-version: '3.10'

jobs:
  osx:
    runs-on: macos-11.0
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.python-osx-version }}
      - name: Install system dependencies
        run: |
          brew install cmake ninja ccache
          python -m pip install --upgrade pip
      - name: Build with Python ${{ env.python-osx-version }} & Test
        shell: bash
        env:
          LIEF_SAMPLES_DIR: "/tmp/samples"
          LIEF_BUILD_DIR: "/tmp/lief-build"
          PYLIEF_CONF: "${{ github.workspace }}/scripts/osx/osx-testing-x64.toml"
        run: |
          python -m pip install -r tests/requirements.txt
          python -m pip install -e api/python
          python tests/dl_samples.py ${LIEF_SAMPLES_DIR}
          python tests/run_pytest.py
          python tests/run_tools_check.py ${BUILD_DIR}
          ctest --output-on-failure --test-dir ${BUILD_DIR}
  linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Build & Test
        shell: bash
        env:
          PYTHON_VERSION: 39
        run: |
          docker run --rm                                       \
           -e PYTHON_VERSION=$PYTHON_VERSION                    \
           -e PYTHON_BINARY=/opt/python/cp39-cp39/bin/python3.9 \
           -v $GITHUB_WORKSPACE:/src                            \
           liefproject/manylinux_2_28_x86_64                    \
           bash /src/scripts/docker/test-linux-x64
  windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ env.python-win-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.python-win-version }}
        architecture: x64
    - name: Install system dependencies
      run: |
        choco install ninja
        python -m pip install --upgrade pip
    - uses: ilammy/msvc-dev-cmd@v1
      with:
        vsversion: "2022"
    - name: Set env variables
      run: |
        chcp 65001 #set code page to utf-8
        echo ("LIEF_SAMPLES_DIR=" + $env:RUNNER_TEMP + "/samples") >> $env:GITHUB_ENV
        echo ("LIEF_BUILD_DIR=" + $env:RUNNER_TEMP + "/lief-build") >> $env:GITHUB_ENV
        echo ("PYLIEF_CONF=" + $env:GITHUB_WORKSPACE + "/scripts/windows/py-x64-test.toml") >> $env:GITHUB_ENV
    - name: Build Python ${{ env.python-win-version }}
      shell: cmd
      run: |
        python -m pip install -r tests/requirements.txt
        python -m pip install api/python
        python tests/dl_samples.py %LIEF_SAMPLES_DIR%
    - name: Run tests
      timeout-minutes: 20
      shell: cmd
      run: |
        cd %GITHUB_WORKSPACE%
        python tests/run_pytest.py
        ctest --output-on-failure --test-dir %BUILD_DIR%


