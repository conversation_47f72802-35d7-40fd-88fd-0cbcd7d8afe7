/* Copyright 2017 - 2025 <PERSON><PERSON>
 * Copyright 2017 - 2025 Quarkslab
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "Header.hpp"

namespace LIEF {
namespace MachO {
void init_c_header(Macho_Binary_t* c_binary, Binary* binary) {
  Header& header = binary->header();
  c_binary->header.magic       = static_cast<uint32_t>(header.magic());
  c_binary->header.cpu_type    = static_cast<enum LIEF_MACHO_CPU_TYPES>(header.cpu_type());
  c_binary->header.cpu_subtype = header.cpu_subtype();
  c_binary->header.file_type   = static_cast<enum LIEF_MACHO_FILE_TYPES>(header.file_type());
  c_binary->header.nb_cmds     = header.nb_cmds();
  c_binary->header.sizeof_cmds = header.sizeof_cmds();
  c_binary->header.flags       = header.flags();
  c_binary->header.reserved    = header.reserved();
}

}
}
