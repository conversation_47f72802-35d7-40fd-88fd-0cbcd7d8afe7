#[allow(non_camel_case_types)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Eq, <PERSON><PERSON><PERSON>rd, Or<PERSON>, Hash)]
pub enum Reg {
  NoRegister,
  BP,
  CARRY,
  CTR,
  FP,
  LR,
  RM,
  SPEFSCR,
  VRSAVE,
  XER,
  ZERO,
  ACC0,
  ACC1,
  ACC2,
  ACC3,
  ACC4,
  ACC5,
  ACC6,
  ACC7,
  BP8,
  CR0,
  CR1,
  CR2,
  CR3,
  CR4,
  CR5,
  CR6,
  CR7,
  CTR8,
  DMR0,
  DMR1,
  DMR2,
  DMR3,
  DMR4,
  DMR5,
  DMR6,
  DMR7,
  DMRROW0,
  DMRROW1,
  DMRROW2,
  DMRROW3,
  DMRROW4,
  DMRROW5,
  DMRROW6,
  DMRROW7,
  DMRROW8,
  DMRROW9,
  DMRROW10,
  DMRROW11,
  DMRROW12,
  DMRROW13,
  DMRROW14,
  DMRROW15,
  DMRROW16,
  DMRROW17,
  DMRROW18,
  DMRRO<PERSON>19,
  <PERSON>MR<PERSON>W20,
  DMR<PERSON><PERSON>21,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>23,
  <PERSON><PERSON><PERSON><PERSON>24,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>27,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>30,
  <PERSON>MR<PERSON>W31,
  DMR<PERSON>W32,
  D<PERSON><PERSON><PERSON>33,
  DMRROW34,
  DMRROW35,
  DMRROW36,
  DMRROW37,
  DMRROW38,
  DMRROW39,
  DMRROW40,
  DMRROW41,
  DMRROW42,
  DMRROW43,
  DMRROW44,
  DMRROW45,
  DMRROW46,
  DMRROW47,
  DMRROW48,
  DMRROW49,
  DMRROW50,
  DMRROW51,
  DMRROW52,
  DMRROW53,
  DMRROW54,
  DMRROW55,
  DMRROW56,
  DMRROW57,
  DMRROW58,
  DMRROW59,
  DMRROW60,
  DMRROW61,
  DMRROW62,
  DMRROW63,
  DMRROWp0,
  DMRROWp1,
  DMRROWp2,
  DMRROWp3,
  DMRROWp4,
  DMRROWp5,
  DMRROWp6,
  DMRROWp7,
  DMRROWp8,
  DMRROWp9,
  DMRROWp10,
  DMRROWp11,
  DMRROWp12,
  DMRROWp13,
  DMRROWp14,
  DMRROWp15,
  DMRROWp16,
  DMRROWp17,
  DMRROWp18,
  DMRROWp19,
  DMRROWp20,
  DMRROWp21,
  DMRROWp22,
  DMRROWp23,
  DMRROWp24,
  DMRROWp25,
  DMRROWp26,
  DMRROWp27,
  DMRROWp28,
  DMRROWp29,
  DMRROWp30,
  DMRROWp31,
  DMRp0,
  DMRp1,
  DMRp2,
  DMRp3,
  F0,
  F1,
  F2,
  F3,
  F4,
  F5,
  F6,
  F7,
  F8,
  F9,
  F10,
  F11,
  F12,
  F13,
  F14,
  F15,
  F16,
  F17,
  F18,
  F19,
  F20,
  F21,
  F22,
  F23,
  F24,
  F25,
  F26,
  F27,
  F28,
  F29,
  F30,
  F31,
  FH0,
  FH1,
  FH2,
  FH3,
  FH4,
  FH5,
  FH6,
  FH7,
  FH8,
  FH9,
  FH10,
  FH11,
  FH12,
  FH13,
  FH14,
  FH15,
  FH16,
  FH17,
  FH18,
  FH19,
  FH20,
  FH21,
  FH22,
  FH23,
  FH24,
  FH25,
  FH26,
  FH27,
  FH28,
  FH29,
  FH30,
  FH31,
  FP8,
  Fpair0,
  Fpair2,
  Fpair4,
  Fpair6,
  Fpair8,
  Fpair10,
  Fpair12,
  Fpair14,
  Fpair16,
  Fpair18,
  Fpair20,
  Fpair22,
  Fpair24,
  Fpair26,
  Fpair28,
  Fpair30,
  H0,
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  H7,
  H8,
  H9,
  H10,
  H11,
  H12,
  H13,
  H14,
  H15,
  H16,
  H17,
  H18,
  H19,
  H20,
  H21,
  H22,
  H23,
  H24,
  H25,
  H26,
  H27,
  H28,
  H29,
  H30,
  H31,
  LR8,
  R0,
  R1,
  R2,
  R3,
  R4,
  R5,
  R6,
  R7,
  R8,
  R9,
  R10,
  R11,
  R12,
  R13,
  R14,
  R15,
  R16,
  R17,
  R18,
  R19,
  R20,
  R21,
  R22,
  R23,
  R24,
  R25,
  R26,
  R27,
  R28,
  R29,
  R30,
  R31,
  S0,
  S1,
  S2,
  S3,
  S4,
  S5,
  S6,
  S7,
  S8,
  S9,
  S10,
  S11,
  S12,
  S13,
  S14,
  S15,
  S16,
  S17,
  S18,
  S19,
  S20,
  S21,
  S22,
  S23,
  S24,
  S25,
  S26,
  S27,
  S28,
  S29,
  S30,
  S31,
  UACC0,
  UACC1,
  UACC2,
  UACC3,
  UACC4,
  UACC5,
  UACC6,
  UACC7,
  V0,
  V1,
  V2,
  V3,
  V4,
  V5,
  V6,
  V7,
  V8,
  V9,
  V10,
  V11,
  V12,
  V13,
  V14,
  V15,
  V16,
  V17,
  V18,
  V19,
  V20,
  V21,
  V22,
  V23,
  V24,
  V25,
  V26,
  V27,
  V28,
  V29,
  V30,
  V31,
  VF0,
  VF1,
  VF2,
  VF3,
  VF4,
  VF5,
  VF6,
  VF7,
  VF8,
  VF9,
  VF10,
  VF11,
  VF12,
  VF13,
  VF14,
  VF15,
  VF16,
  VF17,
  VF18,
  VF19,
  VF20,
  VF21,
  VF22,
  VF23,
  VF24,
  VF25,
  VF26,
  VF27,
  VF28,
  VF29,
  VF30,
  VF31,
  VFH0,
  VFH1,
  VFH2,
  VFH3,
  VFH4,
  VFH5,
  VFH6,
  VFH7,
  VFH8,
  VFH9,
  VFH10,
  VFH11,
  VFH12,
  VFH13,
  VFH14,
  VFH15,
  VFH16,
  VFH17,
  VFH18,
  VFH19,
  VFH20,
  VFH21,
  VFH22,
  VFH23,
  VFH24,
  VFH25,
  VFH26,
  VFH27,
  VFH28,
  VFH29,
  VFH30,
  VFH31,
  VSL0,
  VSL1,
  VSL2,
  VSL3,
  VSL4,
  VSL5,
  VSL6,
  VSL7,
  VSL8,
  VSL9,
  VSL10,
  VSL11,
  VSL12,
  VSL13,
  VSL14,
  VSL15,
  VSL16,
  VSL17,
  VSL18,
  VSL19,
  VSL20,
  VSL21,
  VSL22,
  VSL23,
  VSL24,
  VSL25,
  VSL26,
  VSL27,
  VSL28,
  VSL29,
  VSL30,
  VSL31,
  VSRp0,
  VSRp1,
  VSRp2,
  VSRp3,
  VSRp4,
  VSRp5,
  VSRp6,
  VSRp7,
  VSRp8,
  VSRp9,
  VSRp10,
  VSRp11,
  VSRp12,
  VSRp13,
  VSRp14,
  VSRp15,
  VSRp16,
  VSRp17,
  VSRp18,
  VSRp19,
  VSRp20,
  VSRp21,
  VSRp22,
  VSRp23,
  VSRp24,
  VSRp25,
  VSRp26,
  VSRp27,
  VSRp28,
  VSRp29,
  VSRp30,
  VSRp31,
  VSX32,
  VSX33,
  VSX34,
  VSX35,
  VSX36,
  VSX37,
  VSX38,
  VSX39,
  VSX40,
  VSX41,
  VSX42,
  VSX43,
  VSX44,
  VSX45,
  VSX46,
  VSX47,
  VSX48,
  VSX49,
  VSX50,
  VSX51,
  VSX52,
  VSX53,
  VSX54,
  VSX55,
  VSX56,
  VSX57,
  VSX58,
  VSX59,
  VSX60,
  VSX61,
  VSX62,
  VSX63,
  WACC0,
  WACC1,
  WACC2,
  WACC3,
  WACC4,
  WACC5,
  WACC6,
  WACC7,
  WACC_HI0,
  WACC_HI1,
  WACC_HI2,
  WACC_HI3,
  WACC_HI4,
  WACC_HI5,
  WACC_HI6,
  WACC_HI7,
  X0,
  X1,
  X2,
  X3,
  X4,
  X5,
  X6,
  X7,
  X8,
  X9,
  X10,
  X11,
  X12,
  X13,
  X14,
  X15,
  X16,
  X17,
  X18,
  X19,
  X20,
  X21,
  X22,
  X23,
  X24,
  X25,
  X26,
  X27,
  X28,
  X29,
  X30,
  X31,
  ZERO8,
  CR0EQ,
  CR1EQ,
  CR2EQ,
  CR3EQ,
  CR4EQ,
  CR5EQ,
  CR6EQ,
  CR7EQ,
  CR0GT,
  CR1GT,
  CR2GT,
  CR3GT,
  CR4GT,
  CR5GT,
  CR6GT,
  CR7GT,
  CR0LT,
  CR1LT,
  CR2LT,
  CR3LT,
  CR4LT,
  CR5LT,
  CR6LT,
  CR7LT,
  CR0UN,
  CR1UN,
  CR2UN,
  CR3UN,
  CR4UN,
  CR5UN,
  CR6UN,
  CR7UN,
  G8p0,
  G8p1,
  G8p2,
  G8p3,
  G8p4,
  G8p5,
  G8p6,
  G8p7,
  G8p8,
  G8p9,
  G8p10,
  G8p11,
  G8p12,
  G8p13,
  G8p14,
  G8p15,
  NUM_TARGET_REGS,
  UNKNOWN(u64),
}

impl From<u64> for Reg {
    fn from(value: u64) -> Self {
        match value {
          0 => Reg::NoRegister,
          1 => Reg::BP,
          2 => Reg::CARRY,
          3 => Reg::CTR,
          4 => Reg::FP,
          5 => Reg::LR,
          6 => Reg::RM,
          7 => Reg::SPEFSCR,
          8 => Reg::VRSAVE,
          9 => Reg::XER,
          10 => Reg::ZERO,
          11 => Reg::ACC0,
          12 => Reg::ACC1,
          13 => Reg::ACC2,
          14 => Reg::ACC3,
          15 => Reg::ACC4,
          16 => Reg::ACC5,
          17 => Reg::ACC6,
          18 => Reg::ACC7,
          19 => Reg::BP8,
          20 => Reg::CR0,
          21 => Reg::CR1,
          22 => Reg::CR2,
          23 => Reg::CR3,
          24 => Reg::CR4,
          25 => Reg::CR5,
          26 => Reg::CR6,
          27 => Reg::CR7,
          28 => Reg::CTR8,
          29 => Reg::DMR0,
          30 => Reg::DMR1,
          31 => Reg::DMR2,
          32 => Reg::DMR3,
          33 => Reg::DMR4,
          34 => Reg::DMR5,
          35 => Reg::DMR6,
          36 => Reg::DMR7,
          37 => Reg::DMRROW0,
          38 => Reg::DMRROW1,
          39 => Reg::DMRROW2,
          40 => Reg::DMRROW3,
          41 => Reg::DMRROW4,
          42 => Reg::DMRROW5,
          43 => Reg::DMRROW6,
          44 => Reg::DMRROW7,
          45 => Reg::DMRROW8,
          46 => Reg::DMRROW9,
          47 => Reg::DMRROW10,
          48 => Reg::DMRROW11,
          49 => Reg::DMRROW12,
          50 => Reg::DMRROW13,
          51 => Reg::DMRROW14,
          52 => Reg::DMRROW15,
          53 => Reg::DMRROW16,
          54 => Reg::DMRROW17,
          55 => Reg::DMRROW18,
          56 => Reg::DMRROW19,
          57 => Reg::DMRROW20,
          58 => Reg::DMRROW21,
          59 => Reg::DMRROW22,
          60 => Reg::DMRROW23,
          61 => Reg::DMRROW24,
          62 => Reg::DMRROW25,
          63 => Reg::DMRROW26,
          64 => Reg::DMRROW27,
          65 => Reg::DMRROW28,
          66 => Reg::DMRROW29,
          67 => Reg::DMRROW30,
          68 => Reg::DMRROW31,
          69 => Reg::DMRROW32,
          70 => Reg::DMRROW33,
          71 => Reg::DMRROW34,
          72 => Reg::DMRROW35,
          73 => Reg::DMRROW36,
          74 => Reg::DMRROW37,
          75 => Reg::DMRROW38,
          76 => Reg::DMRROW39,
          77 => Reg::DMRROW40,
          78 => Reg::DMRROW41,
          79 => Reg::DMRROW42,
          80 => Reg::DMRROW43,
          81 => Reg::DMRROW44,
          82 => Reg::DMRROW45,
          83 => Reg::DMRROW46,
          84 => Reg::DMRROW47,
          85 => Reg::DMRROW48,
          86 => Reg::DMRROW49,
          87 => Reg::DMRROW50,
          88 => Reg::DMRROW51,
          89 => Reg::DMRROW52,
          90 => Reg::DMRROW53,
          91 => Reg::DMRROW54,
          92 => Reg::DMRROW55,
          93 => Reg::DMRROW56,
          94 => Reg::DMRROW57,
          95 => Reg::DMRROW58,
          96 => Reg::DMRROW59,
          97 => Reg::DMRROW60,
          98 => Reg::DMRROW61,
          99 => Reg::DMRROW62,
          100 => Reg::DMRROW63,
          101 => Reg::DMRROWp0,
          102 => Reg::DMRROWp1,
          103 => Reg::DMRROWp2,
          104 => Reg::DMRROWp3,
          105 => Reg::DMRROWp4,
          106 => Reg::DMRROWp5,
          107 => Reg::DMRROWp6,
          108 => Reg::DMRROWp7,
          109 => Reg::DMRROWp8,
          110 => Reg::DMRROWp9,
          111 => Reg::DMRROWp10,
          112 => Reg::DMRROWp11,
          113 => Reg::DMRROWp12,
          114 => Reg::DMRROWp13,
          115 => Reg::DMRROWp14,
          116 => Reg::DMRROWp15,
          117 => Reg::DMRROWp16,
          118 => Reg::DMRROWp17,
          119 => Reg::DMRROWp18,
          120 => Reg::DMRROWp19,
          121 => Reg::DMRROWp20,
          122 => Reg::DMRROWp21,
          123 => Reg::DMRROWp22,
          124 => Reg::DMRROWp23,
          125 => Reg::DMRROWp24,
          126 => Reg::DMRROWp25,
          127 => Reg::DMRROWp26,
          128 => Reg::DMRROWp27,
          129 => Reg::DMRROWp28,
          130 => Reg::DMRROWp29,
          131 => Reg::DMRROWp30,
          132 => Reg::DMRROWp31,
          133 => Reg::DMRp0,
          134 => Reg::DMRp1,
          135 => Reg::DMRp2,
          136 => Reg::DMRp3,
          137 => Reg::F0,
          138 => Reg::F1,
          139 => Reg::F2,
          140 => Reg::F3,
          141 => Reg::F4,
          142 => Reg::F5,
          143 => Reg::F6,
          144 => Reg::F7,
          145 => Reg::F8,
          146 => Reg::F9,
          147 => Reg::F10,
          148 => Reg::F11,
          149 => Reg::F12,
          150 => Reg::F13,
          151 => Reg::F14,
          152 => Reg::F15,
          153 => Reg::F16,
          154 => Reg::F17,
          155 => Reg::F18,
          156 => Reg::F19,
          157 => Reg::F20,
          158 => Reg::F21,
          159 => Reg::F22,
          160 => Reg::F23,
          161 => Reg::F24,
          162 => Reg::F25,
          163 => Reg::F26,
          164 => Reg::F27,
          165 => Reg::F28,
          166 => Reg::F29,
          167 => Reg::F30,
          168 => Reg::F31,
          169 => Reg::FH0,
          170 => Reg::FH1,
          171 => Reg::FH2,
          172 => Reg::FH3,
          173 => Reg::FH4,
          174 => Reg::FH5,
          175 => Reg::FH6,
          176 => Reg::FH7,
          177 => Reg::FH8,
          178 => Reg::FH9,
          179 => Reg::FH10,
          180 => Reg::FH11,
          181 => Reg::FH12,
          182 => Reg::FH13,
          183 => Reg::FH14,
          184 => Reg::FH15,
          185 => Reg::FH16,
          186 => Reg::FH17,
          187 => Reg::FH18,
          188 => Reg::FH19,
          189 => Reg::FH20,
          190 => Reg::FH21,
          191 => Reg::FH22,
          192 => Reg::FH23,
          193 => Reg::FH24,
          194 => Reg::FH25,
          195 => Reg::FH26,
          196 => Reg::FH27,
          197 => Reg::FH28,
          198 => Reg::FH29,
          199 => Reg::FH30,
          200 => Reg::FH31,
          201 => Reg::FP8,
          202 => Reg::Fpair0,
          203 => Reg::Fpair2,
          204 => Reg::Fpair4,
          205 => Reg::Fpair6,
          206 => Reg::Fpair8,
          207 => Reg::Fpair10,
          208 => Reg::Fpair12,
          209 => Reg::Fpair14,
          210 => Reg::Fpair16,
          211 => Reg::Fpair18,
          212 => Reg::Fpair20,
          213 => Reg::Fpair22,
          214 => Reg::Fpair24,
          215 => Reg::Fpair26,
          216 => Reg::Fpair28,
          217 => Reg::Fpair30,
          218 => Reg::H0,
          219 => Reg::H1,
          220 => Reg::H2,
          221 => Reg::H3,
          222 => Reg::H4,
          223 => Reg::H5,
          224 => Reg::H6,
          225 => Reg::H7,
          226 => Reg::H8,
          227 => Reg::H9,
          228 => Reg::H10,
          229 => Reg::H11,
          230 => Reg::H12,
          231 => Reg::H13,
          232 => Reg::H14,
          233 => Reg::H15,
          234 => Reg::H16,
          235 => Reg::H17,
          236 => Reg::H18,
          237 => Reg::H19,
          238 => Reg::H20,
          239 => Reg::H21,
          240 => Reg::H22,
          241 => Reg::H23,
          242 => Reg::H24,
          243 => Reg::H25,
          244 => Reg::H26,
          245 => Reg::H27,
          246 => Reg::H28,
          247 => Reg::H29,
          248 => Reg::H30,
          249 => Reg::H31,
          250 => Reg::LR8,
          251 => Reg::R0,
          252 => Reg::R1,
          253 => Reg::R2,
          254 => Reg::R3,
          255 => Reg::R4,
          256 => Reg::R5,
          257 => Reg::R6,
          258 => Reg::R7,
          259 => Reg::R8,
          260 => Reg::R9,
          261 => Reg::R10,
          262 => Reg::R11,
          263 => Reg::R12,
          264 => Reg::R13,
          265 => Reg::R14,
          266 => Reg::R15,
          267 => Reg::R16,
          268 => Reg::R17,
          269 => Reg::R18,
          270 => Reg::R19,
          271 => Reg::R20,
          272 => Reg::R21,
          273 => Reg::R22,
          274 => Reg::R23,
          275 => Reg::R24,
          276 => Reg::R25,
          277 => Reg::R26,
          278 => Reg::R27,
          279 => Reg::R28,
          280 => Reg::R29,
          281 => Reg::R30,
          282 => Reg::R31,
          283 => Reg::S0,
          284 => Reg::S1,
          285 => Reg::S2,
          286 => Reg::S3,
          287 => Reg::S4,
          288 => Reg::S5,
          289 => Reg::S6,
          290 => Reg::S7,
          291 => Reg::S8,
          292 => Reg::S9,
          293 => Reg::S10,
          294 => Reg::S11,
          295 => Reg::S12,
          296 => Reg::S13,
          297 => Reg::S14,
          298 => Reg::S15,
          299 => Reg::S16,
          300 => Reg::S17,
          301 => Reg::S18,
          302 => Reg::S19,
          303 => Reg::S20,
          304 => Reg::S21,
          305 => Reg::S22,
          306 => Reg::S23,
          307 => Reg::S24,
          308 => Reg::S25,
          309 => Reg::S26,
          310 => Reg::S27,
          311 => Reg::S28,
          312 => Reg::S29,
          313 => Reg::S30,
          314 => Reg::S31,
          315 => Reg::UACC0,
          316 => Reg::UACC1,
          317 => Reg::UACC2,
          318 => Reg::UACC3,
          319 => Reg::UACC4,
          320 => Reg::UACC5,
          321 => Reg::UACC6,
          322 => Reg::UACC7,
          323 => Reg::V0,
          324 => Reg::V1,
          325 => Reg::V2,
          326 => Reg::V3,
          327 => Reg::V4,
          328 => Reg::V5,
          329 => Reg::V6,
          330 => Reg::V7,
          331 => Reg::V8,
          332 => Reg::V9,
          333 => Reg::V10,
          334 => Reg::V11,
          335 => Reg::V12,
          336 => Reg::V13,
          337 => Reg::V14,
          338 => Reg::V15,
          339 => Reg::V16,
          340 => Reg::V17,
          341 => Reg::V18,
          342 => Reg::V19,
          343 => Reg::V20,
          344 => Reg::V21,
          345 => Reg::V22,
          346 => Reg::V23,
          347 => Reg::V24,
          348 => Reg::V25,
          349 => Reg::V26,
          350 => Reg::V27,
          351 => Reg::V28,
          352 => Reg::V29,
          353 => Reg::V30,
          354 => Reg::V31,
          355 => Reg::VF0,
          356 => Reg::VF1,
          357 => Reg::VF2,
          358 => Reg::VF3,
          359 => Reg::VF4,
          360 => Reg::VF5,
          361 => Reg::VF6,
          362 => Reg::VF7,
          363 => Reg::VF8,
          364 => Reg::VF9,
          365 => Reg::VF10,
          366 => Reg::VF11,
          367 => Reg::VF12,
          368 => Reg::VF13,
          369 => Reg::VF14,
          370 => Reg::VF15,
          371 => Reg::VF16,
          372 => Reg::VF17,
          373 => Reg::VF18,
          374 => Reg::VF19,
          375 => Reg::VF20,
          376 => Reg::VF21,
          377 => Reg::VF22,
          378 => Reg::VF23,
          379 => Reg::VF24,
          380 => Reg::VF25,
          381 => Reg::VF26,
          382 => Reg::VF27,
          383 => Reg::VF28,
          384 => Reg::VF29,
          385 => Reg::VF30,
          386 => Reg::VF31,
          387 => Reg::VFH0,
          388 => Reg::VFH1,
          389 => Reg::VFH2,
          390 => Reg::VFH3,
          391 => Reg::VFH4,
          392 => Reg::VFH5,
          393 => Reg::VFH6,
          394 => Reg::VFH7,
          395 => Reg::VFH8,
          396 => Reg::VFH9,
          397 => Reg::VFH10,
          398 => Reg::VFH11,
          399 => Reg::VFH12,
          400 => Reg::VFH13,
          401 => Reg::VFH14,
          402 => Reg::VFH15,
          403 => Reg::VFH16,
          404 => Reg::VFH17,
          405 => Reg::VFH18,
          406 => Reg::VFH19,
          407 => Reg::VFH20,
          408 => Reg::VFH21,
          409 => Reg::VFH22,
          410 => Reg::VFH23,
          411 => Reg::VFH24,
          412 => Reg::VFH25,
          413 => Reg::VFH26,
          414 => Reg::VFH27,
          415 => Reg::VFH28,
          416 => Reg::VFH29,
          417 => Reg::VFH30,
          418 => Reg::VFH31,
          419 => Reg::VSL0,
          420 => Reg::VSL1,
          421 => Reg::VSL2,
          422 => Reg::VSL3,
          423 => Reg::VSL4,
          424 => Reg::VSL5,
          425 => Reg::VSL6,
          426 => Reg::VSL7,
          427 => Reg::VSL8,
          428 => Reg::VSL9,
          429 => Reg::VSL10,
          430 => Reg::VSL11,
          431 => Reg::VSL12,
          432 => Reg::VSL13,
          433 => Reg::VSL14,
          434 => Reg::VSL15,
          435 => Reg::VSL16,
          436 => Reg::VSL17,
          437 => Reg::VSL18,
          438 => Reg::VSL19,
          439 => Reg::VSL20,
          440 => Reg::VSL21,
          441 => Reg::VSL22,
          442 => Reg::VSL23,
          443 => Reg::VSL24,
          444 => Reg::VSL25,
          445 => Reg::VSL26,
          446 => Reg::VSL27,
          447 => Reg::VSL28,
          448 => Reg::VSL29,
          449 => Reg::VSL30,
          450 => Reg::VSL31,
          451 => Reg::VSRp0,
          452 => Reg::VSRp1,
          453 => Reg::VSRp2,
          454 => Reg::VSRp3,
          455 => Reg::VSRp4,
          456 => Reg::VSRp5,
          457 => Reg::VSRp6,
          458 => Reg::VSRp7,
          459 => Reg::VSRp8,
          460 => Reg::VSRp9,
          461 => Reg::VSRp10,
          462 => Reg::VSRp11,
          463 => Reg::VSRp12,
          464 => Reg::VSRp13,
          465 => Reg::VSRp14,
          466 => Reg::VSRp15,
          467 => Reg::VSRp16,
          468 => Reg::VSRp17,
          469 => Reg::VSRp18,
          470 => Reg::VSRp19,
          471 => Reg::VSRp20,
          472 => Reg::VSRp21,
          473 => Reg::VSRp22,
          474 => Reg::VSRp23,
          475 => Reg::VSRp24,
          476 => Reg::VSRp25,
          477 => Reg::VSRp26,
          478 => Reg::VSRp27,
          479 => Reg::VSRp28,
          480 => Reg::VSRp29,
          481 => Reg::VSRp30,
          482 => Reg::VSRp31,
          483 => Reg::VSX32,
          484 => Reg::VSX33,
          485 => Reg::VSX34,
          486 => Reg::VSX35,
          487 => Reg::VSX36,
          488 => Reg::VSX37,
          489 => Reg::VSX38,
          490 => Reg::VSX39,
          491 => Reg::VSX40,
          492 => Reg::VSX41,
          493 => Reg::VSX42,
          494 => Reg::VSX43,
          495 => Reg::VSX44,
          496 => Reg::VSX45,
          497 => Reg::VSX46,
          498 => Reg::VSX47,
          499 => Reg::VSX48,
          500 => Reg::VSX49,
          501 => Reg::VSX50,
          502 => Reg::VSX51,
          503 => Reg::VSX52,
          504 => Reg::VSX53,
          505 => Reg::VSX54,
          506 => Reg::VSX55,
          507 => Reg::VSX56,
          508 => Reg::VSX57,
          509 => Reg::VSX58,
          510 => Reg::VSX59,
          511 => Reg::VSX60,
          512 => Reg::VSX61,
          513 => Reg::VSX62,
          514 => Reg::VSX63,
          515 => Reg::WACC0,
          516 => Reg::WACC1,
          517 => Reg::WACC2,
          518 => Reg::WACC3,
          519 => Reg::WACC4,
          520 => Reg::WACC5,
          521 => Reg::WACC6,
          522 => Reg::WACC7,
          523 => Reg::WACC_HI0,
          524 => Reg::WACC_HI1,
          525 => Reg::WACC_HI2,
          526 => Reg::WACC_HI3,
          527 => Reg::WACC_HI4,
          528 => Reg::WACC_HI5,
          529 => Reg::WACC_HI6,
          530 => Reg::WACC_HI7,
          531 => Reg::X0,
          532 => Reg::X1,
          533 => Reg::X2,
          534 => Reg::X3,
          535 => Reg::X4,
          536 => Reg::X5,
          537 => Reg::X6,
          538 => Reg::X7,
          539 => Reg::X8,
          540 => Reg::X9,
          541 => Reg::X10,
          542 => Reg::X11,
          543 => Reg::X12,
          544 => Reg::X13,
          545 => Reg::X14,
          546 => Reg::X15,
          547 => Reg::X16,
          548 => Reg::X17,
          549 => Reg::X18,
          550 => Reg::X19,
          551 => Reg::X20,
          552 => Reg::X21,
          553 => Reg::X22,
          554 => Reg::X23,
          555 => Reg::X24,
          556 => Reg::X25,
          557 => Reg::X26,
          558 => Reg::X27,
          559 => Reg::X28,
          560 => Reg::X29,
          561 => Reg::X30,
          562 => Reg::X31,
          563 => Reg::ZERO8,
          564 => Reg::CR0EQ,
          565 => Reg::CR1EQ,
          566 => Reg::CR2EQ,
          567 => Reg::CR3EQ,
          568 => Reg::CR4EQ,
          569 => Reg::CR5EQ,
          570 => Reg::CR6EQ,
          571 => Reg::CR7EQ,
          572 => Reg::CR0GT,
          573 => Reg::CR1GT,
          574 => Reg::CR2GT,
          575 => Reg::CR3GT,
          576 => Reg::CR4GT,
          577 => Reg::CR5GT,
          578 => Reg::CR6GT,
          579 => Reg::CR7GT,
          580 => Reg::CR0LT,
          581 => Reg::CR1LT,
          582 => Reg::CR2LT,
          583 => Reg::CR3LT,
          584 => Reg::CR4LT,
          585 => Reg::CR5LT,
          586 => Reg::CR6LT,
          587 => Reg::CR7LT,
          588 => Reg::CR0UN,
          589 => Reg::CR1UN,
          590 => Reg::CR2UN,
          591 => Reg::CR3UN,
          592 => Reg::CR4UN,
          593 => Reg::CR5UN,
          594 => Reg::CR6UN,
          595 => Reg::CR7UN,
          596 => Reg::G8p0,
          597 => Reg::G8p1,
          598 => Reg::G8p2,
          599 => Reg::G8p3,
          600 => Reg::G8p4,
          601 => Reg::G8p5,
          602 => Reg::G8p6,
          603 => Reg::G8p7,
          604 => Reg::G8p8,
          605 => Reg::G8p9,
          606 => Reg::G8p10,
          607 => Reg::G8p11,
          608 => Reg::G8p12,
          609 => Reg::G8p13,
          610 => Reg::G8p14,
          611 => Reg::G8p15,
          612 => Reg::NUM_TARGET_REGS,
          _ => Reg::UNKNOWN(value),
        }
    }
}
