import enum
from typing import Iterator, Optional, Union

import lief.assembly


class OPCODE(enum.Enum):
    PHI = 0

    INLINEASM = 1

    INLINEASM_BR = 2

    CFI_INSTRUCTION = 3

    EH_LABEL = 4

    GC_LABEL = 5

    ANNOTATION_LABEL = 6

    KILL = 7

    EXTRACT_SUBREG = 8

    INSERT_SUBREG = 9

    IMPLICIT_DEF = 10

    INIT_UNDEF = 11

    SUBREG_TO_REG = 12

    COPY_TO_REGCLASS = 13

    DBG_VALUE = 14

    DBG_VALUE_LIST = 15

    DBG_INSTR_REF = 16

    DBG_PHI = 17

    DBG_LABEL = 18

    REG_SEQUENCE = 19

    COPY = 20

    BUNDLE = 21

    LIFETIME_START = 22

    LIFETIME_END = 23

    PSEUDO_PROBE = 24

    ARITH_FENCE = 25

    STACKMAP = 26

    FENTRY_CALL = 27

    PATCHPOINT = 28

    LOAD_STACK_GUARD = 29

    PREALLOCATED_SETUP = 30

    PREALLOCATED_ARG = 31

    STATEPOINT = 32

    LOCAL_ESCAPE = 33

    FAULTING_OP = 34

    PATCHABLE_OP = 35

    PATCHABLE_FUNCTION_ENTER = 36

    PATCHABLE_RET = 37

    PATCHABLE_FUNCTION_EXIT = 38

    PATCHABLE_TAIL_CALL = 39

    PATCHABLE_EVENT_CALL = 40

    PATCHABLE_TYPED_EVENT_CALL = 41

    ICALL_BRANCH_FUNNEL = 42

    FAKE_USE = 43

    MEMBARRIER = 44

    JUMP_TABLE_DEBUG_INFO = 45

    CONVERGENCECTRL_ENTRY = 46

    CONVERGENCECTRL_ANCHOR = 47

    CONVERGENCECTRL_LOOP = 48

    CONVERGENCECTRL_GLUE = 49

    G_ASSERT_SEXT = 50

    G_ASSERT_ZEXT = 51

    G_ASSERT_ALIGN = 52

    G_ADD = 53

    G_SUB = 54

    G_MUL = 55

    G_SDIV = 56

    G_UDIV = 57

    G_SREM = 58

    G_UREM = 59

    G_SDIVREM = 60

    G_UDIVREM = 61

    G_AND = 62

    G_OR = 63

    G_XOR = 64

    G_ABDS = 65

    G_ABDU = 66

    G_IMPLICIT_DEF = 67

    G_PHI = 68

    G_FRAME_INDEX = 69

    G_GLOBAL_VALUE = 70

    G_PTRAUTH_GLOBAL_VALUE = 71

    G_CONSTANT_POOL = 72

    G_EXTRACT = 73

    G_UNMERGE_VALUES = 74

    G_INSERT = 75

    G_MERGE_VALUES = 76

    G_BUILD_VECTOR = 77

    G_BUILD_VECTOR_TRUNC = 78

    G_CONCAT_VECTORS = 79

    G_PTRTOINT = 80

    G_INTTOPTR = 81

    G_BITCAST = 82

    G_FREEZE = 83

    G_CONSTANT_FOLD_BARRIER = 84

    G_INTRINSIC_FPTRUNC_ROUND = 85

    G_INTRINSIC_TRUNC = 86

    G_INTRINSIC_ROUND = 87

    G_INTRINSIC_LRINT = 88

    G_INTRINSIC_LLRINT = 89

    G_INTRINSIC_ROUNDEVEN = 90

    G_READCYCLECOUNTER = 91

    G_READSTEADYCOUNTER = 92

    G_LOAD = 93

    G_SEXTLOAD = 94

    G_ZEXTLOAD = 95

    G_INDEXED_LOAD = 96

    G_INDEXED_SEXTLOAD = 97

    G_INDEXED_ZEXTLOAD = 98

    G_STORE = 99

    G_INDEXED_STORE = 100

    G_ATOMIC_CMPXCHG_WITH_SUCCESS = 101

    G_ATOMIC_CMPXCHG = 102

    G_ATOMICRMW_XCHG = 103

    G_ATOMICRMW_ADD = 104

    G_ATOMICRMW_SUB = 105

    G_ATOMICRMW_AND = 106

    G_ATOMICRMW_NAND = 107

    G_ATOMICRMW_OR = 108

    G_ATOMICRMW_XOR = 109

    G_ATOMICRMW_MAX = 110

    G_ATOMICRMW_MIN = 111

    G_ATOMICRMW_UMAX = 112

    G_ATOMICRMW_UMIN = 113

    G_ATOMICRMW_FADD = 114

    G_ATOMICRMW_FSUB = 115

    G_ATOMICRMW_FMAX = 116

    G_ATOMICRMW_FMIN = 117

    G_ATOMICRMW_UINC_WRAP = 118

    G_ATOMICRMW_UDEC_WRAP = 119

    G_ATOMICRMW_USUB_COND = 120

    G_ATOMICRMW_USUB_SAT = 121

    G_FENCE = 122

    G_PREFETCH = 123

    G_BRCOND = 124

    G_BRINDIRECT = 125

    G_INVOKE_REGION_START = 126

    G_INTRINSIC = 127

    G_INTRINSIC_W_SIDE_EFFECTS = 128

    G_INTRINSIC_CONVERGENT = 129

    G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS = 130

    G_ANYEXT = 131

    G_TRUNC = 132

    G_CONSTANT = 133

    G_FCONSTANT = 134

    G_VASTART = 135

    G_VAARG = 136

    G_SEXT = 137

    G_SEXT_INREG = 138

    G_ZEXT = 139

    G_SHL = 140

    G_LSHR = 141

    G_ASHR = 142

    G_FSHL = 143

    G_FSHR = 144

    G_ROTR = 145

    G_ROTL = 146

    G_ICMP = 147

    G_FCMP = 148

    G_SCMP = 149

    G_UCMP = 150

    G_SELECT = 151

    G_UADDO = 152

    G_UADDE = 153

    G_USUBO = 154

    G_USUBE = 155

    G_SADDO = 156

    G_SADDE = 157

    G_SSUBO = 158

    G_SSUBE = 159

    G_UMULO = 160

    G_SMULO = 161

    G_UMULH = 162

    G_SMULH = 163

    G_UADDSAT = 164

    G_SADDSAT = 165

    G_USUBSAT = 166

    G_SSUBSAT = 167

    G_USHLSAT = 168

    G_SSHLSAT = 169

    G_SMULFIX = 170

    G_UMULFIX = 171

    G_SMULFIXSAT = 172

    G_UMULFIXSAT = 173

    G_SDIVFIX = 174

    G_UDIVFIX = 175

    G_SDIVFIXSAT = 176

    G_UDIVFIXSAT = 177

    G_FADD = 178

    G_FSUB = 179

    G_FMUL = 180

    G_FMA = 181

    G_FMAD = 182

    G_FDIV = 183

    G_FREM = 184

    G_FPOW = 185

    G_FPOWI = 186

    G_FEXP = 187

    G_FEXP2 = 188

    G_FEXP10 = 189

    G_FLOG = 190

    G_FLOG2 = 191

    G_FLOG10 = 192

    G_FLDEXP = 193

    G_FFREXP = 194

    G_FNEG = 195

    G_FPEXT = 196

    G_FPTRUNC = 197

    G_FPTOSI = 198

    G_FPTOUI = 199

    G_SITOFP = 200

    G_UITOFP = 201

    G_FPTOSI_SAT = 202

    G_FPTOUI_SAT = 203

    G_FABS = 204

    G_FCOPYSIGN = 205

    G_IS_FPCLASS = 206

    G_FCANONICALIZE = 207

    G_FMINNUM = 208

    G_FMAXNUM = 209

    G_FMINNUM_IEEE = 210

    G_FMAXNUM_IEEE = 211

    G_FMINIMUM = 212

    G_FMAXIMUM = 213

    G_GET_FPENV = 214

    G_SET_FPENV = 215

    G_RESET_FPENV = 216

    G_GET_FPMODE = 217

    G_SET_FPMODE = 218

    G_RESET_FPMODE = 219

    G_PTR_ADD = 220

    G_PTRMASK = 221

    G_SMIN = 222

    G_SMAX = 223

    G_UMIN = 224

    G_UMAX = 225

    G_ABS = 226

    G_LROUND = 227

    G_LLROUND = 228

    G_BR = 229

    G_BRJT = 230

    G_VSCALE = 231

    G_INSERT_SUBVECTOR = 232

    G_EXTRACT_SUBVECTOR = 233

    G_INSERT_VECTOR_ELT = 234

    G_EXTRACT_VECTOR_ELT = 235

    G_SHUFFLE_VECTOR = 236

    G_SPLAT_VECTOR = 237

    G_STEP_VECTOR = 238

    G_VECTOR_COMPRESS = 239

    G_CTTZ = 240

    G_CTTZ_ZERO_UNDEF = 241

    G_CTLZ = 242

    G_CTLZ_ZERO_UNDEF = 243

    G_CTPOP = 244

    G_BSWAP = 245

    G_BITREVERSE = 246

    G_FCEIL = 247

    G_FCOS = 248

    G_FSIN = 249

    G_FSINCOS = 250

    G_FTAN = 251

    G_FACOS = 252

    G_FASIN = 253

    G_FATAN = 254

    G_FATAN2 = 255

    G_FCOSH = 256

    G_FSINH = 257

    G_FTANH = 258

    G_FSQRT = 259

    G_FFLOOR = 260

    G_FRINT = 261

    G_FNEARBYINT = 262

    G_ADDRSPACE_CAST = 263

    G_BLOCK_ADDR = 264

    G_JUMP_TABLE = 265

    G_DYN_STACKALLOC = 266

    G_STACKSAVE = 267

    G_STACKRESTORE = 268

    G_STRICT_FADD = 269

    G_STRICT_FSUB = 270

    G_STRICT_FMUL = 271

    G_STRICT_FDIV = 272

    G_STRICT_FREM = 273

    G_STRICT_FMA = 274

    G_STRICT_FSQRT = 275

    G_STRICT_FLDEXP = 276

    G_READ_REGISTER = 277

    G_WRITE_REGISTER = 278

    G_MEMCPY = 279

    G_MEMCPY_INLINE = 280

    G_MEMMOVE = 281

    G_MEMSET = 282

    G_BZERO = 283

    G_TRAP = 284

    G_DEBUGTRAP = 285

    G_UBSANTRAP = 286

    G_VECREDUCE_SEQ_FADD = 287

    G_VECREDUCE_SEQ_FMUL = 288

    G_VECREDUCE_FADD = 289

    G_VECREDUCE_FMUL = 290

    G_VECREDUCE_FMAX = 291

    G_VECREDUCE_FMIN = 292

    G_VECREDUCE_FMAXIMUM = 293

    G_VECREDUCE_FMINIMUM = 294

    G_VECREDUCE_ADD = 295

    G_VECREDUCE_MUL = 296

    G_VECREDUCE_AND = 297

    G_VECREDUCE_OR = 298

    G_VECREDUCE_XOR = 299

    G_VECREDUCE_SMAX = 300

    G_VECREDUCE_SMIN = 301

    G_VECREDUCE_UMAX = 302

    G_VECREDUCE_UMIN = 303

    G_SBFX = 304

    G_UBFX = 305

    ABSMacro = 306

    ADJCALLSTACKDOWN = 307

    ADJCALLSTACKUP = 308

    AND_V_D_PSEUDO = 309

    AND_V_H_PSEUDO = 310

    AND_V_W_PSEUDO = 311

    ATOMIC_CMP_SWAP_I16 = 312

    ATOMIC_CMP_SWAP_I16_POSTRA = 313

    ATOMIC_CMP_SWAP_I32 = 314

    ATOMIC_CMP_SWAP_I32_POSTRA = 315

    ATOMIC_CMP_SWAP_I64 = 316

    ATOMIC_CMP_SWAP_I64_POSTRA = 317

    ATOMIC_CMP_SWAP_I8 = 318

    ATOMIC_CMP_SWAP_I8_POSTRA = 319

    ATOMIC_LOAD_ADD_I16 = 320

    ATOMIC_LOAD_ADD_I16_POSTRA = 321

    ATOMIC_LOAD_ADD_I32 = 322

    ATOMIC_LOAD_ADD_I32_POSTRA = 323

    ATOMIC_LOAD_ADD_I64 = 324

    ATOMIC_LOAD_ADD_I64_POSTRA = 325

    ATOMIC_LOAD_ADD_I8 = 326

    ATOMIC_LOAD_ADD_I8_POSTRA = 327

    ATOMIC_LOAD_AND_I16 = 328

    ATOMIC_LOAD_AND_I16_POSTRA = 329

    ATOMIC_LOAD_AND_I32 = 330

    ATOMIC_LOAD_AND_I32_POSTRA = 331

    ATOMIC_LOAD_AND_I64 = 332

    ATOMIC_LOAD_AND_I64_POSTRA = 333

    ATOMIC_LOAD_AND_I8 = 334

    ATOMIC_LOAD_AND_I8_POSTRA = 335

    ATOMIC_LOAD_MAX_I16 = 336

    ATOMIC_LOAD_MAX_I16_POSTRA = 337

    ATOMIC_LOAD_MAX_I32 = 338

    ATOMIC_LOAD_MAX_I32_POSTRA = 339

    ATOMIC_LOAD_MAX_I64 = 340

    ATOMIC_LOAD_MAX_I64_POSTRA = 341

    ATOMIC_LOAD_MAX_I8 = 342

    ATOMIC_LOAD_MAX_I8_POSTRA = 343

    ATOMIC_LOAD_MIN_I16 = 344

    ATOMIC_LOAD_MIN_I16_POSTRA = 345

    ATOMIC_LOAD_MIN_I32 = 346

    ATOMIC_LOAD_MIN_I32_POSTRA = 347

    ATOMIC_LOAD_MIN_I64 = 348

    ATOMIC_LOAD_MIN_I64_POSTRA = 349

    ATOMIC_LOAD_MIN_I8 = 350

    ATOMIC_LOAD_MIN_I8_POSTRA = 351

    ATOMIC_LOAD_NAND_I16 = 352

    ATOMIC_LOAD_NAND_I16_POSTRA = 353

    ATOMIC_LOAD_NAND_I32 = 354

    ATOMIC_LOAD_NAND_I32_POSTRA = 355

    ATOMIC_LOAD_NAND_I64 = 356

    ATOMIC_LOAD_NAND_I64_POSTRA = 357

    ATOMIC_LOAD_NAND_I8 = 358

    ATOMIC_LOAD_NAND_I8_POSTRA = 359

    ATOMIC_LOAD_OR_I16 = 360

    ATOMIC_LOAD_OR_I16_POSTRA = 361

    ATOMIC_LOAD_OR_I32 = 362

    ATOMIC_LOAD_OR_I32_POSTRA = 363

    ATOMIC_LOAD_OR_I64 = 364

    ATOMIC_LOAD_OR_I64_POSTRA = 365

    ATOMIC_LOAD_OR_I8 = 366

    ATOMIC_LOAD_OR_I8_POSTRA = 367

    ATOMIC_LOAD_SUB_I16 = 368

    ATOMIC_LOAD_SUB_I16_POSTRA = 369

    ATOMIC_LOAD_SUB_I32 = 370

    ATOMIC_LOAD_SUB_I32_POSTRA = 371

    ATOMIC_LOAD_SUB_I64 = 372

    ATOMIC_LOAD_SUB_I64_POSTRA = 373

    ATOMIC_LOAD_SUB_I8 = 374

    ATOMIC_LOAD_SUB_I8_POSTRA = 375

    ATOMIC_LOAD_UMAX_I16 = 376

    ATOMIC_LOAD_UMAX_I16_POSTRA = 377

    ATOMIC_LOAD_UMAX_I32 = 378

    ATOMIC_LOAD_UMAX_I32_POSTRA = 379

    ATOMIC_LOAD_UMAX_I64 = 380

    ATOMIC_LOAD_UMAX_I64_POSTRA = 381

    ATOMIC_LOAD_UMAX_I8 = 382

    ATOMIC_LOAD_UMAX_I8_POSTRA = 383

    ATOMIC_LOAD_UMIN_I16 = 384

    ATOMIC_LOAD_UMIN_I16_POSTRA = 385

    ATOMIC_LOAD_UMIN_I32 = 386

    ATOMIC_LOAD_UMIN_I32_POSTRA = 387

    ATOMIC_LOAD_UMIN_I64 = 388

    ATOMIC_LOAD_UMIN_I64_POSTRA = 389

    ATOMIC_LOAD_UMIN_I8 = 390

    ATOMIC_LOAD_UMIN_I8_POSTRA = 391

    ATOMIC_LOAD_XOR_I16 = 392

    ATOMIC_LOAD_XOR_I16_POSTRA = 393

    ATOMIC_LOAD_XOR_I32 = 394

    ATOMIC_LOAD_XOR_I32_POSTRA = 395

    ATOMIC_LOAD_XOR_I64 = 396

    ATOMIC_LOAD_XOR_I64_POSTRA = 397

    ATOMIC_LOAD_XOR_I8 = 398

    ATOMIC_LOAD_XOR_I8_POSTRA = 399

    ATOMIC_SWAP_I16 = 400

    ATOMIC_SWAP_I16_POSTRA = 401

    ATOMIC_SWAP_I32 = 402

    ATOMIC_SWAP_I32_POSTRA = 403

    ATOMIC_SWAP_I64 = 404

    ATOMIC_SWAP_I64_POSTRA = 405

    ATOMIC_SWAP_I8 = 406

    ATOMIC_SWAP_I8_POSTRA = 407

    B = 408

    BAL_BR = 409

    BAL_BR_MM = 410

    BEQLImmMacro = 411

    BGE = 412

    BGEImmMacro = 413

    BGEL = 414

    BGELImmMacro = 415

    BGEU = 416

    BGEUImmMacro = 417

    BGEUL = 418

    BGEULImmMacro = 419

    BGT = 420

    BGTImmMacro = 421

    BGTL = 422

    BGTLImmMacro = 423

    BGTU = 424

    BGTUImmMacro = 425

    BGTUL = 426

    BGTULImmMacro = 427

    BLE = 428

    BLEImmMacro = 429

    BLEL = 430

    BLELImmMacro = 431

    BLEU = 432

    BLEUImmMacro = 433

    BLEUL = 434

    BLEULImmMacro = 435

    BLT = 436

    BLTImmMacro = 437

    BLTL = 438

    BLTLImmMacro = 439

    BLTU = 440

    BLTUImmMacro = 441

    BLTUL = 442

    BLTULImmMacro = 443

    BNELImmMacro = 444

    BPOSGE32_PSEUDO = 445

    BSEL_D_PSEUDO = 446

    BSEL_FD_PSEUDO = 447

    BSEL_FW_PSEUDO = 448

    BSEL_H_PSEUDO = 449

    BSEL_W_PSEUDO = 450

    B_MM = 451

    B_MMR6_Pseudo = 452

    B_MM_Pseudo = 453

    BeqImm = 454

    BneImm = 455

    BteqzT8CmpX16 = 456

    BteqzT8CmpiX16 = 457

    BteqzT8SltX16 = 458

    BteqzT8SltiX16 = 459

    BteqzT8SltiuX16 = 460

    BteqzT8SltuX16 = 461

    BtnezT8CmpX16 = 462

    BtnezT8CmpiX16 = 463

    BtnezT8SltX16 = 464

    BtnezT8SltiX16 = 465

    BtnezT8SltiuX16 = 466

    BtnezT8SltuX16 = 467

    BuildPairF64 = 468

    BuildPairF64_64 = 469

    CFTC1 = 470

    CONSTPOOL_ENTRY = 471

    COPY_FD_PSEUDO = 472

    COPY_FW_PSEUDO = 473

    CTTC1 = 474

    Constant32 = 475

    DMULImmMacro = 476

    DMULMacro = 477

    DMULOMacro = 478

    DMULOUMacro = 479

    DROL = 480

    DROLImm = 481

    DROR = 482

    DRORImm = 483

    DSDivIMacro = 484

    DSDivMacro = 485

    DSRemIMacro = 486

    DSRemMacro = 487

    DUDivIMacro = 488

    DUDivMacro = 489

    DURemIMacro = 490

    DURemMacro = 491

    ERet = 492

    ExtractElementF64 = 493

    ExtractElementF64_64 = 494

    FABS_D = 495

    FABS_W = 496

    FEXP2_D_1_PSEUDO = 497

    FEXP2_W_1_PSEUDO = 498

    FILL_FD_PSEUDO = 499

    FILL_FW_PSEUDO = 500

    GotPrologue16 = 501

    INSERT_B_VIDX64_PSEUDO = 502

    INSERT_B_VIDX_PSEUDO = 503

    INSERT_D_VIDX64_PSEUDO = 504

    INSERT_D_VIDX_PSEUDO = 505

    INSERT_FD_PSEUDO = 506

    INSERT_FD_VIDX64_PSEUDO = 507

    INSERT_FD_VIDX_PSEUDO = 508

    INSERT_FW_PSEUDO = 509

    INSERT_FW_VIDX64_PSEUDO = 510

    INSERT_FW_VIDX_PSEUDO = 511

    INSERT_H_VIDX64_PSEUDO = 512

    INSERT_H_VIDX_PSEUDO = 513

    INSERT_W_VIDX64_PSEUDO = 514

    INSERT_W_VIDX_PSEUDO = 515

    JALR64Pseudo = 516

    JALRHB64Pseudo = 517

    JALRHBPseudo = 518

    JALRPseudo = 519

    JAL_MMR6 = 520

    JalOneReg = 521

    JalTwoReg = 522

    LDMacro = 523

    LDR_D = 524

    LDR_W = 525

    LD_F16 = 526

    LOAD_ACC128 = 527

    LOAD_ACC64 = 528

    LOAD_ACC64DSP = 529

    LOAD_CCOND_DSP = 530

    LONG_BRANCH_ADDiu = 531

    LONG_BRANCH_ADDiu2Op = 532

    LONG_BRANCH_DADDiu = 533

    LONG_BRANCH_DADDiu2Op = 534

    LONG_BRANCH_LUi = 535

    LONG_BRANCH_LUi2Op = 536

    LONG_BRANCH_LUi2Op_64 = 537

    LWM_MM = 538

    LoadAddrImm32 = 539

    LoadAddrImm64 = 540

    LoadAddrReg32 = 541

    LoadAddrReg64 = 542

    LoadImm32 = 543

    LoadImm64 = 544

    LoadImmDoubleFGR = 545

    LoadImmDoubleFGR_32 = 546

    LoadImmDoubleGPR = 547

    LoadImmSingleFGR = 548

    LoadImmSingleGPR = 549

    LwConstant32 = 550

    MFTACX = 551

    MFTC0 = 552

    MFTC1 = 553

    MFTDSP = 554

    MFTGPR = 555

    MFTHC1 = 556

    MFTHI = 557

    MFTLO = 558

    MIPSeh_return32 = 559

    MIPSeh_return64 = 560

    MSA_FP_EXTEND_D_PSEUDO = 561

    MSA_FP_EXTEND_W_PSEUDO = 562

    MSA_FP_ROUND_D_PSEUDO = 563

    MSA_FP_ROUND_W_PSEUDO = 564

    MTTACX = 565

    MTTC0 = 566

    MTTC1 = 567

    MTTDSP = 568

    MTTGPR = 569

    MTTHC1 = 570

    MTTHI = 571

    MTTLO = 572

    MULImmMacro = 573

    MULOMacro = 574

    MULOUMacro = 575

    MultRxRy16 = 576

    MultRxRyRz16 = 577

    MultuRxRy16 = 578

    MultuRxRyRz16 = 579

    NOP = 580

    NORImm = 581

    NORImm64 = 582

    NOR_V_D_PSEUDO = 583

    NOR_V_H_PSEUDO = 584

    NOR_V_W_PSEUDO = 585

    OR_V_D_PSEUDO = 586

    OR_V_H_PSEUDO = 587

    OR_V_W_PSEUDO = 588

    PseudoCMPU_EQ_QB = 589

    PseudoCMPU_LE_QB = 590

    PseudoCMPU_LT_QB = 591

    PseudoCMP_EQ_PH = 592

    PseudoCMP_LE_PH = 593

    PseudoCMP_LT_PH = 594

    PseudoCVT_D32_W = 595

    PseudoCVT_D64_L = 596

    PseudoCVT_D64_W = 597

    PseudoCVT_S_L = 598

    PseudoCVT_S_W = 599

    PseudoDMULT = 600

    PseudoDMULTu = 601

    PseudoDSDIV = 602

    PseudoDUDIV = 603

    PseudoD_SELECT_I = 604

    PseudoD_SELECT_I64 = 605

    PseudoIndirectBranch = 606

    PseudoIndirectBranch64 = 607

    PseudoIndirectBranch64R6 = 608

    PseudoIndirectBranchR6 = 609

    PseudoIndirectBranch_MM = 610

    PseudoIndirectBranch_MMR6 = 611

    PseudoIndirectHazardBranch = 612

    PseudoIndirectHazardBranch64 = 613

    PseudoIndrectHazardBranch64R6 = 614

    PseudoIndrectHazardBranchR6 = 615

    PseudoMADD = 616

    PseudoMADDU = 617

    PseudoMADDU_MM = 618

    PseudoMADD_MM = 619

    PseudoMFHI = 620

    PseudoMFHI64 = 621

    PseudoMFHI_MM = 622

    PseudoMFLO = 623

    PseudoMFLO64 = 624

    PseudoMFLO_MM = 625

    PseudoMSUB = 626

    PseudoMSUBU = 627

    PseudoMSUBU_MM = 628

    PseudoMSUB_MM = 629

    PseudoMTLOHI = 630

    PseudoMTLOHI64 = 631

    PseudoMTLOHI_DSP = 632

    PseudoMTLOHI_MM = 633

    PseudoMULT = 634

    PseudoMULT_MM = 635

    PseudoMULTu = 636

    PseudoMULTu_MM = 637

    PseudoPICK_PH = 638

    PseudoPICK_QB = 639

    PseudoReturn = 640

    PseudoReturn64 = 641

    PseudoSDIV = 642

    PseudoSELECTFP_F_D32 = 643

    PseudoSELECTFP_F_D64 = 644

    PseudoSELECTFP_F_I = 645

    PseudoSELECTFP_F_I64 = 646

    PseudoSELECTFP_F_S = 647

    PseudoSELECTFP_T_D32 = 648

    PseudoSELECTFP_T_D64 = 649

    PseudoSELECTFP_T_I = 650

    PseudoSELECTFP_T_I64 = 651

    PseudoSELECTFP_T_S = 652

    PseudoSELECT_D32 = 653

    PseudoSELECT_D64 = 654

    PseudoSELECT_I = 655

    PseudoSELECT_I64 = 656

    PseudoSELECT_S = 657

    PseudoTRUNC_W_D = 658

    PseudoTRUNC_W_D32 = 659

    PseudoTRUNC_W_S = 660

    PseudoUDIV = 661

    ROL = 662

    ROLImm = 663

    ROR = 664

    RORImm = 665

    RetRA = 666

    RetRA16 = 667

    SDC1_M1 = 668

    SDIV_MM_Pseudo = 669

    SDMacro = 670

    SDivIMacro = 671

    SDivMacro = 672

    SEQIMacro = 673

    SEQMacro = 674

    SGE = 675

    SGEImm = 676

    SGEImm64 = 677

    SGEU = 678

    SGEUImm = 679

    SGEUImm64 = 680

    SGTImm = 681

    SGTImm64 = 682

    SGTUImm = 683

    SGTUImm64 = 684

    SLE = 685

    SLEImm = 686

    SLEImm64 = 687

    SLEU = 688

    SLEUImm = 689

    SLEUImm64 = 690

    SLTImm64 = 691

    SLTUImm64 = 692

    SNEIMacro = 693

    SNEMacro = 694

    SNZ_B_PSEUDO = 695

    SNZ_D_PSEUDO = 696

    SNZ_H_PSEUDO = 697

    SNZ_V_PSEUDO = 698

    SNZ_W_PSEUDO = 699

    SRemIMacro = 700

    SRemMacro = 701

    STORE_ACC128 = 702

    STORE_ACC64 = 703

    STORE_ACC64DSP = 704

    STORE_CCOND_DSP = 705

    STR_D = 706

    STR_W = 707

    ST_F16 = 708

    SWM_MM = 709

    SZ_B_PSEUDO = 710

    SZ_D_PSEUDO = 711

    SZ_H_PSEUDO = 712

    SZ_V_PSEUDO = 713

    SZ_W_PSEUDO = 714

    SaaAddr = 715

    SaadAddr = 716

    SelBeqZ = 717

    SelBneZ = 718

    SelTBteqZCmp = 719

    SelTBteqZCmpi = 720

    SelTBteqZSlt = 721

    SelTBteqZSlti = 722

    SelTBteqZSltiu = 723

    SelTBteqZSltu = 724

    SelTBtneZCmp = 725

    SelTBtneZCmpi = 726

    SelTBtneZSlt = 727

    SelTBtneZSlti = 728

    SelTBtneZSltiu = 729

    SelTBtneZSltu = 730

    SltCCRxRy16 = 731

    SltiCCRxImmX16 = 732

    SltiuCCRxImmX16 = 733

    SltuCCRxRy16 = 734

    SltuRxRyRz16 = 735

    TAILCALL = 736

    TAILCALL64R6REG = 737

    TAILCALLHB64R6REG = 738

    TAILCALLHBR6REG = 739

    TAILCALLR6REG = 740

    TAILCALLREG = 741

    TAILCALLREG64 = 742

    TAILCALLREGHB = 743

    TAILCALLREGHB64 = 744

    TAILCALLREG_MM = 745

    TAILCALLREG_MMR6 = 746

    TAILCALL_MM = 747

    TAILCALL_MMR6 = 748

    TRAP = 749

    TRAP_MM = 750

    UDIV_MM_Pseudo = 751

    UDivIMacro = 752

    UDivMacro = 753

    URemIMacro = 754

    URemMacro = 755

    Ulh = 756

    Ulhu = 757

    Ulw = 758

    Ush = 759

    Usw = 760

    XOR_V_D_PSEUDO = 761

    XOR_V_H_PSEUDO = 762

    XOR_V_W_PSEUDO = 763

    ABSQ_S_PH = 764

    ABSQ_S_PH_MM = 765

    ABSQ_S_QB = 766

    ABSQ_S_QB_MMR2 = 767

    ABSQ_S_W = 768

    ABSQ_S_W_MM = 769

    ADD = 770

    ADDIUPC = 771

    ADDIUPC_MM = 772

    ADDIUPC_MMR6 = 773

    ADDIUR1SP_MM = 774

    ADDIUR2_MM = 775

    ADDIUS5_MM = 776

    ADDIUSP_MM = 777

    ADDIU_MMR6 = 778

    ADDQH_PH = 779

    ADDQH_PH_MMR2 = 780

    ADDQH_R_PH = 781

    ADDQH_R_PH_MMR2 = 782

    ADDQH_R_W = 783

    ADDQH_R_W_MMR2 = 784

    ADDQH_W = 785

    ADDQH_W_MMR2 = 786

    ADDQ_PH = 787

    ADDQ_PH_MM = 788

    ADDQ_S_PH = 789

    ADDQ_S_PH_MM = 790

    ADDQ_S_W = 791

    ADDQ_S_W_MM = 792

    ADDR_PS64 = 793

    ADDSC = 794

    ADDSC_MM = 795

    ADDS_A_B = 796

    ADDS_A_D = 797

    ADDS_A_H = 798

    ADDS_A_W = 799

    ADDS_S_B = 800

    ADDS_S_D = 801

    ADDS_S_H = 802

    ADDS_S_W = 803

    ADDS_U_B = 804

    ADDS_U_D = 805

    ADDS_U_H = 806

    ADDS_U_W = 807

    ADDU16_MM = 808

    ADDU16_MMR6 = 809

    ADDUH_QB = 810

    ADDUH_QB_MMR2 = 811

    ADDUH_R_QB = 812

    ADDUH_R_QB_MMR2 = 813

    ADDU_MMR6 = 814

    ADDU_PH = 815

    ADDU_PH_MMR2 = 816

    ADDU_QB = 817

    ADDU_QB_MM = 818

    ADDU_S_PH = 819

    ADDU_S_PH_MMR2 = 820

    ADDU_S_QB = 821

    ADDU_S_QB_MM = 822

    ADDVI_B = 823

    ADDVI_D = 824

    ADDVI_H = 825

    ADDVI_W = 826

    ADDV_B = 827

    ADDV_D = 828

    ADDV_H = 829

    ADDV_W = 830

    ADDWC = 831

    ADDWC_MM = 832

    ADD_A_B = 833

    ADD_A_D = 834

    ADD_A_H = 835

    ADD_A_W = 836

    ADD_MM = 837

    ADD_MMR6 = 838

    ADDi = 839

    ADDi_MM = 840

    ADDiu = 841

    ADDiu_MM = 842

    ADDu = 843

    ADDu_MM = 844

    ALIGN = 845

    ALIGN_MMR6 = 846

    ALUIPC = 847

    ALUIPC_MMR6 = 848

    AND = 849

    AND16_MM = 850

    AND16_MMR6 = 851

    AND64 = 852

    ANDI16_MM = 853

    ANDI16_MMR6 = 854

    ANDI_B = 855

    ANDI_MMR6 = 856

    AND_MM = 857

    AND_MMR6 = 858

    AND_V = 859

    ANDi = 860

    ANDi64 = 861

    ANDi_MM = 862

    APPEND = 863

    APPEND_MMR2 = 864

    ASUB_S_B = 865

    ASUB_S_D = 866

    ASUB_S_H = 867

    ASUB_S_W = 868

    ASUB_U_B = 869

    ASUB_U_D = 870

    ASUB_U_H = 871

    ASUB_U_W = 872

    AUI = 873

    AUIPC = 874

    AUIPC_MMR6 = 875

    AUI_MMR6 = 876

    AVER_S_B = 877

    AVER_S_D = 878

    AVER_S_H = 879

    AVER_S_W = 880

    AVER_U_B = 881

    AVER_U_D = 882

    AVER_U_H = 883

    AVER_U_W = 884

    AVE_S_B = 885

    AVE_S_D = 886

    AVE_S_H = 887

    AVE_S_W = 888

    AVE_U_B = 889

    AVE_U_D = 890

    AVE_U_H = 891

    AVE_U_W = 892

    AddiuRxImmX16 = 893

    AddiuRxPcImmX16 = 894

    AddiuRxRxImm16 = 895

    AddiuRxRxImmX16 = 896

    AddiuRxRyOffMemX16 = 897

    AddiuSpImm16 = 898

    AddiuSpImmX16 = 899

    AdduRxRyRz16 = 900

    AndRxRxRy16 = 901

    B16_MM = 902

    BADDu = 903

    BAL = 904

    BALC = 905

    BALC_MMR6 = 906

    BALIGN = 907

    BALIGN_MMR2 = 908

    BBIT0 = 909

    BBIT032 = 910

    BBIT1 = 911

    BBIT132 = 912

    BC = 913

    BC16_MMR6 = 914

    BC1EQZ = 915

    BC1EQZC_MMR6 = 916

    BC1F = 917

    BC1FL = 918

    BC1F_MM = 919

    BC1NEZ = 920

    BC1NEZC_MMR6 = 921

    BC1T = 922

    BC1TL = 923

    BC1T_MM = 924

    BC2EQZ = 925

    BC2EQZC_MMR6 = 926

    BC2NEZ = 927

    BC2NEZC_MMR6 = 928

    BCLRI_B = 929

    BCLRI_D = 930

    BCLRI_H = 931

    BCLRI_W = 932

    BCLR_B = 933

    BCLR_D = 934

    BCLR_H = 935

    BCLR_W = 936

    BC_MMR6 = 937

    BEQ = 938

    BEQ64 = 939

    BEQC = 940

    BEQC64 = 941

    BEQC_MMR6 = 942

    BEQL = 943

    BEQZ16_MM = 944

    BEQZALC = 945

    BEQZALC_MMR6 = 946

    BEQZC = 947

    BEQZC16_MMR6 = 948

    BEQZC64 = 949

    BEQZC_MM = 950

    BEQZC_MMR6 = 951

    BEQ_MM = 952

    BGEC = 953

    BGEC64 = 954

    BGEC_MMR6 = 955

    BGEUC = 956

    BGEUC64 = 957

    BGEUC_MMR6 = 958

    BGEZ = 959

    BGEZ64 = 960

    BGEZAL = 961

    BGEZALC = 962

    BGEZALC_MMR6 = 963

    BGEZALL = 964

    BGEZALS_MM = 965

    BGEZAL_MM = 966

    BGEZC = 967

    BGEZC64 = 968

    BGEZC_MMR6 = 969

    BGEZL = 970

    BGEZ_MM = 971

    BGTZ = 972

    BGTZ64 = 973

    BGTZALC = 974

    BGTZALC_MMR6 = 975

    BGTZC = 976

    BGTZC64 = 977

    BGTZC_MMR6 = 978

    BGTZL = 979

    BGTZ_MM = 980

    BINSLI_B = 981

    BINSLI_D = 982

    BINSLI_H = 983

    BINSLI_W = 984

    BINSL_B = 985

    BINSL_D = 986

    BINSL_H = 987

    BINSL_W = 988

    BINSRI_B = 989

    BINSRI_D = 990

    BINSRI_H = 991

    BINSRI_W = 992

    BINSR_B = 993

    BINSR_D = 994

    BINSR_H = 995

    BINSR_W = 996

    BITREV = 997

    BITREV_MM = 998

    BITSWAP = 999

    BITSWAP_MMR6 = 1000

    BLEZ = 1001

    BLEZ64 = 1002

    BLEZALC = 1003

    BLEZALC_MMR6 = 1004

    BLEZC = 1005

    BLEZC64 = 1006

    BLEZC_MMR6 = 1007

    BLEZL = 1008

    BLEZ_MM = 1009

    BLTC = 1010

    BLTC64 = 1011

    BLTC_MMR6 = 1012

    BLTUC = 1013

    BLTUC64 = 1014

    BLTUC_MMR6 = 1015

    BLTZ = 1016

    BLTZ64 = 1017

    BLTZAL = 1018

    BLTZALC = 1019

    BLTZALC_MMR6 = 1020

    BLTZALL = 1021

    BLTZALS_MM = 1022

    BLTZAL_MM = 1023

    BLTZC = 1024

    BLTZC64 = 1025

    BLTZC_MMR6 = 1026

    BLTZL = 1027

    BLTZ_MM = 1028

    BMNZI_B = 1029

    BMNZ_V = 1030

    BMZI_B = 1031

    BMZ_V = 1032

    BNE = 1033

    BNE64 = 1034

    BNEC = 1035

    BNEC64 = 1036

    BNEC_MMR6 = 1037

    BNEGI_B = 1038

    BNEGI_D = 1039

    BNEGI_H = 1040

    BNEGI_W = 1041

    BNEG_B = 1042

    BNEG_D = 1043

    BNEG_H = 1044

    BNEG_W = 1045

    BNEL = 1046

    BNEZ16_MM = 1047

    BNEZALC = 1048

    BNEZALC_MMR6 = 1049

    BNEZC = 1050

    BNEZC16_MMR6 = 1051

    BNEZC64 = 1052

    BNEZC_MM = 1053

    BNEZC_MMR6 = 1054

    BNE_MM = 1055

    BNVC = 1056

    BNVC_MMR6 = 1057

    BNZ_B = 1058

    BNZ_D = 1059

    BNZ_H = 1060

    BNZ_V = 1061

    BNZ_W = 1062

    BOVC = 1063

    BOVC_MMR6 = 1064

    BPOSGE32 = 1065

    BPOSGE32C_MMR3 = 1066

    BPOSGE32_MM = 1067

    BREAK = 1068

    BREAK16_MM = 1069

    BREAK16_MMR6 = 1070

    BREAK_MM = 1071

    BREAK_MMR6 = 1072

    BSELI_B = 1073

    BSEL_V = 1074

    BSETI_B = 1075

    BSETI_D = 1076

    BSETI_H = 1077

    BSETI_W = 1078

    BSET_B = 1079

    BSET_D = 1080

    BSET_H = 1081

    BSET_W = 1082

    BZ_B = 1083

    BZ_D = 1084

    BZ_H = 1085

    BZ_V = 1086

    BZ_W = 1087

    BeqzRxImm16 = 1088

    BeqzRxImmX16 = 1089

    Bimm16 = 1090

    BimmX16 = 1091

    BnezRxImm16 = 1092

    BnezRxImmX16 = 1093

    Break16 = 1094

    Bteqz16 = 1095

    BteqzX16 = 1096

    Btnez16 = 1097

    BtnezX16 = 1098

    CACHE = 1099

    CACHEE = 1100

    CACHEE_MM = 1101

    CACHE_MM = 1102

    CACHE_MMR6 = 1103

    CACHE_R6 = 1104

    CEIL_L_D64 = 1105

    CEIL_L_D_MMR6 = 1106

    CEIL_L_S = 1107

    CEIL_L_S_MMR6 = 1108

    CEIL_W_D32 = 1109

    CEIL_W_D64 = 1110

    CEIL_W_D_MMR6 = 1111

    CEIL_W_MM = 1112

    CEIL_W_S = 1113

    CEIL_W_S_MM = 1114

    CEIL_W_S_MMR6 = 1115

    CEQI_B = 1116

    CEQI_D = 1117

    CEQI_H = 1118

    CEQI_W = 1119

    CEQ_B = 1120

    CEQ_D = 1121

    CEQ_H = 1122

    CEQ_W = 1123

    CFC1 = 1124

    CFC1_MM = 1125

    CFC2_MM = 1126

    CFCMSA = 1127

    CINS = 1128

    CINS32 = 1129

    CINS64_32 = 1130

    CINS_i32 = 1131

    CLASS_D = 1132

    CLASS_D_MMR6 = 1133

    CLASS_S = 1134

    CLASS_S_MMR6 = 1135

    CLEI_S_B = 1136

    CLEI_S_D = 1137

    CLEI_S_H = 1138

    CLEI_S_W = 1139

    CLEI_U_B = 1140

    CLEI_U_D = 1141

    CLEI_U_H = 1142

    CLEI_U_W = 1143

    CLE_S_B = 1144

    CLE_S_D = 1145

    CLE_S_H = 1146

    CLE_S_W = 1147

    CLE_U_B = 1148

    CLE_U_D = 1149

    CLE_U_H = 1150

    CLE_U_W = 1151

    CLO = 1152

    CLO_MM = 1153

    CLO_MMR6 = 1154

    CLO_R6 = 1155

    CLTI_S_B = 1156

    CLTI_S_D = 1157

    CLTI_S_H = 1158

    CLTI_S_W = 1159

    CLTI_U_B = 1160

    CLTI_U_D = 1161

    CLTI_U_H = 1162

    CLTI_U_W = 1163

    CLT_S_B = 1164

    CLT_S_D = 1165

    CLT_S_H = 1166

    CLT_S_W = 1167

    CLT_U_B = 1168

    CLT_U_D = 1169

    CLT_U_H = 1170

    CLT_U_W = 1171

    CLZ = 1172

    CLZ_MM = 1173

    CLZ_MMR6 = 1174

    CLZ_R6 = 1175

    CMPGDU_EQ_QB = 1176

    CMPGDU_EQ_QB_MMR2 = 1177

    CMPGDU_LE_QB = 1178

    CMPGDU_LE_QB_MMR2 = 1179

    CMPGDU_LT_QB = 1180

    CMPGDU_LT_QB_MMR2 = 1181

    CMPGU_EQ_QB = 1182

    CMPGU_EQ_QB_MM = 1183

    CMPGU_LE_QB = 1184

    CMPGU_LE_QB_MM = 1185

    CMPGU_LT_QB = 1186

    CMPGU_LT_QB_MM = 1187

    CMPU_EQ_QB = 1188

    CMPU_EQ_QB_MM = 1189

    CMPU_LE_QB = 1190

    CMPU_LE_QB_MM = 1191

    CMPU_LT_QB = 1192

    CMPU_LT_QB_MM = 1193

    CMP_AF_D_MMR6 = 1194

    CMP_AF_S_MMR6 = 1195

    CMP_EQ_D = 1196

    CMP_EQ_D_MMR6 = 1197

    CMP_EQ_PH = 1198

    CMP_EQ_PH_MM = 1199

    CMP_EQ_S = 1200

    CMP_EQ_S_MMR6 = 1201

    CMP_F_D = 1202

    CMP_F_S = 1203

    CMP_LE_D = 1204

    CMP_LE_D_MMR6 = 1205

    CMP_LE_PH = 1206

    CMP_LE_PH_MM = 1207

    CMP_LE_S = 1208

    CMP_LE_S_MMR6 = 1209

    CMP_LT_D = 1210

    CMP_LT_D_MMR6 = 1211

    CMP_LT_PH = 1212

    CMP_LT_PH_MM = 1213

    CMP_LT_S = 1214

    CMP_LT_S_MMR6 = 1215

    CMP_SAF_D = 1216

    CMP_SAF_D_MMR6 = 1217

    CMP_SAF_S = 1218

    CMP_SAF_S_MMR6 = 1219

    CMP_SEQ_D = 1220

    CMP_SEQ_D_MMR6 = 1221

    CMP_SEQ_S = 1222

    CMP_SEQ_S_MMR6 = 1223

    CMP_SLE_D = 1224

    CMP_SLE_D_MMR6 = 1225

    CMP_SLE_S = 1226

    CMP_SLE_S_MMR6 = 1227

    CMP_SLT_D = 1228

    CMP_SLT_D_MMR6 = 1229

    CMP_SLT_S = 1230

    CMP_SLT_S_MMR6 = 1231

    CMP_SUEQ_D = 1232

    CMP_SUEQ_D_MMR6 = 1233

    CMP_SUEQ_S = 1234

    CMP_SUEQ_S_MMR6 = 1235

    CMP_SULE_D = 1236

    CMP_SULE_D_MMR6 = 1237

    CMP_SULE_S = 1238

    CMP_SULE_S_MMR6 = 1239

    CMP_SULT_D = 1240

    CMP_SULT_D_MMR6 = 1241

    CMP_SULT_S = 1242

    CMP_SULT_S_MMR6 = 1243

    CMP_SUN_D = 1244

    CMP_SUN_D_MMR6 = 1245

    CMP_SUN_S = 1246

    CMP_SUN_S_MMR6 = 1247

    CMP_UEQ_D = 1248

    CMP_UEQ_D_MMR6 = 1249

    CMP_UEQ_S = 1250

    CMP_UEQ_S_MMR6 = 1251

    CMP_ULE_D = 1252

    CMP_ULE_D_MMR6 = 1253

    CMP_ULE_S = 1254

    CMP_ULE_S_MMR6 = 1255

    CMP_ULT_D = 1256

    CMP_ULT_D_MMR6 = 1257

    CMP_ULT_S = 1258

    CMP_ULT_S_MMR6 = 1259

    CMP_UN_D = 1260

    CMP_UN_D_MMR6 = 1261

    CMP_UN_S = 1262

    CMP_UN_S_MMR6 = 1263

    COPY_S_B = 1264

    COPY_S_D = 1265

    COPY_S_H = 1266

    COPY_S_W = 1267

    COPY_U_B = 1268

    COPY_U_H = 1269

    COPY_U_W = 1270

    CRC32B = 1271

    CRC32CB = 1272

    CRC32CD = 1273

    CRC32CH = 1274

    CRC32CW = 1275

    CRC32D = 1276

    CRC32H = 1277

    CRC32W = 1278

    CTC1 = 1279

    CTC1_MM = 1280

    CTC2_MM = 1281

    CTCMSA = 1282

    CVT_D32_S = 1283

    CVT_D32_S_MM = 1284

    CVT_D32_W = 1285

    CVT_D32_W_MM = 1286

    CVT_D64_L = 1287

    CVT_D64_S = 1288

    CVT_D64_S_MM = 1289

    CVT_D64_W = 1290

    CVT_D64_W_MM = 1291

    CVT_D_L_MMR6 = 1292

    CVT_L_D64 = 1293

    CVT_L_D64_MM = 1294

    CVT_L_D_MMR6 = 1295

    CVT_L_S = 1296

    CVT_L_S_MM = 1297

    CVT_L_S_MMR6 = 1298

    CVT_PS_PW64 = 1299

    CVT_PS_S64 = 1300

    CVT_PW_PS64 = 1301

    CVT_S_D32 = 1302

    CVT_S_D32_MM = 1303

    CVT_S_D64 = 1304

    CVT_S_D64_MM = 1305

    CVT_S_L = 1306

    CVT_S_L_MMR6 = 1307

    CVT_S_PL64 = 1308

    CVT_S_PU64 = 1309

    CVT_S_W = 1310

    CVT_S_W_MM = 1311

    CVT_S_W_MMR6 = 1312

    CVT_W_D32 = 1313

    CVT_W_D32_MM = 1314

    CVT_W_D64 = 1315

    CVT_W_D64_MM = 1316

    CVT_W_S = 1317

    CVT_W_S_MM = 1318

    CVT_W_S_MMR6 = 1319

    C_EQ_D32 = 1320

    C_EQ_D32_MM = 1321

    C_EQ_D64 = 1322

    C_EQ_D64_MM = 1323

    C_EQ_S = 1324

    C_EQ_S_MM = 1325

    C_F_D32 = 1326

    C_F_D32_MM = 1327

    C_F_D64 = 1328

    C_F_D64_MM = 1329

    C_F_S = 1330

    C_F_S_MM = 1331

    C_LE_D32 = 1332

    C_LE_D32_MM = 1333

    C_LE_D64 = 1334

    C_LE_D64_MM = 1335

    C_LE_S = 1336

    C_LE_S_MM = 1337

    C_LT_D32 = 1338

    C_LT_D32_MM = 1339

    C_LT_D64 = 1340

    C_LT_D64_MM = 1341

    C_LT_S = 1342

    C_LT_S_MM = 1343

    C_NGE_D32 = 1344

    C_NGE_D32_MM = 1345

    C_NGE_D64 = 1346

    C_NGE_D64_MM = 1347

    C_NGE_S = 1348

    C_NGE_S_MM = 1349

    C_NGLE_D32 = 1350

    C_NGLE_D32_MM = 1351

    C_NGLE_D64 = 1352

    C_NGLE_D64_MM = 1353

    C_NGLE_S = 1354

    C_NGLE_S_MM = 1355

    C_NGL_D32 = 1356

    C_NGL_D32_MM = 1357

    C_NGL_D64 = 1358

    C_NGL_D64_MM = 1359

    C_NGL_S = 1360

    C_NGL_S_MM = 1361

    C_NGT_D32 = 1362

    C_NGT_D32_MM = 1363

    C_NGT_D64 = 1364

    C_NGT_D64_MM = 1365

    C_NGT_S = 1366

    C_NGT_S_MM = 1367

    C_OLE_D32 = 1368

    C_OLE_D32_MM = 1369

    C_OLE_D64 = 1370

    C_OLE_D64_MM = 1371

    C_OLE_S = 1372

    C_OLE_S_MM = 1373

    C_OLT_D32 = 1374

    C_OLT_D32_MM = 1375

    C_OLT_D64 = 1376

    C_OLT_D64_MM = 1377

    C_OLT_S = 1378

    C_OLT_S_MM = 1379

    C_SEQ_D32 = 1380

    C_SEQ_D32_MM = 1381

    C_SEQ_D64 = 1382

    C_SEQ_D64_MM = 1383

    C_SEQ_S = 1384

    C_SEQ_S_MM = 1385

    C_SF_D32 = 1386

    C_SF_D32_MM = 1387

    C_SF_D64 = 1388

    C_SF_D64_MM = 1389

    C_SF_S = 1390

    C_SF_S_MM = 1391

    C_UEQ_D32 = 1392

    C_UEQ_D32_MM = 1393

    C_UEQ_D64 = 1394

    C_UEQ_D64_MM = 1395

    C_UEQ_S = 1396

    C_UEQ_S_MM = 1397

    C_ULE_D32 = 1398

    C_ULE_D32_MM = 1399

    C_ULE_D64 = 1400

    C_ULE_D64_MM = 1401

    C_ULE_S = 1402

    C_ULE_S_MM = 1403

    C_ULT_D32 = 1404

    C_ULT_D32_MM = 1405

    C_ULT_D64 = 1406

    C_ULT_D64_MM = 1407

    C_ULT_S = 1408

    C_ULT_S_MM = 1409

    C_UN_D32 = 1410

    C_UN_D32_MM = 1411

    C_UN_D64 = 1412

    C_UN_D64_MM = 1413

    C_UN_S = 1414

    C_UN_S_MM = 1415

    CmpRxRy16 = 1416

    CmpiRxImm16 = 1417

    CmpiRxImmX16 = 1418

    DADD = 1419

    DADDi = 1420

    DADDiu = 1421

    DADDu = 1422

    DAHI = 1423

    DALIGN = 1424

    DATI = 1425

    DAUI = 1426

    DBITSWAP = 1427

    DCLO = 1428

    DCLO_R6 = 1429

    DCLZ = 1430

    DCLZ_R6 = 1431

    DDIV = 1432

    DDIVU = 1433

    DERET = 1434

    DERET_MM = 1435

    DERET_MMR6 = 1436

    DEXT = 1437

    DEXT64_32 = 1438

    DEXTM = 1439

    DEXTU = 1440

    DI = 1441

    DINS = 1442

    DINSM = 1443

    DINSU = 1444

    DIV = 1445

    DIVU = 1446

    DIVU_MMR6 = 1447

    DIV_MMR6 = 1448

    DIV_S_B = 1449

    DIV_S_D = 1450

    DIV_S_H = 1451

    DIV_S_W = 1452

    DIV_U_B = 1453

    DIV_U_D = 1454

    DIV_U_H = 1455

    DIV_U_W = 1456

    DI_MM = 1457

    DI_MMR6 = 1458

    DLSA = 1459

    DLSA_R6 = 1460

    DMFC0 = 1461

    DMFC1 = 1462

    DMFC2 = 1463

    DMFC2_OCTEON = 1464

    DMFGC0 = 1465

    DMOD = 1466

    DMODU = 1467

    DMT = 1468

    DMTC0 = 1469

    DMTC1 = 1470

    DMTC2 = 1471

    DMTC2_OCTEON = 1472

    DMTGC0 = 1473

    DMUH = 1474

    DMUHU = 1475

    DMUL = 1476

    DMULT = 1477

    DMULTu = 1478

    DMULU = 1479

    DMUL_R6 = 1480

    DOTP_S_D = 1481

    DOTP_S_H = 1482

    DOTP_S_W = 1483

    DOTP_U_D = 1484

    DOTP_U_H = 1485

    DOTP_U_W = 1486

    DPADD_S_D = 1487

    DPADD_S_H = 1488

    DPADD_S_W = 1489

    DPADD_U_D = 1490

    DPADD_U_H = 1491

    DPADD_U_W = 1492

    DPAQX_SA_W_PH = 1493

    DPAQX_SA_W_PH_MMR2 = 1494

    DPAQX_S_W_PH = 1495

    DPAQX_S_W_PH_MMR2 = 1496

    DPAQ_SA_L_W = 1497

    DPAQ_SA_L_W_MM = 1498

    DPAQ_S_W_PH = 1499

    DPAQ_S_W_PH_MM = 1500

    DPAU_H_QBL = 1501

    DPAU_H_QBL_MM = 1502

    DPAU_H_QBR = 1503

    DPAU_H_QBR_MM = 1504

    DPAX_W_PH = 1505

    DPAX_W_PH_MMR2 = 1506

    DPA_W_PH = 1507

    DPA_W_PH_MMR2 = 1508

    DPOP = 1509

    DPSQX_SA_W_PH = 1510

    DPSQX_SA_W_PH_MMR2 = 1511

    DPSQX_S_W_PH = 1512

    DPSQX_S_W_PH_MMR2 = 1513

    DPSQ_SA_L_W = 1514

    DPSQ_SA_L_W_MM = 1515

    DPSQ_S_W_PH = 1516

    DPSQ_S_W_PH_MM = 1517

    DPSUB_S_D = 1518

    DPSUB_S_H = 1519

    DPSUB_S_W = 1520

    DPSUB_U_D = 1521

    DPSUB_U_H = 1522

    DPSUB_U_W = 1523

    DPSU_H_QBL = 1524

    DPSU_H_QBL_MM = 1525

    DPSU_H_QBR = 1526

    DPSU_H_QBR_MM = 1527

    DPSX_W_PH = 1528

    DPSX_W_PH_MMR2 = 1529

    DPS_W_PH = 1530

    DPS_W_PH_MMR2 = 1531

    DROTR = 1532

    DROTR32 = 1533

    DROTRV = 1534

    DSBH = 1535

    DSDIV = 1536

    DSHD = 1537

    DSLL = 1538

    DSLL32 = 1539

    DSLL64_32 = 1540

    DSLLV = 1541

    DSRA = 1542

    DSRA32 = 1543

    DSRAV = 1544

    DSRL = 1545

    DSRL32 = 1546

    DSRLV = 1547

    DSUB = 1548

    DSUBu = 1549

    DUDIV = 1550

    DVP = 1551

    DVPE = 1552

    DVP_MMR6 = 1553

    DivRxRy16 = 1554

    DivuRxRy16 = 1555

    EHB = 1556

    EHB_MM = 1557

    EHB_MMR6 = 1558

    EI = 1559

    EI_MM = 1560

    EI_MMR6 = 1561

    EMT = 1562

    ERET = 1563

    ERETNC = 1564

    ERETNC_MMR6 = 1565

    ERET_MM = 1566

    ERET_MMR6 = 1567

    EVP = 1568

    EVPE = 1569

    EVP_MMR6 = 1570

    EXT = 1571

    EXTP = 1572

    EXTPDP = 1573

    EXTPDPV = 1574

    EXTPDPV_MM = 1575

    EXTPDP_MM = 1576

    EXTPV = 1577

    EXTPV_MM = 1578

    EXTP_MM = 1579

    EXTRV_RS_W = 1580

    EXTRV_RS_W_MM = 1581

    EXTRV_R_W = 1582

    EXTRV_R_W_MM = 1583

    EXTRV_S_H = 1584

    EXTRV_S_H_MM = 1585

    EXTRV_W = 1586

    EXTRV_W_MM = 1587

    EXTR_RS_W = 1588

    EXTR_RS_W_MM = 1589

    EXTR_R_W = 1590

    EXTR_R_W_MM = 1591

    EXTR_S_H = 1592

    EXTR_S_H_MM = 1593

    EXTR_W = 1594

    EXTR_W_MM = 1595

    EXTS = 1596

    EXTS32 = 1597

    EXT_MM = 1598

    EXT_MMR6 = 1599

    FABS_D32 = 1600

    FABS_D32_MM = 1601

    FABS_D64 = 1602

    FABS_D64_MM = 1603

    FABS_S = 1604

    FABS_S_MM = 1605

    FADD_D = 1606

    FADD_D32 = 1607

    FADD_D32_MM = 1608

    FADD_D64 = 1609

    FADD_D64_MM = 1610

    FADD_PS64 = 1611

    FADD_S = 1612

    FADD_S_MM = 1613

    FADD_S_MMR6 = 1614

    FADD_W = 1615

    FCAF_D = 1616

    FCAF_W = 1617

    FCEQ_D = 1618

    FCEQ_W = 1619

    FCLASS_D = 1620

    FCLASS_W = 1621

    FCLE_D = 1622

    FCLE_W = 1623

    FCLT_D = 1624

    FCLT_W = 1625

    FCMP_D32 = 1626

    FCMP_D32_MM = 1627

    FCMP_D64 = 1628

    FCMP_S32 = 1629

    FCMP_S32_MM = 1630

    FCNE_D = 1631

    FCNE_W = 1632

    FCOR_D = 1633

    FCOR_W = 1634

    FCUEQ_D = 1635

    FCUEQ_W = 1636

    FCULE_D = 1637

    FCULE_W = 1638

    FCULT_D = 1639

    FCULT_W = 1640

    FCUNE_D = 1641

    FCUNE_W = 1642

    FCUN_D = 1643

    FCUN_W = 1644

    FDIV_D = 1645

    FDIV_D32 = 1646

    FDIV_D32_MM = 1647

    FDIV_D64 = 1648

    FDIV_D64_MM = 1649

    FDIV_S = 1650

    FDIV_S_MM = 1651

    FDIV_S_MMR6 = 1652

    FDIV_W = 1653

    FEXDO_H = 1654

    FEXDO_W = 1655

    FEXP2_D = 1656

    FEXP2_W = 1657

    FEXUPL_D = 1658

    FEXUPL_W = 1659

    FEXUPR_D = 1660

    FEXUPR_W = 1661

    FFINT_S_D = 1662

    FFINT_S_W = 1663

    FFINT_U_D = 1664

    FFINT_U_W = 1665

    FFQL_D = 1666

    FFQL_W = 1667

    FFQR_D = 1668

    FFQR_W = 1669

    FILL_B = 1670

    FILL_D = 1671

    FILL_H = 1672

    FILL_W = 1673

    FLOG2_D = 1674

    FLOG2_W = 1675

    FLOOR_L_D64 = 1676

    FLOOR_L_D_MMR6 = 1677

    FLOOR_L_S = 1678

    FLOOR_L_S_MMR6 = 1679

    FLOOR_W_D32 = 1680

    FLOOR_W_D64 = 1681

    FLOOR_W_D_MMR6 = 1682

    FLOOR_W_MM = 1683

    FLOOR_W_S = 1684

    FLOOR_W_S_MM = 1685

    FLOOR_W_S_MMR6 = 1686

    FMADD_D = 1687

    FMADD_W = 1688

    FMAX_A_D = 1689

    FMAX_A_W = 1690

    FMAX_D = 1691

    FMAX_W = 1692

    FMIN_A_D = 1693

    FMIN_A_W = 1694

    FMIN_D = 1695

    FMIN_W = 1696

    FMOV_D32 = 1697

    FMOV_D32_MM = 1698

    FMOV_D64 = 1699

    FMOV_D64_MM = 1700

    FMOV_D_MMR6 = 1701

    FMOV_S = 1702

    FMOV_S_MM = 1703

    FMOV_S_MMR6 = 1704

    FMSUB_D = 1705

    FMSUB_W = 1706

    FMUL_D = 1707

    FMUL_D32 = 1708

    FMUL_D32_MM = 1709

    FMUL_D64 = 1710

    FMUL_D64_MM = 1711

    FMUL_PS64 = 1712

    FMUL_S = 1713

    FMUL_S_MM = 1714

    FMUL_S_MMR6 = 1715

    FMUL_W = 1716

    FNEG_D32 = 1717

    FNEG_D32_MM = 1718

    FNEG_D64 = 1719

    FNEG_D64_MM = 1720

    FNEG_S = 1721

    FNEG_S_MM = 1722

    FNEG_S_MMR6 = 1723

    FORK = 1724

    FRCP_D = 1725

    FRCP_W = 1726

    FRINT_D = 1727

    FRINT_W = 1728

    FRSQRT_D = 1729

    FRSQRT_W = 1730

    FSAF_D = 1731

    FSAF_W = 1732

    FSEQ_D = 1733

    FSEQ_W = 1734

    FSLE_D = 1735

    FSLE_W = 1736

    FSLT_D = 1737

    FSLT_W = 1738

    FSNE_D = 1739

    FSNE_W = 1740

    FSOR_D = 1741

    FSOR_W = 1742

    FSQRT_D = 1743

    FSQRT_D32 = 1744

    FSQRT_D32_MM = 1745

    FSQRT_D64 = 1746

    FSQRT_D64_MM = 1747

    FSQRT_S = 1748

    FSQRT_S_MM = 1749

    FSQRT_W = 1750

    FSUB_D = 1751

    FSUB_D32 = 1752

    FSUB_D32_MM = 1753

    FSUB_D64 = 1754

    FSUB_D64_MM = 1755

    FSUB_PS64 = 1756

    FSUB_S = 1757

    FSUB_S_MM = 1758

    FSUB_S_MMR6 = 1759

    FSUB_W = 1760

    FSUEQ_D = 1761

    FSUEQ_W = 1762

    FSULE_D = 1763

    FSULE_W = 1764

    FSULT_D = 1765

    FSULT_W = 1766

    FSUNE_D = 1767

    FSUNE_W = 1768

    FSUN_D = 1769

    FSUN_W = 1770

    FTINT_S_D = 1771

    FTINT_S_W = 1772

    FTINT_U_D = 1773

    FTINT_U_W = 1774

    FTQ_H = 1775

    FTQ_W = 1776

    FTRUNC_S_D = 1777

    FTRUNC_S_W = 1778

    FTRUNC_U_D = 1779

    FTRUNC_U_W = 1780

    GINVI = 1781

    GINVI_MMR6 = 1782

    GINVT = 1783

    GINVT_MMR6 = 1784

    HADD_S_D = 1785

    HADD_S_H = 1786

    HADD_S_W = 1787

    HADD_U_D = 1788

    HADD_U_H = 1789

    HADD_U_W = 1790

    HSUB_S_D = 1791

    HSUB_S_H = 1792

    HSUB_S_W = 1793

    HSUB_U_D = 1794

    HSUB_U_H = 1795

    HSUB_U_W = 1796

    HYPCALL = 1797

    HYPCALL_MM = 1798

    ILVEV_B = 1799

    ILVEV_D = 1800

    ILVEV_H = 1801

    ILVEV_W = 1802

    ILVL_B = 1803

    ILVL_D = 1804

    ILVL_H = 1805

    ILVL_W = 1806

    ILVOD_B = 1807

    ILVOD_D = 1808

    ILVOD_H = 1809

    ILVOD_W = 1810

    ILVR_B = 1811

    ILVR_D = 1812

    ILVR_H = 1813

    ILVR_W = 1814

    INS = 1815

    INSERT_B = 1816

    INSERT_D = 1817

    INSERT_H = 1818

    INSERT_W = 1819

    INSV = 1820

    INSVE_B = 1821

    INSVE_D = 1822

    INSVE_H = 1823

    INSVE_W = 1824

    INSV_MM = 1825

    INS_MM = 1826

    INS_MMR6 = 1827

    J = 1828

    JAL = 1829

    JALR = 1830

    JALR16_MM = 1831

    JALR64 = 1832

    JALRC16_MMR6 = 1833

    JALRC_HB_MMR6 = 1834

    JALRC_MMR6 = 1835

    JALRS16_MM = 1836

    JALRS_MM = 1837

    JALR_HB = 1838

    JALR_HB64 = 1839

    JALR_MM = 1840

    JALS_MM = 1841

    JALX = 1842

    JALX_MM = 1843

    JAL_MM = 1844

    JIALC = 1845

    JIALC64 = 1846

    JIALC_MMR6 = 1847

    JIC = 1848

    JIC64 = 1849

    JIC_MMR6 = 1850

    JR = 1851

    JR16_MM = 1852

    JR64 = 1853

    JRADDIUSP = 1854

    JRC16_MM = 1855

    JRC16_MMR6 = 1856

    JRCADDIUSP_MMR6 = 1857

    JR_HB = 1858

    JR_HB64 = 1859

    JR_HB64_R6 = 1860

    JR_HB_R6 = 1861

    JR_MM = 1862

    J_MM = 1863

    Jal16 = 1864

    JalB16 = 1865

    JrRa16 = 1866

    JrcRa16 = 1867

    JrcRx16 = 1868

    JumpLinkReg16 = 1869

    LB = 1870

    LB64 = 1871

    LBE = 1872

    LBE_MM = 1873

    LBU16_MM = 1874

    LBUX = 1875

    LBUX_MM = 1876

    LBU_MMR6 = 1877

    LB_MM = 1878

    LB_MMR6 = 1879

    LBu = 1880

    LBu64 = 1881

    LBuE = 1882

    LBuE_MM = 1883

    LBu_MM = 1884

    LD = 1885

    LDC1 = 1886

    LDC164 = 1887

    LDC1_D64_MMR6 = 1888

    LDC1_MM_D32 = 1889

    LDC1_MM_D64 = 1890

    LDC2 = 1891

    LDC2_MMR6 = 1892

    LDC2_R6 = 1893

    LDC3 = 1894

    LDI_B = 1895

    LDI_D = 1896

    LDI_H = 1897

    LDI_W = 1898

    LDL = 1899

    LDPC = 1900

    LDR = 1901

    LDXC1 = 1902

    LDXC164 = 1903

    LD_B = 1904

    LD_D = 1905

    LD_H = 1906

    LD_W = 1907

    LEA_ADDiu = 1908

    LEA_ADDiu64 = 1909

    LEA_ADDiu_MM = 1910

    LH = 1911

    LH64 = 1912

    LHE = 1913

    LHE_MM = 1914

    LHU16_MM = 1915

    LHX = 1916

    LHX_MM = 1917

    LH_MM = 1918

    LHu = 1919

    LHu64 = 1920

    LHuE = 1921

    LHuE_MM = 1922

    LHu_MM = 1923

    LI16_MM = 1924

    LI16_MMR6 = 1925

    LL = 1926

    LL64 = 1927

    LL64_R6 = 1928

    LLD = 1929

    LLD_R6 = 1930

    LLE = 1931

    LLE_MM = 1932

    LL_MM = 1933

    LL_MMR6 = 1934

    LL_R6 = 1935

    LSA = 1936

    LSA_MMR6 = 1937

    LSA_R6 = 1938

    LUI_MMR6 = 1939

    LUXC1 = 1940

    LUXC164 = 1941

    LUXC1_MM = 1942

    LUi = 1943

    LUi64 = 1944

    LUi_MM = 1945

    LW = 1946

    LW16_MM = 1947

    LW64 = 1948

    LWC1 = 1949

    LWC1_MM = 1950

    LWC2 = 1951

    LWC2_MMR6 = 1952

    LWC2_R6 = 1953

    LWC3 = 1954

    LWDSP = 1955

    LWDSP_MM = 1956

    LWE = 1957

    LWE_MM = 1958

    LWGP_MM = 1959

    LWL = 1960

    LWL64 = 1961

    LWLE = 1962

    LWLE_MM = 1963

    LWL_MM = 1964

    LWM16_MM = 1965

    LWM16_MMR6 = 1966

    LWM32_MM = 1967

    LWPC = 1968

    LWPC_MMR6 = 1969

    LWP_MM = 1970

    LWR = 1971

    LWR64 = 1972

    LWRE = 1973

    LWRE_MM = 1974

    LWR_MM = 1975

    LWSP_MM = 1976

    LWUPC = 1977

    LWU_MM = 1978

    LWX = 1979

    LWXC1 = 1980

    LWXC1_MM = 1981

    LWXS_MM = 1982

    LWX_MM = 1983

    LW_MM = 1984

    LW_MMR6 = 1985

    LWu = 1986

    LbRxRyOffMemX16 = 1987

    LbuRxRyOffMemX16 = 1988

    LhRxRyOffMemX16 = 1989

    LhuRxRyOffMemX16 = 1990

    LiRxImm16 = 1991

    LiRxImmAlignX16 = 1992

    LiRxImmX16 = 1993

    LwRxPcTcp16 = 1994

    LwRxPcTcpX16 = 1995

    LwRxRyOffMemX16 = 1996

    LwRxSpImmX16 = 1997

    MADD = 1998

    MADDF_D = 1999

    MADDF_D_MMR6 = 2000

    MADDF_S = 2001

    MADDF_S_MMR6 = 2002

    MADDR_Q_H = 2003

    MADDR_Q_W = 2004

    MADDU = 2005

    MADDU_DSP = 2006

    MADDU_DSP_MM = 2007

    MADDU_MM = 2008

    MADDV_B = 2009

    MADDV_D = 2010

    MADDV_H = 2011

    MADDV_W = 2012

    MADD_D32 = 2013

    MADD_D32_MM = 2014

    MADD_D64 = 2015

    MADD_DSP = 2016

    MADD_DSP_MM = 2017

    MADD_MM = 2018

    MADD_Q_H = 2019

    MADD_Q_W = 2020

    MADD_S = 2021

    MADD_S_MM = 2022

    MAQ_SA_W_PHL = 2023

    MAQ_SA_W_PHL_MM = 2024

    MAQ_SA_W_PHR = 2025

    MAQ_SA_W_PHR_MM = 2026

    MAQ_S_W_PHL = 2027

    MAQ_S_W_PHL_MM = 2028

    MAQ_S_W_PHR = 2029

    MAQ_S_W_PHR_MM = 2030

    MAXA_D = 2031

    MAXA_D_MMR6 = 2032

    MAXA_S = 2033

    MAXA_S_MMR6 = 2034

    MAXI_S_B = 2035

    MAXI_S_D = 2036

    MAXI_S_H = 2037

    MAXI_S_W = 2038

    MAXI_U_B = 2039

    MAXI_U_D = 2040

    MAXI_U_H = 2041

    MAXI_U_W = 2042

    MAX_A_B = 2043

    MAX_A_D = 2044

    MAX_A_H = 2045

    MAX_A_W = 2046

    MAX_D = 2047

    MAX_D_MMR6 = 2048

    MAX_S = 2049

    MAX_S_B = 2050

    MAX_S_D = 2051

    MAX_S_H = 2052

    MAX_S_MMR6 = 2053

    MAX_S_W = 2054

    MAX_U_B = 2055

    MAX_U_D = 2056

    MAX_U_H = 2057

    MAX_U_W = 2058

    MFC0 = 2059

    MFC0_MMR6 = 2060

    MFC1 = 2061

    MFC1_D64 = 2062

    MFC1_MM = 2063

    MFC1_MMR6 = 2064

    MFC2 = 2065

    MFC2_MMR6 = 2066

    MFGC0 = 2067

    MFGC0_MM = 2068

    MFHC0_MMR6 = 2069

    MFHC1_D32 = 2070

    MFHC1_D32_MM = 2071

    MFHC1_D64 = 2072

    MFHC1_D64_MM = 2073

    MFHC2_MMR6 = 2074

    MFHGC0 = 2075

    MFHGC0_MM = 2076

    MFHI = 2077

    MFHI16_MM = 2078

    MFHI64 = 2079

    MFHI_DSP = 2080

    MFHI_DSP_MM = 2081

    MFHI_MM = 2082

    MFLO = 2083

    MFLO16_MM = 2084

    MFLO64 = 2085

    MFLO_DSP = 2086

    MFLO_DSP_MM = 2087

    MFLO_MM = 2088

    MFTR = 2089

    MINA_D = 2090

    MINA_D_MMR6 = 2091

    MINA_S = 2092

    MINA_S_MMR6 = 2093

    MINI_S_B = 2094

    MINI_S_D = 2095

    MINI_S_H = 2096

    MINI_S_W = 2097

    MINI_U_B = 2098

    MINI_U_D = 2099

    MINI_U_H = 2100

    MINI_U_W = 2101

    MIN_A_B = 2102

    MIN_A_D = 2103

    MIN_A_H = 2104

    MIN_A_W = 2105

    MIN_D = 2106

    MIN_D_MMR6 = 2107

    MIN_S = 2108

    MIN_S_B = 2109

    MIN_S_D = 2110

    MIN_S_H = 2111

    MIN_S_MMR6 = 2112

    MIN_S_W = 2113

    MIN_U_B = 2114

    MIN_U_D = 2115

    MIN_U_H = 2116

    MIN_U_W = 2117

    MOD = 2118

    MODSUB = 2119

    MODSUB_MM = 2120

    MODU = 2121

    MODU_MMR6 = 2122

    MOD_MMR6 = 2123

    MOD_S_B = 2124

    MOD_S_D = 2125

    MOD_S_H = 2126

    MOD_S_W = 2127

    MOD_U_B = 2128

    MOD_U_D = 2129

    MOD_U_H = 2130

    MOD_U_W = 2131

    MOVE16_MM = 2132

    MOVE16_MMR6 = 2133

    MOVEP_MM = 2134

    MOVEP_MMR6 = 2135

    MOVE_V = 2136

    MOVF_D32 = 2137

    MOVF_D32_MM = 2138

    MOVF_D64 = 2139

    MOVF_I = 2140

    MOVF_I64 = 2141

    MOVF_I_MM = 2142

    MOVF_S = 2143

    MOVF_S_MM = 2144

    MOVN_I64_D64 = 2145

    MOVN_I64_I = 2146

    MOVN_I64_I64 = 2147

    MOVN_I64_S = 2148

    MOVN_I_D32 = 2149

    MOVN_I_D32_MM = 2150

    MOVN_I_D64 = 2151

    MOVN_I_I = 2152

    MOVN_I_I64 = 2153

    MOVN_I_MM = 2154

    MOVN_I_S = 2155

    MOVN_I_S_MM = 2156

    MOVT_D32 = 2157

    MOVT_D32_MM = 2158

    MOVT_D64 = 2159

    MOVT_I = 2160

    MOVT_I64 = 2161

    MOVT_I_MM = 2162

    MOVT_S = 2163

    MOVT_S_MM = 2164

    MOVZ_I64_D64 = 2165

    MOVZ_I64_I = 2166

    MOVZ_I64_I64 = 2167

    MOVZ_I64_S = 2168

    MOVZ_I_D32 = 2169

    MOVZ_I_D32_MM = 2170

    MOVZ_I_D64 = 2171

    MOVZ_I_I = 2172

    MOVZ_I_I64 = 2173

    MOVZ_I_MM = 2174

    MOVZ_I_S = 2175

    MOVZ_I_S_MM = 2176

    MSUB = 2177

    MSUBF_D = 2178

    MSUBF_D_MMR6 = 2179

    MSUBF_S = 2180

    MSUBF_S_MMR6 = 2181

    MSUBR_Q_H = 2182

    MSUBR_Q_W = 2183

    MSUBU = 2184

    MSUBU_DSP = 2185

    MSUBU_DSP_MM = 2186

    MSUBU_MM = 2187

    MSUBV_B = 2188

    MSUBV_D = 2189

    MSUBV_H = 2190

    MSUBV_W = 2191

    MSUB_D32 = 2192

    MSUB_D32_MM = 2193

    MSUB_D64 = 2194

    MSUB_DSP = 2195

    MSUB_DSP_MM = 2196

    MSUB_MM = 2197

    MSUB_Q_H = 2198

    MSUB_Q_W = 2199

    MSUB_S = 2200

    MSUB_S_MM = 2201

    MTC0 = 2202

    MTC0_MMR6 = 2203

    MTC1 = 2204

    MTC1_D64 = 2205

    MTC1_D64_MM = 2206

    MTC1_MM = 2207

    MTC1_MMR6 = 2208

    MTC2 = 2209

    MTC2_MMR6 = 2210

    MTGC0 = 2211

    MTGC0_MM = 2212

    MTHC0_MMR6 = 2213

    MTHC1_D32 = 2214

    MTHC1_D32_MM = 2215

    MTHC1_D64 = 2216

    MTHC1_D64_MM = 2217

    MTHC2_MMR6 = 2218

    MTHGC0 = 2219

    MTHGC0_MM = 2220

    MTHI = 2221

    MTHI64 = 2222

    MTHI_DSP = 2223

    MTHI_DSP_MM = 2224

    MTHI_MM = 2225

    MTHLIP = 2226

    MTHLIP_MM = 2227

    MTLO = 2228

    MTLO64 = 2229

    MTLO_DSP = 2230

    MTLO_DSP_MM = 2231

    MTLO_MM = 2232

    MTM0 = 2233

    MTM1 = 2234

    MTM2 = 2235

    MTP0 = 2236

    MTP1 = 2237

    MTP2 = 2238

    MTTR = 2239

    MUH = 2240

    MUHU = 2241

    MUHU_MMR6 = 2242

    MUH_MMR6 = 2243

    MUL = 2244

    MULEQ_S_W_PHL = 2245

    MULEQ_S_W_PHL_MM = 2246

    MULEQ_S_W_PHR = 2247

    MULEQ_S_W_PHR_MM = 2248

    MULEU_S_PH_QBL = 2249

    MULEU_S_PH_QBL_MM = 2250

    MULEU_S_PH_QBR = 2251

    MULEU_S_PH_QBR_MM = 2252

    MULQ_RS_PH = 2253

    MULQ_RS_PH_MM = 2254

    MULQ_RS_W = 2255

    MULQ_RS_W_MMR2 = 2256

    MULQ_S_PH = 2257

    MULQ_S_PH_MMR2 = 2258

    MULQ_S_W = 2259

    MULQ_S_W_MMR2 = 2260

    MULR_PS64 = 2261

    MULR_Q_H = 2262

    MULR_Q_W = 2263

    MULSAQ_S_W_PH = 2264

    MULSAQ_S_W_PH_MM = 2265

    MULSA_W_PH = 2266

    MULSA_W_PH_MMR2 = 2267

    MULT = 2268

    MULTU_DSP = 2269

    MULTU_DSP_MM = 2270

    MULT_DSP = 2271

    MULT_DSP_MM = 2272

    MULT_MM = 2273

    MULTu = 2274

    MULTu_MM = 2275

    MULU = 2276

    MULU_MMR6 = 2277

    MULV_B = 2278

    MULV_D = 2279

    MULV_H = 2280

    MULV_W = 2281

    MUL_MM = 2282

    MUL_MMR6 = 2283

    MUL_PH = 2284

    MUL_PH_MMR2 = 2285

    MUL_Q_H = 2286

    MUL_Q_W = 2287

    MUL_R6 = 2288

    MUL_S_PH = 2289

    MUL_S_PH_MMR2 = 2290

    Mfhi16 = 2291

    Mflo16 = 2292

    Move32R16 = 2293

    MoveR3216 = 2294

    NAL = 2295

    NLOC_B = 2296

    NLOC_D = 2297

    NLOC_H = 2298

    NLOC_W = 2299

    NLZC_B = 2300

    NLZC_D = 2301

    NLZC_H = 2302

    NLZC_W = 2303

    NMADD_D32 = 2304

    NMADD_D32_MM = 2305

    NMADD_D64 = 2306

    NMADD_S = 2307

    NMADD_S_MM = 2308

    NMSUB_D32 = 2309

    NMSUB_D32_MM = 2310

    NMSUB_D64 = 2311

    NMSUB_S = 2312

    NMSUB_S_MM = 2313

    NOR = 2314

    NOR64 = 2315

    NORI_B = 2316

    NOR_MM = 2317

    NOR_MMR6 = 2318

    NOR_V = 2319

    NOT16_MM = 2320

    NOT16_MMR6 = 2321

    NegRxRy16 = 2322

    NotRxRy16 = 2323

    OR = 2324

    OR16_MM = 2325

    OR16_MMR6 = 2326

    OR64 = 2327

    ORI_B = 2328

    ORI_MMR6 = 2329

    OR_MM = 2330

    OR_MMR6 = 2331

    OR_V = 2332

    ORi = 2333

    ORi64 = 2334

    ORi_MM = 2335

    OrRxRxRy16 = 2336

    PACKRL_PH = 2337

    PACKRL_PH_MM = 2338

    PAUSE = 2339

    PAUSE_MM = 2340

    PAUSE_MMR6 = 2341

    PCKEV_B = 2342

    PCKEV_D = 2343

    PCKEV_H = 2344

    PCKEV_W = 2345

    PCKOD_B = 2346

    PCKOD_D = 2347

    PCKOD_H = 2348

    PCKOD_W = 2349

    PCNT_B = 2350

    PCNT_D = 2351

    PCNT_H = 2352

    PCNT_W = 2353

    PICK_PH = 2354

    PICK_PH_MM = 2355

    PICK_QB = 2356

    PICK_QB_MM = 2357

    PLL_PS64 = 2358

    PLU_PS64 = 2359

    POP = 2360

    PRECEQU_PH_QBL = 2361

    PRECEQU_PH_QBLA = 2362

    PRECEQU_PH_QBLA_MM = 2363

    PRECEQU_PH_QBL_MM = 2364

    PRECEQU_PH_QBR = 2365

    PRECEQU_PH_QBRA = 2366

    PRECEQU_PH_QBRA_MM = 2367

    PRECEQU_PH_QBR_MM = 2368

    PRECEQ_W_PHL = 2369

    PRECEQ_W_PHL_MM = 2370

    PRECEQ_W_PHR = 2371

    PRECEQ_W_PHR_MM = 2372

    PRECEU_PH_QBL = 2373

    PRECEU_PH_QBLA = 2374

    PRECEU_PH_QBLA_MM = 2375

    PRECEU_PH_QBL_MM = 2376

    PRECEU_PH_QBR = 2377

    PRECEU_PH_QBRA = 2378

    PRECEU_PH_QBRA_MM = 2379

    PRECEU_PH_QBR_MM = 2380

    PRECRQU_S_QB_PH = 2381

    PRECRQU_S_QB_PH_MM = 2382

    PRECRQ_PH_W = 2383

    PRECRQ_PH_W_MM = 2384

    PRECRQ_QB_PH = 2385

    PRECRQ_QB_PH_MM = 2386

    PRECRQ_RS_PH_W = 2387

    PRECRQ_RS_PH_W_MM = 2388

    PRECR_QB_PH = 2389

    PRECR_QB_PH_MMR2 = 2390

    PRECR_SRA_PH_W = 2391

    PRECR_SRA_PH_W_MMR2 = 2392

    PRECR_SRA_R_PH_W = 2393

    PRECR_SRA_R_PH_W_MMR2 = 2394

    PREF = 2395

    PREFE = 2396

    PREFE_MM = 2397

    PREFX_MM = 2398

    PREF_MM = 2399

    PREF_MMR6 = 2400

    PREF_R6 = 2401

    PREPEND = 2402

    PREPEND_MMR2 = 2403

    PUL_PS64 = 2404

    PUU_PS64 = 2405

    RADDU_W_QB = 2406

    RADDU_W_QB_MM = 2407

    RDDSP = 2408

    RDDSP_MM = 2409

    RDHWR = 2410

    RDHWR64 = 2411

    RDHWR_MM = 2412

    RDHWR_MMR6 = 2413

    RDPGPR_MMR6 = 2414

    RECIP_D32 = 2415

    RECIP_D32_MM = 2416

    RECIP_D64 = 2417

    RECIP_D64_MM = 2418

    RECIP_S = 2419

    RECIP_S_MM = 2420

    REPLV_PH = 2421

    REPLV_PH_MM = 2422

    REPLV_QB = 2423

    REPLV_QB_MM = 2424

    REPL_PH = 2425

    REPL_PH_MM = 2426

    REPL_QB = 2427

    REPL_QB_MM = 2428

    RINT_D = 2429

    RINT_D_MMR6 = 2430

    RINT_S = 2431

    RINT_S_MMR6 = 2432

    ROTR = 2433

    ROTRV = 2434

    ROTRV_MM = 2435

    ROTR_MM = 2436

    ROUND_L_D64 = 2437

    ROUND_L_D_MMR6 = 2438

    ROUND_L_S = 2439

    ROUND_L_S_MMR6 = 2440

    ROUND_W_D32 = 2441

    ROUND_W_D64 = 2442

    ROUND_W_D_MMR6 = 2443

    ROUND_W_MM = 2444

    ROUND_W_S = 2445

    ROUND_W_S_MM = 2446

    ROUND_W_S_MMR6 = 2447

    RSQRT_D32 = 2448

    RSQRT_D32_MM = 2449

    RSQRT_D64 = 2450

    RSQRT_D64_MM = 2451

    RSQRT_S = 2452

    RSQRT_S_MM = 2453

    Restore16 = 2454

    RestoreX16 = 2455

    SAA = 2456

    SAAD = 2457

    SAT_S_B = 2458

    SAT_S_D = 2459

    SAT_S_H = 2460

    SAT_S_W = 2461

    SAT_U_B = 2462

    SAT_U_D = 2463

    SAT_U_H = 2464

    SAT_U_W = 2465

    SB = 2466

    SB16_MM = 2467

    SB16_MMR6 = 2468

    SB64 = 2469

    SBE = 2470

    SBE_MM = 2471

    SB_MM = 2472

    SB_MMR6 = 2473

    SC = 2474

    SC64 = 2475

    SC64_R6 = 2476

    SCD = 2477

    SCD_R6 = 2478

    SCE = 2479

    SCE_MM = 2480

    SC_MM = 2481

    SC_MMR6 = 2482

    SC_R6 = 2483

    SD = 2484

    SDBBP = 2485

    SDBBP16_MM = 2486

    SDBBP16_MMR6 = 2487

    SDBBP_MM = 2488

    SDBBP_MMR6 = 2489

    SDBBP_R6 = 2490

    SDC1 = 2491

    SDC164 = 2492

    SDC1_D64_MMR6 = 2493

    SDC1_MM_D32 = 2494

    SDC1_MM_D64 = 2495

    SDC2 = 2496

    SDC2_MMR6 = 2497

    SDC2_R6 = 2498

    SDC3 = 2499

    SDIV = 2500

    SDIV_MM = 2501

    SDL = 2502

    SDR = 2503

    SDXC1 = 2504

    SDXC164 = 2505

    SEB = 2506

    SEB64 = 2507

    SEB_MM = 2508

    SEH = 2509

    SEH64 = 2510

    SEH_MM = 2511

    SELEQZ = 2512

    SELEQZ64 = 2513

    SELEQZ_D = 2514

    SELEQZ_D_MMR6 = 2515

    SELEQZ_MMR6 = 2516

    SELEQZ_S = 2517

    SELEQZ_S_MMR6 = 2518

    SELNEZ = 2519

    SELNEZ64 = 2520

    SELNEZ_D = 2521

    SELNEZ_D_MMR6 = 2522

    SELNEZ_MMR6 = 2523

    SELNEZ_S = 2524

    SELNEZ_S_MMR6 = 2525

    SEL_D = 2526

    SEL_D_MMR6 = 2527

    SEL_S = 2528

    SEL_S_MMR6 = 2529

    SEQ = 2530

    SEQi = 2531

    SH = 2532

    SH16_MM = 2533

    SH16_MMR6 = 2534

    SH64 = 2535

    SHE = 2536

    SHE_MM = 2537

    SHF_B = 2538

    SHF_H = 2539

    SHF_W = 2540

    SHILO = 2541

    SHILOV = 2542

    SHILOV_MM = 2543

    SHILO_MM = 2544

    SHLLV_PH = 2545

    SHLLV_PH_MM = 2546

    SHLLV_QB = 2547

    SHLLV_QB_MM = 2548

    SHLLV_S_PH = 2549

    SHLLV_S_PH_MM = 2550

    SHLLV_S_W = 2551

    SHLLV_S_W_MM = 2552

    SHLL_PH = 2553

    SHLL_PH_MM = 2554

    SHLL_QB = 2555

    SHLL_QB_MM = 2556

    SHLL_S_PH = 2557

    SHLL_S_PH_MM = 2558

    SHLL_S_W = 2559

    SHLL_S_W_MM = 2560

    SHRAV_PH = 2561

    SHRAV_PH_MM = 2562

    SHRAV_QB = 2563

    SHRAV_QB_MMR2 = 2564

    SHRAV_R_PH = 2565

    SHRAV_R_PH_MM = 2566

    SHRAV_R_QB = 2567

    SHRAV_R_QB_MMR2 = 2568

    SHRAV_R_W = 2569

    SHRAV_R_W_MM = 2570

    SHRA_PH = 2571

    SHRA_PH_MM = 2572

    SHRA_QB = 2573

    SHRA_QB_MMR2 = 2574

    SHRA_R_PH = 2575

    SHRA_R_PH_MM = 2576

    SHRA_R_QB = 2577

    SHRA_R_QB_MMR2 = 2578

    SHRA_R_W = 2579

    SHRA_R_W_MM = 2580

    SHRLV_PH = 2581

    SHRLV_PH_MMR2 = 2582

    SHRLV_QB = 2583

    SHRLV_QB_MM = 2584

    SHRL_PH = 2585

    SHRL_PH_MMR2 = 2586

    SHRL_QB = 2587

    SHRL_QB_MM = 2588

    SH_MM = 2589

    SH_MMR6 = 2590

    SIGRIE = 2591

    SIGRIE_MMR6 = 2592

    SLDI_B = 2593

    SLDI_D = 2594

    SLDI_H = 2595

    SLDI_W = 2596

    SLD_B = 2597

    SLD_D = 2598

    SLD_H = 2599

    SLD_W = 2600

    SLL = 2601

    SLL16_MM = 2602

    SLL16_MMR6 = 2603

    SLL64_32 = 2604

    SLL64_64 = 2605

    SLLI_B = 2606

    SLLI_D = 2607

    SLLI_H = 2608

    SLLI_W = 2609

    SLLV = 2610

    SLLV_MM = 2611

    SLL_B = 2612

    SLL_D = 2613

    SLL_H = 2614

    SLL_MM = 2615

    SLL_MMR6 = 2616

    SLL_W = 2617

    SLT = 2618

    SLT64 = 2619

    SLT_MM = 2620

    SLTi = 2621

    SLTi64 = 2622

    SLTi_MM = 2623

    SLTiu = 2624

    SLTiu64 = 2625

    SLTiu_MM = 2626

    SLTu = 2627

    SLTu64 = 2628

    SLTu_MM = 2629

    SNE = 2630

    SNEi = 2631

    SPLATI_B = 2632

    SPLATI_D = 2633

    SPLATI_H = 2634

    SPLATI_W = 2635

    SPLAT_B = 2636

    SPLAT_D = 2637

    SPLAT_H = 2638

    SPLAT_W = 2639

    SRA = 2640

    SRAI_B = 2641

    SRAI_D = 2642

    SRAI_H = 2643

    SRAI_W = 2644

    SRARI_B = 2645

    SRARI_D = 2646

    SRARI_H = 2647

    SRARI_W = 2648

    SRAR_B = 2649

    SRAR_D = 2650

    SRAR_H = 2651

    SRAR_W = 2652

    SRAV = 2653

    SRAV_MM = 2654

    SRA_B = 2655

    SRA_D = 2656

    SRA_H = 2657

    SRA_MM = 2658

    SRA_W = 2659

    SRL = 2660

    SRL16_MM = 2661

    SRL16_MMR6 = 2662

    SRLI_B = 2663

    SRLI_D = 2664

    SRLI_H = 2665

    SRLI_W = 2666

    SRLRI_B = 2667

    SRLRI_D = 2668

    SRLRI_H = 2669

    SRLRI_W = 2670

    SRLR_B = 2671

    SRLR_D = 2672

    SRLR_H = 2673

    SRLR_W = 2674

    SRLV = 2675

    SRLV_MM = 2676

    SRL_B = 2677

    SRL_D = 2678

    SRL_H = 2679

    SRL_MM = 2680

    SRL_W = 2681

    SSNOP = 2682

    SSNOP_MM = 2683

    SSNOP_MMR6 = 2684

    ST_B = 2685

    ST_D = 2686

    ST_H = 2687

    ST_W = 2688

    SUB = 2689

    SUBQH_PH = 2690

    SUBQH_PH_MMR2 = 2691

    SUBQH_R_PH = 2692

    SUBQH_R_PH_MMR2 = 2693

    SUBQH_R_W = 2694

    SUBQH_R_W_MMR2 = 2695

    SUBQH_W = 2696

    SUBQH_W_MMR2 = 2697

    SUBQ_PH = 2698

    SUBQ_PH_MM = 2699

    SUBQ_S_PH = 2700

    SUBQ_S_PH_MM = 2701

    SUBQ_S_W = 2702

    SUBQ_S_W_MM = 2703

    SUBSUS_U_B = 2704

    SUBSUS_U_D = 2705

    SUBSUS_U_H = 2706

    SUBSUS_U_W = 2707

    SUBSUU_S_B = 2708

    SUBSUU_S_D = 2709

    SUBSUU_S_H = 2710

    SUBSUU_S_W = 2711

    SUBS_S_B = 2712

    SUBS_S_D = 2713

    SUBS_S_H = 2714

    SUBS_S_W = 2715

    SUBS_U_B = 2716

    SUBS_U_D = 2717

    SUBS_U_H = 2718

    SUBS_U_W = 2719

    SUBU16_MM = 2720

    SUBU16_MMR6 = 2721

    SUBUH_QB = 2722

    SUBUH_QB_MMR2 = 2723

    SUBUH_R_QB = 2724

    SUBUH_R_QB_MMR2 = 2725

    SUBU_MMR6 = 2726

    SUBU_PH = 2727

    SUBU_PH_MMR2 = 2728

    SUBU_QB = 2729

    SUBU_QB_MM = 2730

    SUBU_S_PH = 2731

    SUBU_S_PH_MMR2 = 2732

    SUBU_S_QB = 2733

    SUBU_S_QB_MM = 2734

    SUBVI_B = 2735

    SUBVI_D = 2736

    SUBVI_H = 2737

    SUBVI_W = 2738

    SUBV_B = 2739

    SUBV_D = 2740

    SUBV_H = 2741

    SUBV_W = 2742

    SUB_MM = 2743

    SUB_MMR6 = 2744

    SUBu = 2745

    SUBu_MM = 2746

    SUXC1 = 2747

    SUXC164 = 2748

    SUXC1_MM = 2749

    SW = 2750

    SW16_MM = 2751

    SW16_MMR6 = 2752

    SW64 = 2753

    SWC1 = 2754

    SWC1_MM = 2755

    SWC2 = 2756

    SWC2_MMR6 = 2757

    SWC2_R6 = 2758

    SWC3 = 2759

    SWDSP = 2760

    SWDSP_MM = 2761

    SWE = 2762

    SWE_MM = 2763

    SWL = 2764

    SWL64 = 2765

    SWLE = 2766

    SWLE_MM = 2767

    SWL_MM = 2768

    SWM16_MM = 2769

    SWM16_MMR6 = 2770

    SWM32_MM = 2771

    SWP_MM = 2772

    SWR = 2773

    SWR64 = 2774

    SWRE = 2775

    SWRE_MM = 2776

    SWR_MM = 2777

    SWSP_MM = 2778

    SWSP_MMR6 = 2779

    SWXC1 = 2780

    SWXC1_MM = 2781

    SW_MM = 2782

    SW_MMR6 = 2783

    SYNC = 2784

    SYNCI = 2785

    SYNCI_MM = 2786

    SYNCI_MMR6 = 2787

    SYNC_MM = 2788

    SYNC_MMR6 = 2789

    SYSCALL = 2790

    SYSCALL_MM = 2791

    Save16 = 2792

    SaveX16 = 2793

    SbRxRyOffMemX16 = 2794

    SebRx16 = 2795

    SehRx16 = 2796

    ShRxRyOffMemX16 = 2797

    SllX16 = 2798

    SllvRxRy16 = 2799

    SltRxRy16 = 2800

    SltiRxImm16 = 2801

    SltiRxImmX16 = 2802

    SltiuRxImm16 = 2803

    SltiuRxImmX16 = 2804

    SltuRxRy16 = 2805

    SraX16 = 2806

    SravRxRy16 = 2807

    SrlX16 = 2808

    SrlvRxRy16 = 2809

    SubuRxRyRz16 = 2810

    SwRxRyOffMemX16 = 2811

    SwRxSpImmX16 = 2812

    TEQ = 2813

    TEQI = 2814

    TEQI_MM = 2815

    TEQ_MM = 2816

    TGE = 2817

    TGEI = 2818

    TGEIU = 2819

    TGEIU_MM = 2820

    TGEI_MM = 2821

    TGEU = 2822

    TGEU_MM = 2823

    TGE_MM = 2824

    TLBGINV = 2825

    TLBGINVF = 2826

    TLBGINVF_MM = 2827

    TLBGINV_MM = 2828

    TLBGP = 2829

    TLBGP_MM = 2830

    TLBGR = 2831

    TLBGR_MM = 2832

    TLBGWI = 2833

    TLBGWI_MM = 2834

    TLBGWR = 2835

    TLBGWR_MM = 2836

    TLBINV = 2837

    TLBINVF = 2838

    TLBINVF_MMR6 = 2839

    TLBINV_MMR6 = 2840

    TLBP = 2841

    TLBP_MM = 2842

    TLBR = 2843

    TLBR_MM = 2844

    TLBWI = 2845

    TLBWI_MM = 2846

    TLBWR = 2847

    TLBWR_MM = 2848

    TLT = 2849

    TLTI = 2850

    TLTIU_MM = 2851

    TLTI_MM = 2852

    TLTU = 2853

    TLTU_MM = 2854

    TLT_MM = 2855

    TNE = 2856

    TNEI = 2857

    TNEI_MM = 2858

    TNE_MM = 2859

    TRUNC_L_D64 = 2860

    TRUNC_L_D_MMR6 = 2861

    TRUNC_L_S = 2862

    TRUNC_L_S_MMR6 = 2863

    TRUNC_W_D32 = 2864

    TRUNC_W_D64 = 2865

    TRUNC_W_D_MMR6 = 2866

    TRUNC_W_MM = 2867

    TRUNC_W_S = 2868

    TRUNC_W_S_MM = 2869

    TRUNC_W_S_MMR6 = 2870

    TTLTIU = 2871

    UDIV = 2872

    UDIV_MM = 2873

    V3MULU = 2874

    VMM0 = 2875

    VMULU = 2876

    VSHF_B = 2877

    VSHF_D = 2878

    VSHF_H = 2879

    VSHF_W = 2880

    WAIT = 2881

    WAIT_MM = 2882

    WAIT_MMR6 = 2883

    WRDSP = 2884

    WRDSP_MM = 2885

    WRPGPR_MMR6 = 2886

    WSBH = 2887

    WSBH_MM = 2888

    WSBH_MMR6 = 2889

    XOR = 2890

    XOR16_MM = 2891

    XOR16_MMR6 = 2892

    XOR64 = 2893

    XORI_B = 2894

    XORI_MMR6 = 2895

    XOR_MM = 2896

    XOR_MMR6 = 2897

    XOR_V = 2898

    XORi = 2899

    XORi64 = 2900

    XORi_MM = 2901

    XorRxRxRy16 = 2902

    YIELD = 2903

    INSTRUCTION_LIST_END = 2904

class Instruction(lief.assembly.Instruction):
    @property
    def opcode(self) -> OPCODE: ...
