#include "asm/aarch64/init.hpp"

#include "LIEF/asm/aarch64/registers.hpp"

namespace LIEF::assembly::aarch64::py {
template<>
void create<LIEF::assembly::aarch64::REG>(nb::module_& m) {
  nb::enum_<LIEF::assembly::aarch64::REG> reg(m, "REG");
  reg.value("NoRegister", LIEF::assembly::aarch64::REG::NoRegister)
  .value("FFR", LIEF::assembly::aarch64::REG::FFR)
  .value("FP", LIEF::assembly::aarch64::REG::FP)
  .value("FPCR", LIEF::assembly::aarch64::REG::FPCR)
  .value("FPMR", LIEF::assembly::aarch64::REG::FPMR)
  .value("FPSR", LIEF::assembly::aarch64::REG::FPSR)
  .value("LR", LIEF::assembly::aarch64::REG::LR)
  .value("NZCV", LIEF::assembly::aarch64::REG::NZCV)
  .value("SP", LIEF::assembly::aarch64::REG::SP)
  .value("VG", LIEF::assembly::aarch64::REG::VG)
  .value("WSP", LIEF::assembly::aarch64::REG::WSP)
  .value("WSP_HI", LIEF::assembly::aarch64::REG::WSP_HI)
  .value("WZR", LIEF::assembly::aarch64::REG::WZR)
  .value("WZR_HI", LIEF::assembly::aarch64::REG::WZR_HI)
  .value("XZR", LIEF::assembly::aarch64::REG::XZR)
  .value("ZA", LIEF::assembly::aarch64::REG::ZA)
  .value("B0", LIEF::assembly::aarch64::REG::B0)
  .value("B1", LIEF::assembly::aarch64::REG::B1)
  .value("B2", LIEF::assembly::aarch64::REG::B2)
  .value("B3", LIEF::assembly::aarch64::REG::B3)
  .value("B4", LIEF::assembly::aarch64::REG::B4)
  .value("B5", LIEF::assembly::aarch64::REG::B5)
  .value("B6", LIEF::assembly::aarch64::REG::B6)
  .value("B7", LIEF::assembly::aarch64::REG::B7)
  .value("B8", LIEF::assembly::aarch64::REG::B8)
  .value("B9", LIEF::assembly::aarch64::REG::B9)
  .value("B10", LIEF::assembly::aarch64::REG::B10)
  .value("B11", LIEF::assembly::aarch64::REG::B11)
  .value("B12", LIEF::assembly::aarch64::REG::B12)
  .value("B13", LIEF::assembly::aarch64::REG::B13)
  .value("B14", LIEF::assembly::aarch64::REG::B14)
  .value("B15", LIEF::assembly::aarch64::REG::B15)
  .value("B16", LIEF::assembly::aarch64::REG::B16)
  .value("B17", LIEF::assembly::aarch64::REG::B17)
  .value("B18", LIEF::assembly::aarch64::REG::B18)
  .value("B19", LIEF::assembly::aarch64::REG::B19)
  .value("B20", LIEF::assembly::aarch64::REG::B20)
  .value("B21", LIEF::assembly::aarch64::REG::B21)
  .value("B22", LIEF::assembly::aarch64::REG::B22)
  .value("B23", LIEF::assembly::aarch64::REG::B23)
  .value("B24", LIEF::assembly::aarch64::REG::B24)
  .value("B25", LIEF::assembly::aarch64::REG::B25)
  .value("B26", LIEF::assembly::aarch64::REG::B26)
  .value("B27", LIEF::assembly::aarch64::REG::B27)
  .value("B28", LIEF::assembly::aarch64::REG::B28)
  .value("B29", LIEF::assembly::aarch64::REG::B29)
  .value("B30", LIEF::assembly::aarch64::REG::B30)
  .value("B31", LIEF::assembly::aarch64::REG::B31)
  .value("D0", LIEF::assembly::aarch64::REG::D0)
  .value("D1", LIEF::assembly::aarch64::REG::D1)
  .value("D2", LIEF::assembly::aarch64::REG::D2)
  .value("D3", LIEF::assembly::aarch64::REG::D3)
  .value("D4", LIEF::assembly::aarch64::REG::D4)
  .value("D5", LIEF::assembly::aarch64::REG::D5)
  .value("D6", LIEF::assembly::aarch64::REG::D6)
  .value("D7", LIEF::assembly::aarch64::REG::D7)
  .value("D8", LIEF::assembly::aarch64::REG::D8)
  .value("D9", LIEF::assembly::aarch64::REG::D9)
  .value("D10", LIEF::assembly::aarch64::REG::D10)
  .value("D11", LIEF::assembly::aarch64::REG::D11)
  .value("D12", LIEF::assembly::aarch64::REG::D12)
  .value("D13", LIEF::assembly::aarch64::REG::D13)
  .value("D14", LIEF::assembly::aarch64::REG::D14)
  .value("D15", LIEF::assembly::aarch64::REG::D15)
  .value("D16", LIEF::assembly::aarch64::REG::D16)
  .value("D17", LIEF::assembly::aarch64::REG::D17)
  .value("D18", LIEF::assembly::aarch64::REG::D18)
  .value("D19", LIEF::assembly::aarch64::REG::D19)
  .value("D20", LIEF::assembly::aarch64::REG::D20)
  .value("D21", LIEF::assembly::aarch64::REG::D21)
  .value("D22", LIEF::assembly::aarch64::REG::D22)
  .value("D23", LIEF::assembly::aarch64::REG::D23)
  .value("D24", LIEF::assembly::aarch64::REG::D24)
  .value("D25", LIEF::assembly::aarch64::REG::D25)
  .value("D26", LIEF::assembly::aarch64::REG::D26)
  .value("D27", LIEF::assembly::aarch64::REG::D27)
  .value("D28", LIEF::assembly::aarch64::REG::D28)
  .value("D29", LIEF::assembly::aarch64::REG::D29)
  .value("D30", LIEF::assembly::aarch64::REG::D30)
  .value("D31", LIEF::assembly::aarch64::REG::D31)
  .value("H0", LIEF::assembly::aarch64::REG::H0)
  .value("H1", LIEF::assembly::aarch64::REG::H1)
  .value("H2", LIEF::assembly::aarch64::REG::H2)
  .value("H3", LIEF::assembly::aarch64::REG::H3)
  .value("H4", LIEF::assembly::aarch64::REG::H4)
  .value("H5", LIEF::assembly::aarch64::REG::H5)
  .value("H6", LIEF::assembly::aarch64::REG::H6)
  .value("H7", LIEF::assembly::aarch64::REG::H7)
  .value("H8", LIEF::assembly::aarch64::REG::H8)
  .value("H9", LIEF::assembly::aarch64::REG::H9)
  .value("H10", LIEF::assembly::aarch64::REG::H10)
  .value("H11", LIEF::assembly::aarch64::REG::H11)
  .value("H12", LIEF::assembly::aarch64::REG::H12)
  .value("H13", LIEF::assembly::aarch64::REG::H13)
  .value("H14", LIEF::assembly::aarch64::REG::H14)
  .value("H15", LIEF::assembly::aarch64::REG::H15)
  .value("H16", LIEF::assembly::aarch64::REG::H16)
  .value("H17", LIEF::assembly::aarch64::REG::H17)
  .value("H18", LIEF::assembly::aarch64::REG::H18)
  .value("H19", LIEF::assembly::aarch64::REG::H19)
  .value("H20", LIEF::assembly::aarch64::REG::H20)
  .value("H21", LIEF::assembly::aarch64::REG::H21)
  .value("H22", LIEF::assembly::aarch64::REG::H22)
  .value("H23", LIEF::assembly::aarch64::REG::H23)
  .value("H24", LIEF::assembly::aarch64::REG::H24)
  .value("H25", LIEF::assembly::aarch64::REG::H25)
  .value("H26", LIEF::assembly::aarch64::REG::H26)
  .value("H27", LIEF::assembly::aarch64::REG::H27)
  .value("H28", LIEF::assembly::aarch64::REG::H28)
  .value("H29", LIEF::assembly::aarch64::REG::H29)
  .value("H30", LIEF::assembly::aarch64::REG::H30)
  .value("H31", LIEF::assembly::aarch64::REG::H31)
  .value("P0", LIEF::assembly::aarch64::REG::P0)
  .value("P1", LIEF::assembly::aarch64::REG::P1)
  .value("P2", LIEF::assembly::aarch64::REG::P2)
  .value("P3", LIEF::assembly::aarch64::REG::P3)
  .value("P4", LIEF::assembly::aarch64::REG::P4)
  .value("P5", LIEF::assembly::aarch64::REG::P5)
  .value("P6", LIEF::assembly::aarch64::REG::P6)
  .value("P7", LIEF::assembly::aarch64::REG::P7)
  .value("P8", LIEF::assembly::aarch64::REG::P8)
  .value("P9", LIEF::assembly::aarch64::REG::P9)
  .value("P10", LIEF::assembly::aarch64::REG::P10)
  .value("P11", LIEF::assembly::aarch64::REG::P11)
  .value("P12", LIEF::assembly::aarch64::REG::P12)
  .value("P13", LIEF::assembly::aarch64::REG::P13)
  .value("P14", LIEF::assembly::aarch64::REG::P14)
  .value("P15", LIEF::assembly::aarch64::REG::P15)
  .value("PN0", LIEF::assembly::aarch64::REG::PN0)
  .value("PN1", LIEF::assembly::aarch64::REG::PN1)
  .value("PN2", LIEF::assembly::aarch64::REG::PN2)
  .value("PN3", LIEF::assembly::aarch64::REG::PN3)
  .value("PN4", LIEF::assembly::aarch64::REG::PN4)
  .value("PN5", LIEF::assembly::aarch64::REG::PN5)
  .value("PN6", LIEF::assembly::aarch64::REG::PN6)
  .value("PN7", LIEF::assembly::aarch64::REG::PN7)
  .value("PN8", LIEF::assembly::aarch64::REG::PN8)
  .value("PN9", LIEF::assembly::aarch64::REG::PN9)
  .value("PN10", LIEF::assembly::aarch64::REG::PN10)
  .value("PN11", LIEF::assembly::aarch64::REG::PN11)
  .value("PN12", LIEF::assembly::aarch64::REG::PN12)
  .value("PN13", LIEF::assembly::aarch64::REG::PN13)
  .value("PN14", LIEF::assembly::aarch64::REG::PN14)
  .value("PN15", LIEF::assembly::aarch64::REG::PN15)
  .value("Q0", LIEF::assembly::aarch64::REG::Q0)
  .value("Q1", LIEF::assembly::aarch64::REG::Q1)
  .value("Q2", LIEF::assembly::aarch64::REG::Q2)
  .value("Q3", LIEF::assembly::aarch64::REG::Q3)
  .value("Q4", LIEF::assembly::aarch64::REG::Q4)
  .value("Q5", LIEF::assembly::aarch64::REG::Q5)
  .value("Q6", LIEF::assembly::aarch64::REG::Q6)
  .value("Q7", LIEF::assembly::aarch64::REG::Q7)
  .value("Q8", LIEF::assembly::aarch64::REG::Q8)
  .value("Q9", LIEF::assembly::aarch64::REG::Q9)
  .value("Q10", LIEF::assembly::aarch64::REG::Q10)
  .value("Q11", LIEF::assembly::aarch64::REG::Q11)
  .value("Q12", LIEF::assembly::aarch64::REG::Q12)
  .value("Q13", LIEF::assembly::aarch64::REG::Q13)
  .value("Q14", LIEF::assembly::aarch64::REG::Q14)
  .value("Q15", LIEF::assembly::aarch64::REG::Q15)
  .value("Q16", LIEF::assembly::aarch64::REG::Q16)
  .value("Q17", LIEF::assembly::aarch64::REG::Q17)
  .value("Q18", LIEF::assembly::aarch64::REG::Q18)
  .value("Q19", LIEF::assembly::aarch64::REG::Q19)
  .value("Q20", LIEF::assembly::aarch64::REG::Q20)
  .value("Q21", LIEF::assembly::aarch64::REG::Q21)
  .value("Q22", LIEF::assembly::aarch64::REG::Q22)
  .value("Q23", LIEF::assembly::aarch64::REG::Q23)
  .value("Q24", LIEF::assembly::aarch64::REG::Q24)
  .value("Q25", LIEF::assembly::aarch64::REG::Q25)
  .value("Q26", LIEF::assembly::aarch64::REG::Q26)
  .value("Q27", LIEF::assembly::aarch64::REG::Q27)
  .value("Q28", LIEF::assembly::aarch64::REG::Q28)
  .value("Q29", LIEF::assembly::aarch64::REG::Q29)
  .value("Q30", LIEF::assembly::aarch64::REG::Q30)
  .value("Q31", LIEF::assembly::aarch64::REG::Q31)
  .value("S0", LIEF::assembly::aarch64::REG::S0)
  .value("S1", LIEF::assembly::aarch64::REG::S1)
  .value("S2", LIEF::assembly::aarch64::REG::S2)
  .value("S3", LIEF::assembly::aarch64::REG::S3)
  .value("S4", LIEF::assembly::aarch64::REG::S4)
  .value("S5", LIEF::assembly::aarch64::REG::S5)
  .value("S6", LIEF::assembly::aarch64::REG::S6)
  .value("S7", LIEF::assembly::aarch64::REG::S7)
  .value("S8", LIEF::assembly::aarch64::REG::S8)
  .value("S9", LIEF::assembly::aarch64::REG::S9)
  .value("S10", LIEF::assembly::aarch64::REG::S10)
  .value("S11", LIEF::assembly::aarch64::REG::S11)
  .value("S12", LIEF::assembly::aarch64::REG::S12)
  .value("S13", LIEF::assembly::aarch64::REG::S13)
  .value("S14", LIEF::assembly::aarch64::REG::S14)
  .value("S15", LIEF::assembly::aarch64::REG::S15)
  .value("S16", LIEF::assembly::aarch64::REG::S16)
  .value("S17", LIEF::assembly::aarch64::REG::S17)
  .value("S18", LIEF::assembly::aarch64::REG::S18)
  .value("S19", LIEF::assembly::aarch64::REG::S19)
  .value("S20", LIEF::assembly::aarch64::REG::S20)
  .value("S21", LIEF::assembly::aarch64::REG::S21)
  .value("S22", LIEF::assembly::aarch64::REG::S22)
  .value("S23", LIEF::assembly::aarch64::REG::S23)
  .value("S24", LIEF::assembly::aarch64::REG::S24)
  .value("S25", LIEF::assembly::aarch64::REG::S25)
  .value("S26", LIEF::assembly::aarch64::REG::S26)
  .value("S27", LIEF::assembly::aarch64::REG::S27)
  .value("S28", LIEF::assembly::aarch64::REG::S28)
  .value("S29", LIEF::assembly::aarch64::REG::S29)
  .value("S30", LIEF::assembly::aarch64::REG::S30)
  .value("S31", LIEF::assembly::aarch64::REG::S31)
  .value("W0", LIEF::assembly::aarch64::REG::W0)
  .value("W1", LIEF::assembly::aarch64::REG::W1)
  .value("W2", LIEF::assembly::aarch64::REG::W2)
  .value("W3", LIEF::assembly::aarch64::REG::W3)
  .value("W4", LIEF::assembly::aarch64::REG::W4)
  .value("W5", LIEF::assembly::aarch64::REG::W5)
  .value("W6", LIEF::assembly::aarch64::REG::W6)
  .value("W7", LIEF::assembly::aarch64::REG::W7)
  .value("W8", LIEF::assembly::aarch64::REG::W8)
  .value("W9", LIEF::assembly::aarch64::REG::W9)
  .value("W10", LIEF::assembly::aarch64::REG::W10)
  .value("W11", LIEF::assembly::aarch64::REG::W11)
  .value("W12", LIEF::assembly::aarch64::REG::W12)
  .value("W13", LIEF::assembly::aarch64::REG::W13)
  .value("W14", LIEF::assembly::aarch64::REG::W14)
  .value("W15", LIEF::assembly::aarch64::REG::W15)
  .value("W16", LIEF::assembly::aarch64::REG::W16)
  .value("W17", LIEF::assembly::aarch64::REG::W17)
  .value("W18", LIEF::assembly::aarch64::REG::W18)
  .value("W19", LIEF::assembly::aarch64::REG::W19)
  .value("W20", LIEF::assembly::aarch64::REG::W20)
  .value("W21", LIEF::assembly::aarch64::REG::W21)
  .value("W22", LIEF::assembly::aarch64::REG::W22)
  .value("W23", LIEF::assembly::aarch64::REG::W23)
  .value("W24", LIEF::assembly::aarch64::REG::W24)
  .value("W25", LIEF::assembly::aarch64::REG::W25)
  .value("W26", LIEF::assembly::aarch64::REG::W26)
  .value("W27", LIEF::assembly::aarch64::REG::W27)
  .value("W28", LIEF::assembly::aarch64::REG::W28)
  .value("W29", LIEF::assembly::aarch64::REG::W29)
  .value("W30", LIEF::assembly::aarch64::REG::W30)
  .value("X0", LIEF::assembly::aarch64::REG::X0)
  .value("X1", LIEF::assembly::aarch64::REG::X1)
  .value("X2", LIEF::assembly::aarch64::REG::X2)
  .value("X3", LIEF::assembly::aarch64::REG::X3)
  .value("X4", LIEF::assembly::aarch64::REG::X4)
  .value("X5", LIEF::assembly::aarch64::REG::X5)
  .value("X6", LIEF::assembly::aarch64::REG::X6)
  .value("X7", LIEF::assembly::aarch64::REG::X7)
  .value("X8", LIEF::assembly::aarch64::REG::X8)
  .value("X9", LIEF::assembly::aarch64::REG::X9)
  .value("X10", LIEF::assembly::aarch64::REG::X10)
  .value("X11", LIEF::assembly::aarch64::REG::X11)
  .value("X12", LIEF::assembly::aarch64::REG::X12)
  .value("X13", LIEF::assembly::aarch64::REG::X13)
  .value("X14", LIEF::assembly::aarch64::REG::X14)
  .value("X15", LIEF::assembly::aarch64::REG::X15)
  .value("X16", LIEF::assembly::aarch64::REG::X16)
  .value("X17", LIEF::assembly::aarch64::REG::X17)
  .value("X18", LIEF::assembly::aarch64::REG::X18)
  .value("X19", LIEF::assembly::aarch64::REG::X19)
  .value("X20", LIEF::assembly::aarch64::REG::X20)
  .value("X21", LIEF::assembly::aarch64::REG::X21)
  .value("X22", LIEF::assembly::aarch64::REG::X22)
  .value("X23", LIEF::assembly::aarch64::REG::X23)
  .value("X24", LIEF::assembly::aarch64::REG::X24)
  .value("X25", LIEF::assembly::aarch64::REG::X25)
  .value("X26", LIEF::assembly::aarch64::REG::X26)
  .value("X27", LIEF::assembly::aarch64::REG::X27)
  .value("X28", LIEF::assembly::aarch64::REG::X28)
  .value("Z0", LIEF::assembly::aarch64::REG::Z0)
  .value("Z1", LIEF::assembly::aarch64::REG::Z1)
  .value("Z2", LIEF::assembly::aarch64::REG::Z2)
  .value("Z3", LIEF::assembly::aarch64::REG::Z3)
  .value("Z4", LIEF::assembly::aarch64::REG::Z4)
  .value("Z5", LIEF::assembly::aarch64::REG::Z5)
  .value("Z6", LIEF::assembly::aarch64::REG::Z6)
  .value("Z7", LIEF::assembly::aarch64::REG::Z7)
  .value("Z8", LIEF::assembly::aarch64::REG::Z8)
  .value("Z9", LIEF::assembly::aarch64::REG::Z9)
  .value("Z10", LIEF::assembly::aarch64::REG::Z10)
  .value("Z11", LIEF::assembly::aarch64::REG::Z11)
  .value("Z12", LIEF::assembly::aarch64::REG::Z12)
  .value("Z13", LIEF::assembly::aarch64::REG::Z13)
  .value("Z14", LIEF::assembly::aarch64::REG::Z14)
  .value("Z15", LIEF::assembly::aarch64::REG::Z15)
  .value("Z16", LIEF::assembly::aarch64::REG::Z16)
  .value("Z17", LIEF::assembly::aarch64::REG::Z17)
  .value("Z18", LIEF::assembly::aarch64::REG::Z18)
  .value("Z19", LIEF::assembly::aarch64::REG::Z19)
  .value("Z20", LIEF::assembly::aarch64::REG::Z20)
  .value("Z21", LIEF::assembly::aarch64::REG::Z21)
  .value("Z22", LIEF::assembly::aarch64::REG::Z22)
  .value("Z23", LIEF::assembly::aarch64::REG::Z23)
  .value("Z24", LIEF::assembly::aarch64::REG::Z24)
  .value("Z25", LIEF::assembly::aarch64::REG::Z25)
  .value("Z26", LIEF::assembly::aarch64::REG::Z26)
  .value("Z27", LIEF::assembly::aarch64::REG::Z27)
  .value("Z28", LIEF::assembly::aarch64::REG::Z28)
  .value("Z29", LIEF::assembly::aarch64::REG::Z29)
  .value("Z30", LIEF::assembly::aarch64::REG::Z30)
  .value("Z31", LIEF::assembly::aarch64::REG::Z31);
  reg.value("ZAB0", LIEF::assembly::aarch64::REG::ZAB0)
  .value("ZAD0", LIEF::assembly::aarch64::REG::ZAD0)
  .value("ZAD1", LIEF::assembly::aarch64::REG::ZAD1)
  .value("ZAD2", LIEF::assembly::aarch64::REG::ZAD2)
  .value("ZAD3", LIEF::assembly::aarch64::REG::ZAD3)
  .value("ZAD4", LIEF::assembly::aarch64::REG::ZAD4)
  .value("ZAD5", LIEF::assembly::aarch64::REG::ZAD5)
  .value("ZAD6", LIEF::assembly::aarch64::REG::ZAD6)
  .value("ZAD7", LIEF::assembly::aarch64::REG::ZAD7)
  .value("ZAH0", LIEF::assembly::aarch64::REG::ZAH0)
  .value("ZAH1", LIEF::assembly::aarch64::REG::ZAH1)
  .value("ZAQ0", LIEF::assembly::aarch64::REG::ZAQ0)
  .value("ZAQ1", LIEF::assembly::aarch64::REG::ZAQ1)
  .value("ZAQ2", LIEF::assembly::aarch64::REG::ZAQ2)
  .value("ZAQ3", LIEF::assembly::aarch64::REG::ZAQ3)
  .value("ZAQ4", LIEF::assembly::aarch64::REG::ZAQ4)
  .value("ZAQ5", LIEF::assembly::aarch64::REG::ZAQ5)
  .value("ZAQ6", LIEF::assembly::aarch64::REG::ZAQ6)
  .value("ZAQ7", LIEF::assembly::aarch64::REG::ZAQ7)
  .value("ZAQ8", LIEF::assembly::aarch64::REG::ZAQ8)
  .value("ZAQ9", LIEF::assembly::aarch64::REG::ZAQ9)
  .value("ZAQ10", LIEF::assembly::aarch64::REG::ZAQ10)
  .value("ZAQ11", LIEF::assembly::aarch64::REG::ZAQ11)
  .value("ZAQ12", LIEF::assembly::aarch64::REG::ZAQ12)
  .value("ZAQ13", LIEF::assembly::aarch64::REG::ZAQ13)
  .value("ZAQ14", LIEF::assembly::aarch64::REG::ZAQ14)
  .value("ZAQ15", LIEF::assembly::aarch64::REG::ZAQ15)
  .value("ZAS0", LIEF::assembly::aarch64::REG::ZAS0)
  .value("ZAS1", LIEF::assembly::aarch64::REG::ZAS1)
  .value("ZAS2", LIEF::assembly::aarch64::REG::ZAS2)
  .value("ZAS3", LIEF::assembly::aarch64::REG::ZAS3)
  .value("ZT0", LIEF::assembly::aarch64::REG::ZT0)
  .value("B0_HI", LIEF::assembly::aarch64::REG::B0_HI)
  .value("B1_HI", LIEF::assembly::aarch64::REG::B1_HI)
  .value("B2_HI", LIEF::assembly::aarch64::REG::B2_HI)
  .value("B3_HI", LIEF::assembly::aarch64::REG::B3_HI)
  .value("B4_HI", LIEF::assembly::aarch64::REG::B4_HI)
  .value("B5_HI", LIEF::assembly::aarch64::REG::B5_HI)
  .value("B6_HI", LIEF::assembly::aarch64::REG::B6_HI)
  .value("B7_HI", LIEF::assembly::aarch64::REG::B7_HI)
  .value("B8_HI", LIEF::assembly::aarch64::REG::B8_HI)
  .value("B9_HI", LIEF::assembly::aarch64::REG::B9_HI)
  .value("B10_HI", LIEF::assembly::aarch64::REG::B10_HI)
  .value("B11_HI", LIEF::assembly::aarch64::REG::B11_HI)
  .value("B12_HI", LIEF::assembly::aarch64::REG::B12_HI)
  .value("B13_HI", LIEF::assembly::aarch64::REG::B13_HI)
  .value("B14_HI", LIEF::assembly::aarch64::REG::B14_HI)
  .value("B15_HI", LIEF::assembly::aarch64::REG::B15_HI)
  .value("B16_HI", LIEF::assembly::aarch64::REG::B16_HI)
  .value("B17_HI", LIEF::assembly::aarch64::REG::B17_HI)
  .value("B18_HI", LIEF::assembly::aarch64::REG::B18_HI)
  .value("B19_HI", LIEF::assembly::aarch64::REG::B19_HI)
  .value("B20_HI", LIEF::assembly::aarch64::REG::B20_HI)
  .value("B21_HI", LIEF::assembly::aarch64::REG::B21_HI)
  .value("B22_HI", LIEF::assembly::aarch64::REG::B22_HI)
  .value("B23_HI", LIEF::assembly::aarch64::REG::B23_HI)
  .value("B24_HI", LIEF::assembly::aarch64::REG::B24_HI)
  .value("B25_HI", LIEF::assembly::aarch64::REG::B25_HI)
  .value("B26_HI", LIEF::assembly::aarch64::REG::B26_HI)
  .value("B27_HI", LIEF::assembly::aarch64::REG::B27_HI)
  .value("B28_HI", LIEF::assembly::aarch64::REG::B28_HI)
  .value("B29_HI", LIEF::assembly::aarch64::REG::B29_HI)
  .value("B30_HI", LIEF::assembly::aarch64::REG::B30_HI)
  .value("B31_HI", LIEF::assembly::aarch64::REG::B31_HI)
  .value("D0_HI", LIEF::assembly::aarch64::REG::D0_HI)
  .value("D1_HI", LIEF::assembly::aarch64::REG::D1_HI)
  .value("D2_HI", LIEF::assembly::aarch64::REG::D2_HI)
  .value("D3_HI", LIEF::assembly::aarch64::REG::D3_HI)
  .value("D4_HI", LIEF::assembly::aarch64::REG::D4_HI)
  .value("D5_HI", LIEF::assembly::aarch64::REG::D5_HI)
  .value("D6_HI", LIEF::assembly::aarch64::REG::D6_HI)
  .value("D7_HI", LIEF::assembly::aarch64::REG::D7_HI)
  .value("D8_HI", LIEF::assembly::aarch64::REG::D8_HI)
  .value("D9_HI", LIEF::assembly::aarch64::REG::D9_HI)
  .value("D10_HI", LIEF::assembly::aarch64::REG::D10_HI)
  .value("D11_HI", LIEF::assembly::aarch64::REG::D11_HI)
  .value("D12_HI", LIEF::assembly::aarch64::REG::D12_HI)
  .value("D13_HI", LIEF::assembly::aarch64::REG::D13_HI)
  .value("D14_HI", LIEF::assembly::aarch64::REG::D14_HI)
  .value("D15_HI", LIEF::assembly::aarch64::REG::D15_HI)
  .value("D16_HI", LIEF::assembly::aarch64::REG::D16_HI)
  .value("D17_HI", LIEF::assembly::aarch64::REG::D17_HI)
  .value("D18_HI", LIEF::assembly::aarch64::REG::D18_HI)
  .value("D19_HI", LIEF::assembly::aarch64::REG::D19_HI)
  .value("D20_HI", LIEF::assembly::aarch64::REG::D20_HI)
  .value("D21_HI", LIEF::assembly::aarch64::REG::D21_HI)
  .value("D22_HI", LIEF::assembly::aarch64::REG::D22_HI)
  .value("D23_HI", LIEF::assembly::aarch64::REG::D23_HI)
  .value("D24_HI", LIEF::assembly::aarch64::REG::D24_HI)
  .value("D25_HI", LIEF::assembly::aarch64::REG::D25_HI)
  .value("D26_HI", LIEF::assembly::aarch64::REG::D26_HI)
  .value("D27_HI", LIEF::assembly::aarch64::REG::D27_HI)
  .value("D28_HI", LIEF::assembly::aarch64::REG::D28_HI)
  .value("D29_HI", LIEF::assembly::aarch64::REG::D29_HI)
  .value("D30_HI", LIEF::assembly::aarch64::REG::D30_HI)
  .value("D31_HI", LIEF::assembly::aarch64::REG::D31_HI)
  .value("H0_HI", LIEF::assembly::aarch64::REG::H0_HI)
  .value("H1_HI", LIEF::assembly::aarch64::REG::H1_HI)
  .value("H2_HI", LIEF::assembly::aarch64::REG::H2_HI)
  .value("H3_HI", LIEF::assembly::aarch64::REG::H3_HI)
  .value("H4_HI", LIEF::assembly::aarch64::REG::H4_HI)
  .value("H5_HI", LIEF::assembly::aarch64::REG::H5_HI)
  .value("H6_HI", LIEF::assembly::aarch64::REG::H6_HI)
  .value("H7_HI", LIEF::assembly::aarch64::REG::H7_HI)
  .value("H8_HI", LIEF::assembly::aarch64::REG::H8_HI)
  .value("H9_HI", LIEF::assembly::aarch64::REG::H9_HI)
  .value("H10_HI", LIEF::assembly::aarch64::REG::H10_HI)
  .value("H11_HI", LIEF::assembly::aarch64::REG::H11_HI)
  .value("H12_HI", LIEF::assembly::aarch64::REG::H12_HI)
  .value("H13_HI", LIEF::assembly::aarch64::REG::H13_HI)
  .value("H14_HI", LIEF::assembly::aarch64::REG::H14_HI)
  .value("H15_HI", LIEF::assembly::aarch64::REG::H15_HI)
  .value("H16_HI", LIEF::assembly::aarch64::REG::H16_HI)
  .value("H17_HI", LIEF::assembly::aarch64::REG::H17_HI)
  .value("H18_HI", LIEF::assembly::aarch64::REG::H18_HI)
  .value("H19_HI", LIEF::assembly::aarch64::REG::H19_HI)
  .value("H20_HI", LIEF::assembly::aarch64::REG::H20_HI)
  .value("H21_HI", LIEF::assembly::aarch64::REG::H21_HI)
  .value("H22_HI", LIEF::assembly::aarch64::REG::H22_HI)
  .value("H23_HI", LIEF::assembly::aarch64::REG::H23_HI)
  .value("H24_HI", LIEF::assembly::aarch64::REG::H24_HI)
  .value("H25_HI", LIEF::assembly::aarch64::REG::H25_HI)
  .value("H26_HI", LIEF::assembly::aarch64::REG::H26_HI)
  .value("H27_HI", LIEF::assembly::aarch64::REG::H27_HI)
  .value("H28_HI", LIEF::assembly::aarch64::REG::H28_HI)
  .value("H29_HI", LIEF::assembly::aarch64::REG::H29_HI)
  .value("H30_HI", LIEF::assembly::aarch64::REG::H30_HI)
  .value("H31_HI", LIEF::assembly::aarch64::REG::H31_HI)
  .value("Q0_HI", LIEF::assembly::aarch64::REG::Q0_HI)
  .value("Q1_HI", LIEF::assembly::aarch64::REG::Q1_HI)
  .value("Q2_HI", LIEF::assembly::aarch64::REG::Q2_HI)
  .value("Q3_HI", LIEF::assembly::aarch64::REG::Q3_HI)
  .value("Q4_HI", LIEF::assembly::aarch64::REG::Q4_HI)
  .value("Q5_HI", LIEF::assembly::aarch64::REG::Q5_HI)
  .value("Q6_HI", LIEF::assembly::aarch64::REG::Q6_HI)
  .value("Q7_HI", LIEF::assembly::aarch64::REG::Q7_HI)
  .value("Q8_HI", LIEF::assembly::aarch64::REG::Q8_HI)
  .value("Q9_HI", LIEF::assembly::aarch64::REG::Q9_HI)
  .value("Q10_HI", LIEF::assembly::aarch64::REG::Q10_HI)
  .value("Q11_HI", LIEF::assembly::aarch64::REG::Q11_HI)
  .value("Q12_HI", LIEF::assembly::aarch64::REG::Q12_HI)
  .value("Q13_HI", LIEF::assembly::aarch64::REG::Q13_HI)
  .value("Q14_HI", LIEF::assembly::aarch64::REG::Q14_HI)
  .value("Q15_HI", LIEF::assembly::aarch64::REG::Q15_HI)
  .value("Q16_HI", LIEF::assembly::aarch64::REG::Q16_HI)
  .value("Q17_HI", LIEF::assembly::aarch64::REG::Q17_HI)
  .value("Q18_HI", LIEF::assembly::aarch64::REG::Q18_HI)
  .value("Q19_HI", LIEF::assembly::aarch64::REG::Q19_HI)
  .value("Q20_HI", LIEF::assembly::aarch64::REG::Q20_HI)
  .value("Q21_HI", LIEF::assembly::aarch64::REG::Q21_HI)
  .value("Q22_HI", LIEF::assembly::aarch64::REG::Q22_HI)
  .value("Q23_HI", LIEF::assembly::aarch64::REG::Q23_HI)
  .value("Q24_HI", LIEF::assembly::aarch64::REG::Q24_HI)
  .value("Q25_HI", LIEF::assembly::aarch64::REG::Q25_HI)
  .value("Q26_HI", LIEF::assembly::aarch64::REG::Q26_HI)
  .value("Q27_HI", LIEF::assembly::aarch64::REG::Q27_HI)
  .value("Q28_HI", LIEF::assembly::aarch64::REG::Q28_HI)
  .value("Q29_HI", LIEF::assembly::aarch64::REG::Q29_HI)
  .value("Q30_HI", LIEF::assembly::aarch64::REG::Q30_HI)
  .value("Q31_HI", LIEF::assembly::aarch64::REG::Q31_HI)
  .value("S0_HI", LIEF::assembly::aarch64::REG::S0_HI)
  .value("S1_HI", LIEF::assembly::aarch64::REG::S1_HI)
  .value("S2_HI", LIEF::assembly::aarch64::REG::S2_HI)
  .value("S3_HI", LIEF::assembly::aarch64::REG::S3_HI)
  .value("S4_HI", LIEF::assembly::aarch64::REG::S4_HI)
  .value("S5_HI", LIEF::assembly::aarch64::REG::S5_HI)
  .value("S6_HI", LIEF::assembly::aarch64::REG::S6_HI)
  .value("S7_HI", LIEF::assembly::aarch64::REG::S7_HI)
  .value("S8_HI", LIEF::assembly::aarch64::REG::S8_HI)
  .value("S9_HI", LIEF::assembly::aarch64::REG::S9_HI)
  .value("S10_HI", LIEF::assembly::aarch64::REG::S10_HI)
  .value("S11_HI", LIEF::assembly::aarch64::REG::S11_HI)
  .value("S12_HI", LIEF::assembly::aarch64::REG::S12_HI)
  .value("S13_HI", LIEF::assembly::aarch64::REG::S13_HI)
  .value("S14_HI", LIEF::assembly::aarch64::REG::S14_HI)
  .value("S15_HI", LIEF::assembly::aarch64::REG::S15_HI)
  .value("S16_HI", LIEF::assembly::aarch64::REG::S16_HI)
  .value("S17_HI", LIEF::assembly::aarch64::REG::S17_HI)
  .value("S18_HI", LIEF::assembly::aarch64::REG::S18_HI)
  .value("S19_HI", LIEF::assembly::aarch64::REG::S19_HI)
  .value("S20_HI", LIEF::assembly::aarch64::REG::S20_HI)
  .value("S21_HI", LIEF::assembly::aarch64::REG::S21_HI)
  .value("S22_HI", LIEF::assembly::aarch64::REG::S22_HI)
  .value("S23_HI", LIEF::assembly::aarch64::REG::S23_HI)
  .value("S24_HI", LIEF::assembly::aarch64::REG::S24_HI)
  .value("S25_HI", LIEF::assembly::aarch64::REG::S25_HI)
  .value("S26_HI", LIEF::assembly::aarch64::REG::S26_HI)
  .value("S27_HI", LIEF::assembly::aarch64::REG::S27_HI)
  .value("S28_HI", LIEF::assembly::aarch64::REG::S28_HI)
  .value("S29_HI", LIEF::assembly::aarch64::REG::S29_HI)
  .value("S30_HI", LIEF::assembly::aarch64::REG::S30_HI)
  .value("S31_HI", LIEF::assembly::aarch64::REG::S31_HI)
  .value("W0_HI", LIEF::assembly::aarch64::REG::W0_HI)
  .value("W1_HI", LIEF::assembly::aarch64::REG::W1_HI)
  .value("W2_HI", LIEF::assembly::aarch64::REG::W2_HI)
  .value("W3_HI", LIEF::assembly::aarch64::REG::W3_HI)
  .value("W4_HI", LIEF::assembly::aarch64::REG::W4_HI)
  .value("W5_HI", LIEF::assembly::aarch64::REG::W5_HI)
  .value("W6_HI", LIEF::assembly::aarch64::REG::W6_HI)
  .value("W7_HI", LIEF::assembly::aarch64::REG::W7_HI)
  .value("W8_HI", LIEF::assembly::aarch64::REG::W8_HI)
  .value("W9_HI", LIEF::assembly::aarch64::REG::W9_HI)
  .value("W10_HI", LIEF::assembly::aarch64::REG::W10_HI)
  .value("W11_HI", LIEF::assembly::aarch64::REG::W11_HI)
  .value("W12_HI", LIEF::assembly::aarch64::REG::W12_HI)
  .value("W13_HI", LIEF::assembly::aarch64::REG::W13_HI)
  .value("W14_HI", LIEF::assembly::aarch64::REG::W14_HI)
  .value("W15_HI", LIEF::assembly::aarch64::REG::W15_HI)
  .value("W16_HI", LIEF::assembly::aarch64::REG::W16_HI)
  .value("W17_HI", LIEF::assembly::aarch64::REG::W17_HI)
  .value("W18_HI", LIEF::assembly::aarch64::REG::W18_HI)
  .value("W19_HI", LIEF::assembly::aarch64::REG::W19_HI)
  .value("W20_HI", LIEF::assembly::aarch64::REG::W20_HI)
  .value("W21_HI", LIEF::assembly::aarch64::REG::W21_HI)
  .value("W22_HI", LIEF::assembly::aarch64::REG::W22_HI)
  .value("W23_HI", LIEF::assembly::aarch64::REG::W23_HI)
  .value("W24_HI", LIEF::assembly::aarch64::REG::W24_HI)
  .value("W25_HI", LIEF::assembly::aarch64::REG::W25_HI)
  .value("W26_HI", LIEF::assembly::aarch64::REG::W26_HI)
  .value("W27_HI", LIEF::assembly::aarch64::REG::W27_HI)
  .value("W28_HI", LIEF::assembly::aarch64::REG::W28_HI)
  .value("W29_HI", LIEF::assembly::aarch64::REG::W29_HI)
  .value("W30_HI", LIEF::assembly::aarch64::REG::W30_HI)
  .value("D0_D1", LIEF::assembly::aarch64::REG::D0_D1)
  .value("D1_D2", LIEF::assembly::aarch64::REG::D1_D2)
  .value("D2_D3", LIEF::assembly::aarch64::REG::D2_D3)
  .value("D3_D4", LIEF::assembly::aarch64::REG::D3_D4)
  .value("D4_D5", LIEF::assembly::aarch64::REG::D4_D5)
  .value("D5_D6", LIEF::assembly::aarch64::REG::D5_D6)
  .value("D6_D7", LIEF::assembly::aarch64::REG::D6_D7)
  .value("D7_D8", LIEF::assembly::aarch64::REG::D7_D8)
  .value("D8_D9", LIEF::assembly::aarch64::REG::D8_D9)
  .value("D9_D10", LIEF::assembly::aarch64::REG::D9_D10)
  .value("D10_D11", LIEF::assembly::aarch64::REG::D10_D11)
  .value("D11_D12", LIEF::assembly::aarch64::REG::D11_D12)
  .value("D12_D13", LIEF::assembly::aarch64::REG::D12_D13)
  .value("D13_D14", LIEF::assembly::aarch64::REG::D13_D14)
  .value("D14_D15", LIEF::assembly::aarch64::REG::D14_D15)
  .value("D15_D16", LIEF::assembly::aarch64::REG::D15_D16)
  .value("D16_D17", LIEF::assembly::aarch64::REG::D16_D17)
  .value("D17_D18", LIEF::assembly::aarch64::REG::D17_D18)
  .value("D18_D19", LIEF::assembly::aarch64::REG::D18_D19)
  .value("D19_D20", LIEF::assembly::aarch64::REG::D19_D20)
  .value("D20_D21", LIEF::assembly::aarch64::REG::D20_D21)
  .value("D21_D22", LIEF::assembly::aarch64::REG::D21_D22)
  .value("D22_D23", LIEF::assembly::aarch64::REG::D22_D23)
  .value("D23_D24", LIEF::assembly::aarch64::REG::D23_D24)
  .value("D24_D25", LIEF::assembly::aarch64::REG::D24_D25)
  .value("D25_D26", LIEF::assembly::aarch64::REG::D25_D26)
  .value("D26_D27", LIEF::assembly::aarch64::REG::D26_D27)
  .value("D27_D28", LIEF::assembly::aarch64::REG::D27_D28)
  .value("D28_D29", LIEF::assembly::aarch64::REG::D28_D29)
  .value("D29_D30", LIEF::assembly::aarch64::REG::D29_D30)
  .value("D30_D31", LIEF::assembly::aarch64::REG::D30_D31)
  .value("D31_D0", LIEF::assembly::aarch64::REG::D31_D0)
  .value("D0_D1_D2_D3", LIEF::assembly::aarch64::REG::D0_D1_D2_D3)
  .value("D1_D2_D3_D4", LIEF::assembly::aarch64::REG::D1_D2_D3_D4)
  .value("D2_D3_D4_D5", LIEF::assembly::aarch64::REG::D2_D3_D4_D5)
  .value("D3_D4_D5_D6", LIEF::assembly::aarch64::REG::D3_D4_D5_D6)
  .value("D4_D5_D6_D7", LIEF::assembly::aarch64::REG::D4_D5_D6_D7)
  .value("D5_D6_D7_D8", LIEF::assembly::aarch64::REG::D5_D6_D7_D8)
  .value("D6_D7_D8_D9", LIEF::assembly::aarch64::REG::D6_D7_D8_D9)
  .value("D7_D8_D9_D10", LIEF::assembly::aarch64::REG::D7_D8_D9_D10)
  .value("D8_D9_D10_D11", LIEF::assembly::aarch64::REG::D8_D9_D10_D11)
  .value("D9_D10_D11_D12", LIEF::assembly::aarch64::REG::D9_D10_D11_D12)
  .value("D10_D11_D12_D13", LIEF::assembly::aarch64::REG::D10_D11_D12_D13)
  .value("D11_D12_D13_D14", LIEF::assembly::aarch64::REG::D11_D12_D13_D14)
  .value("D12_D13_D14_D15", LIEF::assembly::aarch64::REG::D12_D13_D14_D15)
  .value("D13_D14_D15_D16", LIEF::assembly::aarch64::REG::D13_D14_D15_D16)
  .value("D14_D15_D16_D17", LIEF::assembly::aarch64::REG::D14_D15_D16_D17)
  .value("D15_D16_D17_D18", LIEF::assembly::aarch64::REG::D15_D16_D17_D18)
  .value("D16_D17_D18_D19", LIEF::assembly::aarch64::REG::D16_D17_D18_D19)
  .value("D17_D18_D19_D20", LIEF::assembly::aarch64::REG::D17_D18_D19_D20)
  .value("D18_D19_D20_D21", LIEF::assembly::aarch64::REG::D18_D19_D20_D21)
  .value("D19_D20_D21_D22", LIEF::assembly::aarch64::REG::D19_D20_D21_D22)
  .value("D20_D21_D22_D23", LIEF::assembly::aarch64::REG::D20_D21_D22_D23)
  .value("D21_D22_D23_D24", LIEF::assembly::aarch64::REG::D21_D22_D23_D24)
  .value("D22_D23_D24_D25", LIEF::assembly::aarch64::REG::D22_D23_D24_D25)
  .value("D23_D24_D25_D26", LIEF::assembly::aarch64::REG::D23_D24_D25_D26)
  .value("D24_D25_D26_D27", LIEF::assembly::aarch64::REG::D24_D25_D26_D27)
  .value("D25_D26_D27_D28", LIEF::assembly::aarch64::REG::D25_D26_D27_D28)
  .value("D26_D27_D28_D29", LIEF::assembly::aarch64::REG::D26_D27_D28_D29)
  .value("D27_D28_D29_D30", LIEF::assembly::aarch64::REG::D27_D28_D29_D30)
  .value("D28_D29_D30_D31", LIEF::assembly::aarch64::REG::D28_D29_D30_D31)
  .value("D29_D30_D31_D0", LIEF::assembly::aarch64::REG::D29_D30_D31_D0)
  .value("D30_D31_D0_D1", LIEF::assembly::aarch64::REG::D30_D31_D0_D1)
  .value("D31_D0_D1_D2", LIEF::assembly::aarch64::REG::D31_D0_D1_D2)
  .value("D0_D1_D2", LIEF::assembly::aarch64::REG::D0_D1_D2)
  .value("D1_D2_D3", LIEF::assembly::aarch64::REG::D1_D2_D3)
  .value("D2_D3_D4", LIEF::assembly::aarch64::REG::D2_D3_D4)
  .value("D3_D4_D5", LIEF::assembly::aarch64::REG::D3_D4_D5)
  .value("D4_D5_D6", LIEF::assembly::aarch64::REG::D4_D5_D6)
  .value("D5_D6_D7", LIEF::assembly::aarch64::REG::D5_D6_D7)
  .value("D6_D7_D8", LIEF::assembly::aarch64::REG::D6_D7_D8)
  .value("D7_D8_D9", LIEF::assembly::aarch64::REG::D7_D8_D9)
  .value("D8_D9_D10", LIEF::assembly::aarch64::REG::D8_D9_D10)
  .value("D9_D10_D11", LIEF::assembly::aarch64::REG::D9_D10_D11)
  .value("D10_D11_D12", LIEF::assembly::aarch64::REG::D10_D11_D12)
  .value("D11_D12_D13", LIEF::assembly::aarch64::REG::D11_D12_D13)
  .value("D12_D13_D14", LIEF::assembly::aarch64::REG::D12_D13_D14);
  reg.value("D13_D14_D15", LIEF::assembly::aarch64::REG::D13_D14_D15)
  .value("D14_D15_D16", LIEF::assembly::aarch64::REG::D14_D15_D16)
  .value("D15_D16_D17", LIEF::assembly::aarch64::REG::D15_D16_D17)
  .value("D16_D17_D18", LIEF::assembly::aarch64::REG::D16_D17_D18)
  .value("D17_D18_D19", LIEF::assembly::aarch64::REG::D17_D18_D19)
  .value("D18_D19_D20", LIEF::assembly::aarch64::REG::D18_D19_D20)
  .value("D19_D20_D21", LIEF::assembly::aarch64::REG::D19_D20_D21)
  .value("D20_D21_D22", LIEF::assembly::aarch64::REG::D20_D21_D22)
  .value("D21_D22_D23", LIEF::assembly::aarch64::REG::D21_D22_D23)
  .value("D22_D23_D24", LIEF::assembly::aarch64::REG::D22_D23_D24)
  .value("D23_D24_D25", LIEF::assembly::aarch64::REG::D23_D24_D25)
  .value("D24_D25_D26", LIEF::assembly::aarch64::REG::D24_D25_D26)
  .value("D25_D26_D27", LIEF::assembly::aarch64::REG::D25_D26_D27)
  .value("D26_D27_D28", LIEF::assembly::aarch64::REG::D26_D27_D28)
  .value("D27_D28_D29", LIEF::assembly::aarch64::REG::D27_D28_D29)
  .value("D28_D29_D30", LIEF::assembly::aarch64::REG::D28_D29_D30)
  .value("D29_D30_D31", LIEF::assembly::aarch64::REG::D29_D30_D31)
  .value("D30_D31_D0", LIEF::assembly::aarch64::REG::D30_D31_D0)
  .value("D31_D0_D1", LIEF::assembly::aarch64::REG::D31_D0_D1)
  .value("P0_P1", LIEF::assembly::aarch64::REG::P0_P1)
  .value("P1_P2", LIEF::assembly::aarch64::REG::P1_P2)
  .value("P2_P3", LIEF::assembly::aarch64::REG::P2_P3)
  .value("P3_P4", LIEF::assembly::aarch64::REG::P3_P4)
  .value("P4_P5", LIEF::assembly::aarch64::REG::P4_P5)
  .value("P5_P6", LIEF::assembly::aarch64::REG::P5_P6)
  .value("P6_P7", LIEF::assembly::aarch64::REG::P6_P7)
  .value("P7_P8", LIEF::assembly::aarch64::REG::P7_P8)
  .value("P8_P9", LIEF::assembly::aarch64::REG::P8_P9)
  .value("P9_P10", LIEF::assembly::aarch64::REG::P9_P10)
  .value("P10_P11", LIEF::assembly::aarch64::REG::P10_P11)
  .value("P11_P12", LIEF::assembly::aarch64::REG::P11_P12)
  .value("P12_P13", LIEF::assembly::aarch64::REG::P12_P13)
  .value("P13_P14", LIEF::assembly::aarch64::REG::P13_P14)
  .value("P14_P15", LIEF::assembly::aarch64::REG::P14_P15)
  .value("P15_P0", LIEF::assembly::aarch64::REG::P15_P0)
  .value("Q0_Q1", LIEF::assembly::aarch64::REG::Q0_Q1)
  .value("Q1_Q2", LIEF::assembly::aarch64::REG::Q1_Q2)
  .value("Q2_Q3", LIEF::assembly::aarch64::REG::Q2_Q3)
  .value("Q3_Q4", LIEF::assembly::aarch64::REG::Q3_Q4)
  .value("Q4_Q5", LIEF::assembly::aarch64::REG::Q4_Q5)
  .value("Q5_Q6", LIEF::assembly::aarch64::REG::Q5_Q6)
  .value("Q6_Q7", LIEF::assembly::aarch64::REG::Q6_Q7)
  .value("Q7_Q8", LIEF::assembly::aarch64::REG::Q7_Q8)
  .value("Q8_Q9", LIEF::assembly::aarch64::REG::Q8_Q9)
  .value("Q9_Q10", LIEF::assembly::aarch64::REG::Q9_Q10)
  .value("Q10_Q11", LIEF::assembly::aarch64::REG::Q10_Q11)
  .value("Q11_Q12", LIEF::assembly::aarch64::REG::Q11_Q12)
  .value("Q12_Q13", LIEF::assembly::aarch64::REG::Q12_Q13)
  .value("Q13_Q14", LIEF::assembly::aarch64::REG::Q13_Q14)
  .value("Q14_Q15", LIEF::assembly::aarch64::REG::Q14_Q15)
  .value("Q15_Q16", LIEF::assembly::aarch64::REG::Q15_Q16)
  .value("Q16_Q17", LIEF::assembly::aarch64::REG::Q16_Q17)
  .value("Q17_Q18", LIEF::assembly::aarch64::REG::Q17_Q18)
  .value("Q18_Q19", LIEF::assembly::aarch64::REG::Q18_Q19)
  .value("Q19_Q20", LIEF::assembly::aarch64::REG::Q19_Q20)
  .value("Q20_Q21", LIEF::assembly::aarch64::REG::Q20_Q21)
  .value("Q21_Q22", LIEF::assembly::aarch64::REG::Q21_Q22)
  .value("Q22_Q23", LIEF::assembly::aarch64::REG::Q22_Q23)
  .value("Q23_Q24", LIEF::assembly::aarch64::REG::Q23_Q24)
  .value("Q24_Q25", LIEF::assembly::aarch64::REG::Q24_Q25)
  .value("Q25_Q26", LIEF::assembly::aarch64::REG::Q25_Q26)
  .value("Q26_Q27", LIEF::assembly::aarch64::REG::Q26_Q27)
  .value("Q27_Q28", LIEF::assembly::aarch64::REG::Q27_Q28)
  .value("Q28_Q29", LIEF::assembly::aarch64::REG::Q28_Q29)
  .value("Q29_Q30", LIEF::assembly::aarch64::REG::Q29_Q30)
  .value("Q30_Q31", LIEF::assembly::aarch64::REG::Q30_Q31)
  .value("Q31_Q0", LIEF::assembly::aarch64::REG::Q31_Q0)
  .value("Q0_Q1_Q2_Q3", LIEF::assembly::aarch64::REG::Q0_Q1_Q2_Q3)
  .value("Q1_Q2_Q3_Q4", LIEF::assembly::aarch64::REG::Q1_Q2_Q3_Q4)
  .value("Q2_Q3_Q4_Q5", LIEF::assembly::aarch64::REG::Q2_Q3_Q4_Q5)
  .value("Q3_Q4_Q5_Q6", LIEF::assembly::aarch64::REG::Q3_Q4_Q5_Q6)
  .value("Q4_Q5_Q6_Q7", LIEF::assembly::aarch64::REG::Q4_Q5_Q6_Q7)
  .value("Q5_Q6_Q7_Q8", LIEF::assembly::aarch64::REG::Q5_Q6_Q7_Q8)
  .value("Q6_Q7_Q8_Q9", LIEF::assembly::aarch64::REG::Q6_Q7_Q8_Q9)
  .value("Q7_Q8_Q9_Q10", LIEF::assembly::aarch64::REG::Q7_Q8_Q9_Q10)
  .value("Q8_Q9_Q10_Q11", LIEF::assembly::aarch64::REG::Q8_Q9_Q10_Q11)
  .value("Q9_Q10_Q11_Q12", LIEF::assembly::aarch64::REG::Q9_Q10_Q11_Q12)
  .value("Q10_Q11_Q12_Q13", LIEF::assembly::aarch64::REG::Q10_Q11_Q12_Q13)
  .value("Q11_Q12_Q13_Q14", LIEF::assembly::aarch64::REG::Q11_Q12_Q13_Q14)
  .value("Q12_Q13_Q14_Q15", LIEF::assembly::aarch64::REG::Q12_Q13_Q14_Q15)
  .value("Q13_Q14_Q15_Q16", LIEF::assembly::aarch64::REG::Q13_Q14_Q15_Q16)
  .value("Q14_Q15_Q16_Q17", LIEF::assembly::aarch64::REG::Q14_Q15_Q16_Q17)
  .value("Q15_Q16_Q17_Q18", LIEF::assembly::aarch64::REG::Q15_Q16_Q17_Q18)
  .value("Q16_Q17_Q18_Q19", LIEF::assembly::aarch64::REG::Q16_Q17_Q18_Q19)
  .value("Q17_Q18_Q19_Q20", LIEF::assembly::aarch64::REG::Q17_Q18_Q19_Q20)
  .value("Q18_Q19_Q20_Q21", LIEF::assembly::aarch64::REG::Q18_Q19_Q20_Q21)
  .value("Q19_Q20_Q21_Q22", LIEF::assembly::aarch64::REG::Q19_Q20_Q21_Q22)
  .value("Q20_Q21_Q22_Q23", LIEF::assembly::aarch64::REG::Q20_Q21_Q22_Q23)
  .value("Q21_Q22_Q23_Q24", LIEF::assembly::aarch64::REG::Q21_Q22_Q23_Q24)
  .value("Q22_Q23_Q24_Q25", LIEF::assembly::aarch64::REG::Q22_Q23_Q24_Q25)
  .value("Q23_Q24_Q25_Q26", LIEF::assembly::aarch64::REG::Q23_Q24_Q25_Q26)
  .value("Q24_Q25_Q26_Q27", LIEF::assembly::aarch64::REG::Q24_Q25_Q26_Q27)
  .value("Q25_Q26_Q27_Q28", LIEF::assembly::aarch64::REG::Q25_Q26_Q27_Q28)
  .value("Q26_Q27_Q28_Q29", LIEF::assembly::aarch64::REG::Q26_Q27_Q28_Q29)
  .value("Q27_Q28_Q29_Q30", LIEF::assembly::aarch64::REG::Q27_Q28_Q29_Q30)
  .value("Q28_Q29_Q30_Q31", LIEF::assembly::aarch64::REG::Q28_Q29_Q30_Q31)
  .value("Q29_Q30_Q31_Q0", LIEF::assembly::aarch64::REG::Q29_Q30_Q31_Q0)
  .value("Q30_Q31_Q0_Q1", LIEF::assembly::aarch64::REG::Q30_Q31_Q0_Q1)
  .value("Q31_Q0_Q1_Q2", LIEF::assembly::aarch64::REG::Q31_Q0_Q1_Q2)
  .value("Q0_Q1_Q2", LIEF::assembly::aarch64::REG::Q0_Q1_Q2)
  .value("Q1_Q2_Q3", LIEF::assembly::aarch64::REG::Q1_Q2_Q3)
  .value("Q2_Q3_Q4", LIEF::assembly::aarch64::REG::Q2_Q3_Q4)
  .value("Q3_Q4_Q5", LIEF::assembly::aarch64::REG::Q3_Q4_Q5)
  .value("Q4_Q5_Q6", LIEF::assembly::aarch64::REG::Q4_Q5_Q6)
  .value("Q5_Q6_Q7", LIEF::assembly::aarch64::REG::Q5_Q6_Q7)
  .value("Q6_Q7_Q8", LIEF::assembly::aarch64::REG::Q6_Q7_Q8)
  .value("Q7_Q8_Q9", LIEF::assembly::aarch64::REG::Q7_Q8_Q9)
  .value("Q8_Q9_Q10", LIEF::assembly::aarch64::REG::Q8_Q9_Q10)
  .value("Q9_Q10_Q11", LIEF::assembly::aarch64::REG::Q9_Q10_Q11)
  .value("Q10_Q11_Q12", LIEF::assembly::aarch64::REG::Q10_Q11_Q12)
  .value("Q11_Q12_Q13", LIEF::assembly::aarch64::REG::Q11_Q12_Q13)
  .value("Q12_Q13_Q14", LIEF::assembly::aarch64::REG::Q12_Q13_Q14)
  .value("Q13_Q14_Q15", LIEF::assembly::aarch64::REG::Q13_Q14_Q15)
  .value("Q14_Q15_Q16", LIEF::assembly::aarch64::REG::Q14_Q15_Q16)
  .value("Q15_Q16_Q17", LIEF::assembly::aarch64::REG::Q15_Q16_Q17)
  .value("Q16_Q17_Q18", LIEF::assembly::aarch64::REG::Q16_Q17_Q18)
  .value("Q17_Q18_Q19", LIEF::assembly::aarch64::REG::Q17_Q18_Q19)
  .value("Q18_Q19_Q20", LIEF::assembly::aarch64::REG::Q18_Q19_Q20)
  .value("Q19_Q20_Q21", LIEF::assembly::aarch64::REG::Q19_Q20_Q21)
  .value("Q20_Q21_Q22", LIEF::assembly::aarch64::REG::Q20_Q21_Q22)
  .value("Q21_Q22_Q23", LIEF::assembly::aarch64::REG::Q21_Q22_Q23)
  .value("Q22_Q23_Q24", LIEF::assembly::aarch64::REG::Q22_Q23_Q24)
  .value("Q23_Q24_Q25", LIEF::assembly::aarch64::REG::Q23_Q24_Q25)
  .value("Q24_Q25_Q26", LIEF::assembly::aarch64::REG::Q24_Q25_Q26)
  .value("Q25_Q26_Q27", LIEF::assembly::aarch64::REG::Q25_Q26_Q27)
  .value("Q26_Q27_Q28", LIEF::assembly::aarch64::REG::Q26_Q27_Q28)
  .value("Q27_Q28_Q29", LIEF::assembly::aarch64::REG::Q27_Q28_Q29)
  .value("Q28_Q29_Q30", LIEF::assembly::aarch64::REG::Q28_Q29_Q30)
  .value("Q29_Q30_Q31", LIEF::assembly::aarch64::REG::Q29_Q30_Q31)
  .value("Q30_Q31_Q0", LIEF::assembly::aarch64::REG::Q30_Q31_Q0)
  .value("Q31_Q0_Q1", LIEF::assembly::aarch64::REG::Q31_Q0_Q1)
  .value("X22_X23_X24_X25_X26_X27_X28_FP", LIEF::assembly::aarch64::REG::X22_X23_X24_X25_X26_X27_X28_FP)
  .value("X0_X1_X2_X3_X4_X5_X6_X7", LIEF::assembly::aarch64::REG::X0_X1_X2_X3_X4_X5_X6_X7)
  .value("X2_X3_X4_X5_X6_X7_X8_X9", LIEF::assembly::aarch64::REG::X2_X3_X4_X5_X6_X7_X8_X9)
  .value("X4_X5_X6_X7_X8_X9_X10_X11", LIEF::assembly::aarch64::REG::X4_X5_X6_X7_X8_X9_X10_X11)
  .value("X6_X7_X8_X9_X10_X11_X12_X13", LIEF::assembly::aarch64::REG::X6_X7_X8_X9_X10_X11_X12_X13)
  .value("X8_X9_X10_X11_X12_X13_X14_X15", LIEF::assembly::aarch64::REG::X8_X9_X10_X11_X12_X13_X14_X15)
  .value("X10_X11_X12_X13_X14_X15_X16_X17", LIEF::assembly::aarch64::REG::X10_X11_X12_X13_X14_X15_X16_X17)
  .value("X12_X13_X14_X15_X16_X17_X18_X19", LIEF::assembly::aarch64::REG::X12_X13_X14_X15_X16_X17_X18_X19)
  .value("X14_X15_X16_X17_X18_X19_X20_X21", LIEF::assembly::aarch64::REG::X14_X15_X16_X17_X18_X19_X20_X21)
  .value("X16_X17_X18_X19_X20_X21_X22_X23", LIEF::assembly::aarch64::REG::X16_X17_X18_X19_X20_X21_X22_X23)
  .value("X18_X19_X20_X21_X22_X23_X24_X25", LIEF::assembly::aarch64::REG::X18_X19_X20_X21_X22_X23_X24_X25)
  .value("X20_X21_X22_X23_X24_X25_X26_X27", LIEF::assembly::aarch64::REG::X20_X21_X22_X23_X24_X25_X26_X27)
  .value("W30_WZR", LIEF::assembly::aarch64::REG::W30_WZR)
  .value("W0_W1", LIEF::assembly::aarch64::REG::W0_W1)
  .value("W2_W3", LIEF::assembly::aarch64::REG::W2_W3)
  .value("W4_W5", LIEF::assembly::aarch64::REG::W4_W5)
  .value("W6_W7", LIEF::assembly::aarch64::REG::W6_W7)
  .value("W8_W9", LIEF::assembly::aarch64::REG::W8_W9)
  .value("W10_W11", LIEF::assembly::aarch64::REG::W10_W11)
  .value("W12_W13", LIEF::assembly::aarch64::REG::W12_W13)
  .value("W14_W15", LIEF::assembly::aarch64::REG::W14_W15)
  .value("W16_W17", LIEF::assembly::aarch64::REG::W16_W17)
  .value("W18_W19", LIEF::assembly::aarch64::REG::W18_W19)
  .value("W20_W21", LIEF::assembly::aarch64::REG::W20_W21)
  .value("W22_W23", LIEF::assembly::aarch64::REG::W22_W23)
  .value("W24_W25", LIEF::assembly::aarch64::REG::W24_W25)
  .value("W26_W27", LIEF::assembly::aarch64::REG::W26_W27)
  .value("W28_W29", LIEF::assembly::aarch64::REG::W28_W29)
  .value("LR_XZR", LIEF::assembly::aarch64::REG::LR_XZR)
  .value("X28_FP", LIEF::assembly::aarch64::REG::X28_FP)
  .value("X0_X1", LIEF::assembly::aarch64::REG::X0_X1)
  .value("X2_X3", LIEF::assembly::aarch64::REG::X2_X3)
  .value("X4_X5", LIEF::assembly::aarch64::REG::X4_X5)
  .value("X6_X7", LIEF::assembly::aarch64::REG::X6_X7)
  .value("X8_X9", LIEF::assembly::aarch64::REG::X8_X9)
  .value("X10_X11", LIEF::assembly::aarch64::REG::X10_X11)
  .value("X12_X13", LIEF::assembly::aarch64::REG::X12_X13)
  .value("X14_X15", LIEF::assembly::aarch64::REG::X14_X15)
  .value("X16_X17", LIEF::assembly::aarch64::REG::X16_X17)
  .value("X18_X19", LIEF::assembly::aarch64::REG::X18_X19)
  .value("X20_X21", LIEF::assembly::aarch64::REG::X20_X21)
  .value("X22_X23", LIEF::assembly::aarch64::REG::X22_X23)
  .value("X24_X25", LIEF::assembly::aarch64::REG::X24_X25)
  .value("X26_X27", LIEF::assembly::aarch64::REG::X26_X27)
  .value("Z0_Z1", LIEF::assembly::aarch64::REG::Z0_Z1)
  .value("Z1_Z2", LIEF::assembly::aarch64::REG::Z1_Z2)
  .value("Z2_Z3", LIEF::assembly::aarch64::REG::Z2_Z3)
  .value("Z3_Z4", LIEF::assembly::aarch64::REG::Z3_Z4)
  .value("Z4_Z5", LIEF::assembly::aarch64::REG::Z4_Z5)
  .value("Z5_Z6", LIEF::assembly::aarch64::REG::Z5_Z6)
  .value("Z6_Z7", LIEF::assembly::aarch64::REG::Z6_Z7)
  .value("Z7_Z8", LIEF::assembly::aarch64::REG::Z7_Z8)
  .value("Z8_Z9", LIEF::assembly::aarch64::REG::Z8_Z9)
  .value("Z9_Z10", LIEF::assembly::aarch64::REG::Z9_Z10)
  .value("Z10_Z11", LIEF::assembly::aarch64::REG::Z10_Z11)
  .value("Z11_Z12", LIEF::assembly::aarch64::REG::Z11_Z12)
  .value("Z12_Z13", LIEF::assembly::aarch64::REG::Z12_Z13)
  .value("Z13_Z14", LIEF::assembly::aarch64::REG::Z13_Z14)
  .value("Z14_Z15", LIEF::assembly::aarch64::REG::Z14_Z15)
  .value("Z15_Z16", LIEF::assembly::aarch64::REG::Z15_Z16)
  .value("Z16_Z17", LIEF::assembly::aarch64::REG::Z16_Z17)
  .value("Z17_Z18", LIEF::assembly::aarch64::REG::Z17_Z18)
  .value("Z18_Z19", LIEF::assembly::aarch64::REG::Z18_Z19)
  .value("Z19_Z20", LIEF::assembly::aarch64::REG::Z19_Z20)
  .value("Z20_Z21", LIEF::assembly::aarch64::REG::Z20_Z21)
  .value("Z21_Z22", LIEF::assembly::aarch64::REG::Z21_Z22)
  .value("Z22_Z23", LIEF::assembly::aarch64::REG::Z22_Z23)
  .value("Z23_Z24", LIEF::assembly::aarch64::REG::Z23_Z24)
  .value("Z24_Z25", LIEF::assembly::aarch64::REG::Z24_Z25)
  .value("Z25_Z26", LIEF::assembly::aarch64::REG::Z25_Z26)
  .value("Z26_Z27", LIEF::assembly::aarch64::REG::Z26_Z27)
  .value("Z27_Z28", LIEF::assembly::aarch64::REG::Z27_Z28)
  .value("Z28_Z29", LIEF::assembly::aarch64::REG::Z28_Z29)
  .value("Z29_Z30", LIEF::assembly::aarch64::REG::Z29_Z30)
  .value("Z30_Z31", LIEF::assembly::aarch64::REG::Z30_Z31)
  .value("Z31_Z0", LIEF::assembly::aarch64::REG::Z31_Z0)
  .value("Z0_Z1_Z2_Z3", LIEF::assembly::aarch64::REG::Z0_Z1_Z2_Z3)
  .value("Z1_Z2_Z3_Z4", LIEF::assembly::aarch64::REG::Z1_Z2_Z3_Z4)
  .value("Z2_Z3_Z4_Z5", LIEF::assembly::aarch64::REG::Z2_Z3_Z4_Z5)
  .value("Z3_Z4_Z5_Z6", LIEF::assembly::aarch64::REG::Z3_Z4_Z5_Z6)
  .value("Z4_Z5_Z6_Z7", LIEF::assembly::aarch64::REG::Z4_Z5_Z6_Z7)
  .value("Z5_Z6_Z7_Z8", LIEF::assembly::aarch64::REG::Z5_Z6_Z7_Z8)
  .value("Z6_Z7_Z8_Z9", LIEF::assembly::aarch64::REG::Z6_Z7_Z8_Z9)
  .value("Z7_Z8_Z9_Z10", LIEF::assembly::aarch64::REG::Z7_Z8_Z9_Z10)
  .value("Z8_Z9_Z10_Z11", LIEF::assembly::aarch64::REG::Z8_Z9_Z10_Z11)
  .value("Z9_Z10_Z11_Z12", LIEF::assembly::aarch64::REG::Z9_Z10_Z11_Z12)
  .value("Z10_Z11_Z12_Z13", LIEF::assembly::aarch64::REG::Z10_Z11_Z12_Z13)
  .value("Z11_Z12_Z13_Z14", LIEF::assembly::aarch64::REG::Z11_Z12_Z13_Z14)
  .value("Z12_Z13_Z14_Z15", LIEF::assembly::aarch64::REG::Z12_Z13_Z14_Z15)
  .value("Z13_Z14_Z15_Z16", LIEF::assembly::aarch64::REG::Z13_Z14_Z15_Z16)
  .value("Z14_Z15_Z16_Z17", LIEF::assembly::aarch64::REG::Z14_Z15_Z16_Z17)
  .value("Z15_Z16_Z17_Z18", LIEF::assembly::aarch64::REG::Z15_Z16_Z17_Z18)
  .value("Z16_Z17_Z18_Z19", LIEF::assembly::aarch64::REG::Z16_Z17_Z18_Z19)
  .value("Z17_Z18_Z19_Z20", LIEF::assembly::aarch64::REG::Z17_Z18_Z19_Z20)
  .value("Z18_Z19_Z20_Z21", LIEF::assembly::aarch64::REG::Z18_Z19_Z20_Z21)
  .value("Z19_Z20_Z21_Z22", LIEF::assembly::aarch64::REG::Z19_Z20_Z21_Z22)
  .value("Z20_Z21_Z22_Z23", LIEF::assembly::aarch64::REG::Z20_Z21_Z22_Z23)
  .value("Z21_Z22_Z23_Z24", LIEF::assembly::aarch64::REG::Z21_Z22_Z23_Z24)
  .value("Z22_Z23_Z24_Z25", LIEF::assembly::aarch64::REG::Z22_Z23_Z24_Z25)
  .value("Z23_Z24_Z25_Z26", LIEF::assembly::aarch64::REG::Z23_Z24_Z25_Z26)
  .value("Z24_Z25_Z26_Z27", LIEF::assembly::aarch64::REG::Z24_Z25_Z26_Z27)
  .value("Z25_Z26_Z27_Z28", LIEF::assembly::aarch64::REG::Z25_Z26_Z27_Z28)
  .value("Z26_Z27_Z28_Z29", LIEF::assembly::aarch64::REG::Z26_Z27_Z28_Z29)
  .value("Z27_Z28_Z29_Z30", LIEF::assembly::aarch64::REG::Z27_Z28_Z29_Z30)
  .value("Z28_Z29_Z30_Z31", LIEF::assembly::aarch64::REG::Z28_Z29_Z30_Z31)
  .value("Z29_Z30_Z31_Z0", LIEF::assembly::aarch64::REG::Z29_Z30_Z31_Z0)
  .value("Z30_Z31_Z0_Z1", LIEF::assembly::aarch64::REG::Z30_Z31_Z0_Z1)
  .value("Z31_Z0_Z1_Z2", LIEF::assembly::aarch64::REG::Z31_Z0_Z1_Z2)
  .value("Z0_Z1_Z2", LIEF::assembly::aarch64::REG::Z0_Z1_Z2)
  .value("Z1_Z2_Z3", LIEF::assembly::aarch64::REG::Z1_Z2_Z3)
  .value("Z2_Z3_Z4", LIEF::assembly::aarch64::REG::Z2_Z3_Z4)
  .value("Z3_Z4_Z5", LIEF::assembly::aarch64::REG::Z3_Z4_Z5)
  .value("Z4_Z5_Z6", LIEF::assembly::aarch64::REG::Z4_Z5_Z6)
  .value("Z5_Z6_Z7", LIEF::assembly::aarch64::REG::Z5_Z6_Z7)
  .value("Z6_Z7_Z8", LIEF::assembly::aarch64::REG::Z6_Z7_Z8)
  .value("Z7_Z8_Z9", LIEF::assembly::aarch64::REG::Z7_Z8_Z9)
  .value("Z8_Z9_Z10", LIEF::assembly::aarch64::REG::Z8_Z9_Z10)
  .value("Z9_Z10_Z11", LIEF::assembly::aarch64::REG::Z9_Z10_Z11)
  .value("Z10_Z11_Z12", LIEF::assembly::aarch64::REG::Z10_Z11_Z12)
  .value("Z11_Z12_Z13", LIEF::assembly::aarch64::REG::Z11_Z12_Z13)
  .value("Z12_Z13_Z14", LIEF::assembly::aarch64::REG::Z12_Z13_Z14)
  .value("Z13_Z14_Z15", LIEF::assembly::aarch64::REG::Z13_Z14_Z15)
  .value("Z14_Z15_Z16", LIEF::assembly::aarch64::REG::Z14_Z15_Z16)
  .value("Z15_Z16_Z17", LIEF::assembly::aarch64::REG::Z15_Z16_Z17)
  .value("Z16_Z17_Z18", LIEF::assembly::aarch64::REG::Z16_Z17_Z18)
  .value("Z17_Z18_Z19", LIEF::assembly::aarch64::REG::Z17_Z18_Z19)
  .value("Z18_Z19_Z20", LIEF::assembly::aarch64::REG::Z18_Z19_Z20)
  .value("Z19_Z20_Z21", LIEF::assembly::aarch64::REG::Z19_Z20_Z21)
  .value("Z20_Z21_Z22", LIEF::assembly::aarch64::REG::Z20_Z21_Z22)
  .value("Z21_Z22_Z23", LIEF::assembly::aarch64::REG::Z21_Z22_Z23)
  .value("Z22_Z23_Z24", LIEF::assembly::aarch64::REG::Z22_Z23_Z24)
  .value("Z23_Z24_Z25", LIEF::assembly::aarch64::REG::Z23_Z24_Z25)
  .value("Z24_Z25_Z26", LIEF::assembly::aarch64::REG::Z24_Z25_Z26)
  .value("Z25_Z26_Z27", LIEF::assembly::aarch64::REG::Z25_Z26_Z27)
  .value("Z26_Z27_Z28", LIEF::assembly::aarch64::REG::Z26_Z27_Z28)
  .value("Z27_Z28_Z29", LIEF::assembly::aarch64::REG::Z27_Z28_Z29)
  .value("Z28_Z29_Z30", LIEF::assembly::aarch64::REG::Z28_Z29_Z30)
  .value("Z29_Z30_Z31", LIEF::assembly::aarch64::REG::Z29_Z30_Z31)
  .value("Z30_Z31_Z0", LIEF::assembly::aarch64::REG::Z30_Z31_Z0)
  .value("Z31_Z0_Z1", LIEF::assembly::aarch64::REG::Z31_Z0_Z1)
  .value("Z16_Z24", LIEF::assembly::aarch64::REG::Z16_Z24)
  .value("Z17_Z25", LIEF::assembly::aarch64::REG::Z17_Z25)
  .value("Z18_Z26", LIEF::assembly::aarch64::REG::Z18_Z26)
  .value("Z19_Z27", LIEF::assembly::aarch64::REG::Z19_Z27)
  .value("Z20_Z28", LIEF::assembly::aarch64::REG::Z20_Z28)
  .value("Z21_Z29", LIEF::assembly::aarch64::REG::Z21_Z29)
  .value("Z22_Z30", LIEF::assembly::aarch64::REG::Z22_Z30)
  .value("Z23_Z31", LIEF::assembly::aarch64::REG::Z23_Z31)
  .value("Z0_Z8", LIEF::assembly::aarch64::REG::Z0_Z8)
  .value("Z1_Z9", LIEF::assembly::aarch64::REG::Z1_Z9)
  .value("Z2_Z10", LIEF::assembly::aarch64::REG::Z2_Z10)
  .value("Z3_Z11", LIEF::assembly::aarch64::REG::Z3_Z11)
  .value("Z4_Z12", LIEF::assembly::aarch64::REG::Z4_Z12)
  .value("Z5_Z13", LIEF::assembly::aarch64::REG::Z5_Z13)
  .value("Z6_Z14", LIEF::assembly::aarch64::REG::Z6_Z14)
  .value("Z7_Z15", LIEF::assembly::aarch64::REG::Z7_Z15)
  .value("Z16_Z20_Z24_Z28", LIEF::assembly::aarch64::REG::Z16_Z20_Z24_Z28)
  .value("Z17_Z21_Z25_Z29", LIEF::assembly::aarch64::REG::Z17_Z21_Z25_Z29)
  .value("Z18_Z22_Z26_Z30", LIEF::assembly::aarch64::REG::Z18_Z22_Z26_Z30)
  .value("Z19_Z23_Z27_Z31", LIEF::assembly::aarch64::REG::Z19_Z23_Z27_Z31)
  .value("Z0_Z4_Z8_Z12", LIEF::assembly::aarch64::REG::Z0_Z4_Z8_Z12)
  .value("Z1_Z5_Z9_Z13", LIEF::assembly::aarch64::REG::Z1_Z5_Z9_Z13)
  .value("Z2_Z6_Z10_Z14", LIEF::assembly::aarch64::REG::Z2_Z6_Z10_Z14)
  .value("Z3_Z7_Z11_Z15", LIEF::assembly::aarch64::REG::Z3_Z7_Z11_Z15)
  .value("NUM_TARGET_REGS", LIEF::assembly::aarch64::REG::NUM_TARGET_REGS)
  ;
}

template<>
void create<LIEF::assembly::aarch64::SYSREG>(nb::module_& m) {
  nb::enum_<LIEF::assembly::aarch64::SYSREG> reg(m, "SYSREG");
  reg.value("OSDTRRX_EL1", LIEF::assembly::aarch64::SYSREG::OSDTRRX_EL1)
  .value("DBGBVR0_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR0_EL1)
  .value("DBGBCR0_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR0_EL1)
  .value("DBGWVR0_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR0_EL1)
  .value("DBGWCR0_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR0_EL1)
  .value("DBGBVR1_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR1_EL1)
  .value("DBGBCR1_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR1_EL1)
  .value("DBGWVR1_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR1_EL1)
  .value("DBGWCR1_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR1_EL1)
  .value("MDCCINT_EL1", LIEF::assembly::aarch64::SYSREG::MDCCINT_EL1)
  .value("MDSCR_EL1", LIEF::assembly::aarch64::SYSREG::MDSCR_EL1)
  .value("DBGBVR2_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR2_EL1)
  .value("DBGBCR2_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR2_EL1)
  .value("DBGWVR2_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR2_EL1)
  .value("DBGWCR2_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR2_EL1)
  .value("OSDTRTX_EL1", LIEF::assembly::aarch64::SYSREG::OSDTRTX_EL1)
  .value("DBGBVR3_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR3_EL1)
  .value("DBGBCR3_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR3_EL1)
  .value("DBGWVR3_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR3_EL1)
  .value("DBGWCR3_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR3_EL1)
  .value("MDSELR_EL1", LIEF::assembly::aarch64::SYSREG::MDSELR_EL1)
  .value("DBGBVR4_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR4_EL1)
  .value("DBGBCR4_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR4_EL1)
  .value("DBGWVR4_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR4_EL1)
  .value("DBGWCR4_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR4_EL1)
  .value("MDSTEPOP_EL1", LIEF::assembly::aarch64::SYSREG::MDSTEPOP_EL1)
  .value("DBGBVR5_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR5_EL1)
  .value("DBGBCR5_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR5_EL1)
  .value("DBGWVR5_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR5_EL1)
  .value("DBGWCR5_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR5_EL1)
  .value("OSECCR_EL1", LIEF::assembly::aarch64::SYSREG::OSECCR_EL1)
  .value("DBGBVR6_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR6_EL1)
  .value("DBGBCR6_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR6_EL1)
  .value("DBGWVR6_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR6_EL1)
  .value("DBGWCR6_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR6_EL1)
  .value("DBGBVR7_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR7_EL1)
  .value("DBGBCR7_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR7_EL1)
  .value("DBGWVR7_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR7_EL1)
  .value("DBGWCR7_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR7_EL1)
  .value("DBGBVR8_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR8_EL1)
  .value("DBGBCR8_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR8_EL1)
  .value("DBGWVR8_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR8_EL1)
  .value("DBGWCR8_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR8_EL1)
  .value("DBGBVR9_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR9_EL1)
  .value("DBGBCR9_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR9_EL1)
  .value("DBGWVR9_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR9_EL1)
  .value("DBGWCR9_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR9_EL1)
  .value("DBGBVR10_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR10_EL1)
  .value("DBGBCR10_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR10_EL1)
  .value("DBGWVR10_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR10_EL1)
  .value("DBGWCR10_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR10_EL1)
  .value("DBGBVR11_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR11_EL1)
  .value("DBGBCR11_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR11_EL1)
  .value("DBGWVR11_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR11_EL1)
  .value("DBGWCR11_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR11_EL1)
  .value("DBGBVR12_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR12_EL1)
  .value("DBGBCR12_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR12_EL1)
  .value("DBGWVR12_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR12_EL1)
  .value("DBGWCR12_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR12_EL1)
  .value("DBGBVR13_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR13_EL1)
  .value("DBGBCR13_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR13_EL1)
  .value("DBGWVR13_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR13_EL1)
  .value("DBGWCR13_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR13_EL1)
  .value("DBGBVR14_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR14_EL1)
  .value("DBGBCR14_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR14_EL1)
  .value("DBGWVR14_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR14_EL1)
  .value("DBGWCR14_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR14_EL1)
  .value("DBGBVR15_EL1", LIEF::assembly::aarch64::SYSREG::DBGBVR15_EL1)
  .value("DBGBCR15_EL1", LIEF::assembly::aarch64::SYSREG::DBGBCR15_EL1)
  .value("DBGWVR15_EL1", LIEF::assembly::aarch64::SYSREG::DBGWVR15_EL1)
  .value("DBGWCR15_EL1", LIEF::assembly::aarch64::SYSREG::DBGWCR15_EL1)
  .value("MDRAR_EL1", LIEF::assembly::aarch64::SYSREG::MDRAR_EL1)
  .value("OSLAR_EL1", LIEF::assembly::aarch64::SYSREG::OSLAR_EL1)
  .value("OSLSR_EL1", LIEF::assembly::aarch64::SYSREG::OSLSR_EL1)
  .value("OSDLR_EL1", LIEF::assembly::aarch64::SYSREG::OSDLR_EL1)
  .value("DBGPRCR_EL1", LIEF::assembly::aarch64::SYSREG::DBGPRCR_EL1)
  .value("DBGCLAIMSET_EL1", LIEF::assembly::aarch64::SYSREG::DBGCLAIMSET_EL1)
  .value("DBGCLAIMCLR_EL1", LIEF::assembly::aarch64::SYSREG::DBGCLAIMCLR_EL1)
  .value("DBGAUTHSTATUS_EL1", LIEF::assembly::aarch64::SYSREG::DBGAUTHSTATUS_EL1)
  .value("SPMCGCR0_EL1", LIEF::assembly::aarch64::SYSREG::SPMCGCR0_EL1)
  .value("SPMCGCR1_EL1", LIEF::assembly::aarch64::SYSREG::SPMCGCR1_EL1)
  .value("SPMACCESSR_EL1", LIEF::assembly::aarch64::SYSREG::SPMACCESSR_EL1)
  .value("SPMIIDR_EL1", LIEF::assembly::aarch64::SYSREG::SPMIIDR_EL1)
  .value("SPMDEVARCH_EL1", LIEF::assembly::aarch64::SYSREG::SPMDEVARCH_EL1)
  .value("SPMDEVAFF_EL1", LIEF::assembly::aarch64::SYSREG::SPMDEVAFF_EL1)
  .value("SPMCFGR_EL1", LIEF::assembly::aarch64::SYSREG::SPMCFGR_EL1)
  .value("SPMINTENSET_EL1", LIEF::assembly::aarch64::SYSREG::SPMINTENSET_EL1)
  .value("SPMINTENCLR_EL1", LIEF::assembly::aarch64::SYSREG::SPMINTENCLR_EL1)
  .value("PMEVCNTSVR0_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR0_EL1)
  .value("PMEVCNTSVR1_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR1_EL1)
  .value("PMEVCNTSVR2_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR2_EL1)
  .value("PMEVCNTSVR3_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR3_EL1)
  .value("PMEVCNTSVR4_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR4_EL1)
  .value("PMEVCNTSVR5_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR5_EL1)
  .value("PMEVCNTSVR6_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR6_EL1)
  .value("PMEVCNTSVR7_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR7_EL1)
  .value("PMEVCNTSVR8_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR8_EL1)
  .value("PMEVCNTSVR9_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR9_EL1)
  .value("PMEVCNTSVR10_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR10_EL1)
  .value("PMEVCNTSVR11_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR11_EL1)
  .value("PMEVCNTSVR12_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR12_EL1)
  .value("PMEVCNTSVR13_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR13_EL1)
  .value("PMEVCNTSVR14_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR14_EL1)
  .value("PMEVCNTSVR15_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR15_EL1)
  .value("PMEVCNTSVR16_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR16_EL1)
  .value("PMEVCNTSVR17_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR17_EL1)
  .value("PMEVCNTSVR18_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR18_EL1)
  .value("PMEVCNTSVR19_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR19_EL1)
  .value("PMEVCNTSVR20_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR20_EL1)
  .value("PMEVCNTSVR21_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR21_EL1)
  .value("PMEVCNTSVR22_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR22_EL1)
  .value("PMEVCNTSVR23_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR23_EL1)
  .value("PMEVCNTSVR24_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR24_EL1)
  .value("PMEVCNTSVR25_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR25_EL1)
  .value("PMEVCNTSVR26_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR26_EL1)
  .value("PMEVCNTSVR27_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR27_EL1)
  .value("PMEVCNTSVR28_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR28_EL1)
  .value("PMEVCNTSVR29_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR29_EL1)
  .value("PMEVCNTSVR30_EL1", LIEF::assembly::aarch64::SYSREG::PMEVCNTSVR30_EL1)
  .value("PMCCNTSVR_EL1", LIEF::assembly::aarch64::SYSREG::PMCCNTSVR_EL1)
  .value("PMICNTSVR_EL1", LIEF::assembly::aarch64::SYSREG::PMICNTSVR_EL1)
  .value("TRCTRACEIDR", LIEF::assembly::aarch64::SYSREG::TRCTRACEIDR)
  .value("TRCVICTLR", LIEF::assembly::aarch64::SYSREG::TRCVICTLR)
  .value("TRCSEQEVR0", LIEF::assembly::aarch64::SYSREG::TRCSEQEVR0)
  .value("TRCCNTRLDVR0", LIEF::assembly::aarch64::SYSREG::TRCCNTRLDVR0)
  .value("TRCIDR8", LIEF::assembly::aarch64::SYSREG::TRCIDR8)
  .value("TRCIMSPEC0", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC0)
  .value("TRCPRGCTLR", LIEF::assembly::aarch64::SYSREG::TRCPRGCTLR)
  .value("TRCQCTLR", LIEF::assembly::aarch64::SYSREG::TRCQCTLR)
  .value("TRCVIIECTLR", LIEF::assembly::aarch64::SYSREG::TRCVIIECTLR)
  .value("TRCSEQEVR1", LIEF::assembly::aarch64::SYSREG::TRCSEQEVR1)
  .value("TRCCNTRLDVR1", LIEF::assembly::aarch64::SYSREG::TRCCNTRLDVR1)
  .value("TRCIDR9", LIEF::assembly::aarch64::SYSREG::TRCIDR9)
  .value("TRCIMSPEC1", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC1)
  .value("TRCPROCSELR", LIEF::assembly::aarch64::SYSREG::TRCPROCSELR)
  .value("TRCITEEDCR", LIEF::assembly::aarch64::SYSREG::TRCITEEDCR)
  .value("TRCVISSCTLR", LIEF::assembly::aarch64::SYSREG::TRCVISSCTLR)
  .value("TRCSEQEVR2", LIEF::assembly::aarch64::SYSREG::TRCSEQEVR2)
  .value("TRCCNTRLDVR2", LIEF::assembly::aarch64::SYSREG::TRCCNTRLDVR2)
  .value("TRCIDR10", LIEF::assembly::aarch64::SYSREG::TRCIDR10)
  .value("TRCIMSPEC2", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC2)
  .value("TRCSTATR", LIEF::assembly::aarch64::SYSREG::TRCSTATR)
  .value("TRCVIPCSSCTLR", LIEF::assembly::aarch64::SYSREG::TRCVIPCSSCTLR)
  .value("TRCCNTRLDVR3", LIEF::assembly::aarch64::SYSREG::TRCCNTRLDVR3)
  .value("TRCIDR11", LIEF::assembly::aarch64::SYSREG::TRCIDR11)
  .value("TRCIMSPEC3", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC3)
  .value("TRCCONFIGR", LIEF::assembly::aarch64::SYSREG::TRCCONFIGR)
  .value("TRCCNTCTLR0", LIEF::assembly::aarch64::SYSREG::TRCCNTCTLR0)
  .value("TRCIDR12", LIEF::assembly::aarch64::SYSREG::TRCIDR12)
  .value("TRCIMSPEC4", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC4)
  .value("TRCCNTCTLR1", LIEF::assembly::aarch64::SYSREG::TRCCNTCTLR1)
  .value("TRCIDR13", LIEF::assembly::aarch64::SYSREG::TRCIDR13)
  .value("TRCIMSPEC5", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC5)
  .value("TRCAUXCTLR", LIEF::assembly::aarch64::SYSREG::TRCAUXCTLR)
  .value("TRCSEQRSTEVR", LIEF::assembly::aarch64::SYSREG::TRCSEQRSTEVR)
  .value("TRCCNTCTLR2", LIEF::assembly::aarch64::SYSREG::TRCCNTCTLR2)
  .value("TRCIMSPEC6", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC6)
  .value("TRCSEQSTR", LIEF::assembly::aarch64::SYSREG::TRCSEQSTR)
  .value("TRCCNTCTLR3", LIEF::assembly::aarch64::SYSREG::TRCCNTCTLR3)
  .value("TRCIMSPEC7", LIEF::assembly::aarch64::SYSREG::TRCIMSPEC7)
  .value("TRCEVENTCTL0R", LIEF::assembly::aarch64::SYSREG::TRCEVENTCTL0R)
  .value("TRCVDCTLR", LIEF::assembly::aarch64::SYSREG::TRCVDCTLR)
  .value("TRCEXTINSELR", LIEF::assembly::aarch64::SYSREG::TRCEXTINSELR)
  .value("TRCEXTINSELR0", LIEF::assembly::aarch64::SYSREG::TRCEXTINSELR0)
  .value("TRCCNTVR0", LIEF::assembly::aarch64::SYSREG::TRCCNTVR0)
  .value("TRCIDR0", LIEF::assembly::aarch64::SYSREG::TRCIDR0)
  .value("TRCEVENTCTL1R", LIEF::assembly::aarch64::SYSREG::TRCEVENTCTL1R)
  .value("TRCVDSACCTLR", LIEF::assembly::aarch64::SYSREG::TRCVDSACCTLR)
  .value("TRCEXTINSELR1", LIEF::assembly::aarch64::SYSREG::TRCEXTINSELR1)
  .value("TRCCNTVR1", LIEF::assembly::aarch64::SYSREG::TRCCNTVR1)
  .value("TRCIDR1", LIEF::assembly::aarch64::SYSREG::TRCIDR1)
  .value("TRCRSR", LIEF::assembly::aarch64::SYSREG::TRCRSR)
  .value("TRCVDARCCTLR", LIEF::assembly::aarch64::SYSREG::TRCVDARCCTLR)
  .value("TRCEXTINSELR2", LIEF::assembly::aarch64::SYSREG::TRCEXTINSELR2)
  .value("TRCCNTVR2", LIEF::assembly::aarch64::SYSREG::TRCCNTVR2)
  .value("TRCIDR2", LIEF::assembly::aarch64::SYSREG::TRCIDR2)
  .value("TRCSTALLCTLR", LIEF::assembly::aarch64::SYSREG::TRCSTALLCTLR)
  .value("TRCEXTINSELR3", LIEF::assembly::aarch64::SYSREG::TRCEXTINSELR3)
  .value("TRCCNTVR3", LIEF::assembly::aarch64::SYSREG::TRCCNTVR3)
  .value("TRCIDR3", LIEF::assembly::aarch64::SYSREG::TRCIDR3)
  .value("TRCTSCTLR", LIEF::assembly::aarch64::SYSREG::TRCTSCTLR)
  .value("TRCIDR4", LIEF::assembly::aarch64::SYSREG::TRCIDR4)
  .value("TRCSYNCPR", LIEF::assembly::aarch64::SYSREG::TRCSYNCPR)
  .value("TRCIDR5", LIEF::assembly::aarch64::SYSREG::TRCIDR5)
  .value("TRCCCCTLR", LIEF::assembly::aarch64::SYSREG::TRCCCCTLR)
  .value("TRCIDR6", LIEF::assembly::aarch64::SYSREG::TRCIDR6)
  .value("TRCBBCTLR", LIEF::assembly::aarch64::SYSREG::TRCBBCTLR)
  .value("TRCIDR7", LIEF::assembly::aarch64::SYSREG::TRCIDR7)
  .value("TRCRSCTLR16", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR16)
  .value("TRCSSCCR0", LIEF::assembly::aarch64::SYSREG::TRCSSCCR0)
  .value("TRCSSPCICR0", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR0)
  .value("TRCOSLAR", LIEF::assembly::aarch64::SYSREG::TRCOSLAR)
  .value("TRCRSCTLR17", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR17)
  .value("TRCSSCCR1", LIEF::assembly::aarch64::SYSREG::TRCSSCCR1)
  .value("TRCSSPCICR1", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR1)
  .value("TRCOSLSR", LIEF::assembly::aarch64::SYSREG::TRCOSLSR)
  .value("TRCRSCTLR2", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR2)
  .value("TRCRSCTLR18", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR18)
  .value("TRCSSCCR2", LIEF::assembly::aarch64::SYSREG::TRCSSCCR2)
  .value("TRCSSPCICR2", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR2)
  .value("TRCRSCTLR3", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR3)
  .value("TRCRSCTLR19", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR19)
  .value("TRCSSCCR3", LIEF::assembly::aarch64::SYSREG::TRCSSCCR3)
  .value("TRCSSPCICR3", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR3)
  .value("TRCRSCTLR4", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR4)
  .value("TRCRSCTLR20", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR20)
  .value("TRCSSCCR4", LIEF::assembly::aarch64::SYSREG::TRCSSCCR4)
  .value("TRCSSPCICR4", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR4)
  .value("TRCPDCR", LIEF::assembly::aarch64::SYSREG::TRCPDCR)
  .value("TRCRSCTLR5", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR5)
  .value("TRCRSCTLR21", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR21)
  .value("TRCSSCCR5", LIEF::assembly::aarch64::SYSREG::TRCSSCCR5)
  .value("TRCSSPCICR5", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR5)
  .value("TRCPDSR", LIEF::assembly::aarch64::SYSREG::TRCPDSR)
  .value("TRCRSCTLR6", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR6)
  .value("TRCRSCTLR22", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR22)
  .value("TRCSSCCR6", LIEF::assembly::aarch64::SYSREG::TRCSSCCR6)
  .value("TRCSSPCICR6", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR6)
  .value("TRCRSCTLR7", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR7)
  .value("TRCRSCTLR23", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR23)
  .value("TRCSSCCR7", LIEF::assembly::aarch64::SYSREG::TRCSSCCR7)
  .value("TRCSSPCICR7", LIEF::assembly::aarch64::SYSREG::TRCSSPCICR7)
  .value("TRCRSCTLR8", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR8)
  .value("TRCRSCTLR24", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR24)
  .value("TRCSSCSR0", LIEF::assembly::aarch64::SYSREG::TRCSSCSR0)
  .value("TRCRSCTLR9", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR9)
  .value("TRCRSCTLR25", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR25)
  .value("TRCSSCSR1", LIEF::assembly::aarch64::SYSREG::TRCSSCSR1)
  .value("TRCRSCTLR10", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR10)
  .value("TRCRSCTLR26", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR26)
  .value("TRCSSCSR2", LIEF::assembly::aarch64::SYSREG::TRCSSCSR2)
  .value("TRCRSCTLR11", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR11)
  .value("TRCRSCTLR27", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR27)
  .value("TRCSSCSR3", LIEF::assembly::aarch64::SYSREG::TRCSSCSR3)
  .value("TRCRSCTLR12", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR12)
  .value("TRCRSCTLR28", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR28)
  .value("TRCSSCSR4", LIEF::assembly::aarch64::SYSREG::TRCSSCSR4)
  .value("TRCRSCTLR13", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR13)
  .value("TRCRSCTLR29", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR29)
  .value("TRCSSCSR5", LIEF::assembly::aarch64::SYSREG::TRCSSCSR5)
  .value("TRCRSCTLR14", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR14)
  .value("TRCRSCTLR30", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR30)
  .value("TRCSSCSR6", LIEF::assembly::aarch64::SYSREG::TRCSSCSR6)
  .value("TRCRSCTLR15", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR15)
  .value("TRCRSCTLR31", LIEF::assembly::aarch64::SYSREG::TRCRSCTLR31)
  .value("TRCSSCSR7", LIEF::assembly::aarch64::SYSREG::TRCSSCSR7)
  .value("TRCACVR0", LIEF::assembly::aarch64::SYSREG::TRCACVR0)
  .value("TRCACVR8", LIEF::assembly::aarch64::SYSREG::TRCACVR8)
  .value("TRCACATR0", LIEF::assembly::aarch64::SYSREG::TRCACATR0)
  .value("TRCACATR8", LIEF::assembly::aarch64::SYSREG::TRCACATR8)
  .value("TRCDVCVR0", LIEF::assembly::aarch64::SYSREG::TRCDVCVR0)
  .value("TRCDVCVR4", LIEF::assembly::aarch64::SYSREG::TRCDVCVR4)
  .value("TRCDVCMR0", LIEF::assembly::aarch64::SYSREG::TRCDVCMR0)
  .value("TRCDVCMR4", LIEF::assembly::aarch64::SYSREG::TRCDVCMR4)
  .value("TRCACVR1", LIEF::assembly::aarch64::SYSREG::TRCACVR1)
  .value("TRCACVR9", LIEF::assembly::aarch64::SYSREG::TRCACVR9)
  .value("TRCACATR1", LIEF::assembly::aarch64::SYSREG::TRCACATR1)
  .value("TRCACATR9", LIEF::assembly::aarch64::SYSREG::TRCACATR9)
  .value("TRCACVR2", LIEF::assembly::aarch64::SYSREG::TRCACVR2)
  .value("TRCACVR10", LIEF::assembly::aarch64::SYSREG::TRCACVR10)
  .value("TRCACATR2", LIEF::assembly::aarch64::SYSREG::TRCACATR2)
  .value("TRCACATR10", LIEF::assembly::aarch64::SYSREG::TRCACATR10)
  .value("TRCDVCVR1", LIEF::assembly::aarch64::SYSREG::TRCDVCVR1)
  .value("TRCDVCVR5", LIEF::assembly::aarch64::SYSREG::TRCDVCVR5)
  .value("TRCDVCMR1", LIEF::assembly::aarch64::SYSREG::TRCDVCMR1)
  .value("TRCDVCMR5", LIEF::assembly::aarch64::SYSREG::TRCDVCMR5)
  .value("TRCACVR3", LIEF::assembly::aarch64::SYSREG::TRCACVR3)
  .value("TRCACVR11", LIEF::assembly::aarch64::SYSREG::TRCACVR11)
  .value("TRCACATR3", LIEF::assembly::aarch64::SYSREG::TRCACATR3)
  .value("TRCACATR11", LIEF::assembly::aarch64::SYSREG::TRCACATR11)
  .value("TRCACVR4", LIEF::assembly::aarch64::SYSREG::TRCACVR4)
  .value("TRCACVR12", LIEF::assembly::aarch64::SYSREG::TRCACVR12)
  .value("TRCACATR4", LIEF::assembly::aarch64::SYSREG::TRCACATR4)
  .value("TRCACATR12", LIEF::assembly::aarch64::SYSREG::TRCACATR12)
  .value("TRCDVCVR2", LIEF::assembly::aarch64::SYSREG::TRCDVCVR2)
  .value("TRCDVCVR6", LIEF::assembly::aarch64::SYSREG::TRCDVCVR6)
  .value("TRCDVCMR2", LIEF::assembly::aarch64::SYSREG::TRCDVCMR2)
  .value("TRCDVCMR6", LIEF::assembly::aarch64::SYSREG::TRCDVCMR6)
  .value("TRCACVR5", LIEF::assembly::aarch64::SYSREG::TRCACVR5)
  .value("TRCACVR13", LIEF::assembly::aarch64::SYSREG::TRCACVR13)
  .value("TRCACATR5", LIEF::assembly::aarch64::SYSREG::TRCACATR5)
  .value("TRCACATR13", LIEF::assembly::aarch64::SYSREG::TRCACATR13)
  .value("TRCACVR6", LIEF::assembly::aarch64::SYSREG::TRCACVR6)
  .value("TRCACVR14", LIEF::assembly::aarch64::SYSREG::TRCACVR14)
  .value("TRCACATR6", LIEF::assembly::aarch64::SYSREG::TRCACATR6)
  .value("TRCACATR14", LIEF::assembly::aarch64::SYSREG::TRCACATR14)
  .value("TRCDVCVR3", LIEF::assembly::aarch64::SYSREG::TRCDVCVR3)
  .value("TRCDVCVR7", LIEF::assembly::aarch64::SYSREG::TRCDVCVR7)
  .value("TRCDVCMR3", LIEF::assembly::aarch64::SYSREG::TRCDVCMR3)
  .value("TRCDVCMR7", LIEF::assembly::aarch64::SYSREG::TRCDVCMR7)
  .value("TRCACVR7", LIEF::assembly::aarch64::SYSREG::TRCACVR7)
  .value("TRCACVR15", LIEF::assembly::aarch64::SYSREG::TRCACVR15)
  .value("TRCACATR7", LIEF::assembly::aarch64::SYSREG::TRCACATR7)
  .value("TRCACATR15", LIEF::assembly::aarch64::SYSREG::TRCACATR15)
  .value("TRCCIDCVR0", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR0)
  .value("TRCVMIDCVR0", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR0)
  .value("TRCCIDCCTLR0", LIEF::assembly::aarch64::SYSREG::TRCCIDCCTLR0)
  .value("TRCCIDCCTLR1", LIEF::assembly::aarch64::SYSREG::TRCCIDCCTLR1)
  .value("TRCCIDCVR1", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR1)
  .value("TRCVMIDCVR1", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR1);
  reg.value("TRCVMIDCCTLR0", LIEF::assembly::aarch64::SYSREG::TRCVMIDCCTLR0)
  .value("TRCVMIDCCTLR1", LIEF::assembly::aarch64::SYSREG::TRCVMIDCCTLR1)
  .value("TRCCIDCVR2", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR2)
  .value("TRCVMIDCVR2", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR2)
  .value("TRCCIDCVR3", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR3)
  .value("TRCVMIDCVR3", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR3)
  .value("TRCCIDCVR4", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR4)
  .value("TRCVMIDCVR4", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR4)
  .value("TRCCIDCVR5", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR5)
  .value("TRCVMIDCVR5", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR5)
  .value("TRCCIDCVR6", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR6)
  .value("TRCVMIDCVR6", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR6)
  .value("TRCCIDCVR7", LIEF::assembly::aarch64::SYSREG::TRCCIDCVR7)
  .value("TRCVMIDCVR7", LIEF::assembly::aarch64::SYSREG::TRCVMIDCVR7)
  .value("TRCITCTRL", LIEF::assembly::aarch64::SYSREG::TRCITCTRL)
  .value("TRCDEVID", LIEF::assembly::aarch64::SYSREG::TRCDEVID)
  .value("TRCDEVTYPE", LIEF::assembly::aarch64::SYSREG::TRCDEVTYPE)
  .value("TRCPIDR4", LIEF::assembly::aarch64::SYSREG::TRCPIDR4)
  .value("TRCPIDR5", LIEF::assembly::aarch64::SYSREG::TRCPIDR5)
  .value("TRCPIDR6", LIEF::assembly::aarch64::SYSREG::TRCPIDR6)
  .value("TRCPIDR7", LIEF::assembly::aarch64::SYSREG::TRCPIDR7)
  .value("TRCCLAIMSET", LIEF::assembly::aarch64::SYSREG::TRCCLAIMSET)
  .value("TRCPIDR0", LIEF::assembly::aarch64::SYSREG::TRCPIDR0)
  .value("TRCCLAIMCLR", LIEF::assembly::aarch64::SYSREG::TRCCLAIMCLR)
  .value("TRCPIDR1", LIEF::assembly::aarch64::SYSREG::TRCPIDR1)
  .value("TRCDEVAFF0", LIEF::assembly::aarch64::SYSREG::TRCDEVAFF0)
  .value("TRCPIDR2", LIEF::assembly::aarch64::SYSREG::TRCPIDR2)
  .value("TRCDEVAFF1", LIEF::assembly::aarch64::SYSREG::TRCDEVAFF1)
  .value("TRCPIDR3", LIEF::assembly::aarch64::SYSREG::TRCPIDR3)
  .value("TRCLAR", LIEF::assembly::aarch64::SYSREG::TRCLAR)
  .value("TRCCIDR0", LIEF::assembly::aarch64::SYSREG::TRCCIDR0)
  .value("TRCLSR", LIEF::assembly::aarch64::SYSREG::TRCLSR)
  .value("TRCCIDR1", LIEF::assembly::aarch64::SYSREG::TRCCIDR1)
  .value("TRCAUTHSTATUS", LIEF::assembly::aarch64::SYSREG::TRCAUTHSTATUS)
  .value("TRCCIDR2", LIEF::assembly::aarch64::SYSREG::TRCCIDR2)
  .value("TRCDEVARCH", LIEF::assembly::aarch64::SYSREG::TRCDEVARCH)
  .value("TRCCIDR3", LIEF::assembly::aarch64::SYSREG::TRCCIDR3)
  .value("BRBINF0_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF0_EL1)
  .value("BRBSRC0_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC0_EL1)
  .value("BRBTGT0_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT0_EL1)
  .value("BRBINF16_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF16_EL1)
  .value("BRBSRC16_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC16_EL1)
  .value("BRBTGT16_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT16_EL1)
  .value("BRBINF1_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF1_EL1)
  .value("BRBSRC1_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC1_EL1)
  .value("BRBTGT1_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT1_EL1)
  .value("BRBINF17_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF17_EL1)
  .value("BRBSRC17_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC17_EL1)
  .value("BRBTGT17_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT17_EL1)
  .value("BRBINF2_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF2_EL1)
  .value("BRBSRC2_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC2_EL1)
  .value("BRBTGT2_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT2_EL1)
  .value("BRBINF18_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF18_EL1)
  .value("BRBSRC18_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC18_EL1)
  .value("BRBTGT18_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT18_EL1)
  .value("BRBINF3_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF3_EL1)
  .value("BRBSRC3_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC3_EL1)
  .value("BRBTGT3_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT3_EL1)
  .value("BRBINF19_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF19_EL1)
  .value("BRBSRC19_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC19_EL1)
  .value("BRBTGT19_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT19_EL1)
  .value("BRBINF4_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF4_EL1)
  .value("BRBSRC4_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC4_EL1)
  .value("BRBTGT4_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT4_EL1)
  .value("BRBINF20_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF20_EL1)
  .value("BRBSRC20_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC20_EL1)
  .value("BRBTGT20_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT20_EL1)
  .value("BRBINF5_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF5_EL1)
  .value("BRBSRC5_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC5_EL1)
  .value("BRBTGT5_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT5_EL1)
  .value("BRBINF21_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF21_EL1)
  .value("BRBSRC21_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC21_EL1)
  .value("BRBTGT21_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT21_EL1)
  .value("BRBINF6_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF6_EL1)
  .value("BRBSRC6_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC6_EL1)
  .value("BRBTGT6_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT6_EL1)
  .value("BRBINF22_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF22_EL1)
  .value("BRBSRC22_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC22_EL1)
  .value("BRBTGT22_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT22_EL1)
  .value("BRBINF7_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF7_EL1)
  .value("BRBSRC7_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC7_EL1)
  .value("BRBTGT7_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT7_EL1)
  .value("BRBINF23_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF23_EL1)
  .value("BRBSRC23_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC23_EL1)
  .value("BRBTGT23_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT23_EL1)
  .value("BRBINF8_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF8_EL1)
  .value("BRBSRC8_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC8_EL1)
  .value("BRBTGT8_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT8_EL1)
  .value("BRBINF24_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF24_EL1)
  .value("BRBSRC24_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC24_EL1)
  .value("BRBTGT24_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT24_EL1)
  .value("BRBINF9_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF9_EL1)
  .value("BRBSRC9_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC9_EL1)
  .value("BRBTGT9_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT9_EL1)
  .value("BRBINF25_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF25_EL1)
  .value("BRBSRC25_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC25_EL1)
  .value("BRBTGT25_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT25_EL1)
  .value("BRBINF10_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF10_EL1)
  .value("BRBSRC10_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC10_EL1)
  .value("BRBTGT10_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT10_EL1)
  .value("BRBINF26_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF26_EL1)
  .value("BRBSRC26_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC26_EL1)
  .value("BRBTGT26_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT26_EL1)
  .value("BRBINF11_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF11_EL1)
  .value("BRBSRC11_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC11_EL1)
  .value("BRBTGT11_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT11_EL1)
  .value("BRBINF27_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF27_EL1)
  .value("BRBSRC27_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC27_EL1)
  .value("BRBTGT27_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT27_EL1)
  .value("BRBINF12_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF12_EL1)
  .value("BRBSRC12_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC12_EL1)
  .value("BRBTGT12_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT12_EL1)
  .value("BRBINF28_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF28_EL1)
  .value("BRBSRC28_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC28_EL1)
  .value("BRBTGT28_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT28_EL1)
  .value("BRBINF13_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF13_EL1)
  .value("BRBSRC13_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC13_EL1)
  .value("BRBTGT13_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT13_EL1)
  .value("BRBINF29_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF29_EL1)
  .value("BRBSRC29_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC29_EL1)
  .value("BRBTGT29_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT29_EL1)
  .value("BRBINF14_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF14_EL1)
  .value("BRBSRC14_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC14_EL1)
  .value("BRBTGT14_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT14_EL1)
  .value("BRBINF30_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF30_EL1)
  .value("BRBSRC30_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC30_EL1)
  .value("BRBTGT30_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT30_EL1)
  .value("BRBINF15_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF15_EL1)
  .value("BRBSRC15_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC15_EL1)
  .value("BRBTGT15_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT15_EL1)
  .value("BRBINF31_EL1", LIEF::assembly::aarch64::SYSREG::BRBINF31_EL1)
  .value("BRBSRC31_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRC31_EL1)
  .value("BRBTGT31_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGT31_EL1)
  .value("BRBCR_EL1", LIEF::assembly::aarch64::SYSREG::BRBCR_EL1)
  .value("BRBFCR_EL1", LIEF::assembly::aarch64::SYSREG::BRBFCR_EL1)
  .value("BRBTS_EL1", LIEF::assembly::aarch64::SYSREG::BRBTS_EL1)
  .value("BRBINFINJ_EL1", LIEF::assembly::aarch64::SYSREG::BRBINFINJ_EL1)
  .value("BRBSRCINJ_EL1", LIEF::assembly::aarch64::SYSREG::BRBSRCINJ_EL1)
  .value("BRBTGTINJ_EL1", LIEF::assembly::aarch64::SYSREG::BRBTGTINJ_EL1)
  .value("BRBIDR0_EL1", LIEF::assembly::aarch64::SYSREG::BRBIDR0_EL1)
  .value("TEECR32_EL1", LIEF::assembly::aarch64::SYSREG::TEECR32_EL1)
  .value("TEEHBR32_EL1", LIEF::assembly::aarch64::SYSREG::TEEHBR32_EL1)
  .value("MDCCSR_EL0", LIEF::assembly::aarch64::SYSREG::MDCCSR_EL0)
  .value("DBGDTR_EL0", LIEF::assembly::aarch64::SYSREG::DBGDTR_EL0)
  .value("DBGDTRRX_EL0", LIEF::assembly::aarch64::SYSREG::DBGDTRRX_EL0)
  .value("DBGDTRTX_EL0", LIEF::assembly::aarch64::SYSREG::DBGDTRTX_EL0)
  .value("SPMCR_EL0", LIEF::assembly::aarch64::SYSREG::SPMCR_EL0)
  .value("SPMCNTENSET_EL0", LIEF::assembly::aarch64::SYSREG::SPMCNTENSET_EL0)
  .value("SPMCNTENCLR_EL0", LIEF::assembly::aarch64::SYSREG::SPMCNTENCLR_EL0)
  .value("SPMOVSCLR_EL0", LIEF::assembly::aarch64::SYSREG::SPMOVSCLR_EL0)
  .value("SPMZR_EL0", LIEF::assembly::aarch64::SYSREG::SPMZR_EL0)
  .value("SPMSELR_EL0", LIEF::assembly::aarch64::SYSREG::SPMSELR_EL0)
  .value("SPMOVSSET_EL0", LIEF::assembly::aarch64::SYSREG::SPMOVSSET_EL0)
  .value("SPMEVCNTR0_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR0_EL0)
  .value("SPMEVCNTR1_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR1_EL0)
  .value("SPMEVCNTR2_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR2_EL0)
  .value("SPMEVCNTR3_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR3_EL0)
  .value("SPMEVCNTR4_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR4_EL0)
  .value("SPMEVCNTR5_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR5_EL0)
  .value("SPMEVCNTR6_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR6_EL0)
  .value("SPMEVCNTR7_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR7_EL0)
  .value("SPMEVCNTR8_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR8_EL0)
  .value("SPMEVCNTR9_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR9_EL0)
  .value("SPMEVCNTR10_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR10_EL0)
  .value("SPMEVCNTR11_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR11_EL0)
  .value("SPMEVCNTR12_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR12_EL0)
  .value("SPMEVCNTR13_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR13_EL0)
  .value("SPMEVCNTR14_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR14_EL0)
  .value("SPMEVCNTR15_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVCNTR15_EL0)
  .value("SPMEVTYPER0_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER0_EL0)
  .value("SPMEVTYPER1_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER1_EL0)
  .value("SPMEVTYPER2_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER2_EL0)
  .value("SPMEVTYPER3_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER3_EL0)
  .value("SPMEVTYPER4_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER4_EL0)
  .value("SPMEVTYPER5_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER5_EL0)
  .value("SPMEVTYPER6_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER6_EL0)
  .value("SPMEVTYPER7_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER7_EL0)
  .value("SPMEVTYPER8_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER8_EL0)
  .value("SPMEVTYPER9_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER9_EL0)
  .value("SPMEVTYPER10_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER10_EL0)
  .value("SPMEVTYPER11_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER11_EL0)
  .value("SPMEVTYPER12_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER12_EL0)
  .value("SPMEVTYPER13_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER13_EL0)
  .value("SPMEVTYPER14_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER14_EL0)
  .value("SPMEVTYPER15_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVTYPER15_EL0)
  .value("SPMEVFILTR0_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR0_EL0)
  .value("SPMEVFILTR1_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR1_EL0)
  .value("SPMEVFILTR2_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR2_EL0)
  .value("SPMEVFILTR3_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR3_EL0)
  .value("SPMEVFILTR4_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR4_EL0)
  .value("SPMEVFILTR5_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR5_EL0)
  .value("SPMEVFILTR6_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR6_EL0)
  .value("SPMEVFILTR7_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR7_EL0)
  .value("SPMEVFILTR8_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR8_EL0)
  .value("SPMEVFILTR9_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR9_EL0)
  .value("SPMEVFILTR10_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR10_EL0)
  .value("SPMEVFILTR11_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR11_EL0)
  .value("SPMEVFILTR12_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR12_EL0)
  .value("SPMEVFILTR13_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR13_EL0)
  .value("SPMEVFILTR14_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR14_EL0)
  .value("SPMEVFILTR15_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILTR15_EL0)
  .value("SPMEVFILT2R0_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R0_EL0)
  .value("SPMEVFILT2R1_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R1_EL0)
  .value("SPMEVFILT2R2_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R2_EL0)
  .value("SPMEVFILT2R3_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R3_EL0)
  .value("SPMEVFILT2R4_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R4_EL0)
  .value("SPMEVFILT2R5_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R5_EL0)
  .value("SPMEVFILT2R6_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R6_EL0)
  .value("SPMEVFILT2R7_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R7_EL0)
  .value("SPMEVFILT2R8_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R8_EL0)
  .value("SPMEVFILT2R9_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R9_EL0)
  .value("SPMEVFILT2R10_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R10_EL0)
  .value("SPMEVFILT2R11_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R11_EL0)
  .value("SPMEVFILT2R12_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R12_EL0)
  .value("SPMEVFILT2R13_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R13_EL0)
  .value("SPMEVFILT2R14_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R14_EL0)
  .value("SPMEVFILT2R15_EL0", LIEF::assembly::aarch64::SYSREG::SPMEVFILT2R15_EL0)
  .value("DBGVCR32_EL2", LIEF::assembly::aarch64::SYSREG::DBGVCR32_EL2)
  .value("BRBCR_EL2", LIEF::assembly::aarch64::SYSREG::BRBCR_EL2)
  .value("SPMACCESSR_EL2", LIEF::assembly::aarch64::SYSREG::SPMACCESSR_EL2)
  .value("BRBCR_EL12", LIEF::assembly::aarch64::SYSREG::BRBCR_EL12)
  .value("SPMACCESSR_EL12", LIEF::assembly::aarch64::SYSREG::SPMACCESSR_EL12)
  .value("SPMACCESSR_EL3", LIEF::assembly::aarch64::SYSREG::SPMACCESSR_EL3)
  .value("SPMROOTCR_EL3", LIEF::assembly::aarch64::SYSREG::SPMROOTCR_EL3)
  .value("SPMSCR_EL1", LIEF::assembly::aarch64::SYSREG::SPMSCR_EL1)
  .value("MIDR_EL1", LIEF::assembly::aarch64::SYSREG::MIDR_EL1)
  .value("MPUIR_EL1", LIEF::assembly::aarch64::SYSREG::MPUIR_EL1)
  .value("MPIDR_EL1", LIEF::assembly::aarch64::SYSREG::MPIDR_EL1)
  .value("REVIDR_EL1", LIEF::assembly::aarch64::SYSREG::REVIDR_EL1)
  .value("ID_PFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_PFR0_EL1)
  .value("ID_PFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_PFR1_EL1)
  .value("ID_DFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_DFR0_EL1)
  .value("ID_AFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AFR0_EL1)
  .value("ID_MMFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR0_EL1)
  .value("ID_MMFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR1_EL1)
  .value("ID_MMFR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR2_EL1)
  .value("ID_MMFR3_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR3_EL1)
  .value("ID_ISAR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR0_EL1)
  .value("ID_ISAR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR1_EL1)
  .value("ID_ISAR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR2_EL1)
  .value("ID_ISAR3_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR3_EL1)
  .value("ID_ISAR4_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR4_EL1)
  .value("ID_ISAR5_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR5_EL1)
  .value("ID_MMFR4_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR4_EL1)
  .value("ID_ISAR6_EL1", LIEF::assembly::aarch64::SYSREG::ID_ISAR6_EL1)
  .value("MVFR0_EL1", LIEF::assembly::aarch64::SYSREG::MVFR0_EL1)
  .value("MVFR1_EL1", LIEF::assembly::aarch64::SYSREG::MVFR1_EL1)
  .value("MVFR2_EL1", LIEF::assembly::aarch64::SYSREG::MVFR2_EL1)
  .value("ID_PFR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_PFR2_EL1)
  .value("ID_DFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_DFR1_EL1)
  .value("ID_MMFR5_EL1", LIEF::assembly::aarch64::SYSREG::ID_MMFR5_EL1)
  .value("ID_AA64PFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64PFR0_EL1)
  .value("ID_AA64PFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64PFR1_EL1)
  .value("ID_AA64PFR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64PFR2_EL1)
  .value("ID_AA64ZFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64ZFR0_EL1)
  .value("ID_AA64SMFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64SMFR0_EL1)
  .value("ID_AA64FPFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64FPFR0_EL1)
  .value("ID_AA64DFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64DFR0_EL1)
  .value("ID_AA64DFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64DFR1_EL1)
  .value("ID_AA64DFR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64DFR2_EL1)
  .value("ID_AA64AFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64AFR0_EL1)
  .value("ID_AA64AFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64AFR1_EL1)
  .value("ID_AA64ISAR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64ISAR0_EL1)
  .value("ID_AA64ISAR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64ISAR1_EL1)
  .value("ID_AA64ISAR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64ISAR2_EL1)
  .value("ID_AA64ISAR3_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64ISAR3_EL1)
  .value("ID_AA64MMFR0_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64MMFR0_EL1)
  .value("ID_AA64MMFR1_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64MMFR1_EL1)
  .value("ID_AA64MMFR2_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64MMFR2_EL1)
  .value("ID_AA64MMFR3_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64MMFR3_EL1)
  .value("ID_AA64MMFR4_EL1", LIEF::assembly::aarch64::SYSREG::ID_AA64MMFR4_EL1)
  .value("SCTLR_EL1", LIEF::assembly::aarch64::SYSREG::SCTLR_EL1)
  .value("ACTLR_EL1", LIEF::assembly::aarch64::SYSREG::ACTLR_EL1)
  .value("CPACR_EL1", LIEF::assembly::aarch64::SYSREG::CPACR_EL1)
  .value("SCTLR2_EL1", LIEF::assembly::aarch64::SYSREG::SCTLR2_EL1)
  .value("RGSR_EL1", LIEF::assembly::aarch64::SYSREG::RGSR_EL1)
  .value("GCR_EL1", LIEF::assembly::aarch64::SYSREG::GCR_EL1)
  .value("ZCR_EL1", LIEF::assembly::aarch64::SYSREG::ZCR_EL1)
  .value("TRFCR_EL1", LIEF::assembly::aarch64::SYSREG::TRFCR_EL1)
  .value("TRCITECR_EL1", LIEF::assembly::aarch64::SYSREG::TRCITECR_EL1)
  .value("SMPRI_EL1", LIEF::assembly::aarch64::SYSREG::SMPRI_EL1)
  .value("SMCR_EL1", LIEF::assembly::aarch64::SYSREG::SMCR_EL1)
  .value("SCTLRMASK_EL1", LIEF::assembly::aarch64::SYSREG::SCTLRMASK_EL1)
  .value("ACTLRMASK_EL1", LIEF::assembly::aarch64::SYSREG::ACTLRMASK_EL1)
  .value("CPACRMASK_EL1", LIEF::assembly::aarch64::SYSREG::CPACRMASK_EL1)
  .value("SCTLR2MASK_EL1", LIEF::assembly::aarch64::SYSREG::SCTLR2MASK_EL1)
  .value("CPACRALIAS_EL1", LIEF::assembly::aarch64::SYSREG::CPACRALIAS_EL1)
  .value("ACTLRALIAS_EL1", LIEF::assembly::aarch64::SYSREG::ACTLRALIAS_EL1)
  .value("SCTLRALIAS_EL1", LIEF::assembly::aarch64::SYSREG::SCTLRALIAS_EL1)
  .value("SCTLR2ALIAS_EL1", LIEF::assembly::aarch64::SYSREG::SCTLR2ALIAS_EL1)
  .value("TTBR0_EL1", LIEF::assembly::aarch64::SYSREG::TTBR0_EL1)
  .value("TTBR1_EL1", LIEF::assembly::aarch64::SYSREG::TTBR1_EL1)
  .value("TCR_EL1", LIEF::assembly::aarch64::SYSREG::TCR_EL1)
  .value("TCR2_EL1", LIEF::assembly::aarch64::SYSREG::TCR2_EL1)
  .value("APIAKeyLo_EL1", LIEF::assembly::aarch64::SYSREG::APIAKeyLo_EL1)
  .value("APIAKeyHi_EL1", LIEF::assembly::aarch64::SYSREG::APIAKeyHi_EL1)
  .value("APIBKeyLo_EL1", LIEF::assembly::aarch64::SYSREG::APIBKeyLo_EL1)
  .value("APIBKeyHi_EL1", LIEF::assembly::aarch64::SYSREG::APIBKeyHi_EL1)
  .value("APDAKeyLo_EL1", LIEF::assembly::aarch64::SYSREG::APDAKeyLo_EL1)
  .value("APDAKeyHi_EL1", LIEF::assembly::aarch64::SYSREG::APDAKeyHi_EL1);
  reg.value("APDBKeyLo_EL1", LIEF::assembly::aarch64::SYSREG::APDBKeyLo_EL1)
  .value("APDBKeyHi_EL1", LIEF::assembly::aarch64::SYSREG::APDBKeyHi_EL1)
  .value("APGAKeyLo_EL1", LIEF::assembly::aarch64::SYSREG::APGAKeyLo_EL1)
  .value("APGAKeyHi_EL1", LIEF::assembly::aarch64::SYSREG::APGAKeyHi_EL1)
  .value("GCSCR_EL1", LIEF::assembly::aarch64::SYSREG::GCSCR_EL1)
  .value("GCSPR_EL1", LIEF::assembly::aarch64::SYSREG::GCSPR_EL1)
  .value("GCSCRE0_EL1", LIEF::assembly::aarch64::SYSREG::GCSCRE0_EL1)
  .value("TCRMASK_EL1", LIEF::assembly::aarch64::SYSREG::TCRMASK_EL1)
  .value("TCR2MASK_EL1", LIEF::assembly::aarch64::SYSREG::TCR2MASK_EL1)
  .value("TCRALIAS_EL1", LIEF::assembly::aarch64::SYSREG::TCRALIAS_EL1)
  .value("TCR2ALIAS_EL1", LIEF::assembly::aarch64::SYSREG::TCR2ALIAS_EL1)
  .value("SPSR_EL1", LIEF::assembly::aarch64::SYSREG::SPSR_EL1)
  .value("ELR_EL1", LIEF::assembly::aarch64::SYSREG::ELR_EL1)
  .value("SP_EL0", LIEF::assembly::aarch64::SYSREG::SP_EL0)
  .value("SPSel", LIEF::assembly::aarch64::SYSREG::SPSel)
  .value("CurrentEL", LIEF::assembly::aarch64::SYSREG::CurrentEL)
  .value("PAN", LIEF::assembly::aarch64::SYSREG::PAN)
  .value("UAO", LIEF::assembly::aarch64::SYSREG::UAO)
  .value("ALLINT", LIEF::assembly::aarch64::SYSREG::ALLINT)
  .value("PM", LIEF::assembly::aarch64::SYSREG::PM)
  .value("ICC_PMR_EL1", LIEF::assembly::aarch64::SYSREG::ICC_PMR_EL1)
  .value("AFSR0_EL1", LIEF::assembly::aarch64::SYSREG::AFSR0_EL1)
  .value("AFSR1_EL1", LIEF::assembly::aarch64::SYSREG::AFSR1_EL1)
  .value("ESR_EL1", LIEF::assembly::aarch64::SYSREG::ESR_EL1)
  .value("ERRIDR_EL1", LIEF::assembly::aarch64::SYSREG::ERRIDR_EL1)
  .value("ERRSELR_EL1", LIEF::assembly::aarch64::SYSREG::ERRSELR_EL1)
  .value("ERXGSR_EL1", LIEF::assembly::aarch64::SYSREG::ERXGSR_EL1)
  .value("ERXFR_EL1", LIEF::assembly::aarch64::SYSREG::ERXFR_EL1)
  .value("ERXCTLR_EL1", LIEF::assembly::aarch64::SYSREG::ERXCTLR_EL1)
  .value("ERXSTATUS_EL1", LIEF::assembly::aarch64::SYSREG::ERXSTATUS_EL1)
  .value("ERXADDR_EL1", LIEF::assembly::aarch64::SYSREG::ERXADDR_EL1)
  .value("ERXPFGF_EL1", LIEF::assembly::aarch64::SYSREG::ERXPFGF_EL1)
  .value("ERXPFGCTL_EL1", LIEF::assembly::aarch64::SYSREG::ERXPFGCTL_EL1)
  .value("ERXPFGCDN_EL1", LIEF::assembly::aarch64::SYSREG::ERXPFGCDN_EL1)
  .value("ERXMISC0_EL1", LIEF::assembly::aarch64::SYSREG::ERXMISC0_EL1)
  .value("ERXMISC1_EL1", LIEF::assembly::aarch64::SYSREG::ERXMISC1_EL1)
  .value("ERXMISC2_EL1", LIEF::assembly::aarch64::SYSREG::ERXMISC2_EL1)
  .value("ERXMISC3_EL1", LIEF::assembly::aarch64::SYSREG::ERXMISC3_EL1)
  .value("TFSR_EL1", LIEF::assembly::aarch64::SYSREG::TFSR_EL1)
  .value("TFSRE0_EL1", LIEF::assembly::aarch64::SYSREG::TFSRE0_EL1)
  .value("FAR_EL1", LIEF::assembly::aarch64::SYSREG::FAR_EL1)
  .value("PFAR_EL1", LIEF::assembly::aarch64::SYSREG::PFAR_EL1)
  .value("PRENR_EL1", LIEF::assembly::aarch64::SYSREG::PRENR_EL1)
  .value("PRSELR_EL1", LIEF::assembly::aarch64::SYSREG::PRSELR_EL1)
  .value("PRBAR_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR_EL1)
  .value("PRLAR_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR_EL1)
  .value("PRBAR1_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR1_EL1)
  .value("PRLAR1_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR1_EL1)
  .value("PRBAR2_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR2_EL1)
  .value("PRLAR2_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR2_EL1)
  .value("PRBAR3_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR3_EL1)
  .value("PRLAR3_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR3_EL1)
  .value("PRBAR4_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR4_EL1)
  .value("PRLAR4_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR4_EL1)
  .value("PRBAR5_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR5_EL1)
  .value("PRLAR5_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR5_EL1)
  .value("PRBAR6_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR6_EL1)
  .value("PRLAR6_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR6_EL1)
  .value("PRBAR7_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR7_EL1)
  .value("PRLAR7_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR7_EL1)
  .value("PRBAR8_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR8_EL1)
  .value("PRLAR8_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR8_EL1)
  .value("PRBAR9_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR9_EL1)
  .value("PRLAR9_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR9_EL1)
  .value("PRBAR10_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR10_EL1)
  .value("PRLAR10_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR10_EL1)
  .value("PRBAR11_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR11_EL1)
  .value("PRLAR11_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR11_EL1)
  .value("PRBAR12_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR12_EL1)
  .value("PRLAR12_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR12_EL1)
  .value("PRBAR13_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR13_EL1)
  .value("PRLAR13_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR13_EL1)
  .value("PRBAR14_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR14_EL1)
  .value("PRLAR14_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR14_EL1)
  .value("PRBAR15_EL1", LIEF::assembly::aarch64::SYSREG::PRBAR15_EL1)
  .value("PRLAR15_EL1", LIEF::assembly::aarch64::SYSREG::PRLAR15_EL1)
  .value("PAR_EL1", LIEF::assembly::aarch64::SYSREG::PAR_EL1)
  .value("PMSCR_EL1", LIEF::assembly::aarch64::SYSREG::PMSCR_EL1)
  .value("PMSNEVFR_EL1", LIEF::assembly::aarch64::SYSREG::PMSNEVFR_EL1)
  .value("PMSICR_EL1", LIEF::assembly::aarch64::SYSREG::PMSICR_EL1)
  .value("PMSIRR_EL1", LIEF::assembly::aarch64::SYSREG::PMSIRR_EL1)
  .value("PMSFCR_EL1", LIEF::assembly::aarch64::SYSREG::PMSFCR_EL1)
  .value("PMSEVFR_EL1", LIEF::assembly::aarch64::SYSREG::PMSEVFR_EL1)
  .value("PMSLATFR_EL1", LIEF::assembly::aarch64::SYSREG::PMSLATFR_EL1)
  .value("PMSIDR_EL1", LIEF::assembly::aarch64::SYSREG::PMSIDR_EL1)
  .value("PMBLIMITR_EL1", LIEF::assembly::aarch64::SYSREG::PMBLIMITR_EL1)
  .value("PMBPTR_EL1", LIEF::assembly::aarch64::SYSREG::PMBPTR_EL1)
  .value("PMBSR_EL1", LIEF::assembly::aarch64::SYSREG::PMBSR_EL1)
  .value("PMSDSFR_EL1", LIEF::assembly::aarch64::SYSREG::PMSDSFR_EL1)
  .value("PMBMAR_EL1", LIEF::assembly::aarch64::SYSREG::PMBMAR_EL1)
  .value("PMBIDR_EL1", LIEF::assembly::aarch64::SYSREG::PMBIDR_EL1)
  .value("TRBLIMITR_EL1", LIEF::assembly::aarch64::SYSREG::TRBLIMITR_EL1)
  .value("TRBPTR_EL1", LIEF::assembly::aarch64::SYSREG::TRBPTR_EL1)
  .value("TRBBASER_EL1", LIEF::assembly::aarch64::SYSREG::TRBBASER_EL1)
  .value("TRBSR_EL1", LIEF::assembly::aarch64::SYSREG::TRBSR_EL1)
  .value("TRBMAR_EL1", LIEF::assembly::aarch64::SYSREG::TRBMAR_EL1)
  .value("TRBMPAM_EL1", LIEF::assembly::aarch64::SYSREG::TRBMPAM_EL1)
  .value("TRBTRG_EL1", LIEF::assembly::aarch64::SYSREG::TRBTRG_EL1)
  .value("TRBIDR_EL1", LIEF::assembly::aarch64::SYSREG::TRBIDR_EL1)
  .value("PMSSCR_EL1", LIEF::assembly::aarch64::SYSREG::PMSSCR_EL1)
  .value("PMINTENSET_EL1", LIEF::assembly::aarch64::SYSREG::PMINTENSET_EL1)
  .value("PMINTENCLR_EL1", LIEF::assembly::aarch64::SYSREG::PMINTENCLR_EL1)
  .value("PMUACR_EL1", LIEF::assembly::aarch64::SYSREG::PMUACR_EL1)
  .value("PMECR_EL1", LIEF::assembly::aarch64::SYSREG::PMECR_EL1)
  .value("PMMIR_EL1", LIEF::assembly::aarch64::SYSREG::PMMIR_EL1)
  .value("PMIAR_EL1", LIEF::assembly::aarch64::SYSREG::PMIAR_EL1)
  .value("MAIR_EL1", LIEF::assembly::aarch64::SYSREG::MAIR_EL1)
  .value("MAIR2_EL1", LIEF::assembly::aarch64::SYSREG::MAIR2_EL1)
  .value("PIRE0_EL1", LIEF::assembly::aarch64::SYSREG::PIRE0_EL1)
  .value("PIR_EL1", LIEF::assembly::aarch64::SYSREG::PIR_EL1)
  .value("POR_EL1", LIEF::assembly::aarch64::SYSREG::POR_EL1)
  .value("S2POR_EL1", LIEF::assembly::aarch64::SYSREG::S2POR_EL1)
  .value("AMAIR_EL1", LIEF::assembly::aarch64::SYSREG::AMAIR_EL1)
  .value("AMAIR2_EL1", LIEF::assembly::aarch64::SYSREG::AMAIR2_EL1)
  .value("LORSA_EL1", LIEF::assembly::aarch64::SYSREG::LORSA_EL1)
  .value("LOREA_EL1", LIEF::assembly::aarch64::SYSREG::LOREA_EL1)
  .value("LORN_EL1", LIEF::assembly::aarch64::SYSREG::LORN_EL1)
  .value("LORC_EL1", LIEF::assembly::aarch64::SYSREG::LORC_EL1)
  .value("MPAMIDR_EL1", LIEF::assembly::aarch64::SYSREG::MPAMIDR_EL1)
  .value("MPAMBWIDR_EL1", LIEF::assembly::aarch64::SYSREG::MPAMBWIDR_EL1)
  .value("LORID_EL1", LIEF::assembly::aarch64::SYSREG::LORID_EL1)
  .value("MPAM1_EL1", LIEF::assembly::aarch64::SYSREG::MPAM1_EL1)
  .value("MPAM0_EL1", LIEF::assembly::aarch64::SYSREG::MPAM0_EL1)
  .value("MPAMSM_EL1", LIEF::assembly::aarch64::SYSREG::MPAMSM_EL1)
  .value("MPAMBW1_EL1", LIEF::assembly::aarch64::SYSREG::MPAMBW1_EL1)
  .value("MPAMBW0_EL1", LIEF::assembly::aarch64::SYSREG::MPAMBW0_EL1)
  .value("MPAMBWSM_EL1", LIEF::assembly::aarch64::SYSREG::MPAMBWSM_EL1)
  .value("VBAR_EL1", LIEF::assembly::aarch64::SYSREG::VBAR_EL1)
  .value("RVBAR_EL1", LIEF::assembly::aarch64::SYSREG::RVBAR_EL1)
  .value("RMR_EL1", LIEF::assembly::aarch64::SYSREG::RMR_EL1)
  .value("ISR_EL1", LIEF::assembly::aarch64::SYSREG::ISR_EL1)
  .value("DISR_EL1", LIEF::assembly::aarch64::SYSREG::DISR_EL1)
  .value("ICC_IAR0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_IAR0_EL1)
  .value("ICC_EOIR0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_EOIR0_EL1)
  .value("ICC_HPPIR0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_HPPIR0_EL1)
  .value("ICC_BPR0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_BPR0_EL1)
  .value("ICC_AP0R0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP0R0_EL1)
  .value("ICC_AP0R1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP0R1_EL1)
  .value("ICC_AP0R2_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP0R2_EL1)
  .value("ICC_AP0R3_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP0R3_EL1)
  .value("ICC_AP1R0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP1R0_EL1)
  .value("ICC_AP1R1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP1R1_EL1)
  .value("ICC_AP1R2_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP1R2_EL1)
  .value("ICC_AP1R3_EL1", LIEF::assembly::aarch64::SYSREG::ICC_AP1R3_EL1)
  .value("ICC_NMIAR1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_NMIAR1_EL1)
  .value("ICC_DIR_EL1", LIEF::assembly::aarch64::SYSREG::ICC_DIR_EL1)
  .value("ICC_RPR_EL1", LIEF::assembly::aarch64::SYSREG::ICC_RPR_EL1)
  .value("ICC_SGI1R_EL1", LIEF::assembly::aarch64::SYSREG::ICC_SGI1R_EL1)
  .value("ICC_ASGI1R_EL1", LIEF::assembly::aarch64::SYSREG::ICC_ASGI1R_EL1)
  .value("ICC_SGI0R_EL1", LIEF::assembly::aarch64::SYSREG::ICC_SGI0R_EL1)
  .value("ICC_IAR1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_IAR1_EL1)
  .value("ICC_EOIR1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_EOIR1_EL1)
  .value("ICC_HPPIR1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_HPPIR1_EL1)
  .value("ICC_BPR1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_BPR1_EL1)
  .value("ICC_CTLR_EL1", LIEF::assembly::aarch64::SYSREG::ICC_CTLR_EL1)
  .value("ICC_SRE_EL1", LIEF::assembly::aarch64::SYSREG::ICC_SRE_EL1)
  .value("ICC_IGRPEN0_EL1", LIEF::assembly::aarch64::SYSREG::ICC_IGRPEN0_EL1)
  .value("ICC_IGRPEN1_EL1", LIEF::assembly::aarch64::SYSREG::ICC_IGRPEN1_EL1)
  .value("CONTEXTIDR_EL1", LIEF::assembly::aarch64::SYSREG::CONTEXTIDR_EL1)
  .value("RCWSMASK_EL1", LIEF::assembly::aarch64::SYSREG::RCWSMASK_EL1)
  .value("TPIDR_EL1", LIEF::assembly::aarch64::SYSREG::TPIDR_EL1)
  .value("ACCDATA_EL1", LIEF::assembly::aarch64::SYSREG::ACCDATA_EL1)
  .value("RCWMASK_EL1", LIEF::assembly::aarch64::SYSREG::RCWMASK_EL1)
  .value("SCXTNUM_EL1", LIEF::assembly::aarch64::SYSREG::SCXTNUM_EL1)
  .value("CNTKCTL_EL1", LIEF::assembly::aarch64::SYSREG::CNTKCTL_EL1)
  .value("CCSIDR_EL1", LIEF::assembly::aarch64::SYSREG::CCSIDR_EL1)
  .value("CLIDR_EL1", LIEF::assembly::aarch64::SYSREG::CLIDR_EL1)
  .value("CCSIDR2_EL1", LIEF::assembly::aarch64::SYSREG::CCSIDR2_EL1)
  .value("GMID_EL1", LIEF::assembly::aarch64::SYSREG::GMID_EL1)
  .value("SMIDR_EL1", LIEF::assembly::aarch64::SYSREG::SMIDR_EL1)
  .value("AIDR_EL1", LIEF::assembly::aarch64::SYSREG::AIDR_EL1)
  .value("CSSELR_EL1", LIEF::assembly::aarch64::SYSREG::CSSELR_EL1)
  .value("CTR_EL0", LIEF::assembly::aarch64::SYSREG::CTR_EL0)
  .value("DCZID_EL0", LIEF::assembly::aarch64::SYSREG::DCZID_EL0)
  .value("RNDR", LIEF::assembly::aarch64::SYSREG::RNDR)
  .value("RNDRRS", LIEF::assembly::aarch64::SYSREG::RNDRRS)
  .value("GCSPR_EL0", LIEF::assembly::aarch64::SYSREG::GCSPR_EL0)
  .value("NZCV", LIEF::assembly::aarch64::SYSREG::NZCV)
  .value("DAIF", LIEF::assembly::aarch64::SYSREG::DAIF)
  .value("SVCR", LIEF::assembly::aarch64::SYSREG::SVCR)
  .value("DIT", LIEF::assembly::aarch64::SYSREG::DIT)
  .value("SSBS", LIEF::assembly::aarch64::SYSREG::SSBS)
  .value("TCO", LIEF::assembly::aarch64::SYSREG::TCO)
  .value("FPCR", LIEF::assembly::aarch64::SYSREG::FPCR)
  .value("FPSR", LIEF::assembly::aarch64::SYSREG::FPSR)
  .value("FPMR", LIEF::assembly::aarch64::SYSREG::FPMR)
  .value("DSPSR_EL0", LIEF::assembly::aarch64::SYSREG::DSPSR_EL0)
  .value("DLR_EL0", LIEF::assembly::aarch64::SYSREG::DLR_EL0)
  .value("PMICNTR_EL0", LIEF::assembly::aarch64::SYSREG::PMICNTR_EL0)
  .value("PMICFILTR_EL0", LIEF::assembly::aarch64::SYSREG::PMICFILTR_EL0)
  .value("PMCR_EL0", LIEF::assembly::aarch64::SYSREG::PMCR_EL0)
  .value("PMCNTENSET_EL0", LIEF::assembly::aarch64::SYSREG::PMCNTENSET_EL0)
  .value("PMCNTENCLR_EL0", LIEF::assembly::aarch64::SYSREG::PMCNTENCLR_EL0)
  .value("PMOVSCLR_EL0", LIEF::assembly::aarch64::SYSREG::PMOVSCLR_EL0)
  .value("PMSWINC_EL0", LIEF::assembly::aarch64::SYSREG::PMSWINC_EL0)
  .value("PMSELR_EL0", LIEF::assembly::aarch64::SYSREG::PMSELR_EL0)
  .value("PMCEID0_EL0", LIEF::assembly::aarch64::SYSREG::PMCEID0_EL0)
  .value("PMCEID1_EL0", LIEF::assembly::aarch64::SYSREG::PMCEID1_EL0)
  .value("PMCCNTR_EL0", LIEF::assembly::aarch64::SYSREG::PMCCNTR_EL0)
  .value("PMXEVTYPER_EL0", LIEF::assembly::aarch64::SYSREG::PMXEVTYPER_EL0)
  .value("PMXEVCNTR_EL0", LIEF::assembly::aarch64::SYSREG::PMXEVCNTR_EL0)
  .value("PMZR_EL0", LIEF::assembly::aarch64::SYSREG::PMZR_EL0)
  .value("PMUSERENR_EL0", LIEF::assembly::aarch64::SYSREG::PMUSERENR_EL0)
  .value("PMOVSSET_EL0", LIEF::assembly::aarch64::SYSREG::PMOVSSET_EL0)
  .value("POR_EL0", LIEF::assembly::aarch64::SYSREG::POR_EL0)
  .value("TPIDR_EL0", LIEF::assembly::aarch64::SYSREG::TPIDR_EL0)
  .value("TPIDRRO_EL0", LIEF::assembly::aarch64::SYSREG::TPIDRRO_EL0)
  .value("TPIDR2_EL0", LIEF::assembly::aarch64::SYSREG::TPIDR2_EL0)
  .value("SCXTNUM_EL0", LIEF::assembly::aarch64::SYSREG::SCXTNUM_EL0)
  .value("AMCR_EL0", LIEF::assembly::aarch64::SYSREG::AMCR_EL0)
  .value("AMCFGR_EL0", LIEF::assembly::aarch64::SYSREG::AMCFGR_EL0)
  .value("AMCGCR_EL0", LIEF::assembly::aarch64::SYSREG::AMCGCR_EL0)
  .value("AMUSERENR_EL0", LIEF::assembly::aarch64::SYSREG::AMUSERENR_EL0)
  .value("AMCNTENCLR0_EL0", LIEF::assembly::aarch64::SYSREG::AMCNTENCLR0_EL0)
  .value("AMCNTENSET0_EL0", LIEF::assembly::aarch64::SYSREG::AMCNTENSET0_EL0)
  .value("AMCG1IDR_EL0", LIEF::assembly::aarch64::SYSREG::AMCG1IDR_EL0)
  .value("AMCNTENCLR1_EL0", LIEF::assembly::aarch64::SYSREG::AMCNTENCLR1_EL0)
  .value("AMCNTENSET1_EL0", LIEF::assembly::aarch64::SYSREG::AMCNTENSET1_EL0)
  .value("AMEVCNTR00_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR00_EL0)
  .value("AMEVCNTR01_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR01_EL0)
  .value("AMEVCNTR02_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR02_EL0)
  .value("AMEVCNTR03_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR03_EL0)
  .value("AMEVTYPER00_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER00_EL0)
  .value("AMEVTYPER01_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER01_EL0)
  .value("AMEVTYPER02_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER02_EL0)
  .value("AMEVTYPER03_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER03_EL0)
  .value("AMEVCNTR10_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR10_EL0)
  .value("AMEVCNTR11_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR11_EL0)
  .value("AMEVCNTR12_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR12_EL0)
  .value("AMEVCNTR13_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR13_EL0)
  .value("AMEVCNTR14_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR14_EL0)
  .value("AMEVCNTR15_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR15_EL0)
  .value("AMEVCNTR16_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR16_EL0)
  .value("AMEVCNTR17_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR17_EL0)
  .value("AMEVCNTR18_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR18_EL0)
  .value("AMEVCNTR19_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR19_EL0)
  .value("AMEVCNTR110_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR110_EL0)
  .value("AMEVCNTR111_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR111_EL0)
  .value("AMEVCNTR112_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR112_EL0)
  .value("AMEVCNTR113_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR113_EL0)
  .value("AMEVCNTR114_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR114_EL0)
  .value("AMEVCNTR115_EL0", LIEF::assembly::aarch64::SYSREG::AMEVCNTR115_EL0)
  .value("AMEVTYPER10_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER10_EL0)
  .value("AMEVTYPER11_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER11_EL0)
  .value("AMEVTYPER12_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER12_EL0)
  .value("AMEVTYPER13_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER13_EL0)
  .value("AMEVTYPER14_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER14_EL0)
  .value("AMEVTYPER15_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER15_EL0)
  .value("AMEVTYPER16_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER16_EL0)
  .value("AMEVTYPER17_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER17_EL0)
  .value("AMEVTYPER18_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER18_EL0)
  .value("AMEVTYPER19_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER19_EL0)
  .value("AMEVTYPER110_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER110_EL0)
  .value("AMEVTYPER111_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER111_EL0)
  .value("AMEVTYPER112_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER112_EL0)
  .value("AMEVTYPER113_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER113_EL0)
  .value("AMEVTYPER114_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER114_EL0)
  .value("AMEVTYPER115_EL0", LIEF::assembly::aarch64::SYSREG::AMEVTYPER115_EL0)
  .value("CNTFRQ_EL0", LIEF::assembly::aarch64::SYSREG::CNTFRQ_EL0)
  .value("CNTPCT_EL0", LIEF::assembly::aarch64::SYSREG::CNTPCT_EL0)
  .value("CNTVCT_EL0", LIEF::assembly::aarch64::SYSREG::CNTVCT_EL0)
  .value("CNTPCTSS_EL0", LIEF::assembly::aarch64::SYSREG::CNTPCTSS_EL0)
  .value("CNTVCTSS_EL0", LIEF::assembly::aarch64::SYSREG::CNTVCTSS_EL0)
  .value("CNTP_TVAL_EL0", LIEF::assembly::aarch64::SYSREG::CNTP_TVAL_EL0)
  .value("CNTP_CTL_EL0", LIEF::assembly::aarch64::SYSREG::CNTP_CTL_EL0)
  .value("CNTP_CVAL_EL0", LIEF::assembly::aarch64::SYSREG::CNTP_CVAL_EL0)
  .value("CNTV_TVAL_EL0", LIEF::assembly::aarch64::SYSREG::CNTV_TVAL_EL0)
  .value("CNTV_CTL_EL0", LIEF::assembly::aarch64::SYSREG::CNTV_CTL_EL0)
  .value("CNTV_CVAL_EL0", LIEF::assembly::aarch64::SYSREG::CNTV_CVAL_EL0)
  .value("PMEVCNTR0_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR0_EL0)
  .value("PMEVCNTR1_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR1_EL0)
  .value("PMEVCNTR2_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR2_EL0)
  .value("PMEVCNTR3_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR3_EL0)
  .value("PMEVCNTR4_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR4_EL0)
  .value("PMEVCNTR5_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR5_EL0)
  .value("PMEVCNTR6_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR6_EL0)
  .value("PMEVCNTR7_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR7_EL0)
  .value("PMEVCNTR8_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR8_EL0)
  .value("PMEVCNTR9_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR9_EL0)
  .value("PMEVCNTR10_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR10_EL0)
  .value("PMEVCNTR11_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR11_EL0)
  .value("PMEVCNTR12_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR12_EL0)
  .value("PMEVCNTR13_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR13_EL0)
  .value("PMEVCNTR14_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR14_EL0)
  .value("PMEVCNTR15_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR15_EL0)
  .value("PMEVCNTR16_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR16_EL0)
  .value("PMEVCNTR17_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR17_EL0)
  .value("PMEVCNTR18_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR18_EL0)
  .value("PMEVCNTR19_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR19_EL0)
  .value("PMEVCNTR20_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR20_EL0)
  .value("PMEVCNTR21_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR21_EL0)
  .value("PMEVCNTR22_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR22_EL0)
  .value("PMEVCNTR23_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR23_EL0)
  .value("PMEVCNTR24_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR24_EL0)
  .value("PMEVCNTR25_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR25_EL0)
  .value("PMEVCNTR26_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR26_EL0)
  .value("PMEVCNTR27_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR27_EL0)
  .value("PMEVCNTR28_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR28_EL0)
  .value("PMEVCNTR29_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR29_EL0)
  .value("PMEVCNTR30_EL0", LIEF::assembly::aarch64::SYSREG::PMEVCNTR30_EL0);
  reg.value("PMEVTYPER0_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER0_EL0)
  .value("PMEVTYPER1_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER1_EL0)
  .value("PMEVTYPER2_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER2_EL0)
  .value("PMEVTYPER3_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER3_EL0)
  .value("PMEVTYPER4_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER4_EL0)
  .value("PMEVTYPER5_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER5_EL0)
  .value("PMEVTYPER6_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER6_EL0)
  .value("PMEVTYPER7_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER7_EL0)
  .value("PMEVTYPER8_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER8_EL0)
  .value("PMEVTYPER9_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER9_EL0)
  .value("PMEVTYPER10_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER10_EL0)
  .value("PMEVTYPER11_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER11_EL0)
  .value("PMEVTYPER12_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER12_EL0)
  .value("PMEVTYPER13_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER13_EL0)
  .value("PMEVTYPER14_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER14_EL0)
  .value("PMEVTYPER15_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER15_EL0)
  .value("PMEVTYPER16_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER16_EL0)
  .value("PMEVTYPER17_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER17_EL0)
  .value("PMEVTYPER18_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER18_EL0)
  .value("PMEVTYPER19_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER19_EL0)
  .value("PMEVTYPER20_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER20_EL0)
  .value("PMEVTYPER21_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER21_EL0)
  .value("PMEVTYPER22_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER22_EL0)
  .value("PMEVTYPER23_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER23_EL0)
  .value("PMEVTYPER24_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER24_EL0)
  .value("PMEVTYPER25_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER25_EL0)
  .value("PMEVTYPER26_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER26_EL0)
  .value("PMEVTYPER27_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER27_EL0)
  .value("PMEVTYPER28_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER28_EL0)
  .value("PMEVTYPER29_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER29_EL0)
  .value("PMEVTYPER30_EL0", LIEF::assembly::aarch64::SYSREG::PMEVTYPER30_EL0)
  .value("PMCCFILTR_EL0", LIEF::assembly::aarch64::SYSREG::PMCCFILTR_EL0)
  .value("VPIDR_EL2", LIEF::assembly::aarch64::SYSREG::VPIDR_EL2)
  .value("MPUIR_EL2", LIEF::assembly::aarch64::SYSREG::MPUIR_EL2)
  .value("VMPIDR_EL2", LIEF::assembly::aarch64::SYSREG::VMPIDR_EL2)
  .value("SCTLR_EL2", LIEF::assembly::aarch64::SYSREG::SCTLR_EL2)
  .value("ACTLR_EL2", LIEF::assembly::aarch64::SYSREG::ACTLR_EL2)
  .value("SCTLR2_EL2", LIEF::assembly::aarch64::SYSREG::SCTLR2_EL2)
  .value("HCR_EL2", LIEF::assembly::aarch64::SYSREG::HCR_EL2)
  .value("MDCR_EL2", LIEF::assembly::aarch64::SYSREG::MDCR_EL2)
  .value("CPTR_EL2", LIEF::assembly::aarch64::SYSREG::CPTR_EL2)
  .value("HSTR_EL2", LIEF::assembly::aarch64::SYSREG::HSTR_EL2)
  .value("HFGRTR_EL2", LIEF::assembly::aarch64::SYSREG::HFGRTR_EL2)
  .value("HFGWTR_EL2", LIEF::assembly::aarch64::SYSREG::HFGWTR_EL2)
  .value("HFGITR_EL2", LIEF::assembly::aarch64::SYSREG::HFGITR_EL2)
  .value("HACR_EL2", LIEF::assembly::aarch64::SYSREG::HACR_EL2)
  .value("ZCR_EL2", LIEF::assembly::aarch64::SYSREG::ZCR_EL2)
  .value("TRFCR_EL2", LIEF::assembly::aarch64::SYSREG::TRFCR_EL2)
  .value("HCRX_EL2", LIEF::assembly::aarch64::SYSREG::HCRX_EL2)
  .value("TRCITECR_EL2", LIEF::assembly::aarch64::SYSREG::TRCITECR_EL2)
  .value("SMPRIMAP_EL2", LIEF::assembly::aarch64::SYSREG::SMPRIMAP_EL2)
  .value("SMCR_EL2", LIEF::assembly::aarch64::SYSREG::SMCR_EL2)
  .value("SDER32_EL2", LIEF::assembly::aarch64::SYSREG::SDER32_EL2)
  .value("SCTLRMASK_EL2", LIEF::assembly::aarch64::SYSREG::SCTLRMASK_EL2)
  .value("ACTLRMASK_EL2", LIEF::assembly::aarch64::SYSREG::ACTLRMASK_EL2)
  .value("CPTRMASK_EL2", LIEF::assembly::aarch64::SYSREG::CPTRMASK_EL2)
  .value("SCTLR2MASK_EL2", LIEF::assembly::aarch64::SYSREG::SCTLR2MASK_EL2)
  .value("TTBR0_EL2", LIEF::assembly::aarch64::SYSREG::TTBR0_EL2)
  .value("VSCTLR_EL2", LIEF::assembly::aarch64::SYSREG::VSCTLR_EL2)
  .value("TTBR1_EL2", LIEF::assembly::aarch64::SYSREG::TTBR1_EL2)
  .value("TCR_EL2", LIEF::assembly::aarch64::SYSREG::TCR_EL2)
  .value("TCR2_EL2", LIEF::assembly::aarch64::SYSREG::TCR2_EL2)
  .value("VTTBR_EL2", LIEF::assembly::aarch64::SYSREG::VTTBR_EL2)
  .value("VTCR_EL2", LIEF::assembly::aarch64::SYSREG::VTCR_EL2)
  .value("VNCR_EL2", LIEF::assembly::aarch64::SYSREG::VNCR_EL2)
  .value("HDBSSBR_EL2", LIEF::assembly::aarch64::SYSREG::HDBSSBR_EL2)
  .value("HDBSSPROD_EL2", LIEF::assembly::aarch64::SYSREG::HDBSSPROD_EL2)
  .value("HACDBSBR_EL2", LIEF::assembly::aarch64::SYSREG::HACDBSBR_EL2)
  .value("HACDBSCONS_EL2", LIEF::assembly::aarch64::SYSREG::HACDBSCONS_EL2)
  .value("GCSCR_EL2", LIEF::assembly::aarch64::SYSREG::GCSCR_EL2)
  .value("GCSPR_EL2", LIEF::assembly::aarch64::SYSREG::GCSPR_EL2)
  .value("VSTTBR_EL2", LIEF::assembly::aarch64::SYSREG::VSTTBR_EL2)
  .value("VSTCR_EL2", LIEF::assembly::aarch64::SYSREG::VSTCR_EL2)
  .value("TCRMASK_EL2", LIEF::assembly::aarch64::SYSREG::TCRMASK_EL2)
  .value("TCR2MASK_EL2", LIEF::assembly::aarch64::SYSREG::TCR2MASK_EL2)
  .value("DACR32_EL2", LIEF::assembly::aarch64::SYSREG::DACR32_EL2)
  .value("HDFGRTR2_EL2", LIEF::assembly::aarch64::SYSREG::HDFGRTR2_EL2)
  .value("HDFGWTR2_EL2", LIEF::assembly::aarch64::SYSREG::HDFGWTR2_EL2)
  .value("HFGRTR2_EL2", LIEF::assembly::aarch64::SYSREG::HFGRTR2_EL2)
  .value("HFGWTR2_EL2", LIEF::assembly::aarch64::SYSREG::HFGWTR2_EL2)
  .value("HDFGRTR_EL2", LIEF::assembly::aarch64::SYSREG::HDFGRTR_EL2)
  .value("HDFGWTR_EL2", LIEF::assembly::aarch64::SYSREG::HDFGWTR_EL2)
  .value("HAFGRTR_EL2", LIEF::assembly::aarch64::SYSREG::HAFGRTR_EL2)
  .value("HFGITR2_EL2", LIEF::assembly::aarch64::SYSREG::HFGITR2_EL2)
  .value("SPSR_EL2", LIEF::assembly::aarch64::SYSREG::SPSR_EL2)
  .value("ELR_EL2", LIEF::assembly::aarch64::SYSREG::ELR_EL2)
  .value("SP_EL1", LIEF::assembly::aarch64::SYSREG::SP_EL1)
  .value("SPSR_irq", LIEF::assembly::aarch64::SYSREG::SPSR_irq)
  .value("SPSR_abt", LIEF::assembly::aarch64::SYSREG::SPSR_abt)
  .value("SPSR_und", LIEF::assembly::aarch64::SYSREG::SPSR_und)
  .value("SPSR_fiq", LIEF::assembly::aarch64::SYSREG::SPSR_fiq)
  .value("IFSR32_EL2", LIEF::assembly::aarch64::SYSREG::IFSR32_EL2)
  .value("AFSR0_EL2", LIEF::assembly::aarch64::SYSREG::AFSR0_EL2)
  .value("AFSR1_EL2", LIEF::assembly::aarch64::SYSREG::AFSR1_EL2)
  .value("ESR_EL2", LIEF::assembly::aarch64::SYSREG::ESR_EL2)
  .value("VSESR_EL2", LIEF::assembly::aarch64::SYSREG::VSESR_EL2)
  .value("FPEXC32_EL2", LIEF::assembly::aarch64::SYSREG::FPEXC32_EL2)
  .value("TFSR_EL2", LIEF::assembly::aarch64::SYSREG::TFSR_EL2)
  .value("FAR_EL2", LIEF::assembly::aarch64::SYSREG::FAR_EL2)
  .value("HPFAR_EL2", LIEF::assembly::aarch64::SYSREG::HPFAR_EL2)
  .value("PFAR_EL2", LIEF::assembly::aarch64::SYSREG::PFAR_EL2)
  .value("PRENR_EL2", LIEF::assembly::aarch64::SYSREG::PRENR_EL2)
  .value("PRSELR_EL2", LIEF::assembly::aarch64::SYSREG::PRSELR_EL2)
  .value("PRBAR_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR_EL2)
  .value("PRLAR_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR_EL2)
  .value("PRBAR1_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR1_EL2)
  .value("PRLAR1_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR1_EL2)
  .value("PRBAR2_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR2_EL2)
  .value("PRLAR2_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR2_EL2)
  .value("PRBAR3_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR3_EL2)
  .value("PRLAR3_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR3_EL2)
  .value("PRBAR4_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR4_EL2)
  .value("PRLAR4_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR4_EL2)
  .value("PRBAR5_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR5_EL2)
  .value("PRLAR5_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR5_EL2)
  .value("PRBAR6_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR6_EL2)
  .value("PRLAR6_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR6_EL2)
  .value("PRBAR7_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR7_EL2)
  .value("PRLAR7_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR7_EL2)
  .value("PRBAR8_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR8_EL2)
  .value("PRLAR8_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR8_EL2)
  .value("PRBAR9_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR9_EL2)
  .value("PRLAR9_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR9_EL2)
  .value("PRBAR10_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR10_EL2)
  .value("PRLAR10_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR10_EL2)
  .value("PRBAR11_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR11_EL2)
  .value("PRLAR11_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR11_EL2)
  .value("PRBAR12_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR12_EL2)
  .value("PRLAR12_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR12_EL2)
  .value("PRBAR13_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR13_EL2)
  .value("PRLAR13_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR13_EL2)
  .value("PRBAR14_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR14_EL2)
  .value("PRLAR14_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR14_EL2)
  .value("PRBAR15_EL2", LIEF::assembly::aarch64::SYSREG::PRBAR15_EL2)
  .value("PRLAR15_EL2", LIEF::assembly::aarch64::SYSREG::PRLAR15_EL2)
  .value("PMSCR_EL2", LIEF::assembly::aarch64::SYSREG::PMSCR_EL2)
  .value("PMBSR_EL2", LIEF::assembly::aarch64::SYSREG::PMBSR_EL2)
  .value("TRBSR_EL2", LIEF::assembly::aarch64::SYSREG::TRBSR_EL2)
  .value("MAIR2_EL2", LIEF::assembly::aarch64::SYSREG::MAIR2_EL2)
  .value("MAIR_EL2", LIEF::assembly::aarch64::SYSREG::MAIR_EL2)
  .value("PIRE0_EL2", LIEF::assembly::aarch64::SYSREG::PIRE0_EL2)
  .value("PIR_EL2", LIEF::assembly::aarch64::SYSREG::PIR_EL2)
  .value("POR_EL2", LIEF::assembly::aarch64::SYSREG::POR_EL2)
  .value("S2PIR_EL2", LIEF::assembly::aarch64::SYSREG::S2PIR_EL2)
  .value("AMAIR_EL2", LIEF::assembly::aarch64::SYSREG::AMAIR_EL2)
  .value("AMAIR2_EL2", LIEF::assembly::aarch64::SYSREG::AMAIR2_EL2)
  .value("MPAMHCR_EL2", LIEF::assembly::aarch64::SYSREG::MPAMHCR_EL2)
  .value("MPAMVPMV_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPMV_EL2)
  .value("MPAM2_EL2", LIEF::assembly::aarch64::SYSREG::MPAM2_EL2)
  .value("MPAMBW2_EL2", LIEF::assembly::aarch64::SYSREG::MPAMBW2_EL2)
  .value("MPAMBWCAP_EL2", LIEF::assembly::aarch64::SYSREG::MPAMBWCAP_EL2)
  .value("MPAMVPM0_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM0_EL2)
  .value("MPAMVPM1_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM1_EL2)
  .value("MPAMVPM2_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM2_EL2)
  .value("MPAMVPM3_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM3_EL2)
  .value("MPAMVPM4_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM4_EL2)
  .value("MPAMVPM5_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM5_EL2)
  .value("MPAMVPM6_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM6_EL2)
  .value("MPAMVPM7_EL2", LIEF::assembly::aarch64::SYSREG::MPAMVPM7_EL2)
  .value("MECID_P0_EL2", LIEF::assembly::aarch64::SYSREG::MECID_P0_EL2)
  .value("MECID_A0_EL2", LIEF::assembly::aarch64::SYSREG::MECID_A0_EL2)
  .value("MECID_P1_EL2", LIEF::assembly::aarch64::SYSREG::MECID_P1_EL2)
  .value("MECID_A1_EL2", LIEF::assembly::aarch64::SYSREG::MECID_A1_EL2)
  .value("MECIDR_EL2", LIEF::assembly::aarch64::SYSREG::MECIDR_EL2)
  .value("VMECID_P_EL2", LIEF::assembly::aarch64::SYSREG::VMECID_P_EL2)
  .value("VMECID_A_EL2", LIEF::assembly::aarch64::SYSREG::VMECID_A_EL2)
  .value("VBAR_EL2", LIEF::assembly::aarch64::SYSREG::VBAR_EL2)
  .value("RVBAR_EL2", LIEF::assembly::aarch64::SYSREG::RVBAR_EL2)
  .value("RMR_EL2", LIEF::assembly::aarch64::SYSREG::RMR_EL2)
  .value("VDISR_EL2", LIEF::assembly::aarch64::SYSREG::VDISR_EL2)
  .value("ICH_AP0R0_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP0R0_EL2)
  .value("ICH_AP0R1_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP0R1_EL2)
  .value("ICH_AP0R2_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP0R2_EL2)
  .value("ICH_AP0R3_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP0R3_EL2)
  .value("ICH_AP1R0_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP1R0_EL2)
  .value("ICH_AP1R1_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP1R1_EL2)
  .value("ICH_AP1R2_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP1R2_EL2)
  .value("ICH_AP1R3_EL2", LIEF::assembly::aarch64::SYSREG::ICH_AP1R3_EL2)
  .value("ICC_SRE_EL2", LIEF::assembly::aarch64::SYSREG::ICC_SRE_EL2)
  .value("ICH_HCR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_HCR_EL2)
  .value("ICH_VTR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_VTR_EL2)
  .value("ICH_MISR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_MISR_EL2)
  .value("ICH_EISR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_EISR_EL2)
  .value("ICH_ELRSR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_ELRSR_EL2)
  .value("ICH_VMCR_EL2", LIEF::assembly::aarch64::SYSREG::ICH_VMCR_EL2)
  .value("ICH_LR0_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR0_EL2)
  .value("ICH_LR1_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR1_EL2)
  .value("ICH_LR2_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR2_EL2)
  .value("ICH_LR3_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR3_EL2)
  .value("ICH_LR4_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR4_EL2)
  .value("ICH_LR5_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR5_EL2)
  .value("ICH_LR6_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR6_EL2)
  .value("ICH_LR7_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR7_EL2)
  .value("ICH_LR8_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR8_EL2)
  .value("ICH_LR9_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR9_EL2)
  .value("ICH_LR10_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR10_EL2)
  .value("ICH_LR11_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR11_EL2)
  .value("ICH_LR12_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR12_EL2)
  .value("ICH_LR13_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR13_EL2)
  .value("ICH_LR14_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR14_EL2)
  .value("ICH_LR15_EL2", LIEF::assembly::aarch64::SYSREG::ICH_LR15_EL2)
  .value("CONTEXTIDR_EL2", LIEF::assembly::aarch64::SYSREG::CONTEXTIDR_EL2)
  .value("TPIDR_EL2", LIEF::assembly::aarch64::SYSREG::TPIDR_EL2)
  .value("SCXTNUM_EL2", LIEF::assembly::aarch64::SYSREG::SCXTNUM_EL2)
  .value("AMEVCNTVOFF00_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF00_EL2)
  .value("AMEVCNTVOFF01_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF01_EL2)
  .value("AMEVCNTVOFF02_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF02_EL2)
  .value("AMEVCNTVOFF03_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF03_EL2)
  .value("AMEVCNTVOFF04_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF04_EL2)
  .value("AMEVCNTVOFF05_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF05_EL2)
  .value("AMEVCNTVOFF06_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF06_EL2)
  .value("AMEVCNTVOFF07_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF07_EL2)
  .value("AMEVCNTVOFF08_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF08_EL2)
  .value("AMEVCNTVOFF09_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF09_EL2)
  .value("AMEVCNTVOFF010_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF010_EL2)
  .value("AMEVCNTVOFF011_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF011_EL2)
  .value("AMEVCNTVOFF012_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF012_EL2)
  .value("AMEVCNTVOFF013_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF013_EL2)
  .value("AMEVCNTVOFF014_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF014_EL2)
  .value("AMEVCNTVOFF015_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF015_EL2)
  .value("AMEVCNTVOFF10_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF10_EL2)
  .value("AMEVCNTVOFF11_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF11_EL2)
  .value("AMEVCNTVOFF12_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF12_EL2)
  .value("AMEVCNTVOFF13_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF13_EL2)
  .value("AMEVCNTVOFF14_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF14_EL2)
  .value("AMEVCNTVOFF15_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF15_EL2)
  .value("AMEVCNTVOFF16_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF16_EL2)
  .value("AMEVCNTVOFF17_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF17_EL2)
  .value("AMEVCNTVOFF18_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF18_EL2)
  .value("AMEVCNTVOFF19_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF19_EL2)
  .value("AMEVCNTVOFF110_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF110_EL2)
  .value("AMEVCNTVOFF111_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF111_EL2)
  .value("AMEVCNTVOFF112_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF112_EL2)
  .value("AMEVCNTVOFF113_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF113_EL2)
  .value("AMEVCNTVOFF114_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF114_EL2)
  .value("AMEVCNTVOFF115_EL2", LIEF::assembly::aarch64::SYSREG::AMEVCNTVOFF115_EL2)
  .value("CNTVOFF_EL2", LIEF::assembly::aarch64::SYSREG::CNTVOFF_EL2)
  .value("CNTSCALE_EL2", LIEF::assembly::aarch64::SYSREG::CNTSCALE_EL2)
  .value("CNTISCALE_EL2", LIEF::assembly::aarch64::SYSREG::CNTISCALE_EL2)
  .value("CNTPOFF_EL2", LIEF::assembly::aarch64::SYSREG::CNTPOFF_EL2)
  .value("CNTVFRQ_EL2", LIEF::assembly::aarch64::SYSREG::CNTVFRQ_EL2)
  .value("CNTHCTL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHCTL_EL2)
  .value("CNTHP_TVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHP_TVAL_EL2)
  .value("CNTHP_CTL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHP_CTL_EL2)
  .value("CNTHP_CVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHP_CVAL_EL2)
  .value("CNTHV_TVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHV_TVAL_EL2)
  .value("CNTHV_CTL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHV_CTL_EL2)
  .value("CNTHV_CVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHV_CVAL_EL2)
  .value("CNTHVS_TVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHVS_TVAL_EL2)
  .value("CNTHVS_CTL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHVS_CTL_EL2)
  .value("CNTHVS_CVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHVS_CVAL_EL2)
  .value("CNTHPS_TVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHPS_TVAL_EL2)
  .value("CNTHPS_CTL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHPS_CTL_EL2)
  .value("CNTHPS_CVAL_EL2", LIEF::assembly::aarch64::SYSREG::CNTHPS_CVAL_EL2)
  .value("SCTLR_EL12", LIEF::assembly::aarch64::SYSREG::SCTLR_EL12)
  .value("ACTLR_EL12", LIEF::assembly::aarch64::SYSREG::ACTLR_EL12)
  .value("CPACR_EL12", LIEF::assembly::aarch64::SYSREG::CPACR_EL12)
  .value("SCTLR2_EL12", LIEF::assembly::aarch64::SYSREG::SCTLR2_EL12)
  .value("ZCR_EL12", LIEF::assembly::aarch64::SYSREG::ZCR_EL12)
  .value("TRFCR_EL12", LIEF::assembly::aarch64::SYSREG::TRFCR_EL12)
  .value("TRCITECR_EL12", LIEF::assembly::aarch64::SYSREG::TRCITECR_EL12)
  .value("SMCR_EL12", LIEF::assembly::aarch64::SYSREG::SMCR_EL12)
  .value("SCTLRMASK_EL12", LIEF::assembly::aarch64::SYSREG::SCTLRMASK_EL12)
  .value("ACTLRMASK_EL12", LIEF::assembly::aarch64::SYSREG::ACTLRMASK_EL12)
  .value("CPACRMASK_EL12", LIEF::assembly::aarch64::SYSREG::CPACRMASK_EL12)
  .value("SCTLR2MASK_EL12", LIEF::assembly::aarch64::SYSREG::SCTLR2MASK_EL12)
  .value("TTBR0_EL12", LIEF::assembly::aarch64::SYSREG::TTBR0_EL12)
  .value("TTBR1_EL12", LIEF::assembly::aarch64::SYSREG::TTBR1_EL12)
  .value("TCR_EL12", LIEF::assembly::aarch64::SYSREG::TCR_EL12)
  .value("TCR2_EL12", LIEF::assembly::aarch64::SYSREG::TCR2_EL12)
  .value("GCSCR_EL12", LIEF::assembly::aarch64::SYSREG::GCSCR_EL12)
  .value("GCSPR_EL12", LIEF::assembly::aarch64::SYSREG::GCSPR_EL12)
  .value("TCRMASK_EL12", LIEF::assembly::aarch64::SYSREG::TCRMASK_EL12)
  .value("TCR2MASK_EL12", LIEF::assembly::aarch64::SYSREG::TCR2MASK_EL12)
  .value("SPSR_EL12", LIEF::assembly::aarch64::SYSREG::SPSR_EL12)
  .value("ELR_EL12", LIEF::assembly::aarch64::SYSREG::ELR_EL12)
  .value("AFSR0_EL12", LIEF::assembly::aarch64::SYSREG::AFSR0_EL12)
  .value("AFSR1_EL12", LIEF::assembly::aarch64::SYSREG::AFSR1_EL12)
  .value("ESR_EL12", LIEF::assembly::aarch64::SYSREG::ESR_EL12)
  .value("TFSR_EL12", LIEF::assembly::aarch64::SYSREG::TFSR_EL12)
  .value("FAR_EL12", LIEF::assembly::aarch64::SYSREG::FAR_EL12)
  .value("PFAR_EL12", LIEF::assembly::aarch64::SYSREG::PFAR_EL12)
  .value("PMSCR_EL12", LIEF::assembly::aarch64::SYSREG::PMSCR_EL12)
  .value("PMBSR_EL12", LIEF::assembly::aarch64::SYSREG::PMBSR_EL12)
  .value("TRBSR_EL12", LIEF::assembly::aarch64::SYSREG::TRBSR_EL12)
  .value("MAIR_EL12", LIEF::assembly::aarch64::SYSREG::MAIR_EL12)
  .value("MAIR2_EL12", LIEF::assembly::aarch64::SYSREG::MAIR2_EL12)
  .value("PIRE0_EL12", LIEF::assembly::aarch64::SYSREG::PIRE0_EL12)
  .value("PIR_EL12", LIEF::assembly::aarch64::SYSREG::PIR_EL12)
  .value("POR_EL12", LIEF::assembly::aarch64::SYSREG::POR_EL12)
  .value("AMAIR_EL12", LIEF::assembly::aarch64::SYSREG::AMAIR_EL12)
  .value("AMAIR2_EL12", LIEF::assembly::aarch64::SYSREG::AMAIR2_EL12)
  .value("MPAM1_EL12", LIEF::assembly::aarch64::SYSREG::MPAM1_EL12)
  .value("MPAMBW1_EL12", LIEF::assembly::aarch64::SYSREG::MPAMBW1_EL12)
  .value("VBAR_EL12", LIEF::assembly::aarch64::SYSREG::VBAR_EL12)
  .value("CONTEXTIDR_EL12", LIEF::assembly::aarch64::SYSREG::CONTEXTIDR_EL12)
  .value("SCXTNUM_EL12", LIEF::assembly::aarch64::SYSREG::SCXTNUM_EL12)
  .value("CNTKCTL_EL12", LIEF::assembly::aarch64::SYSREG::CNTKCTL_EL12)
  .value("CNTP_TVAL_EL02", LIEF::assembly::aarch64::SYSREG::CNTP_TVAL_EL02)
  .value("CNTP_CTL_EL02", LIEF::assembly::aarch64::SYSREG::CNTP_CTL_EL02);
  reg.value("CNTP_CVAL_EL02", LIEF::assembly::aarch64::SYSREG::CNTP_CVAL_EL02)
  .value("CNTV_TVAL_EL02", LIEF::assembly::aarch64::SYSREG::CNTV_TVAL_EL02)
  .value("CNTV_CTL_EL02", LIEF::assembly::aarch64::SYSREG::CNTV_CTL_EL02)
  .value("CNTV_CVAL_EL02", LIEF::assembly::aarch64::SYSREG::CNTV_CVAL_EL02)
  .value("SCTLR_EL3", LIEF::assembly::aarch64::SYSREG::SCTLR_EL3)
  .value("ACTLR_EL3", LIEF::assembly::aarch64::SYSREG::ACTLR_EL3)
  .value("SCTLR2_EL3", LIEF::assembly::aarch64::SYSREG::SCTLR2_EL3)
  .value("SCR_EL3", LIEF::assembly::aarch64::SYSREG::SCR_EL3)
  .value("SDER32_EL3", LIEF::assembly::aarch64::SYSREG::SDER32_EL3)
  .value("CPTR_EL3", LIEF::assembly::aarch64::SYSREG::CPTR_EL3)
  .value("FGWTE3_EL3", LIEF::assembly::aarch64::SYSREG::FGWTE3_EL3)
  .value("ZCR_EL3", LIEF::assembly::aarch64::SYSREG::ZCR_EL3)
  .value("SMCR_EL3", LIEF::assembly::aarch64::SYSREG::SMCR_EL3)
  .value("MDCR_EL3", LIEF::assembly::aarch64::SYSREG::MDCR_EL3)
  .value("TTBR0_EL3", LIEF::assembly::aarch64::SYSREG::TTBR0_EL3)
  .value("TCR_EL3", LIEF::assembly::aarch64::SYSREG::TCR_EL3)
  .value("GPTBR_EL3", LIEF::assembly::aarch64::SYSREG::GPTBR_EL3)
  .value("GPCBW_EL3", LIEF::assembly::aarch64::SYSREG::GPCBW_EL3)
  .value("GPCCR_EL3", LIEF::assembly::aarch64::SYSREG::GPCCR_EL3)
  .value("GCSCR_EL3", LIEF::assembly::aarch64::SYSREG::GCSCR_EL3)
  .value("GCSPR_EL3", LIEF::assembly::aarch64::SYSREG::GCSPR_EL3)
  .value("SPSR_EL3", LIEF::assembly::aarch64::SYSREG::SPSR_EL3)
  .value("ELR_EL3", LIEF::assembly::aarch64::SYSREG::ELR_EL3)
  .value("SP_EL2", LIEF::assembly::aarch64::SYSREG::SP_EL2)
  .value("AFSR0_EL3", LIEF::assembly::aarch64::SYSREG::AFSR0_EL3)
  .value("AFSR1_EL3", LIEF::assembly::aarch64::SYSREG::AFSR1_EL3)
  .value("ESR_EL3", LIEF::assembly::aarch64::SYSREG::ESR_EL3)
  .value("VSESR_EL3", LIEF::assembly::aarch64::SYSREG::VSESR_EL3)
  .value("TFSR_EL3", LIEF::assembly::aarch64::SYSREG::TFSR_EL3)
  .value("FAR_EL3", LIEF::assembly::aarch64::SYSREG::FAR_EL3)
  .value("MFAR_EL3", LIEF::assembly::aarch64::SYSREG::MFAR_EL3)
  .value("PMBSR_EL3", LIEF::assembly::aarch64::SYSREG::PMBSR_EL3)
  .value("TRBSR_EL3", LIEF::assembly::aarch64::SYSREG::TRBSR_EL3)
  .value("MAIR2_EL3", LIEF::assembly::aarch64::SYSREG::MAIR2_EL3)
  .value("MAIR_EL3", LIEF::assembly::aarch64::SYSREG::MAIR_EL3)
  .value("PIR_EL3", LIEF::assembly::aarch64::SYSREG::PIR_EL3)
  .value("POR_EL3", LIEF::assembly::aarch64::SYSREG::POR_EL3)
  .value("AMAIR_EL3", LIEF::assembly::aarch64::SYSREG::AMAIR_EL3)
  .value("AMAIR2_EL3", LIEF::assembly::aarch64::SYSREG::AMAIR2_EL3)
  .value("MPAM3_EL3", LIEF::assembly::aarch64::SYSREG::MPAM3_EL3)
  .value("MPAMBW3_EL3", LIEF::assembly::aarch64::SYSREG::MPAMBW3_EL3)
  .value("MECID_RL_A_EL3", LIEF::assembly::aarch64::SYSREG::MECID_RL_A_EL3)
  .value("VBAR_EL3", LIEF::assembly::aarch64::SYSREG::VBAR_EL3)
  .value("RVBAR_EL3", LIEF::assembly::aarch64::SYSREG::RVBAR_EL3)
  .value("RMR_EL3", LIEF::assembly::aarch64::SYSREG::RMR_EL3)
  .value("VDISR_EL3", LIEF::assembly::aarch64::SYSREG::VDISR_EL3)
  .value("ICC_CTLR_EL3", LIEF::assembly::aarch64::SYSREG::ICC_CTLR_EL3)
  .value("ICC_SRE_EL3", LIEF::assembly::aarch64::SYSREG::ICC_SRE_EL3)
  .value("ICC_IGRPEN1_EL3", LIEF::assembly::aarch64::SYSREG::ICC_IGRPEN1_EL3)
  .value("TPIDR_EL3", LIEF::assembly::aarch64::SYSREG::TPIDR_EL3)
  .value("SCXTNUM_EL3", LIEF::assembly::aarch64::SYSREG::SCXTNUM_EL3)
  .value("CNTPS_TVAL_EL1", LIEF::assembly::aarch64::SYSREG::CNTPS_TVAL_EL1)
  .value("CNTPS_CTL_EL1", LIEF::assembly::aarch64::SYSREG::CNTPS_CTL_EL1)
  .value("CNTPS_CVAL_EL1", LIEF::assembly::aarch64::SYSREG::CNTPS_CVAL_EL1)
  .value("NUM_TARGET_SYSREGS", LIEF::assembly::aarch64::SYSREG::NUM_TARGET_SYSREGS)
  ;
}

}
