#[allow(non_camel_case_types)]
#[derive(<PERSON>bug, <PERSON>lone, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Eq, <PERSON>ialOrd, Ord, Hash)]
pub enum Opcode {
  PHI,
  INLINEASM,
  INLINEASM_BR,
  CFI_INSTRUCTION,
  EH_LABEL,
  GC_LABEL,
  ANNOTATION_LABEL,
  K<PERSON><PERSON>,
  EXTRA<PERSON>_SUBREG,
  INSERT_SUBREG,
  IMPL<PERSON>IT_DEF,
  INIT_UNDEF,
  SUBREG_TO_REG,
  COPY_TO_REGCLASS,
  DBG_VALUE,
  DBG_VALUE_LIST,
  DBG_INSTR_REF,
  DBG_PHI,
  DBG_LABEL,
  REG_SEQUENCE,
  COPY,
  BUN<PERSON>LE,
  LIF<PERSON>IME_START,
  LIF<PERSON>IME_END,
  PSEUDO_PROBE,
  ARITH_FENCE,
  STACKMAP,
  FENTRY_CALL,
  PATCHPOINT,
  LOAD_STACK_GUARD,
  PREALLOCATED_SETUP,
  PREALLOCATED_ARG,
  STAT<PERSON>OINT,
  LOCAL_ESCAPE,
  FAULTING_OP,
  <PERSON>TCHABLE_OP,
  PATCHABLE_FUNCTION_ENTER,
  PATCHABLE_RET,
  PATCHABLE_FUNCTION_EXIT,
  PATCHABLE_TAIL_CALL,
  PATCHABLE_EVENT_CALL,
  PATCHABLE_TYPED_EVENT_CALL,
  ICALL_BRANCH_FUNNEL,
  FAKE_USE,
  MEMBARRIER,
  JUMP_TABLE_DEBUG_INFO,
  CONVERGENCECTRL_ENTRY,
  CONVERGENCECTRL_ANCHOR,
  CONVERGENCECTRL_LOOP,
  CONVERGENCECTRL_GLUE,
  G_ASSERT_SEXT,
  G_ASSERT_ZEXT,
  G_ASSERT_ALIGN,
  G_ADD,
  G_SUB,
  G_MUL,
  G_SDIV,
  G_UDIV,
  G_SREM,
  G_UREM,
  G_SDIVREM,
  G_UDIVREM,
  G_AND,
  G_OR,
  G_XOR,
  G_ABDS,
  G_ABDU,
  G_IMPLICIT_DEF,
  G_PHI,
  G_FRAME_INDEX,
  G_GLOBAL_VALUE,
  G_PTRAUTH_GLOBAL_VALUE,
  G_CONSTANT_POOL,
  G_EXTRACT,
  G_UNMERGE_VALUES,
  G_INSERT,
  G_MERGE_VALUES,
  G_BUILD_VECTOR,
  G_BUILD_VECTOR_TRUNC,
  G_CONCAT_VECTORS,
  G_PTRTOINT,
  G_INTTOPTR,
  G_BITCAST,
  G_FREEZE,
  G_CONSTANT_FOLD_BARRIER,
  G_INTRINSIC_FPTRUNC_ROUND,
  G_INTRINSIC_TRUNC,
  G_INTRINSIC_ROUND,
  G_INTRINSIC_LRINT,
  G_INTRINSIC_LLRINT,
  G_INTRINSIC_ROUNDEVEN,
  G_READCYCLECOUNTER,
  G_READSTEADYCOUNTER,
  G_LOAD,
  G_SEXTLOAD,
  G_ZEXTLOAD,
  G_INDEXED_LOAD,
  G_INDEXED_SEXTLOAD,
  G_INDEXED_ZEXTLOAD,
  G_STORE,
  G_INDEXED_STORE,
  G_ATOMIC_CMPXCHG_WITH_SUCCESS,
  G_ATOMIC_CMPXCHG,
  G_ATOMICRMW_XCHG,
  G_ATOMICRMW_ADD,
  G_ATOMICRMW_SUB,
  G_ATOMICRMW_AND,
  G_ATOMICRMW_NAND,
  G_ATOMICRMW_OR,
  G_ATOMICRMW_XOR,
  G_ATOMICRMW_MAX,
  G_ATOMICRMW_MIN,
  G_ATOMICRMW_UMAX,
  G_ATOMICRMW_UMIN,
  G_ATOMICRMW_FADD,
  G_ATOMICRMW_FSUB,
  G_ATOMICRMW_FMAX,
  G_ATOMICRMW_FMIN,
  G_ATOMICRMW_UINC_WRAP,
  G_ATOMICRMW_UDEC_WRAP,
  G_ATOMICRMW_USUB_COND,
  G_ATOMICRMW_USUB_SAT,
  G_FENCE,
  G_PREFETCH,
  G_BRCOND,
  G_BRINDIRECT,
  G_INVOKE_REGION_START,
  G_INTRINSIC,
  G_INTRINSIC_W_SIDE_EFFECTS,
  G_INTRINSIC_CONVERGENT,
  G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS,
  G_ANYEXT,
  G_TRUNC,
  G_CONSTANT,
  G_FCONSTANT,
  G_VASTART,
  G_VAARG,
  G_SEXT,
  G_SEXT_INREG,
  G_ZEXT,
  G_SHL,
  G_LSHR,
  G_ASHR,
  G_FSHL,
  G_FSHR,
  G_ROTR,
  G_ROTL,
  G_ICMP,
  G_FCMP,
  G_SCMP,
  G_UCMP,
  G_SELECT,
  G_UADDO,
  G_UADDE,
  G_USUBO,
  G_USUBE,
  G_SADDO,
  G_SADDE,
  G_SSUBO,
  G_SSUBE,
  G_UMULO,
  G_SMULO,
  G_UMULH,
  G_SMULH,
  G_UADDSAT,
  G_SADDSAT,
  G_USUBSAT,
  G_SSUBSAT,
  G_USHLSAT,
  G_SSHLSAT,
  G_SMULFIX,
  G_UMULFIX,
  G_SMULFIXSAT,
  G_UMULFIXSAT,
  G_SDIVFIX,
  G_UDIVFIX,
  G_SDIVFIXSAT,
  G_UDIVFIXSAT,
  G_FADD,
  G_FSUB,
  G_FMUL,
  G_FMA,
  G_FMAD,
  G_FDIV,
  G_FREM,
  G_FPOW,
  G_FPOWI,
  G_FEXP,
  G_FEXP2,
  G_FEXP10,
  G_FLOG,
  G_FLOG2,
  G_FLOG10,
  G_FLDEXP,
  G_FFREXP,
  G_FNEG,
  G_FPEXT,
  G_FPTRUNC,
  G_FPTOSI,
  G_FPTOUI,
  G_SITOFP,
  G_UITOFP,
  G_FPTOSI_SAT,
  G_FPTOUI_SAT,
  G_FABS,
  G_FCOPYSIGN,
  G_IS_FPCLASS,
  G_FCANONICALIZE,
  G_FMINNUM,
  G_FMAXNUM,
  G_FMINNUM_IEEE,
  G_FMAXNUM_IEEE,
  G_FMINIMUM,
  G_FMAXIMUM,
  G_GET_FPENV,
  G_SET_FPENV,
  G_RESET_FPENV,
  G_GET_FPMODE,
  G_SET_FPMODE,
  G_RESET_FPMODE,
  G_PTR_ADD,
  G_PTRMASK,
  G_SMIN,
  G_SMAX,
  G_UMIN,
  G_UMAX,
  G_ABS,
  G_LROUND,
  G_LLROUND,
  G_BR,
  G_BRJT,
  G_VSCALE,
  G_INSERT_SUBVECTOR,
  G_EXTRACT_SUBVECTOR,
  G_INSERT_VECTOR_ELT,
  G_EXTRACT_VECTOR_ELT,
  G_SHUFFLE_VECTOR,
  G_SPLAT_VECTOR,
  G_STEP_VECTOR,
  G_VECTOR_COMPRESS,
  G_CTTZ,
  G_CTTZ_ZERO_UNDEF,
  G_CTLZ,
  G_CTLZ_ZERO_UNDEF,
  G_CTPOP,
  G_BSWAP,
  G_BITREVERSE,
  G_FCEIL,
  G_FCOS,
  G_FSIN,
  G_FSINCOS,
  G_FTAN,
  G_FACOS,
  G_FASIN,
  G_FATAN,
  G_FATAN2,
  G_FCOSH,
  G_FSINH,
  G_FTANH,
  G_FSQRT,
  G_FFLOOR,
  G_FRINT,
  G_FNEARBYINT,
  G_ADDRSPACE_CAST,
  G_BLOCK_ADDR,
  G_JUMP_TABLE,
  G_DYN_STACKALLOC,
  G_STACKSAVE,
  G_STACKRESTORE,
  G_STRICT_FADD,
  G_STRICT_FSUB,
  G_STRICT_FMUL,
  G_STRICT_FDIV,
  G_STRICT_FREM,
  G_STRICT_FMA,
  G_STRICT_FSQRT,
  G_STRICT_FLDEXP,
  G_READ_REGISTER,
  G_WRITE_REGISTER,
  G_MEMCPY,
  G_MEMCPY_INLINE,
  G_MEMMOVE,
  G_MEMSET,
  G_BZERO,
  G_TRAP,
  G_DEBUGTRAP,
  G_UBSANTRAP,
  G_VECREDUCE_SEQ_FADD,
  G_VECREDUCE_SEQ_FMUL,
  G_VECREDUCE_FADD,
  G_VECREDUCE_FMUL,
  G_VECREDUCE_FMAX,
  G_VECREDUCE_FMIN,
  G_VECREDUCE_FMAXIMUM,
  G_VECREDUCE_FMINIMUM,
  G_VECREDUCE_ADD,
  G_VECREDUCE_MUL,
  G_VECREDUCE_AND,
  G_VECREDUCE_OR,
  G_VECREDUCE_XOR,
  G_VECREDUCE_SMAX,
  G_VECREDUCE_SMIN,
  G_VECREDUCE_UMAX,
  G_VECREDUCE_UMIN,
  G_SBFX,
  G_UBFX,
  ATOMIC_CMP_SWAP_I128,
  ATOMIC_LOAD_ADD_I128,
  ATOMIC_LOAD_AND_I128,
  ATOMIC_LOAD_NAND_I128,
  ATOMIC_LOAD_OR_I128,
  ATOMIC_LOAD_SUB_I128,
  ATOMIC_LOAD_XOR_I128,
  ATOMIC_SWAP_I128,
  BUILD_QUADWORD,
  BUILD_UACC,
  CFENCE,
  CFENCE8,
  CLRLSLDI,
  CLRLSLDI_rec,
  CLRLSLWI,
  CLRLSLWI_rec,
  CLRRDI,
  CLRRDI_rec,
  CLRRWI,
  CLRRWI_rec,
  DCBFL,
  DCBFLP,
  DCBFPS,
  DCBFx,
  DCBSTPS,
  DCBTCT,
  DCBTDS,
  DCBTSTCT,
  DCBTSTDS,
  DCBTSTT,
  DCBTSTx,
  DCBTT,
  DCBTx,
  DFLOADf32,
  DFLOADf64,
  DFSTOREf32,
  DFSTOREf64,
  EXTLDI,
  EXTLDI_rec,
  EXTLWI,
  EXTLWI_rec,
  EXTRDI,
  EXTRDI_rec,
  EXTRWI,
  EXTRWI_rec,
  INSLWI,
  INSLWI_rec,
  INSRDI,
  INSRDI_rec,
  INSRWI,
  INSRWI_rec,
  KILL_PAIR,
  LAx,
  LIWAX,
  LIWZX,
  PPCLdFixedAddr,
  PSUBI,
  RLWIMIbm,
  RLWIMIbm_rec,
  RLWINMbm,
  RLWINMbm_rec,
  RLWNMbm,
  RLWNMbm_rec,
  ROTRDI,
  ROTRDI_rec,
  ROTRWI,
  ROTRWI_rec,
  SLDI,
  SLDI_rec,
  SLWI,
  SLWI_rec,
  SPILLTOVSR_LD,
  SPILLTOVSR_LDX,
  SPILLTOVSR_ST,
  SPILLTOVSR_STX,
  SRDI,
  SRDI_rec,
  SRWI,
  SRWI_rec,
  STIWX,
  SUBI,
  SUBIC,
  SUBIC_rec,
  SUBIS,
  SUBPCIS,
  XFLOADf32,
  XFLOADf64,
  XFSTOREf32,
  XFSTOREf64,
  ADD4,
  ADD4O,
  ADD4O_rec,
  ADD4TLS,
  ADD4_rec,
  ADD8,
  ADD8O,
  ADD8O_rec,
  ADD8TLS,
  ADD8TLS_,
  ADD8_rec,
  ADDC,
  ADDC8,
  ADDC8O,
  ADDC8O_rec,
  ADDC8_rec,
  ADDCO,
  ADDCO_rec,
  ADDC_rec,
  ADDE,
  ADDE8,
  ADDE8O,
  ADDE8O_rec,
  ADDE8_rec,
  ADDEO,
  ADDEO_rec,
  ADDEX,
  ADDEX8,
  ADDE_rec,
  ADDG6S,
  ADDG6S8,
  ADDI,
  ADDI8,
  ADDIC,
  ADDIC8,
  ADDIC_rec,
  ADDIS,
  ADDIS8,
  ADDISdtprelHA,
  ADDISdtprelHA32,
  ADDISgotTprelHA,
  ADDIStlsgdHA,
  ADDIStlsldHA,
  ADDIStocHA,
  ADDIStocHA8,
  ADDIdtprelL,
  ADDIdtprelL32,
  ADDItlsgdL,
  ADDItlsgdL32,
  ADDItlsgdLADDR,
  ADDItlsgdLADDR32,
  ADDItlsldL,
  ADDItlsldL32,
  ADDItlsldLADDR,
  ADDItlsldLADDR32,
  ADDItoc,
  ADDItoc8,
  ADDItocL,
  ADDItocL8,
  ADDME,
  ADDME8,
  ADDME8O,
  ADDME8O_rec,
  ADDME8_rec,
  ADDMEO,
  ADDMEO_rec,
  ADDME_rec,
  ADDPCIS,
  ADDZE,
  ADDZE8,
  ADDZE8O,
  ADDZE8O_rec,
  ADDZE8_rec,
  ADDZEO,
  ADDZEO_rec,
  ADDZE_rec,
  ADJCALLSTACKDOWN,
  ADJCALLSTACKUP,
  AND,
  AND8,
  AND8_rec,
  ANDC,
  ANDC8,
  ANDC8_rec,
  ANDC_rec,
  ANDI8_rec,
  ANDIS8_rec,
  ANDIS_rec,
  ANDI_rec,
  ANDI_rec_1_EQ_BIT,
  ANDI_rec_1_EQ_BIT8,
  ANDI_rec_1_GT_BIT,
  ANDI_rec_1_GT_BIT8,
  AND_rec,
  ATOMIC_CMP_SWAP_I16,
  ATOMIC_CMP_SWAP_I32,
  ATOMIC_CMP_SWAP_I64,
  ATOMIC_CMP_SWAP_I8,
  ATOMIC_LOAD_ADD_I16,
  ATOMIC_LOAD_ADD_I32,
  ATOMIC_LOAD_ADD_I64,
  ATOMIC_LOAD_ADD_I8,
  ATOMIC_LOAD_AND_I16,
  ATOMIC_LOAD_AND_I32,
  ATOMIC_LOAD_AND_I64,
  ATOMIC_LOAD_AND_I8,
  ATOMIC_LOAD_MAX_I16,
  ATOMIC_LOAD_MAX_I32,
  ATOMIC_LOAD_MAX_I64,
  ATOMIC_LOAD_MAX_I8,
  ATOMIC_LOAD_MIN_I16,
  ATOMIC_LOAD_MIN_I32,
  ATOMIC_LOAD_MIN_I64,
  ATOMIC_LOAD_MIN_I8,
  ATOMIC_LOAD_NAND_I16,
  ATOMIC_LOAD_NAND_I32,
  ATOMIC_LOAD_NAND_I64,
  ATOMIC_LOAD_NAND_I8,
  ATOMIC_LOAD_OR_I16,
  ATOMIC_LOAD_OR_I32,
  ATOMIC_LOAD_OR_I64,
  ATOMIC_LOAD_OR_I8,
  ATOMIC_LOAD_SUB_I16,
  ATOMIC_LOAD_SUB_I32,
  ATOMIC_LOAD_SUB_I64,
  ATOMIC_LOAD_SUB_I8,
  ATOMIC_LOAD_UMAX_I16,
  ATOMIC_LOAD_UMAX_I32,
  ATOMIC_LOAD_UMAX_I64,
  ATOMIC_LOAD_UMAX_I8,
  ATOMIC_LOAD_UMIN_I16,
  ATOMIC_LOAD_UMIN_I32,
  ATOMIC_LOAD_UMIN_I64,
  ATOMIC_LOAD_UMIN_I8,
  ATOMIC_LOAD_XOR_I16,
  ATOMIC_LOAD_XOR_I32,
  ATOMIC_LOAD_XOR_I64,
  ATOMIC_LOAD_XOR_I8,
  ATOMIC_SWAP_I16,
  ATOMIC_SWAP_I32,
  ATOMIC_SWAP_I64,
  ATOMIC_SWAP_I8,
  ATTN,
  B,
  BA,
  BC,
  BCC,
  BCCA,
  BCCCTR,
  BCCCTR8,
  BCCCTRL,
  BCCCTRL8,
  BCCL,
  BCCLA,
  BCCLR,
  BCCLRL,
  BCCTR,
  BCCTR8,
  BCCTR8n,
  BCCTRL,
  BCCTRL8,
  BCCTRL8n,
  BCCTRLn,
  BCCTRn,
  BCDADD_rec,
  BCDCFN_rec,
  BCDCFSQ_rec,
  BCDCFZ_rec,
  BCDCPSGN_rec,
  BCDCTN_rec,
  BCDCTSQ_rec,
  BCDCTZ_rec,
  BCDSETSGN_rec,
  BCDSR_rec,
  BCDSUB_rec,
  BCDS_rec,
  BCDTRUNC_rec,
  BCDUS_rec,
  BCDUTRUNC_rec,
  BCL,
  BCLR,
  BCLRL,
  BCLRLn,
  BCLRn,
  BCLalways,
  BCLn,
  BCTR,
  BCTR8,
  BCTRL,
  BCTRL8,
  BCTRL8_LDinto_toc,
  BCTRL8_LDinto_toc_RM,
  BCTRL8_RM,
  BCTRL_LWZinto_toc,
  BCTRL_LWZinto_toc_RM,
  BCTRL_RM,
  BCn,
  BDNZ,
  BDNZ8,
  BDNZA,
  BDNZAm,
  BDNZAp,
  BDNZL,
  BDNZLA,
  BDNZLAm,
  BDNZLAp,
  BDNZLR,
  BDNZLR8,
  BDNZLRL,
  BDNZLRLm,
  BDNZLRLp,
  BDNZLRm,
  BDNZLRp,
  BDNZLm,
  BDNZLp,
  BDNZm,
  BDNZp,
  BDZ,
  BDZ8,
  BDZA,
  BDZAm,
  BDZAp,
  BDZL,
  BDZLA,
  BDZLAm,
  BDZLAp,
  BDZLR,
  BDZLR8,
  BDZLRL,
  BDZLRLm,
  BDZLRLp,
  BDZLRm,
  BDZLRp,
  BDZLm,
  BDZLp,
  BDZm,
  BDZp,
  BL,
  BL8,
  BL8_NOP,
  BL8_NOP_RM,
  BL8_NOP_TLS,
  BL8_NOTOC,
  BL8_NOTOC_RM,
  BL8_NOTOC_TLS,
  BL8_RM,
  BL8_TLS,
  BL8_TLS_,
  BLA,
  BLA8,
  BLA8_NOP,
  BLA8_NOP_RM,
  BLA8_RM,
  BLA_RM,
  BLR,
  BLR8,
  BLRL,
  BL_NOP,
  BL_NOP_RM,
  BL_RM,
  BL_TLS,
  BPERMD,
  BRD,
  BRH,
  BRH8,
  BRINC,
  BRW,
  BRW8,
  CBCDTD,
  CBCDTD8,
  CDTBCD,
  CDTBCD8,
  CFUGED,
  CLRBHRB,
  CMPB,
  CMPB8,
  CMPD,
  CMPDI,
  CMPEQB,
  CMPLD,
  CMPLDI,
  CMPLW,
  CMPLWI,
  CMPRB,
  CMPRB8,
  CMPW,
  CMPWI,
  CNTLZD,
  CNTLZDM,
  CNTLZD_rec,
  CNTLZW,
  CNTLZW8,
  CNTLZW8_rec,
  CNTLZW_rec,
  CNTTZD,
  CNTTZDM,
  CNTTZD_rec,
  CNTTZW,
  CNTTZW8,
  CNTTZW8_rec,
  CNTTZW_rec,
  CP_ABORT,
  CP_COPY,
  CP_COPY8,
  CP_PASTE8_rec,
  CP_PASTE_rec,
  CR6SET,
  CR6UNSET,
  CRAND,
  CRANDC,
  CREQV,
  CRNAND,
  CRNOR,
  CRNOT,
  CROR,
  CRORC,
  CRSET,
  CRUNSET,
  CRXOR,
  CTRL_DEP,
  DADD,
  DADDQ,
  DADDQ_rec,
  DADD_rec,
  DARN,
  DCBA,
  DCBF,
  DCBFEP,
  DCBI,
  DCBST,
  DCBSTEP,
  DCBT,
  DCBTEP,
  DCBTST,
  DCBTSTEP,
  DCBZ,
  DCBZEP,
  DCBZL,
  DCBZLEP,
  DCCCI,
  DCFFIX,
  DCFFIXQ,
  DCFFIXQQ,
  DCFFIXQ_rec,
  DCFFIX_rec,
  DCMPO,
  DCMPOQ,
  DCMPU,
  DCMPUQ,
  DCTDP,
  DCTDP_rec,
  DCTFIX,
  DCTFIXQ,
  DCTFIXQQ,
  DCTFIXQ_rec,
  DCTFIX_rec,
  DCTQPQ,
  DCTQPQ_rec,
  DDEDPD,
  DDEDPDQ,
  DDEDPDQ_rec,
  DDEDPD_rec,
  DDIV,
  DDIVQ,
  DDIVQ_rec,
  DDIV_rec,
  DENBCD,
  DENBCDQ,
  DENBCDQ_rec,
  DENBCD_rec,
  DIEX,
  DIEXQ,
  DIEXQ_rec,
  DIEX_rec,
  DIVD,
  DIVDE,
  DIVDEO,
  DIVDEO_rec,
  DIVDEU,
  DIVDEUO,
  DIVDEUO_rec,
  DIVDEU_rec,
  DIVDE_rec,
  DIVDO,
  DIVDO_rec,
  DIVDU,
  DIVDUO,
  DIVDUO_rec,
  DIVDU_rec,
  DIVD_rec,
  DIVW,
  DIVWE,
  DIVWEO,
  DIVWEO_rec,
  DIVWEU,
  DIVWEUO,
  DIVWEUO_rec,
  DIVWEU_rec,
  DIVWE_rec,
  DIVWO,
  DIVWO_rec,
  DIVWU,
  DIVWUO,
  DIVWUO_rec,
  DIVWU_rec,
  DIVW_rec,
  DMMR,
  DMSETDMRZ,
  DMUL,
  DMULQ,
  DMULQ_rec,
  DMUL_rec,
  DMXOR,
  DMXXEXTFDMR256,
  DMXXEXTFDMR512,
  DMXXEXTFDMR512_HI,
  DMXXINSTFDMR256,
  DMXXINSTFDMR512,
  DMXXINSTFDMR512_HI,
  DQUA,
  DQUAI,
  DQUAIQ,
  DQUAIQ_rec,
  DQUAI_rec,
  DQUAQ,
  DQUAQ_rec,
  DQUA_rec,
  DRDPQ,
  DRDPQ_rec,
  DRINTN,
  DRINTNQ,
  DRINTNQ_rec,
  DRINTN_rec,
  DRINTX,
  DRINTXQ,
  DRINTXQ_rec,
  DRINTX_rec,
  DRRND,
  DRRNDQ,
  DRRNDQ_rec,
  DRRND_rec,
  DRSP,
  DRSP_rec,
  DSCLI,
  DSCLIQ,
  DSCLIQ_rec,
  DSCLI_rec,
  DSCRI,
  DSCRIQ,
  DSCRIQ_rec,
  DSCRI_rec,
  DSS,
  DSSALL,
  DST,
  DST64,
  DSTST,
  DSTST64,
  DSTSTT,
  DSTSTT64,
  DSTT,
  DSTT64,
  DSUB,
  DSUBQ,
  DSUBQ_rec,
  DSUB_rec,
  DTSTDC,
  DTSTDCQ,
  DTSTDG,
  DTSTDGQ,
  DTSTEX,
  DTSTEXQ,
  DTSTSF,
  DTSTSFI,
  DTSTSFIQ,
  DTSTSFQ,
  DXEX,
  DXEXQ,
  DXEXQ_rec,
  DXEX_rec,
  DYNALLOC,
  DYNALLOC8,
  DYNAREAOFFSET,
  DYNAREAOFFSET8,
  DecreaseCTR8loop,
  DecreaseCTRloop,
  EFDABS,
  EFDADD,
  EFDCFS,
  EFDCFSF,
  EFDCFSI,
  EFDCFSID,
  EFDCFUF,
  EFDCFUI,
  EFDCFUID,
  EFDCMPEQ,
  EFDCMPGT,
  EFDCMPLT,
  EFDCTSF,
  EFDCTSI,
  EFDCTSIDZ,
  EFDCTSIZ,
  EFDCTUF,
  EFDCTUI,
  EFDCTUIDZ,
  EFDCTUIZ,
  EFDDIV,
  EFDMUL,
  EFDNABS,
  EFDNEG,
  EFDSUB,
  EFDTSTEQ,
  EFDTSTGT,
  EFDTSTLT,
  EFSABS,
  EFSADD,
  EFSCFD,
  EFSCFSF,
  EFSCFSI,
  EFSCFUF,
  EFSCFUI,
  EFSCMPEQ,
  EFSCMPGT,
  EFSCMPLT,
  EFSCTSF,
  EFSCTSI,
  EFSCTSIZ,
  EFSCTUF,
  EFSCTUI,
  EFSCTUIZ,
  EFSDIV,
  EFSMUL,
  EFSNABS,
  EFSNEG,
  EFSSUB,
  EFSTSTEQ,
  EFSTSTGT,
  EFSTSTLT,
  EH_SjLj_LongJmp32,
  EH_SjLj_LongJmp64,
  EH_SjLj_SetJmp32,
  EH_SjLj_SetJmp64,
  EH_SjLj_Setup,
  EQV,
  EQV8,
  EQV8_rec,
  EQV_rec,
  EVABS,
  EVADDIW,
  EVADDSMIAAW,
  EVADDSSIAAW,
  EVADDUMIAAW,
  EVADDUSIAAW,
  EVADDW,
  EVAND,
  EVANDC,
  EVCMPEQ,
  EVCMPGTS,
  EVCMPGTU,
  EVCMPLTS,
  EVCMPLTU,
  EVCNTLSW,
  EVCNTLZW,
  EVDIVWS,
  EVDIVWU,
  EVEQV,
  EVEXTSB,
  EVEXTSH,
  EVFSABS,
  EVFSADD,
  EVFSCFSF,
  EVFSCFSI,
  EVFSCFUF,
  EVFSCFUI,
  EVFSCMPEQ,
  EVFSCMPGT,
  EVFSCMPLT,
  EVFSCTSF,
  EVFSCTSI,
  EVFSCTSIZ,
  EVFSCTUF,
  EVFSCTUI,
  EVFSCTUIZ,
  EVFSDIV,
  EVFSMUL,
  EVFSNABS,
  EVFSNEG,
  EVFSSUB,
  EVFSTSTEQ,
  EVFSTSTGT,
  EVFSTSTLT,
  EVLDD,
  EVLDDX,
  EVLDH,
  EVLDHX,
  EVLDW,
  EVLDWX,
  EVLHHESPLAT,
  EVLHHESPLATX,
  EVLHHOSSPLAT,
  EVLHHOSSPLATX,
  EVLHHOUSPLAT,
  EVLHHOUSPLATX,
  EVLWHE,
  EVLWHEX,
  EVLWHOS,
  EVLWHOSX,
  EVLWHOU,
  EVLWHOUX,
  EVLWHSPLAT,
  EVLWHSPLATX,
  EVLWWSPLAT,
  EVLWWSPLATX,
  EVMERGEHI,
  EVMERGEHILO,
  EVMERGELO,
  EVMERGELOHI,
  EVMHEGSMFAA,
  EVMHEGSMFAN,
  EVMHEGSMIAA,
  EVMHEGSMIAN,
  EVMHEGUMIAA,
  EVMHEGUMIAN,
  EVMHESMF,
  EVMHESMFA,
  EVMHESMFAAW,
  EVMHESMFANW,
  EVMHESMI,
  EVMHESMIA,
  EVMHESMIAAW,
  EVMHESMIANW,
  EVMHESSF,
  EVMHESSFA,
  EVMHESSFAAW,
  EVMHESSFANW,
  EVMHESSIAAW,
  EVMHESSIANW,
  EVMHEUMI,
  EVMHEUMIA,
  EVMHEUMIAAW,
  EVMHEUMIANW,
  EVMHEUSIAAW,
  EVMHEUSIANW,
  EVMHOGSMFAA,
  EVMHOGSMFAN,
  EVMHOGSMIAA,
  EVMHOGSMIAN,
  EVMHOGUMIAA,
  EVMHOGUMIAN,
  EVMHOSMF,
  EVMHOSMFA,
  EVMHOSMFAAW,
  EVMHOSMFANW,
  EVMHOSMI,
  EVMHOSMIA,
  EVMHOSMIAAW,
  EVMHOSMIANW,
  EVMHOSSF,
  EVMHOSSFA,
  EVMHOSSFAAW,
  EVMHOSSFANW,
  EVMHOSSIAAW,
  EVMHOSSIANW,
  EVMHOUMI,
  EVMHOUMIA,
  EVMHOUMIAAW,
  EVMHOUMIANW,
  EVMHOUSIAAW,
  EVMHOUSIANW,
  EVMRA,
  EVMWHSMF,
  EVMWHSMFA,
  EVMWHSMI,
  EVMWHSMIA,
  EVMWHSSF,
  EVMWHSSFA,
  EVMWHUMI,
  EVMWHUMIA,
  EVMWLSMIAAW,
  EVMWLSMIANW,
  EVMWLSSIAAW,
  EVMWLSSIANW,
  EVMWLUMI,
  EVMWLUMIA,
  EVMWLUMIAAW,
  EVMWLUMIANW,
  EVMWLUSIAAW,
  EVMWLUSIANW,
  EVMWSMF,
  EVMWSMFA,
  EVMWSMFAA,
  EVMWSMFAN,
  EVMWSMI,
  EVMWSMIA,
  EVMWSMIAA,
  EVMWSMIAN,
  EVMWSSF,
  EVMWSSFA,
  EVMWSSFAA,
  EVMWSSFAN,
  EVMWUMI,
  EVMWUMIA,
  EVMWUMIAA,
  EVMWUMIAN,
  EVNAND,
  EVNEG,
  EVNOR,
  EVOR,
  EVORC,
  EVRLW,
  EVRLWI,
  EVRNDW,
  EVSEL,
  EVSLW,
  EVSLWI,
  EVSPLATFI,
  EVSPLATI,
  EVSRWIS,
  EVSRWIU,
  EVSRWS,
  EVSRWU,
  EVSTDD,
  EVSTDDX,
  EVSTDH,
  EVSTDHX,
  EVSTDW,
  EVSTDWX,
  EVSTWHE,
  EVSTWHEX,
  EVSTWHO,
  EVSTWHOX,
  EVSTWWE,
  EVSTWWEX,
  EVSTWWO,
  EVSTWWOX,
  EVSUBFSMIAAW,
  EVSUBFSSIAAW,
  EVSUBFUMIAAW,
  EVSUBFUSIAAW,
  EVSUBFW,
  EVSUBIFW,
  EVXOR,
  EXTSB,
  EXTSB8,
  EXTSB8_32_64,
  EXTSB8_rec,
  EXTSB_rec,
  EXTSH,
  EXTSH8,
  EXTSH8_32_64,
  EXTSH8_rec,
  EXTSH_rec,
  EXTSW,
  EXTSWSLI,
  EXTSWSLI_32_64,
  EXTSWSLI_32_64_rec,
  EXTSWSLI_rec,
  EXTSW_32,
  EXTSW_32_64,
  EXTSW_32_64_rec,
  EXTSW_rec,
  EnforceIEIO,
  FABSD,
  FABSD_rec,
  FABSS,
  FABSS_rec,
  FADD,
  FADDS,
  FADDS_rec,
  FADD_rec,
  FADDrtz,
  FCFID,
  FCFIDS,
  FCFIDS_rec,
  FCFIDU,
  FCFIDUS,
  FCFIDUS_rec,
  FCFIDU_rec,
  FCFID_rec,
  FCMPOD,
  FCMPOS,
  FCMPUD,
  FCMPUS,
  FCPSGND,
  FCPSGND_rec,
  FCPSGNS,
  FCPSGNS_rec,
  FCTID,
  FCTIDU,
  FCTIDUZ,
  FCTIDUZ_rec,
  FCTIDU_rec,
  FCTIDZ,
  FCTIDZ_rec,
  FCTID_rec,
  FCTIW,
  FCTIWU,
  FCTIWUZ,
  FCTIWUZ_rec,
  FCTIWU_rec,
  FCTIWZ,
  FCTIWZ_rec,
  FCTIW_rec,
  FDIV,
  FDIVS,
  FDIVS_rec,
  FDIV_rec,
  FENCE,
  FMADD,
  FMADDS,
  FMADDS_rec,
  FMADD_rec,
  FMR,
  FMR_rec,
  FMSUB,
  FMSUBS,
  FMSUBS_rec,
  FMSUB_rec,
  FMUL,
  FMULS,
  FMULS_rec,
  FMUL_rec,
  FNABSD,
  FNABSD_rec,
  FNABSS,
  FNABSS_rec,
  FNEGD,
  FNEGD_rec,
  FNEGS,
  FNEGS_rec,
  FNMADD,
  FNMADDS,
  FNMADDS_rec,
  FNMADD_rec,
  FNMSUB,
  FNMSUBS,
  FNMSUBS_rec,
  FNMSUB_rec,
  FRE,
  FRES,
  FRES_rec,
  FRE_rec,
  FRIMD,
  FRIMD_rec,
  FRIMS,
  FRIMS_rec,
  FRIND,
  FRIND_rec,
  FRINS,
  FRINS_rec,
  FRIPD,
  FRIPD_rec,
  FRIPS,
  FRIPS_rec,
  FRIZD,
  FRIZD_rec,
  FRIZS,
  FRIZS_rec,
  FRSP,
  FRSP_rec,
  FRSQRTE,
  FRSQRTES,
  FRSQRTES_rec,
  FRSQRTE_rec,
  FSELD,
  FSELD_rec,
  FSELS,
  FSELS_rec,
  FSQRT,
  FSQRTS,
  FSQRTS_rec,
  FSQRT_rec,
  FSUB,
  FSUBS,
  FSUBS_rec,
  FSUB_rec,
  FTDIV,
  FTSQRT,
  GETtlsADDR,
  GETtlsADDR32,
  GETtlsADDR32AIX,
  GETtlsADDR64AIX,
  GETtlsADDRPCREL,
  GETtlsMOD32AIX,
  GETtlsMOD64AIX,
  GETtlsTpointer32AIX,
  GETtlsldADDR,
  GETtlsldADDR32,
  GETtlsldADDRPCREL,
  HASHCHK,
  HASHCHK8,
  HASHCHKP,
  HASHCHKP8,
  HASHST,
  HASHST8,
  HASHSTP,
  HASHSTP8,
  HRFID,
  ICBI,
  ICBIEP,
  ICBLC,
  ICBLQ,
  ICBT,
  ICBTLS,
  ICCCI,
  ISEL,
  ISEL8,
  ISYNC,
  LA,
  LA8,
  LBARX,
  LBARXL,
  LBEPX,
  LBZ,
  LBZ8,
  LBZCIX,
  LBZU,
  LBZU8,
  LBZUX,
  LBZUX8,
  LBZX,
  LBZX8,
  LBZXTLS,
  LBZXTLS_,
  LBZXTLS_32,
  LD,
  LDARX,
  LDARXL,
  LDAT,
  LDBRX,
  LDCIX,
  LDU,
  LDUX,
  LDX,
  LDXTLS,
  LDXTLS_,
  LDgotTprelL,
  LDgotTprelL32,
  LDtoc,
  LDtocBA,
  LDtocCPT,
  LDtocJTI,
  LDtocL,
  LFD,
  LFDEPX,
  LFDU,
  LFDUX,
  LFDX,
  LFDXTLS,
  LFDXTLS_,
  LFIWAX,
  LFIWZX,
  LFS,
  LFSU,
  LFSUX,
  LFSX,
  LFSXTLS,
  LFSXTLS_,
  LHA,
  LHA8,
  LHARX,
  LHARXL,
  LHAU,
  LHAU8,
  LHAUX,
  LHAUX8,
  LHAX,
  LHAX8,
  LHAXTLS,
  LHAXTLS_,
  LHAXTLS_32,
  LHBRX,
  LHBRX8,
  LHEPX,
  LHZ,
  LHZ8,
  LHZCIX,
  LHZU,
  LHZU8,
  LHZUX,
  LHZUX8,
  LHZX,
  LHZX8,
  LHZXTLS,
  LHZXTLS_,
  LHZXTLS_32,
  LI,
  LI8,
  LIS,
  LIS8,
  LMW,
  LQ,
  LQARX,
  LQARXL,
  LQX_PSEUDO,
  LSWI,
  LVEBX,
  LVEHX,
  LVEWX,
  LVSL,
  LVSR,
  LVX,
  LVXL,
  LWA,
  LWARX,
  LWARXL,
  LWAT,
  LWAUX,
  LWAX,
  LWAXTLS,
  LWAXTLS_,
  LWAXTLS_32,
  LWAX_32,
  LWA_32,
  LWBRX,
  LWBRX8,
  LWEPX,
  LWZ,
  LWZ8,
  LWZCIX,
  LWZU,
  LWZU8,
  LWZUX,
  LWZUX8,
  LWZX,
  LWZX8,
  LWZXTLS,
  LWZXTLS_,
  LWZXTLS_32,
  LWZtoc,
  LWZtocL,
  LXSD,
  LXSDX,
  LXSIBZX,
  LXSIHZX,
  LXSIWAX,
  LXSIWZX,
  LXSSP,
  LXSSPX,
  LXV,
  LXVB16X,
  LXVD2X,
  LXVDSX,
  LXVH8X,
  LXVKQ,
  LXVL,
  LXVLL,
  LXVP,
  LXVPRL,
  LXVPRLL,
  LXVPX,
  LXVRBX,
  LXVRDX,
  LXVRHX,
  LXVRL,
  LXVRLL,
  LXVRWX,
  LXVW4X,
  LXVWSX,
  LXVX,
  MADDHD,
  MADDHDU,
  MADDLD,
  MADDLD8,
  MBAR,
  MCRF,
  MCRFS,
  MCRXRX,
  MFBHRBE,
  MFCR,
  MFCR8,
  MFCTR,
  MFCTR8,
  MFDCR,
  MFFS,
  MFFSCDRN,
  MFFSCDRNI,
  MFFSCE,
  MFFSCRN,
  MFFSCRNI,
  MFFSL,
  MFFS_rec,
  MFLR,
  MFLR8,
  MFMSR,
  MFOCRF,
  MFOCRF8,
  MFPMR,
  MFSPR,
  MFSPR8,
  MFSR,
  MFSRIN,
  MFTB,
  MFTB8,
  MFUDSCR,
  MFVRD,
  MFVRSAVE,
  MFVRSAVEv,
  MFVRWZ,
  MFVSCR,
  MFVSRD,
  MFVSRLD,
  MFVSRWZ,
  MODSD,
  MODSW,
  MODUD,
  MODUW,
  MSGSYNC,
  MSYNC,
  MTCRF,
  MTCRF8,
  MTCTR,
  MTCTR8,
  MTCTR8loop,
  MTCTRloop,
  MTDCR,
  MTFSB0,
  MTFSB1,
  MTFSF,
  MTFSFI,
  MTFSFI_rec,
  MTFSFIb,
  MTFSF_rec,
  MTFSFb,
  MTLR,
  MTLR8,
  MTMSR,
  MTMSRD,
  MTOCRF,
  MTOCRF8,
  MTPMR,
  MTSPR,
  MTSPR8,
  MTSR,
  MTSRIN,
  MTUDSCR,
  MTVRD,
  MTVRSAVE,
  MTVRSAVEv,
  MTVRWA,
  MTVRWZ,
  MTVSCR,
  MTVSRBM,
  MTVSRBMI,
  MTVSRD,
  MTVSRDD,
  MTVSRDM,
  MTVSRHM,
  MTVSRQM,
  MTVSRWA,
  MTVSRWM,
  MTVSRWS,
  MTVSRWZ,
  MULHD,
  MULHDU,
  MULHDU_rec,
  MULHD_rec,
  MULHW,
  MULHWU,
  MULHWU_rec,
  MULHW_rec,
  MULLD,
  MULLDO,
  MULLDO_rec,
  MULLD_rec,
  MULLI,
  MULLI8,
  MULLW,
  MULLWO,
  MULLWO_rec,
  MULLW_rec,
  MoveGOTtoLR,
  MovePCtoLR,
  MovePCtoLR8,
  NAND,
  NAND8,
  NAND8_rec,
  NAND_rec,
  NAP,
  NEG,
  NEG8,
  NEG8O,
  NEG8O_rec,
  NEG8_rec,
  NEGO,
  NEGO_rec,
  NEG_rec,
  NOP,
  NOP_GT_PWR6,
  NOP_GT_PWR7,
  NOR,
  NOR8,
  NOR8_rec,
  NOR_rec,
  OR,
  OR8,
  OR8_rec,
  ORC,
  ORC8,
  ORC8_rec,
  ORC_rec,
  ORI,
  ORI8,
  ORIS,
  ORIS8,
  OR_rec,
  PADDI,
  PADDI8,
  PADDI8pc,
  PADDIdtprel,
  PADDIpc,
  PDEPD,
  PEXTD,
  PLA,
  PLA8,
  PLA8pc,
  PLApc,
  PLBZ,
  PLBZ8,
  PLBZ8nopc,
  PLBZ8onlypc,
  PLBZ8pc,
  PLBZnopc,
  PLBZonlypc,
  PLBZpc,
  PLD,
  PLDnopc,
  PLDonlypc,
  PLDpc,
  PLFD,
  PLFDnopc,
  PLFDonlypc,
  PLFDpc,
  PLFS,
  PLFSnopc,
  PLFSonlypc,
  PLFSpc,
  PLHA,
  PLHA8,
  PLHA8nopc,
  PLHA8onlypc,
  PLHA8pc,
  PLHAnopc,
  PLHAonlypc,
  PLHApc,
  PLHZ,
  PLHZ8,
  PLHZ8nopc,
  PLHZ8onlypc,
  PLHZ8pc,
  PLHZnopc,
  PLHZonlypc,
  PLHZpc,
  PLI,
  PLI8,
  PLWA,
  PLWA8,
  PLWA8nopc,
  PLWA8onlypc,
  PLWA8pc,
  PLWAnopc,
  PLWAonlypc,
  PLWApc,
  PLWZ,
  PLWZ8,
  PLWZ8nopc,
  PLWZ8onlypc,
  PLWZ8pc,
  PLWZnopc,
  PLWZonlypc,
  PLWZpc,
  PLXSD,
  PLXSDnopc,
  PLXSDonlypc,
  PLXSDpc,
  PLXSSP,
  PLXSSPnopc,
  PLXSSPonlypc,
  PLXSSPpc,
  PLXV,
  PLXVP,
  PLXVPnopc,
  PLXVPonlypc,
  PLXVPpc,
  PLXVnopc,
  PLXVonlypc,
  PLXVpc,
  PMXVBF16GER2,
  PMXVBF16GER2NN,
  PMXVBF16GER2NP,
  PMXVBF16GER2PN,
  PMXVBF16GER2PP,
  PMXVBF16GER2W,
  PMXVBF16GER2WNN,
  PMXVBF16GER2WNP,
  PMXVBF16GER2WPN,
  PMXVBF16GER2WPP,
  PMXVF16GER2,
  PMXVF16GER2NN,
  PMXVF16GER2NP,
  PMXVF16GER2PN,
  PMXVF16GER2PP,
  PMXVF16GER2W,
  PMXVF16GER2WNN,
  PMXVF16GER2WNP,
  PMXVF16GER2WPN,
  PMXVF16GER2WPP,
  PMXVF32GER,
  PMXVF32GERNN,
  PMXVF32GERNP,
  PMXVF32GERPN,
  PMXVF32GERPP,
  PMXVF32GERW,
  PMXVF32GERWNN,
  PMXVF32GERWNP,
  PMXVF32GERWPN,
  PMXVF32GERWPP,
  PMXVF64GER,
  PMXVF64GERNN,
  PMXVF64GERNP,
  PMXVF64GERPN,
  PMXVF64GERPP,
  PMXVF64GERW,
  PMXVF64GERWNN,
  PMXVF64GERWNP,
  PMXVF64GERWPN,
  PMXVF64GERWPP,
  PMXVI16GER2,
  PMXVI16GER2PP,
  PMXVI16GER2S,
  PMXVI16GER2SPP,
  PMXVI16GER2SW,
  PMXVI16GER2SWPP,
  PMXVI16GER2W,
  PMXVI16GER2WPP,
  PMXVI4GER8,
  PMXVI4GER8PP,
  PMXVI4GER8W,
  PMXVI4GER8WPP,
  PMXVI8GER4,
  PMXVI8GER4PP,
  PMXVI8GER4SPP,
  PMXVI8GER4W,
  PMXVI8GER4WPP,
  PMXVI8GER4WSPP,
  POPCNTB,
  POPCNTB8,
  POPCNTD,
  POPCNTW,
  PPC32GOT,
  PPC32PICGOT,
  PREPARE_PROBED_ALLOCA_32,
  PREPARE_PROBED_ALLOCA_64,
  PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_32,
  PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_64,
  PROBED_ALLOCA_32,
  PROBED_ALLOCA_64,
  PROBED_STACKALLOC_32,
  PROBED_STACKALLOC_64,
  PSTB,
  PSTB8,
  PSTB8nopc,
  PSTB8onlypc,
  PSTB8pc,
  PSTBnopc,
  PSTBonlypc,
  PSTBpc,
  PSTD,
  PSTDnopc,
  PSTDonlypc,
  PSTDpc,
  PSTFD,
  PSTFDnopc,
  PSTFDonlypc,
  PSTFDpc,
  PSTFS,
  PSTFSnopc,
  PSTFSonlypc,
  PSTFSpc,
  PSTH,
  PSTH8,
  PSTH8nopc,
  PSTH8onlypc,
  PSTH8pc,
  PSTHnopc,
  PSTHonlypc,
  PSTHpc,
  PSTW,
  PSTW8,
  PSTW8nopc,
  PSTW8onlypc,
  PSTW8pc,
  PSTWnopc,
  PSTWonlypc,
  PSTWpc,
  PSTXSD,
  PSTXSDnopc,
  PSTXSDonlypc,
  PSTXSDpc,
  PSTXSSP,
  PSTXSSPnopc,
  PSTXSSPonlypc,
  PSTXSSPpc,
  PSTXV,
  PSTXVP,
  PSTXVPnopc,
  PSTXVPonlypc,
  PSTXVPpc,
  PSTXVnopc,
  PSTXVonlypc,
  PSTXVpc,
  PseudoEIEIO,
  RESTORE_ACC,
  RESTORE_CR,
  RESTORE_CRBIT,
  RESTORE_QUADWORD,
  RESTORE_UACC,
  RESTORE_WACC,
  RFCI,
  RFDI,
  RFEBB,
  RFI,
  RFID,
  RFMCI,
  RLDCL,
  RLDCL_rec,
  RLDCR,
  RLDCR_rec,
  RLDIC,
  RLDICL,
  RLDICL_32,
  RLDICL_32_64,
  RLDICL_32_rec,
  RLDICL_rec,
  RLDICR,
  RLDICR_32,
  RLDICR_rec,
  RLDIC_rec,
  RLDIMI,
  RLDIMI_rec,
  RLWIMI,
  RLWIMI8,
  RLWIMI8_rec,
  RLWIMI_rec,
  RLWINM,
  RLWINM8,
  RLWINM8_rec,
  RLWINM_rec,
  RLWNM,
  RLWNM8,
  RLWNM8_rec,
  RLWNM_rec,
  ReadTB,
  SC,
  SCV,
  SELECT_CC_F16,
  SELECT_CC_F4,
  SELECT_CC_F8,
  SELECT_CC_I4,
  SELECT_CC_I8,
  SELECT_CC_SPE,
  SELECT_CC_SPE4,
  SELECT_CC_VRRC,
  SELECT_CC_VSFRC,
  SELECT_CC_VSRC,
  SELECT_CC_VSSRC,
  SELECT_F16,
  SELECT_F4,
  SELECT_F8,
  SELECT_I4,
  SELECT_I8,
  SELECT_SPE,
  SELECT_SPE4,
  SELECT_VRRC,
  SELECT_VSFRC,
  SELECT_VSRC,
  SELECT_VSSRC,
  SETB,
  SETB8,
  SETBC,
  SETBC8,
  SETBCR,
  SETBCR8,
  SETFLM,
  SETNBC,
  SETNBC8,
  SETNBCR,
  SETNBCR8,
  SETRND,
  SETRNDi,
  SLBFEE_rec,
  SLBIA,
  SLBIE,
  SLBIEG,
  SLBMFEE,
  SLBMFEV,
  SLBMTE,
  SLBSYNC,
  SLD,
  SLD_rec,
  SLW,
  SLW8,
  SLW8_rec,
  SLW_rec,
  SPELWZ,
  SPELWZX,
  SPESTW,
  SPESTWX,
  SPILL_ACC,
  SPILL_CR,
  SPILL_CRBIT,
  SPILL_QUADWORD,
  SPILL_UACC,
  SPILL_WACC,
  SPLIT_QUADWORD,
  SRAD,
  SRADI,
  SRADI_32,
  SRADI_rec,
  SRAD_rec,
  SRAW,
  SRAW8,
  SRAW8_rec,
  SRAWI,
  SRAWI8,
  SRAWI8_rec,
  SRAWI_rec,
  SRAW_rec,
  SRD,
  SRD_rec,
  SRW,
  SRW8,
  SRW8_rec,
  SRW_rec,
  STB,
  STB8,
  STBCIX,
  STBCX,
  STBEPX,
  STBU,
  STBU8,
  STBUX,
  STBUX8,
  STBX,
  STBX8,
  STBXTLS,
  STBXTLS_,
  STBXTLS_32,
  STD,
  STDAT,
  STDBRX,
  STDCIX,
  STDCX,
  STDU,
  STDUX,
  STDX,
  STDXTLS,
  STDXTLS_,
  STFD,
  STFDEPX,
  STFDU,
  STFDUX,
  STFDX,
  STFDXTLS,
  STFDXTLS_,
  STFIWX,
  STFS,
  STFSU,
  STFSUX,
  STFSX,
  STFSXTLS,
  STFSXTLS_,
  STH,
  STH8,
  STHBRX,
  STHCIX,
  STHCX,
  STHEPX,
  STHU,
  STHU8,
  STHUX,
  STHUX8,
  STHX,
  STHX8,
  STHXTLS,
  STHXTLS_,
  STHXTLS_32,
  STMW,
  STOP,
  STQ,
  STQCX,
  STQX_PSEUDO,
  STSWI,
  STVEBX,
  STVEHX,
  STVEWX,
  STVX,
  STVXL,
  STW,
  STW8,
  STWAT,
  STWBRX,
  STWCIX,
  STWCX,
  STWEPX,
  STWU,
  STWU8,
  STWUX,
  STWUX8,
  STWX,
  STWX8,
  STWXTLS,
  STWXTLS_,
  STWXTLS_32,
  STXSD,
  STXSDX,
  STXSIBX,
  STXSIBXv,
  STXSIHX,
  STXSIHXv,
  STXSIWX,
  STXSSP,
  STXSSPX,
  STXV,
  STXVB16X,
  STXVD2X,
  STXVH8X,
  STXVL,
  STXVLL,
  STXVP,
  STXVPRL,
  STXVPRLL,
  STXVPX,
  STXVRBX,
  STXVRDX,
  STXVRHX,
  STXVRL,
  STXVRLL,
  STXVRWX,
  STXVW4X,
  STXVX,
  SUBF,
  SUBF8,
  SUBF8O,
  SUBF8O_rec,
  SUBF8_rec,
  SUBFC,
  SUBFC8,
  SUBFC8O,
  SUBFC8O_rec,
  SUBFC8_rec,
  SUBFCO,
  SUBFCO_rec,
  SUBFC_rec,
  SUBFE,
  SUBFE8,
  SUBFE8O,
  SUBFE8O_rec,
  SUBFE8_rec,
  SUBFEO,
  SUBFEO_rec,
  SUBFE_rec,
  SUBFIC,
  SUBFIC8,
  SUBFME,
  SUBFME8,
  SUBFME8O,
  SUBFME8O_rec,
  SUBFME8_rec,
  SUBFMEO,
  SUBFMEO_rec,
  SUBFME_rec,
  SUBFO,
  SUBFO_rec,
  SUBFUS,
  SUBFUS_rec,
  SUBFZE,
  SUBFZE8,
  SUBFZE8O,
  SUBFZE8O_rec,
  SUBFZE8_rec,
  SUBFZEO,
  SUBFZEO_rec,
  SUBFZE_rec,
  SUBF_rec,
  SYNC,
  SYNCP10,
  TABORT,
  TABORTDC,
  TABORTDCI,
  TABORTWC,
  TABORTWCI,
  TAILB,
  TAILB8,
  TAILBA,
  TAILBA8,
  TAILBCTR,
  TAILBCTR8,
  TBEGIN,
  TBEGIN_RET,
  TCHECK,
  TCHECK_RET,
  TCRETURNai,
  TCRETURNai8,
  TCRETURNdi,
  TCRETURNdi8,
  TCRETURNri,
  TCRETURNri8,
  TD,
  TDI,
  TEND,
  TLBIA,
  TLBIE,
  TLBIEL,
  TLBILX,
  TLBIVAX,
  TLBLD,
  TLBLI,
  TLBRE,
  TLBRE2,
  TLBSX,
  TLBSX2,
  TLBSX2D,
  TLBSYNC,
  TLBWE,
  TLBWE2,
  TLSGDAIX,
  TLSGDAIX8,
  TLSLDAIX,
  TLSLDAIX8,
  TRAP,
  TRECHKPT,
  TRECLAIM,
  TSR,
  TW,
  TWI,
  UNENCODED_NOP,
  UpdateGBR,
  VABSDUB,
  VABSDUH,
  VABSDUW,
  VADDCUQ,
  VADDCUW,
  VADDECUQ,
  VADDEUQM,
  VADDFP,
  VADDSBS,
  VADDSHS,
  VADDSWS,
  VADDUBM,
  VADDUBS,
  VADDUDM,
  VADDUHM,
  VADDUHS,
  VADDUQM,
  VADDUWM,
  VADDUWS,
  VAND,
  VANDC,
  VAVGSB,
  VAVGSH,
  VAVGSW,
  VAVGUB,
  VAVGUH,
  VAVGUW,
  VBPERMD,
  VBPERMQ,
  VCFSX,
  VCFSX_0,
  VCFUGED,
  VCFUX,
  VCFUX_0,
  VCIPHER,
  VCIPHERLAST,
  VCLRLB,
  VCLRRB,
  VCLZB,
  VCLZD,
  VCLZDM,
  VCLZH,
  VCLZLSBB,
  VCLZW,
  VCMPBFP,
  VCMPBFP_rec,
  VCMPEQFP,
  VCMPEQFP_rec,
  VCMPEQUB,
  VCMPEQUB_rec,
  VCMPEQUD,
  VCMPEQUD_rec,
  VCMPEQUH,
  VCMPEQUH_rec,
  VCMPEQUQ,
  VCMPEQUQ_rec,
  VCMPEQUW,
  VCMPEQUW_rec,
  VCMPGEFP,
  VCMPGEFP_rec,
  VCMPGTFP,
  VCMPGTFP_rec,
  VCMPGTSB,
  VCMPGTSB_rec,
  VCMPGTSD,
  VCMPGTSD_rec,
  VCMPGTSH,
  VCMPGTSH_rec,
  VCMPGTSQ,
  VCMPGTSQ_rec,
  VCMPGTSW,
  VCMPGTSW_rec,
  VCMPGTUB,
  VCMPGTUB_rec,
  VCMPGTUD,
  VCMPGTUD_rec,
  VCMPGTUH,
  VCMPGTUH_rec,
  VCMPGTUQ,
  VCMPGTUQ_rec,
  VCMPGTUW,
  VCMPGTUW_rec,
  VCMPNEB,
  VCMPNEB_rec,
  VCMPNEH,
  VCMPNEH_rec,
  VCMPNEW,
  VCMPNEW_rec,
  VCMPNEZB,
  VCMPNEZB_rec,
  VCMPNEZH,
  VCMPNEZH_rec,
  VCMPNEZW,
  VCMPNEZW_rec,
  VCMPSQ,
  VCMPUQ,
  VCNTMBB,
  VCNTMBD,
  VCNTMBH,
  VCNTMBW,
  VCTSXS,
  VCTSXS_0,
  VCTUXS,
  VCTUXS_0,
  VCTZB,
  VCTZD,
  VCTZDM,
  VCTZH,
  VCTZLSBB,
  VCTZW,
  VDIVESD,
  VDIVESQ,
  VDIVESW,
  VDIVEUD,
  VDIVEUQ,
  VDIVEUW,
  VDIVSD,
  VDIVSQ,
  VDIVSW,
  VDIVUD,
  VDIVUQ,
  VDIVUW,
  VEQV,
  VEXPANDBM,
  VEXPANDDM,
  VEXPANDHM,
  VEXPANDQM,
  VEXPANDWM,
  VEXPTEFP,
  VEXTDDVLX,
  VEXTDDVRX,
  VEXTDUBVLX,
  VEXTDUBVRX,
  VEXTDUHVLX,
  VEXTDUHVRX,
  VEXTDUWVLX,
  VEXTDUWVRX,
  VEXTRACTBM,
  VEXTRACTD,
  VEXTRACTDM,
  VEXTRACTHM,
  VEXTRACTQM,
  VEXTRACTUB,
  VEXTRACTUH,
  VEXTRACTUW,
  VEXTRACTWM,
  VEXTSB2D,
  VEXTSB2Ds,
  VEXTSB2W,
  VEXTSB2Ws,
  VEXTSD2Q,
  VEXTSH2D,
  VEXTSH2Ds,
  VEXTSH2W,
  VEXTSH2Ws,
  VEXTSW2D,
  VEXTSW2Ds,
  VEXTUBLX,
  VEXTUBRX,
  VEXTUHLX,
  VEXTUHRX,
  VEXTUWLX,
  VEXTUWRX,
  VGBBD,
  VGNB,
  VINSBLX,
  VINSBRX,
  VINSBVLX,
  VINSBVRX,
  VINSD,
  VINSDLX,
  VINSDRX,
  VINSERTB,
  VINSERTD,
  VINSERTH,
  VINSERTW,
  VINSHLX,
  VINSHRX,
  VINSHVLX,
  VINSHVRX,
  VINSW,
  VINSWLX,
  VINSWRX,
  VINSWVLX,
  VINSWVRX,
  VLOGEFP,
  VMADDFP,
  VMAXFP,
  VMAXSB,
  VMAXSD,
  VMAXSH,
  VMAXSW,
  VMAXUB,
  VMAXUD,
  VMAXUH,
  VMAXUW,
  VMHADDSHS,
  VMHRADDSHS,
  VMINFP,
  VMINSB,
  VMINSD,
  VMINSH,
  VMINSW,
  VMINUB,
  VMINUD,
  VMINUH,
  VMINUW,
  VMLADDUHM,
  VMODSD,
  VMODSQ,
  VMODSW,
  VMODUD,
  VMODUQ,
  VMODUW,
  VMRGEW,
  VMRGHB,
  VMRGHH,
  VMRGHW,
  VMRGLB,
  VMRGLH,
  VMRGLW,
  VMRGOW,
  VMSUMCUD,
  VMSUMMBM,
  VMSUMSHM,
  VMSUMSHS,
  VMSUMUBM,
  VMSUMUDM,
  VMSUMUHM,
  VMSUMUHS,
  VMUL10CUQ,
  VMUL10ECUQ,
  VMUL10EUQ,
  VMUL10UQ,
  VMULESB,
  VMULESD,
  VMULESH,
  VMULESW,
  VMULEUB,
  VMULEUD,
  VMULEUH,
  VMULEUW,
  VMULHSD,
  VMULHSW,
  VMULHUD,
  VMULHUW,
  VMULLD,
  VMULOSB,
  VMULOSD,
  VMULOSH,
  VMULOSW,
  VMULOUB,
  VMULOUD,
  VMULOUH,
  VMULOUW,
  VMULUWM,
  VNAND,
  VNCIPHER,
  VNCIPHERLAST,
  VNEGD,
  VNEGW,
  VNMSUBFP,
  VNOR,
  VOR,
  VORC,
  VPDEPD,
  VPERM,
  VPERMR,
  VPERMXOR,
  VPEXTD,
  VPKPX,
  VPKSDSS,
  VPKSDUS,
  VPKSHSS,
  VPKSHUS,
  VPKSWSS,
  VPKSWUS,
  VPKUDUM,
  VPKUDUS,
  VPKUHUM,
  VPKUHUS,
  VPKUWUM,
  VPKUWUS,
  VPMSUMB,
  VPMSUMD,
  VPMSUMH,
  VPMSUMW,
  VPOPCNTB,
  VPOPCNTD,
  VPOPCNTH,
  VPOPCNTW,
  VPRTYBD,
  VPRTYBQ,
  VPRTYBW,
  VREFP,
  VRFIM,
  VRFIN,
  VRFIP,
  VRFIZ,
  VRLB,
  VRLD,
  VRLDMI,
  VRLDNM,
  VRLH,
  VRLQ,
  VRLQMI,
  VRLQNM,
  VRLW,
  VRLWMI,
  VRLWNM,
  VRSQRTEFP,
  VSBOX,
  VSEL,
  VSHASIGMAD,
  VSHASIGMAW,
  VSL,
  VSLB,
  VSLD,
  VSLDBI,
  VSLDOI,
  VSLH,
  VSLO,
  VSLQ,
  VSLV,
  VSLW,
  VSPLTB,
  VSPLTBs,
  VSPLTH,
  VSPLTHs,
  VSPLTISB,
  VSPLTISH,
  VSPLTISW,
  VSPLTW,
  VSR,
  VSRAB,
  VSRAD,
  VSRAH,
  VSRAQ,
  VSRAW,
  VSRB,
  VSRD,
  VSRDBI,
  VSRH,
  VSRO,
  VSRQ,
  VSRV,
  VSRW,
  VSTRIBL,
  VSTRIBL_rec,
  VSTRIBR,
  VSTRIBR_rec,
  VSTRIHL,
  VSTRIHL_rec,
  VSTRIHR,
  VSTRIHR_rec,
  VSUBCUQ,
  VSUBCUW,
  VSUBECUQ,
  VSUBEUQM,
  VSUBFP,
  VSUBSBS,
  VSUBSHS,
  VSUBSWS,
  VSUBUBM,
  VSUBUBS,
  VSUBUDM,
  VSUBUHM,
  VSUBUHS,
  VSUBUQM,
  VSUBUWM,
  VSUBUWS,
  VSUM2SWS,
  VSUM4SBS,
  VSUM4SHS,
  VSUM4UBS,
  VSUMSWS,
  VUPKHPX,
  VUPKHSB,
  VUPKHSH,
  VUPKHSW,
  VUPKLPX,
  VUPKLSB,
  VUPKLSH,
  VUPKLSW,
  VXOR,
  V_SET0,
  V_SET0B,
  V_SET0H,
  V_SETALLONES,
  V_SETALLONESB,
  V_SETALLONESH,
  WAIT,
  WAITP10,
  WRTEE,
  WRTEEI,
  XOR,
  XOR8,
  XOR8_rec,
  XORI,
  XORI8,
  XORIS,
  XORIS8,
  XOR_rec,
  XSABSDP,
  XSABSQP,
  XSADDDP,
  XSADDQP,
  XSADDQPO,
  XSADDSP,
  XSCMPEQDP,
  XSCMPEQQP,
  XSCMPEXPDP,
  XSCMPEXPQP,
  XSCMPGEDP,
  XSCMPGEQP,
  XSCMPGTDP,
  XSCMPGTQP,
  XSCMPODP,
  XSCMPOQP,
  XSCMPUDP,
  XSCMPUQP,
  XSCPSGNDP,
  XSCPSGNQP,
  XSCVDPHP,
  XSCVDPQP,
  XSCVDPSP,
  XSCVDPSPN,
  XSCVDPSXDS,
  XSCVDPSXDSs,
  XSCVDPSXWS,
  XSCVDPSXWSs,
  XSCVDPUXDS,
  XSCVDPUXDSs,
  XSCVDPUXWS,
  XSCVDPUXWSs,
  XSCVHPDP,
  XSCVQPDP,
  XSCVQPDPO,
  XSCVQPSDZ,
  XSCVQPSQZ,
  XSCVQPSWZ,
  XSCVQPUDZ,
  XSCVQPUQZ,
  XSCVQPUWZ,
  XSCVSDQP,
  XSCVSPDP,
  XSCVSPDPN,
  XSCVSQQP,
  XSCVSXDDP,
  XSCVSXDSP,
  XSCVUDQP,
  XSCVUQQP,
  XSCVUXDDP,
  XSCVUXDSP,
  XSDIVDP,
  XSDIVQP,
  XSDIVQPO,
  XSDIVSP,
  XSIEXPDP,
  XSIEXPQP,
  XSMADDADP,
  XSMADDASP,
  XSMADDMDP,
  XSMADDMSP,
  XSMADDQP,
  XSMADDQPO,
  XSMAXCDP,
  XSMAXCQP,
  XSMAXDP,
  XSMAXJDP,
  XSMINCDP,
  XSMINCQP,
  XSMINDP,
  XSMINJDP,
  XSMSUBADP,
  XSMSUBASP,
  XSMSUBMDP,
  XSMSUBMSP,
  XSMSUBQP,
  XSMSUBQPO,
  XSMULDP,
  XSMULQP,
  XSMULQPO,
  XSMULSP,
  XSNABSDP,
  XSNABSDPs,
  XSNABSQP,
  XSNEGDP,
  XSNEGQP,
  XSNMADDADP,
  XSNMADDASP,
  XSNMADDMDP,
  XSNMADDMSP,
  XSNMADDQP,
  XSNMADDQPO,
  XSNMSUBADP,
  XSNMSUBASP,
  XSNMSUBMDP,
  XSNMSUBMSP,
  XSNMSUBQP,
  XSNMSUBQPO,
  XSRDPI,
  XSRDPIC,
  XSRDPIM,
  XSRDPIP,
  XSRDPIZ,
  XSREDP,
  XSRESP,
  XSRQPI,
  XSRQPIX,
  XSRQPXP,
  XSRSP,
  XSRSQRTEDP,
  XSRSQRTESP,
  XSSQRTDP,
  XSSQRTQP,
  XSSQRTQPO,
  XSSQRTSP,
  XSSUBDP,
  XSSUBQP,
  XSSUBQPO,
  XSSUBSP,
  XSTDIVDP,
  XSTSQRTDP,
  XSTSTDCDP,
  XSTSTDCQP,
  XSTSTDCSP,
  XSXEXPDP,
  XSXEXPQP,
  XSXSIGDP,
  XSXSIGQP,
  XVABSDP,
  XVABSSP,
  XVADDDP,
  XVADDSP,
  XVBF16GER2,
  XVBF16GER2NN,
  XVBF16GER2NP,
  XVBF16GER2PN,
  XVBF16GER2PP,
  XVBF16GER2W,
  XVBF16GER2WNN,
  XVBF16GER2WNP,
  XVBF16GER2WPN,
  XVBF16GER2WPP,
  XVCMPEQDP,
  XVCMPEQDP_rec,
  XVCMPEQSP,
  XVCMPEQSP_rec,
  XVCMPGEDP,
  XVCMPGEDP_rec,
  XVCMPGESP,
  XVCMPGESP_rec,
  XVCMPGTDP,
  XVCMPGTDP_rec,
  XVCMPGTSP,
  XVCMPGTSP_rec,
  XVCPSGNDP,
  XVCPSGNSP,
  XVCVBF16SPN,
  XVCVDPSP,
  XVCVDPSXDS,
  XVCVDPSXWS,
  XVCVDPUXDS,
  XVCVDPUXWS,
  XVCVHPSP,
  XVCVSPBF16,
  XVCVSPDP,
  XVCVSPHP,
  XVCVSPSXDS,
  XVCVSPSXWS,
  XVCVSPUXDS,
  XVCVSPUXWS,
  XVCVSXDDP,
  XVCVSXDSP,
  XVCVSXWDP,
  XVCVSXWSP,
  XVCVUXDDP,
  XVCVUXDSP,
  XVCVUXWDP,
  XVCVUXWSP,
  XVDIVDP,
  XVDIVSP,
  XVF16GER2,
  XVF16GER2NN,
  XVF16GER2NP,
  XVF16GER2PN,
  XVF16GER2PP,
  XVF16GER2W,
  XVF16GER2WNN,
  XVF16GER2WNP,
  XVF16GER2WPN,
  XVF16GER2WPP,
  XVF32GER,
  XVF32GERNN,
  XVF32GERNP,
  XVF32GERPN,
  XVF32GERPP,
  XVF32GERW,
  XVF32GERWNN,
  XVF32GERWNP,
  XVF32GERWPN,
  XVF32GERWPP,
  XVF64GER,
  XVF64GERNN,
  XVF64GERNP,
  XVF64GERPN,
  XVF64GERPP,
  XVF64GERW,
  XVF64GERWNN,
  XVF64GERWNP,
  XVF64GERWPN,
  XVF64GERWPP,
  XVI16GER2,
  XVI16GER2PP,
  XVI16GER2S,
  XVI16GER2SPP,
  XVI16GER2SW,
  XVI16GER2SWPP,
  XVI16GER2W,
  XVI16GER2WPP,
  XVI4GER8,
  XVI4GER8PP,
  XVI4GER8W,
  XVI4GER8WPP,
  XVI8GER4,
  XVI8GER4PP,
  XVI8GER4SPP,
  XVI8GER4W,
  XVI8GER4WPP,
  XVI8GER4WSPP,
  XVIEXPDP,
  XVIEXPSP,
  XVMADDADP,
  XVMADDASP,
  XVMADDMDP,
  XVMADDMSP,
  XVMAXDP,
  XVMAXSP,
  XVMINDP,
  XVMINSP,
  XVMSUBADP,
  XVMSUBASP,
  XVMSUBMDP,
  XVMSUBMSP,
  XVMULDP,
  XVMULSP,
  XVNABSDP,
  XVNABSSP,
  XVNEGDP,
  XVNEGSP,
  XVNMADDADP,
  XVNMADDASP,
  XVNMADDMDP,
  XVNMADDMSP,
  XVNMSUBADP,
  XVNMSUBASP,
  XVNMSUBMDP,
  XVNMSUBMSP,
  XVRDPI,
  XVRDPIC,
  XVRDPIM,
  XVRDPIP,
  XVRDPIZ,
  XVREDP,
  XVRESP,
  XVRSPI,
  XVRSPIC,
  XVRSPIM,
  XVRSPIP,
  XVRSPIZ,
  XVRSQRTEDP,
  XVRSQRTESP,
  XVSQRTDP,
  XVSQRTSP,
  XVSUBDP,
  XVSUBSP,
  XVTDIVDP,
  XVTDIVSP,
  XVTLSBB,
  XVTSQRTDP,
  XVTSQRTSP,
  XVTSTDCDP,
  XVTSTDCSP,
  XVXEXPDP,
  XVXEXPSP,
  XVXSIGDP,
  XVXSIGSP,
  XXBLENDVB,
  XXBLENDVD,
  XXBLENDVH,
  XXBLENDVW,
  XXBRD,
  XXBRH,
  XXBRQ,
  XXBRW,
  XXEVAL,
  XXEXTRACTUW,
  XXGENPCVBM,
  XXGENPCVDM,
  XXGENPCVHM,
  XXGENPCVWM,
  XXINSERTW,
  XXLAND,
  XXLANDC,
  XXLEQV,
  XXLEQVOnes,
  XXLNAND,
  XXLNOR,
  XXLOR,
  XXLORC,
  XXLORf,
  XXLXOR,
  XXLXORdpz,
  XXLXORspz,
  XXLXORz,
  XXMFACC,
  XXMFACCW,
  XXMRGHW,
  XXMRGLW,
  XXMTACC,
  XXMTACCW,
  XXPERM,
  XXPERMDI,
  XXPERMDIs,
  XXPERMR,
  XXPERMX,
  XXSEL,
  XXSETACCZ,
  XXSETACCZW,
  XXSLDWI,
  XXSLDWIs,
  XXSPLTI32DX,
  XXSPLTIB,
  XXSPLTIDP,
  XXSPLTIW,
  XXSPLTW,
  XXSPLTWs,
  gBC,
  gBCA,
  gBCAat,
  gBCCTR,
  gBCCTRL,
  gBCL,
  gBCLA,
  gBCLAat,
  gBCLR,
  gBCLRL,
  gBCLat,
  gBCat,
  INSTRUCTION_LIST_END,
  UNKNOWN(u64),
}

impl From<u64> for Opcode {
    fn from(value: u64) -> Self {
        match value {
          0 => Opcode::PHI,
          1 => Opcode::INLINEASM,
          2 => Opcode::INLINEASM_BR,
          3 => Opcode::CFI_INSTRUCTION,
          4 => Opcode::EH_LABEL,
          5 => Opcode::GC_LABEL,
          6 => Opcode::ANNOTATION_LABEL,
          7 => Opcode::KILL,
          8 => Opcode::EXTRACT_SUBREG,
          9 => Opcode::INSERT_SUBREG,
          10 => Opcode::IMPLICIT_DEF,
          11 => Opcode::INIT_UNDEF,
          12 => Opcode::SUBREG_TO_REG,
          13 => Opcode::COPY_TO_REGCLASS,
          14 => Opcode::DBG_VALUE,
          15 => Opcode::DBG_VALUE_LIST,
          16 => Opcode::DBG_INSTR_REF,
          17 => Opcode::DBG_PHI,
          18 => Opcode::DBG_LABEL,
          19 => Opcode::REG_SEQUENCE,
          20 => Opcode::COPY,
          21 => Opcode::BUNDLE,
          22 => Opcode::LIFETIME_START,
          23 => Opcode::LIFETIME_END,
          24 => Opcode::PSEUDO_PROBE,
          25 => Opcode::ARITH_FENCE,
          26 => Opcode::STACKMAP,
          27 => Opcode::FENTRY_CALL,
          28 => Opcode::PATCHPOINT,
          29 => Opcode::LOAD_STACK_GUARD,
          30 => Opcode::PREALLOCATED_SETUP,
          31 => Opcode::PREALLOCATED_ARG,
          32 => Opcode::STATEPOINT,
          33 => Opcode::LOCAL_ESCAPE,
          34 => Opcode::FAULTING_OP,
          35 => Opcode::PATCHABLE_OP,
          36 => Opcode::PATCHABLE_FUNCTION_ENTER,
          37 => Opcode::PATCHABLE_RET,
          38 => Opcode::PATCHABLE_FUNCTION_EXIT,
          39 => Opcode::PATCHABLE_TAIL_CALL,
          40 => Opcode::PATCHABLE_EVENT_CALL,
          41 => Opcode::PATCHABLE_TYPED_EVENT_CALL,
          42 => Opcode::ICALL_BRANCH_FUNNEL,
          43 => Opcode::FAKE_USE,
          44 => Opcode::MEMBARRIER,
          45 => Opcode::JUMP_TABLE_DEBUG_INFO,
          46 => Opcode::CONVERGENCECTRL_ENTRY,
          47 => Opcode::CONVERGENCECTRL_ANCHOR,
          48 => Opcode::CONVERGENCECTRL_LOOP,
          49 => Opcode::CONVERGENCECTRL_GLUE,
          50 => Opcode::G_ASSERT_SEXT,
          51 => Opcode::G_ASSERT_ZEXT,
          52 => Opcode::G_ASSERT_ALIGN,
          53 => Opcode::G_ADD,
          54 => Opcode::G_SUB,
          55 => Opcode::G_MUL,
          56 => Opcode::G_SDIV,
          57 => Opcode::G_UDIV,
          58 => Opcode::G_SREM,
          59 => Opcode::G_UREM,
          60 => Opcode::G_SDIVREM,
          61 => Opcode::G_UDIVREM,
          62 => Opcode::G_AND,
          63 => Opcode::G_OR,
          64 => Opcode::G_XOR,
          65 => Opcode::G_ABDS,
          66 => Opcode::G_ABDU,
          67 => Opcode::G_IMPLICIT_DEF,
          68 => Opcode::G_PHI,
          69 => Opcode::G_FRAME_INDEX,
          70 => Opcode::G_GLOBAL_VALUE,
          71 => Opcode::G_PTRAUTH_GLOBAL_VALUE,
          72 => Opcode::G_CONSTANT_POOL,
          73 => Opcode::G_EXTRACT,
          74 => Opcode::G_UNMERGE_VALUES,
          75 => Opcode::G_INSERT,
          76 => Opcode::G_MERGE_VALUES,
          77 => Opcode::G_BUILD_VECTOR,
          78 => Opcode::G_BUILD_VECTOR_TRUNC,
          79 => Opcode::G_CONCAT_VECTORS,
          80 => Opcode::G_PTRTOINT,
          81 => Opcode::G_INTTOPTR,
          82 => Opcode::G_BITCAST,
          83 => Opcode::G_FREEZE,
          84 => Opcode::G_CONSTANT_FOLD_BARRIER,
          85 => Opcode::G_INTRINSIC_FPTRUNC_ROUND,
          86 => Opcode::G_INTRINSIC_TRUNC,
          87 => Opcode::G_INTRINSIC_ROUND,
          88 => Opcode::G_INTRINSIC_LRINT,
          89 => Opcode::G_INTRINSIC_LLRINT,
          90 => Opcode::G_INTRINSIC_ROUNDEVEN,
          91 => Opcode::G_READCYCLECOUNTER,
          92 => Opcode::G_READSTEADYCOUNTER,
          93 => Opcode::G_LOAD,
          94 => Opcode::G_SEXTLOAD,
          95 => Opcode::G_ZEXTLOAD,
          96 => Opcode::G_INDEXED_LOAD,
          97 => Opcode::G_INDEXED_SEXTLOAD,
          98 => Opcode::G_INDEXED_ZEXTLOAD,
          99 => Opcode::G_STORE,
          100 => Opcode::G_INDEXED_STORE,
          101 => Opcode::G_ATOMIC_CMPXCHG_WITH_SUCCESS,
          102 => Opcode::G_ATOMIC_CMPXCHG,
          103 => Opcode::G_ATOMICRMW_XCHG,
          104 => Opcode::G_ATOMICRMW_ADD,
          105 => Opcode::G_ATOMICRMW_SUB,
          106 => Opcode::G_ATOMICRMW_AND,
          107 => Opcode::G_ATOMICRMW_NAND,
          108 => Opcode::G_ATOMICRMW_OR,
          109 => Opcode::G_ATOMICRMW_XOR,
          110 => Opcode::G_ATOMICRMW_MAX,
          111 => Opcode::G_ATOMICRMW_MIN,
          112 => Opcode::G_ATOMICRMW_UMAX,
          113 => Opcode::G_ATOMICRMW_UMIN,
          114 => Opcode::G_ATOMICRMW_FADD,
          115 => Opcode::G_ATOMICRMW_FSUB,
          116 => Opcode::G_ATOMICRMW_FMAX,
          117 => Opcode::G_ATOMICRMW_FMIN,
          118 => Opcode::G_ATOMICRMW_UINC_WRAP,
          119 => Opcode::G_ATOMICRMW_UDEC_WRAP,
          120 => Opcode::G_ATOMICRMW_USUB_COND,
          121 => Opcode::G_ATOMICRMW_USUB_SAT,
          122 => Opcode::G_FENCE,
          123 => Opcode::G_PREFETCH,
          124 => Opcode::G_BRCOND,
          125 => Opcode::G_BRINDIRECT,
          126 => Opcode::G_INVOKE_REGION_START,
          127 => Opcode::G_INTRINSIC,
          128 => Opcode::G_INTRINSIC_W_SIDE_EFFECTS,
          129 => Opcode::G_INTRINSIC_CONVERGENT,
          130 => Opcode::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS,
          131 => Opcode::G_ANYEXT,
          132 => Opcode::G_TRUNC,
          133 => Opcode::G_CONSTANT,
          134 => Opcode::G_FCONSTANT,
          135 => Opcode::G_VASTART,
          136 => Opcode::G_VAARG,
          137 => Opcode::G_SEXT,
          138 => Opcode::G_SEXT_INREG,
          139 => Opcode::G_ZEXT,
          140 => Opcode::G_SHL,
          141 => Opcode::G_LSHR,
          142 => Opcode::G_ASHR,
          143 => Opcode::G_FSHL,
          144 => Opcode::G_FSHR,
          145 => Opcode::G_ROTR,
          146 => Opcode::G_ROTL,
          147 => Opcode::G_ICMP,
          148 => Opcode::G_FCMP,
          149 => Opcode::G_SCMP,
          150 => Opcode::G_UCMP,
          151 => Opcode::G_SELECT,
          152 => Opcode::G_UADDO,
          153 => Opcode::G_UADDE,
          154 => Opcode::G_USUBO,
          155 => Opcode::G_USUBE,
          156 => Opcode::G_SADDO,
          157 => Opcode::G_SADDE,
          158 => Opcode::G_SSUBO,
          159 => Opcode::G_SSUBE,
          160 => Opcode::G_UMULO,
          161 => Opcode::G_SMULO,
          162 => Opcode::G_UMULH,
          163 => Opcode::G_SMULH,
          164 => Opcode::G_UADDSAT,
          165 => Opcode::G_SADDSAT,
          166 => Opcode::G_USUBSAT,
          167 => Opcode::G_SSUBSAT,
          168 => Opcode::G_USHLSAT,
          169 => Opcode::G_SSHLSAT,
          170 => Opcode::G_SMULFIX,
          171 => Opcode::G_UMULFIX,
          172 => Opcode::G_SMULFIXSAT,
          173 => Opcode::G_UMULFIXSAT,
          174 => Opcode::G_SDIVFIX,
          175 => Opcode::G_UDIVFIX,
          176 => Opcode::G_SDIVFIXSAT,
          177 => Opcode::G_UDIVFIXSAT,
          178 => Opcode::G_FADD,
          179 => Opcode::G_FSUB,
          180 => Opcode::G_FMUL,
          181 => Opcode::G_FMA,
          182 => Opcode::G_FMAD,
          183 => Opcode::G_FDIV,
          184 => Opcode::G_FREM,
          185 => Opcode::G_FPOW,
          186 => Opcode::G_FPOWI,
          187 => Opcode::G_FEXP,
          188 => Opcode::G_FEXP2,
          189 => Opcode::G_FEXP10,
          190 => Opcode::G_FLOG,
          191 => Opcode::G_FLOG2,
          192 => Opcode::G_FLOG10,
          193 => Opcode::G_FLDEXP,
          194 => Opcode::G_FFREXP,
          195 => Opcode::G_FNEG,
          196 => Opcode::G_FPEXT,
          197 => Opcode::G_FPTRUNC,
          198 => Opcode::G_FPTOSI,
          199 => Opcode::G_FPTOUI,
          200 => Opcode::G_SITOFP,
          201 => Opcode::G_UITOFP,
          202 => Opcode::G_FPTOSI_SAT,
          203 => Opcode::G_FPTOUI_SAT,
          204 => Opcode::G_FABS,
          205 => Opcode::G_FCOPYSIGN,
          206 => Opcode::G_IS_FPCLASS,
          207 => Opcode::G_FCANONICALIZE,
          208 => Opcode::G_FMINNUM,
          209 => Opcode::G_FMAXNUM,
          210 => Opcode::G_FMINNUM_IEEE,
          211 => Opcode::G_FMAXNUM_IEEE,
          212 => Opcode::G_FMINIMUM,
          213 => Opcode::G_FMAXIMUM,
          214 => Opcode::G_GET_FPENV,
          215 => Opcode::G_SET_FPENV,
          216 => Opcode::G_RESET_FPENV,
          217 => Opcode::G_GET_FPMODE,
          218 => Opcode::G_SET_FPMODE,
          219 => Opcode::G_RESET_FPMODE,
          220 => Opcode::G_PTR_ADD,
          221 => Opcode::G_PTRMASK,
          222 => Opcode::G_SMIN,
          223 => Opcode::G_SMAX,
          224 => Opcode::G_UMIN,
          225 => Opcode::G_UMAX,
          226 => Opcode::G_ABS,
          227 => Opcode::G_LROUND,
          228 => Opcode::G_LLROUND,
          229 => Opcode::G_BR,
          230 => Opcode::G_BRJT,
          231 => Opcode::G_VSCALE,
          232 => Opcode::G_INSERT_SUBVECTOR,
          233 => Opcode::G_EXTRACT_SUBVECTOR,
          234 => Opcode::G_INSERT_VECTOR_ELT,
          235 => Opcode::G_EXTRACT_VECTOR_ELT,
          236 => Opcode::G_SHUFFLE_VECTOR,
          237 => Opcode::G_SPLAT_VECTOR,
          238 => Opcode::G_STEP_VECTOR,
          239 => Opcode::G_VECTOR_COMPRESS,
          240 => Opcode::G_CTTZ,
          241 => Opcode::G_CTTZ_ZERO_UNDEF,
          242 => Opcode::G_CTLZ,
          243 => Opcode::G_CTLZ_ZERO_UNDEF,
          244 => Opcode::G_CTPOP,
          245 => Opcode::G_BSWAP,
          246 => Opcode::G_BITREVERSE,
          247 => Opcode::G_FCEIL,
          248 => Opcode::G_FCOS,
          249 => Opcode::G_FSIN,
          250 => Opcode::G_FSINCOS,
          251 => Opcode::G_FTAN,
          252 => Opcode::G_FACOS,
          253 => Opcode::G_FASIN,
          254 => Opcode::G_FATAN,
          255 => Opcode::G_FATAN2,
          256 => Opcode::G_FCOSH,
          257 => Opcode::G_FSINH,
          258 => Opcode::G_FTANH,
          259 => Opcode::G_FSQRT,
          260 => Opcode::G_FFLOOR,
          261 => Opcode::G_FRINT,
          262 => Opcode::G_FNEARBYINT,
          263 => Opcode::G_ADDRSPACE_CAST,
          264 => Opcode::G_BLOCK_ADDR,
          265 => Opcode::G_JUMP_TABLE,
          266 => Opcode::G_DYN_STACKALLOC,
          267 => Opcode::G_STACKSAVE,
          268 => Opcode::G_STACKRESTORE,
          269 => Opcode::G_STRICT_FADD,
          270 => Opcode::G_STRICT_FSUB,
          271 => Opcode::G_STRICT_FMUL,
          272 => Opcode::G_STRICT_FDIV,
          273 => Opcode::G_STRICT_FREM,
          274 => Opcode::G_STRICT_FMA,
          275 => Opcode::G_STRICT_FSQRT,
          276 => Opcode::G_STRICT_FLDEXP,
          277 => Opcode::G_READ_REGISTER,
          278 => Opcode::G_WRITE_REGISTER,
          279 => Opcode::G_MEMCPY,
          280 => Opcode::G_MEMCPY_INLINE,
          281 => Opcode::G_MEMMOVE,
          282 => Opcode::G_MEMSET,
          283 => Opcode::G_BZERO,
          284 => Opcode::G_TRAP,
          285 => Opcode::G_DEBUGTRAP,
          286 => Opcode::G_UBSANTRAP,
          287 => Opcode::G_VECREDUCE_SEQ_FADD,
          288 => Opcode::G_VECREDUCE_SEQ_FMUL,
          289 => Opcode::G_VECREDUCE_FADD,
          290 => Opcode::G_VECREDUCE_FMUL,
          291 => Opcode::G_VECREDUCE_FMAX,
          292 => Opcode::G_VECREDUCE_FMIN,
          293 => Opcode::G_VECREDUCE_FMAXIMUM,
          294 => Opcode::G_VECREDUCE_FMINIMUM,
          295 => Opcode::G_VECREDUCE_ADD,
          296 => Opcode::G_VECREDUCE_MUL,
          297 => Opcode::G_VECREDUCE_AND,
          298 => Opcode::G_VECREDUCE_OR,
          299 => Opcode::G_VECREDUCE_XOR,
          300 => Opcode::G_VECREDUCE_SMAX,
          301 => Opcode::G_VECREDUCE_SMIN,
          302 => Opcode::G_VECREDUCE_UMAX,
          303 => Opcode::G_VECREDUCE_UMIN,
          304 => Opcode::G_SBFX,
          305 => Opcode::G_UBFX,
          306 => Opcode::ATOMIC_CMP_SWAP_I128,
          307 => Opcode::ATOMIC_LOAD_ADD_I128,
          308 => Opcode::ATOMIC_LOAD_AND_I128,
          309 => Opcode::ATOMIC_LOAD_NAND_I128,
          310 => Opcode::ATOMIC_LOAD_OR_I128,
          311 => Opcode::ATOMIC_LOAD_SUB_I128,
          312 => Opcode::ATOMIC_LOAD_XOR_I128,
          313 => Opcode::ATOMIC_SWAP_I128,
          314 => Opcode::BUILD_QUADWORD,
          315 => Opcode::BUILD_UACC,
          316 => Opcode::CFENCE,
          317 => Opcode::CFENCE8,
          318 => Opcode::CLRLSLDI,
          319 => Opcode::CLRLSLDI_rec,
          320 => Opcode::CLRLSLWI,
          321 => Opcode::CLRLSLWI_rec,
          322 => Opcode::CLRRDI,
          323 => Opcode::CLRRDI_rec,
          324 => Opcode::CLRRWI,
          325 => Opcode::CLRRWI_rec,
          326 => Opcode::DCBFL,
          327 => Opcode::DCBFLP,
          328 => Opcode::DCBFPS,
          329 => Opcode::DCBFx,
          330 => Opcode::DCBSTPS,
          331 => Opcode::DCBTCT,
          332 => Opcode::DCBTDS,
          333 => Opcode::DCBTSTCT,
          334 => Opcode::DCBTSTDS,
          335 => Opcode::DCBTSTT,
          336 => Opcode::DCBTSTx,
          337 => Opcode::DCBTT,
          338 => Opcode::DCBTx,
          339 => Opcode::DFLOADf32,
          340 => Opcode::DFLOADf64,
          341 => Opcode::DFSTOREf32,
          342 => Opcode::DFSTOREf64,
          343 => Opcode::EXTLDI,
          344 => Opcode::EXTLDI_rec,
          345 => Opcode::EXTLWI,
          346 => Opcode::EXTLWI_rec,
          347 => Opcode::EXTRDI,
          348 => Opcode::EXTRDI_rec,
          349 => Opcode::EXTRWI,
          350 => Opcode::EXTRWI_rec,
          351 => Opcode::INSLWI,
          352 => Opcode::INSLWI_rec,
          353 => Opcode::INSRDI,
          354 => Opcode::INSRDI_rec,
          355 => Opcode::INSRWI,
          356 => Opcode::INSRWI_rec,
          357 => Opcode::KILL_PAIR,
          358 => Opcode::LAx,
          359 => Opcode::LIWAX,
          360 => Opcode::LIWZX,
          361 => Opcode::PPCLdFixedAddr,
          362 => Opcode::PSUBI,
          363 => Opcode::RLWIMIbm,
          364 => Opcode::RLWIMIbm_rec,
          365 => Opcode::RLWINMbm,
          366 => Opcode::RLWINMbm_rec,
          367 => Opcode::RLWNMbm,
          368 => Opcode::RLWNMbm_rec,
          369 => Opcode::ROTRDI,
          370 => Opcode::ROTRDI_rec,
          371 => Opcode::ROTRWI,
          372 => Opcode::ROTRWI_rec,
          373 => Opcode::SLDI,
          374 => Opcode::SLDI_rec,
          375 => Opcode::SLWI,
          376 => Opcode::SLWI_rec,
          377 => Opcode::SPILLTOVSR_LD,
          378 => Opcode::SPILLTOVSR_LDX,
          379 => Opcode::SPILLTOVSR_ST,
          380 => Opcode::SPILLTOVSR_STX,
          381 => Opcode::SRDI,
          382 => Opcode::SRDI_rec,
          383 => Opcode::SRWI,
          384 => Opcode::SRWI_rec,
          385 => Opcode::STIWX,
          386 => Opcode::SUBI,
          387 => Opcode::SUBIC,
          388 => Opcode::SUBIC_rec,
          389 => Opcode::SUBIS,
          390 => Opcode::SUBPCIS,
          391 => Opcode::XFLOADf32,
          392 => Opcode::XFLOADf64,
          393 => Opcode::XFSTOREf32,
          394 => Opcode::XFSTOREf64,
          395 => Opcode::ADD4,
          396 => Opcode::ADD4O,
          397 => Opcode::ADD4O_rec,
          398 => Opcode::ADD4TLS,
          399 => Opcode::ADD4_rec,
          400 => Opcode::ADD8,
          401 => Opcode::ADD8O,
          402 => Opcode::ADD8O_rec,
          403 => Opcode::ADD8TLS,
          404 => Opcode::ADD8TLS_,
          405 => Opcode::ADD8_rec,
          406 => Opcode::ADDC,
          407 => Opcode::ADDC8,
          408 => Opcode::ADDC8O,
          409 => Opcode::ADDC8O_rec,
          410 => Opcode::ADDC8_rec,
          411 => Opcode::ADDCO,
          412 => Opcode::ADDCO_rec,
          413 => Opcode::ADDC_rec,
          414 => Opcode::ADDE,
          415 => Opcode::ADDE8,
          416 => Opcode::ADDE8O,
          417 => Opcode::ADDE8O_rec,
          418 => Opcode::ADDE8_rec,
          419 => Opcode::ADDEO,
          420 => Opcode::ADDEO_rec,
          421 => Opcode::ADDEX,
          422 => Opcode::ADDEX8,
          423 => Opcode::ADDE_rec,
          424 => Opcode::ADDG6S,
          425 => Opcode::ADDG6S8,
          426 => Opcode::ADDI,
          427 => Opcode::ADDI8,
          428 => Opcode::ADDIC,
          429 => Opcode::ADDIC8,
          430 => Opcode::ADDIC_rec,
          431 => Opcode::ADDIS,
          432 => Opcode::ADDIS8,
          433 => Opcode::ADDISdtprelHA,
          434 => Opcode::ADDISdtprelHA32,
          435 => Opcode::ADDISgotTprelHA,
          436 => Opcode::ADDIStlsgdHA,
          437 => Opcode::ADDIStlsldHA,
          438 => Opcode::ADDIStocHA,
          439 => Opcode::ADDIStocHA8,
          440 => Opcode::ADDIdtprelL,
          441 => Opcode::ADDIdtprelL32,
          442 => Opcode::ADDItlsgdL,
          443 => Opcode::ADDItlsgdL32,
          444 => Opcode::ADDItlsgdLADDR,
          445 => Opcode::ADDItlsgdLADDR32,
          446 => Opcode::ADDItlsldL,
          447 => Opcode::ADDItlsldL32,
          448 => Opcode::ADDItlsldLADDR,
          449 => Opcode::ADDItlsldLADDR32,
          450 => Opcode::ADDItoc,
          451 => Opcode::ADDItoc8,
          452 => Opcode::ADDItocL,
          453 => Opcode::ADDItocL8,
          454 => Opcode::ADDME,
          455 => Opcode::ADDME8,
          456 => Opcode::ADDME8O,
          457 => Opcode::ADDME8O_rec,
          458 => Opcode::ADDME8_rec,
          459 => Opcode::ADDMEO,
          460 => Opcode::ADDMEO_rec,
          461 => Opcode::ADDME_rec,
          462 => Opcode::ADDPCIS,
          463 => Opcode::ADDZE,
          464 => Opcode::ADDZE8,
          465 => Opcode::ADDZE8O,
          466 => Opcode::ADDZE8O_rec,
          467 => Opcode::ADDZE8_rec,
          468 => Opcode::ADDZEO,
          469 => Opcode::ADDZEO_rec,
          470 => Opcode::ADDZE_rec,
          471 => Opcode::ADJCALLSTACKDOWN,
          472 => Opcode::ADJCALLSTACKUP,
          473 => Opcode::AND,
          474 => Opcode::AND8,
          475 => Opcode::AND8_rec,
          476 => Opcode::ANDC,
          477 => Opcode::ANDC8,
          478 => Opcode::ANDC8_rec,
          479 => Opcode::ANDC_rec,
          480 => Opcode::ANDI8_rec,
          481 => Opcode::ANDIS8_rec,
          482 => Opcode::ANDIS_rec,
          483 => Opcode::ANDI_rec,
          484 => Opcode::ANDI_rec_1_EQ_BIT,
          485 => Opcode::ANDI_rec_1_EQ_BIT8,
          486 => Opcode::ANDI_rec_1_GT_BIT,
          487 => Opcode::ANDI_rec_1_GT_BIT8,
          488 => Opcode::AND_rec,
          489 => Opcode::ATOMIC_CMP_SWAP_I16,
          490 => Opcode::ATOMIC_CMP_SWAP_I32,
          491 => Opcode::ATOMIC_CMP_SWAP_I64,
          492 => Opcode::ATOMIC_CMP_SWAP_I8,
          493 => Opcode::ATOMIC_LOAD_ADD_I16,
          494 => Opcode::ATOMIC_LOAD_ADD_I32,
          495 => Opcode::ATOMIC_LOAD_ADD_I64,
          496 => Opcode::ATOMIC_LOAD_ADD_I8,
          497 => Opcode::ATOMIC_LOAD_AND_I16,
          498 => Opcode::ATOMIC_LOAD_AND_I32,
          499 => Opcode::ATOMIC_LOAD_AND_I64,
          500 => Opcode::ATOMIC_LOAD_AND_I8,
          501 => Opcode::ATOMIC_LOAD_MAX_I16,
          502 => Opcode::ATOMIC_LOAD_MAX_I32,
          503 => Opcode::ATOMIC_LOAD_MAX_I64,
          504 => Opcode::ATOMIC_LOAD_MAX_I8,
          505 => Opcode::ATOMIC_LOAD_MIN_I16,
          506 => Opcode::ATOMIC_LOAD_MIN_I32,
          507 => Opcode::ATOMIC_LOAD_MIN_I64,
          508 => Opcode::ATOMIC_LOAD_MIN_I8,
          509 => Opcode::ATOMIC_LOAD_NAND_I16,
          510 => Opcode::ATOMIC_LOAD_NAND_I32,
          511 => Opcode::ATOMIC_LOAD_NAND_I64,
          512 => Opcode::ATOMIC_LOAD_NAND_I8,
          513 => Opcode::ATOMIC_LOAD_OR_I16,
          514 => Opcode::ATOMIC_LOAD_OR_I32,
          515 => Opcode::ATOMIC_LOAD_OR_I64,
          516 => Opcode::ATOMIC_LOAD_OR_I8,
          517 => Opcode::ATOMIC_LOAD_SUB_I16,
          518 => Opcode::ATOMIC_LOAD_SUB_I32,
          519 => Opcode::ATOMIC_LOAD_SUB_I64,
          520 => Opcode::ATOMIC_LOAD_SUB_I8,
          521 => Opcode::ATOMIC_LOAD_UMAX_I16,
          522 => Opcode::ATOMIC_LOAD_UMAX_I32,
          523 => Opcode::ATOMIC_LOAD_UMAX_I64,
          524 => Opcode::ATOMIC_LOAD_UMAX_I8,
          525 => Opcode::ATOMIC_LOAD_UMIN_I16,
          526 => Opcode::ATOMIC_LOAD_UMIN_I32,
          527 => Opcode::ATOMIC_LOAD_UMIN_I64,
          528 => Opcode::ATOMIC_LOAD_UMIN_I8,
          529 => Opcode::ATOMIC_LOAD_XOR_I16,
          530 => Opcode::ATOMIC_LOAD_XOR_I32,
          531 => Opcode::ATOMIC_LOAD_XOR_I64,
          532 => Opcode::ATOMIC_LOAD_XOR_I8,
          533 => Opcode::ATOMIC_SWAP_I16,
          534 => Opcode::ATOMIC_SWAP_I32,
          535 => Opcode::ATOMIC_SWAP_I64,
          536 => Opcode::ATOMIC_SWAP_I8,
          537 => Opcode::ATTN,
          538 => Opcode::B,
          539 => Opcode::BA,
          540 => Opcode::BC,
          541 => Opcode::BCC,
          542 => Opcode::BCCA,
          543 => Opcode::BCCCTR,
          544 => Opcode::BCCCTR8,
          545 => Opcode::BCCCTRL,
          546 => Opcode::BCCCTRL8,
          547 => Opcode::BCCL,
          548 => Opcode::BCCLA,
          549 => Opcode::BCCLR,
          550 => Opcode::BCCLRL,
          551 => Opcode::BCCTR,
          552 => Opcode::BCCTR8,
          553 => Opcode::BCCTR8n,
          554 => Opcode::BCCTRL,
          555 => Opcode::BCCTRL8,
          556 => Opcode::BCCTRL8n,
          557 => Opcode::BCCTRLn,
          558 => Opcode::BCCTRn,
          559 => Opcode::BCDADD_rec,
          560 => Opcode::BCDCFN_rec,
          561 => Opcode::BCDCFSQ_rec,
          562 => Opcode::BCDCFZ_rec,
          563 => Opcode::BCDCPSGN_rec,
          564 => Opcode::BCDCTN_rec,
          565 => Opcode::BCDCTSQ_rec,
          566 => Opcode::BCDCTZ_rec,
          567 => Opcode::BCDSETSGN_rec,
          568 => Opcode::BCDSR_rec,
          569 => Opcode::BCDSUB_rec,
          570 => Opcode::BCDS_rec,
          571 => Opcode::BCDTRUNC_rec,
          572 => Opcode::BCDUS_rec,
          573 => Opcode::BCDUTRUNC_rec,
          574 => Opcode::BCL,
          575 => Opcode::BCLR,
          576 => Opcode::BCLRL,
          577 => Opcode::BCLRLn,
          578 => Opcode::BCLRn,
          579 => Opcode::BCLalways,
          580 => Opcode::BCLn,
          581 => Opcode::BCTR,
          582 => Opcode::BCTR8,
          583 => Opcode::BCTRL,
          584 => Opcode::BCTRL8,
          585 => Opcode::BCTRL8_LDinto_toc,
          586 => Opcode::BCTRL8_LDinto_toc_RM,
          587 => Opcode::BCTRL8_RM,
          588 => Opcode::BCTRL_LWZinto_toc,
          589 => Opcode::BCTRL_LWZinto_toc_RM,
          590 => Opcode::BCTRL_RM,
          591 => Opcode::BCn,
          592 => Opcode::BDNZ,
          593 => Opcode::BDNZ8,
          594 => Opcode::BDNZA,
          595 => Opcode::BDNZAm,
          596 => Opcode::BDNZAp,
          597 => Opcode::BDNZL,
          598 => Opcode::BDNZLA,
          599 => Opcode::BDNZLAm,
          600 => Opcode::BDNZLAp,
          601 => Opcode::BDNZLR,
          602 => Opcode::BDNZLR8,
          603 => Opcode::BDNZLRL,
          604 => Opcode::BDNZLRLm,
          605 => Opcode::BDNZLRLp,
          606 => Opcode::BDNZLRm,
          607 => Opcode::BDNZLRp,
          608 => Opcode::BDNZLm,
          609 => Opcode::BDNZLp,
          610 => Opcode::BDNZm,
          611 => Opcode::BDNZp,
          612 => Opcode::BDZ,
          613 => Opcode::BDZ8,
          614 => Opcode::BDZA,
          615 => Opcode::BDZAm,
          616 => Opcode::BDZAp,
          617 => Opcode::BDZL,
          618 => Opcode::BDZLA,
          619 => Opcode::BDZLAm,
          620 => Opcode::BDZLAp,
          621 => Opcode::BDZLR,
          622 => Opcode::BDZLR8,
          623 => Opcode::BDZLRL,
          624 => Opcode::BDZLRLm,
          625 => Opcode::BDZLRLp,
          626 => Opcode::BDZLRm,
          627 => Opcode::BDZLRp,
          628 => Opcode::BDZLm,
          629 => Opcode::BDZLp,
          630 => Opcode::BDZm,
          631 => Opcode::BDZp,
          632 => Opcode::BL,
          633 => Opcode::BL8,
          634 => Opcode::BL8_NOP,
          635 => Opcode::BL8_NOP_RM,
          636 => Opcode::BL8_NOP_TLS,
          637 => Opcode::BL8_NOTOC,
          638 => Opcode::BL8_NOTOC_RM,
          639 => Opcode::BL8_NOTOC_TLS,
          640 => Opcode::BL8_RM,
          641 => Opcode::BL8_TLS,
          642 => Opcode::BL8_TLS_,
          643 => Opcode::BLA,
          644 => Opcode::BLA8,
          645 => Opcode::BLA8_NOP,
          646 => Opcode::BLA8_NOP_RM,
          647 => Opcode::BLA8_RM,
          648 => Opcode::BLA_RM,
          649 => Opcode::BLR,
          650 => Opcode::BLR8,
          651 => Opcode::BLRL,
          652 => Opcode::BL_NOP,
          653 => Opcode::BL_NOP_RM,
          654 => Opcode::BL_RM,
          655 => Opcode::BL_TLS,
          656 => Opcode::BPERMD,
          657 => Opcode::BRD,
          658 => Opcode::BRH,
          659 => Opcode::BRH8,
          660 => Opcode::BRINC,
          661 => Opcode::BRW,
          662 => Opcode::BRW8,
          663 => Opcode::CBCDTD,
          664 => Opcode::CBCDTD8,
          665 => Opcode::CDTBCD,
          666 => Opcode::CDTBCD8,
          667 => Opcode::CFUGED,
          668 => Opcode::CLRBHRB,
          669 => Opcode::CMPB,
          670 => Opcode::CMPB8,
          671 => Opcode::CMPD,
          672 => Opcode::CMPDI,
          673 => Opcode::CMPEQB,
          674 => Opcode::CMPLD,
          675 => Opcode::CMPLDI,
          676 => Opcode::CMPLW,
          677 => Opcode::CMPLWI,
          678 => Opcode::CMPRB,
          679 => Opcode::CMPRB8,
          680 => Opcode::CMPW,
          681 => Opcode::CMPWI,
          682 => Opcode::CNTLZD,
          683 => Opcode::CNTLZDM,
          684 => Opcode::CNTLZD_rec,
          685 => Opcode::CNTLZW,
          686 => Opcode::CNTLZW8,
          687 => Opcode::CNTLZW8_rec,
          688 => Opcode::CNTLZW_rec,
          689 => Opcode::CNTTZD,
          690 => Opcode::CNTTZDM,
          691 => Opcode::CNTTZD_rec,
          692 => Opcode::CNTTZW,
          693 => Opcode::CNTTZW8,
          694 => Opcode::CNTTZW8_rec,
          695 => Opcode::CNTTZW_rec,
          696 => Opcode::CP_ABORT,
          697 => Opcode::CP_COPY,
          698 => Opcode::CP_COPY8,
          699 => Opcode::CP_PASTE8_rec,
          700 => Opcode::CP_PASTE_rec,
          701 => Opcode::CR6SET,
          702 => Opcode::CR6UNSET,
          703 => Opcode::CRAND,
          704 => Opcode::CRANDC,
          705 => Opcode::CREQV,
          706 => Opcode::CRNAND,
          707 => Opcode::CRNOR,
          708 => Opcode::CRNOT,
          709 => Opcode::CROR,
          710 => Opcode::CRORC,
          711 => Opcode::CRSET,
          712 => Opcode::CRUNSET,
          713 => Opcode::CRXOR,
          714 => Opcode::CTRL_DEP,
          715 => Opcode::DADD,
          716 => Opcode::DADDQ,
          717 => Opcode::DADDQ_rec,
          718 => Opcode::DADD_rec,
          719 => Opcode::DARN,
          720 => Opcode::DCBA,
          721 => Opcode::DCBF,
          722 => Opcode::DCBFEP,
          723 => Opcode::DCBI,
          724 => Opcode::DCBST,
          725 => Opcode::DCBSTEP,
          726 => Opcode::DCBT,
          727 => Opcode::DCBTEP,
          728 => Opcode::DCBTST,
          729 => Opcode::DCBTSTEP,
          730 => Opcode::DCBZ,
          731 => Opcode::DCBZEP,
          732 => Opcode::DCBZL,
          733 => Opcode::DCBZLEP,
          734 => Opcode::DCCCI,
          735 => Opcode::DCFFIX,
          736 => Opcode::DCFFIXQ,
          737 => Opcode::DCFFIXQQ,
          738 => Opcode::DCFFIXQ_rec,
          739 => Opcode::DCFFIX_rec,
          740 => Opcode::DCMPO,
          741 => Opcode::DCMPOQ,
          742 => Opcode::DCMPU,
          743 => Opcode::DCMPUQ,
          744 => Opcode::DCTDP,
          745 => Opcode::DCTDP_rec,
          746 => Opcode::DCTFIX,
          747 => Opcode::DCTFIXQ,
          748 => Opcode::DCTFIXQQ,
          749 => Opcode::DCTFIXQ_rec,
          750 => Opcode::DCTFIX_rec,
          751 => Opcode::DCTQPQ,
          752 => Opcode::DCTQPQ_rec,
          753 => Opcode::DDEDPD,
          754 => Opcode::DDEDPDQ,
          755 => Opcode::DDEDPDQ_rec,
          756 => Opcode::DDEDPD_rec,
          757 => Opcode::DDIV,
          758 => Opcode::DDIVQ,
          759 => Opcode::DDIVQ_rec,
          760 => Opcode::DDIV_rec,
          761 => Opcode::DENBCD,
          762 => Opcode::DENBCDQ,
          763 => Opcode::DENBCDQ_rec,
          764 => Opcode::DENBCD_rec,
          765 => Opcode::DIEX,
          766 => Opcode::DIEXQ,
          767 => Opcode::DIEXQ_rec,
          768 => Opcode::DIEX_rec,
          769 => Opcode::DIVD,
          770 => Opcode::DIVDE,
          771 => Opcode::DIVDEO,
          772 => Opcode::DIVDEO_rec,
          773 => Opcode::DIVDEU,
          774 => Opcode::DIVDEUO,
          775 => Opcode::DIVDEUO_rec,
          776 => Opcode::DIVDEU_rec,
          777 => Opcode::DIVDE_rec,
          778 => Opcode::DIVDO,
          779 => Opcode::DIVDO_rec,
          780 => Opcode::DIVDU,
          781 => Opcode::DIVDUO,
          782 => Opcode::DIVDUO_rec,
          783 => Opcode::DIVDU_rec,
          784 => Opcode::DIVD_rec,
          785 => Opcode::DIVW,
          786 => Opcode::DIVWE,
          787 => Opcode::DIVWEO,
          788 => Opcode::DIVWEO_rec,
          789 => Opcode::DIVWEU,
          790 => Opcode::DIVWEUO,
          791 => Opcode::DIVWEUO_rec,
          792 => Opcode::DIVWEU_rec,
          793 => Opcode::DIVWE_rec,
          794 => Opcode::DIVWO,
          795 => Opcode::DIVWO_rec,
          796 => Opcode::DIVWU,
          797 => Opcode::DIVWUO,
          798 => Opcode::DIVWUO_rec,
          799 => Opcode::DIVWU_rec,
          800 => Opcode::DIVW_rec,
          801 => Opcode::DMMR,
          802 => Opcode::DMSETDMRZ,
          803 => Opcode::DMUL,
          804 => Opcode::DMULQ,
          805 => Opcode::DMULQ_rec,
          806 => Opcode::DMUL_rec,
          807 => Opcode::DMXOR,
          808 => Opcode::DMXXEXTFDMR256,
          809 => Opcode::DMXXEXTFDMR512,
          810 => Opcode::DMXXEXTFDMR512_HI,
          811 => Opcode::DMXXINSTFDMR256,
          812 => Opcode::DMXXINSTFDMR512,
          813 => Opcode::DMXXINSTFDMR512_HI,
          814 => Opcode::DQUA,
          815 => Opcode::DQUAI,
          816 => Opcode::DQUAIQ,
          817 => Opcode::DQUAIQ_rec,
          818 => Opcode::DQUAI_rec,
          819 => Opcode::DQUAQ,
          820 => Opcode::DQUAQ_rec,
          821 => Opcode::DQUA_rec,
          822 => Opcode::DRDPQ,
          823 => Opcode::DRDPQ_rec,
          824 => Opcode::DRINTN,
          825 => Opcode::DRINTNQ,
          826 => Opcode::DRINTNQ_rec,
          827 => Opcode::DRINTN_rec,
          828 => Opcode::DRINTX,
          829 => Opcode::DRINTXQ,
          830 => Opcode::DRINTXQ_rec,
          831 => Opcode::DRINTX_rec,
          832 => Opcode::DRRND,
          833 => Opcode::DRRNDQ,
          834 => Opcode::DRRNDQ_rec,
          835 => Opcode::DRRND_rec,
          836 => Opcode::DRSP,
          837 => Opcode::DRSP_rec,
          838 => Opcode::DSCLI,
          839 => Opcode::DSCLIQ,
          840 => Opcode::DSCLIQ_rec,
          841 => Opcode::DSCLI_rec,
          842 => Opcode::DSCRI,
          843 => Opcode::DSCRIQ,
          844 => Opcode::DSCRIQ_rec,
          845 => Opcode::DSCRI_rec,
          846 => Opcode::DSS,
          847 => Opcode::DSSALL,
          848 => Opcode::DST,
          849 => Opcode::DST64,
          850 => Opcode::DSTST,
          851 => Opcode::DSTST64,
          852 => Opcode::DSTSTT,
          853 => Opcode::DSTSTT64,
          854 => Opcode::DSTT,
          855 => Opcode::DSTT64,
          856 => Opcode::DSUB,
          857 => Opcode::DSUBQ,
          858 => Opcode::DSUBQ_rec,
          859 => Opcode::DSUB_rec,
          860 => Opcode::DTSTDC,
          861 => Opcode::DTSTDCQ,
          862 => Opcode::DTSTDG,
          863 => Opcode::DTSTDGQ,
          864 => Opcode::DTSTEX,
          865 => Opcode::DTSTEXQ,
          866 => Opcode::DTSTSF,
          867 => Opcode::DTSTSFI,
          868 => Opcode::DTSTSFIQ,
          869 => Opcode::DTSTSFQ,
          870 => Opcode::DXEX,
          871 => Opcode::DXEXQ,
          872 => Opcode::DXEXQ_rec,
          873 => Opcode::DXEX_rec,
          874 => Opcode::DYNALLOC,
          875 => Opcode::DYNALLOC8,
          876 => Opcode::DYNAREAOFFSET,
          877 => Opcode::DYNAREAOFFSET8,
          878 => Opcode::DecreaseCTR8loop,
          879 => Opcode::DecreaseCTRloop,
          880 => Opcode::EFDABS,
          881 => Opcode::EFDADD,
          882 => Opcode::EFDCFS,
          883 => Opcode::EFDCFSF,
          884 => Opcode::EFDCFSI,
          885 => Opcode::EFDCFSID,
          886 => Opcode::EFDCFUF,
          887 => Opcode::EFDCFUI,
          888 => Opcode::EFDCFUID,
          889 => Opcode::EFDCMPEQ,
          890 => Opcode::EFDCMPGT,
          891 => Opcode::EFDCMPLT,
          892 => Opcode::EFDCTSF,
          893 => Opcode::EFDCTSI,
          894 => Opcode::EFDCTSIDZ,
          895 => Opcode::EFDCTSIZ,
          896 => Opcode::EFDCTUF,
          897 => Opcode::EFDCTUI,
          898 => Opcode::EFDCTUIDZ,
          899 => Opcode::EFDCTUIZ,
          900 => Opcode::EFDDIV,
          901 => Opcode::EFDMUL,
          902 => Opcode::EFDNABS,
          903 => Opcode::EFDNEG,
          904 => Opcode::EFDSUB,
          905 => Opcode::EFDTSTEQ,
          906 => Opcode::EFDTSTGT,
          907 => Opcode::EFDTSTLT,
          908 => Opcode::EFSABS,
          909 => Opcode::EFSADD,
          910 => Opcode::EFSCFD,
          911 => Opcode::EFSCFSF,
          912 => Opcode::EFSCFSI,
          913 => Opcode::EFSCFUF,
          914 => Opcode::EFSCFUI,
          915 => Opcode::EFSCMPEQ,
          916 => Opcode::EFSCMPGT,
          917 => Opcode::EFSCMPLT,
          918 => Opcode::EFSCTSF,
          919 => Opcode::EFSCTSI,
          920 => Opcode::EFSCTSIZ,
          921 => Opcode::EFSCTUF,
          922 => Opcode::EFSCTUI,
          923 => Opcode::EFSCTUIZ,
          924 => Opcode::EFSDIV,
          925 => Opcode::EFSMUL,
          926 => Opcode::EFSNABS,
          927 => Opcode::EFSNEG,
          928 => Opcode::EFSSUB,
          929 => Opcode::EFSTSTEQ,
          930 => Opcode::EFSTSTGT,
          931 => Opcode::EFSTSTLT,
          932 => Opcode::EH_SjLj_LongJmp32,
          933 => Opcode::EH_SjLj_LongJmp64,
          934 => Opcode::EH_SjLj_SetJmp32,
          935 => Opcode::EH_SjLj_SetJmp64,
          936 => Opcode::EH_SjLj_Setup,
          937 => Opcode::EQV,
          938 => Opcode::EQV8,
          939 => Opcode::EQV8_rec,
          940 => Opcode::EQV_rec,
          941 => Opcode::EVABS,
          942 => Opcode::EVADDIW,
          943 => Opcode::EVADDSMIAAW,
          944 => Opcode::EVADDSSIAAW,
          945 => Opcode::EVADDUMIAAW,
          946 => Opcode::EVADDUSIAAW,
          947 => Opcode::EVADDW,
          948 => Opcode::EVAND,
          949 => Opcode::EVANDC,
          950 => Opcode::EVCMPEQ,
          951 => Opcode::EVCMPGTS,
          952 => Opcode::EVCMPGTU,
          953 => Opcode::EVCMPLTS,
          954 => Opcode::EVCMPLTU,
          955 => Opcode::EVCNTLSW,
          956 => Opcode::EVCNTLZW,
          957 => Opcode::EVDIVWS,
          958 => Opcode::EVDIVWU,
          959 => Opcode::EVEQV,
          960 => Opcode::EVEXTSB,
          961 => Opcode::EVEXTSH,
          962 => Opcode::EVFSABS,
          963 => Opcode::EVFSADD,
          964 => Opcode::EVFSCFSF,
          965 => Opcode::EVFSCFSI,
          966 => Opcode::EVFSCFUF,
          967 => Opcode::EVFSCFUI,
          968 => Opcode::EVFSCMPEQ,
          969 => Opcode::EVFSCMPGT,
          970 => Opcode::EVFSCMPLT,
          971 => Opcode::EVFSCTSF,
          972 => Opcode::EVFSCTSI,
          973 => Opcode::EVFSCTSIZ,
          974 => Opcode::EVFSCTUF,
          975 => Opcode::EVFSCTUI,
          976 => Opcode::EVFSCTUIZ,
          977 => Opcode::EVFSDIV,
          978 => Opcode::EVFSMUL,
          979 => Opcode::EVFSNABS,
          980 => Opcode::EVFSNEG,
          981 => Opcode::EVFSSUB,
          982 => Opcode::EVFSTSTEQ,
          983 => Opcode::EVFSTSTGT,
          984 => Opcode::EVFSTSTLT,
          985 => Opcode::EVLDD,
          986 => Opcode::EVLDDX,
          987 => Opcode::EVLDH,
          988 => Opcode::EVLDHX,
          989 => Opcode::EVLDW,
          990 => Opcode::EVLDWX,
          991 => Opcode::EVLHHESPLAT,
          992 => Opcode::EVLHHESPLATX,
          993 => Opcode::EVLHHOSSPLAT,
          994 => Opcode::EVLHHOSSPLATX,
          995 => Opcode::EVLHHOUSPLAT,
          996 => Opcode::EVLHHOUSPLATX,
          997 => Opcode::EVLWHE,
          998 => Opcode::EVLWHEX,
          999 => Opcode::EVLWHOS,
          1000 => Opcode::EVLWHOSX,
          1001 => Opcode::EVLWHOU,
          1002 => Opcode::EVLWHOUX,
          1003 => Opcode::EVLWHSPLAT,
          1004 => Opcode::EVLWHSPLATX,
          1005 => Opcode::EVLWWSPLAT,
          1006 => Opcode::EVLWWSPLATX,
          1007 => Opcode::EVMERGEHI,
          1008 => Opcode::EVMERGEHILO,
          1009 => Opcode::EVMERGELO,
          1010 => Opcode::EVMERGELOHI,
          1011 => Opcode::EVMHEGSMFAA,
          1012 => Opcode::EVMHEGSMFAN,
          1013 => Opcode::EVMHEGSMIAA,
          1014 => Opcode::EVMHEGSMIAN,
          1015 => Opcode::EVMHEGUMIAA,
          1016 => Opcode::EVMHEGUMIAN,
          1017 => Opcode::EVMHESMF,
          1018 => Opcode::EVMHESMFA,
          1019 => Opcode::EVMHESMFAAW,
          1020 => Opcode::EVMHESMFANW,
          1021 => Opcode::EVMHESMI,
          1022 => Opcode::EVMHESMIA,
          1023 => Opcode::EVMHESMIAAW,
          1024 => Opcode::EVMHESMIANW,
          1025 => Opcode::EVMHESSF,
          1026 => Opcode::EVMHESSFA,
          1027 => Opcode::EVMHESSFAAW,
          1028 => Opcode::EVMHESSFANW,
          1029 => Opcode::EVMHESSIAAW,
          1030 => Opcode::EVMHESSIANW,
          1031 => Opcode::EVMHEUMI,
          1032 => Opcode::EVMHEUMIA,
          1033 => Opcode::EVMHEUMIAAW,
          1034 => Opcode::EVMHEUMIANW,
          1035 => Opcode::EVMHEUSIAAW,
          1036 => Opcode::EVMHEUSIANW,
          1037 => Opcode::EVMHOGSMFAA,
          1038 => Opcode::EVMHOGSMFAN,
          1039 => Opcode::EVMHOGSMIAA,
          1040 => Opcode::EVMHOGSMIAN,
          1041 => Opcode::EVMHOGUMIAA,
          1042 => Opcode::EVMHOGUMIAN,
          1043 => Opcode::EVMHOSMF,
          1044 => Opcode::EVMHOSMFA,
          1045 => Opcode::EVMHOSMFAAW,
          1046 => Opcode::EVMHOSMFANW,
          1047 => Opcode::EVMHOSMI,
          1048 => Opcode::EVMHOSMIA,
          1049 => Opcode::EVMHOSMIAAW,
          1050 => Opcode::EVMHOSMIANW,
          1051 => Opcode::EVMHOSSF,
          1052 => Opcode::EVMHOSSFA,
          1053 => Opcode::EVMHOSSFAAW,
          1054 => Opcode::EVMHOSSFANW,
          1055 => Opcode::EVMHOSSIAAW,
          1056 => Opcode::EVMHOSSIANW,
          1057 => Opcode::EVMHOUMI,
          1058 => Opcode::EVMHOUMIA,
          1059 => Opcode::EVMHOUMIAAW,
          1060 => Opcode::EVMHOUMIANW,
          1061 => Opcode::EVMHOUSIAAW,
          1062 => Opcode::EVMHOUSIANW,
          1063 => Opcode::EVMRA,
          1064 => Opcode::EVMWHSMF,
          1065 => Opcode::EVMWHSMFA,
          1066 => Opcode::EVMWHSMI,
          1067 => Opcode::EVMWHSMIA,
          1068 => Opcode::EVMWHSSF,
          1069 => Opcode::EVMWHSSFA,
          1070 => Opcode::EVMWHUMI,
          1071 => Opcode::EVMWHUMIA,
          1072 => Opcode::EVMWLSMIAAW,
          1073 => Opcode::EVMWLSMIANW,
          1074 => Opcode::EVMWLSSIAAW,
          1075 => Opcode::EVMWLSSIANW,
          1076 => Opcode::EVMWLUMI,
          1077 => Opcode::EVMWLUMIA,
          1078 => Opcode::EVMWLUMIAAW,
          1079 => Opcode::EVMWLUMIANW,
          1080 => Opcode::EVMWLUSIAAW,
          1081 => Opcode::EVMWLUSIANW,
          1082 => Opcode::EVMWSMF,
          1083 => Opcode::EVMWSMFA,
          1084 => Opcode::EVMWSMFAA,
          1085 => Opcode::EVMWSMFAN,
          1086 => Opcode::EVMWSMI,
          1087 => Opcode::EVMWSMIA,
          1088 => Opcode::EVMWSMIAA,
          1089 => Opcode::EVMWSMIAN,
          1090 => Opcode::EVMWSSF,
          1091 => Opcode::EVMWSSFA,
          1092 => Opcode::EVMWSSFAA,
          1093 => Opcode::EVMWSSFAN,
          1094 => Opcode::EVMWUMI,
          1095 => Opcode::EVMWUMIA,
          1096 => Opcode::EVMWUMIAA,
          1097 => Opcode::EVMWUMIAN,
          1098 => Opcode::EVNAND,
          1099 => Opcode::EVNEG,
          1100 => Opcode::EVNOR,
          1101 => Opcode::EVOR,
          1102 => Opcode::EVORC,
          1103 => Opcode::EVRLW,
          1104 => Opcode::EVRLWI,
          1105 => Opcode::EVRNDW,
          1106 => Opcode::EVSEL,
          1107 => Opcode::EVSLW,
          1108 => Opcode::EVSLWI,
          1109 => Opcode::EVSPLATFI,
          1110 => Opcode::EVSPLATI,
          1111 => Opcode::EVSRWIS,
          1112 => Opcode::EVSRWIU,
          1113 => Opcode::EVSRWS,
          1114 => Opcode::EVSRWU,
          1115 => Opcode::EVSTDD,
          1116 => Opcode::EVSTDDX,
          1117 => Opcode::EVSTDH,
          1118 => Opcode::EVSTDHX,
          1119 => Opcode::EVSTDW,
          1120 => Opcode::EVSTDWX,
          1121 => Opcode::EVSTWHE,
          1122 => Opcode::EVSTWHEX,
          1123 => Opcode::EVSTWHO,
          1124 => Opcode::EVSTWHOX,
          1125 => Opcode::EVSTWWE,
          1126 => Opcode::EVSTWWEX,
          1127 => Opcode::EVSTWWO,
          1128 => Opcode::EVSTWWOX,
          1129 => Opcode::EVSUBFSMIAAW,
          1130 => Opcode::EVSUBFSSIAAW,
          1131 => Opcode::EVSUBFUMIAAW,
          1132 => Opcode::EVSUBFUSIAAW,
          1133 => Opcode::EVSUBFW,
          1134 => Opcode::EVSUBIFW,
          1135 => Opcode::EVXOR,
          1136 => Opcode::EXTSB,
          1137 => Opcode::EXTSB8,
          1138 => Opcode::EXTSB8_32_64,
          1139 => Opcode::EXTSB8_rec,
          1140 => Opcode::EXTSB_rec,
          1141 => Opcode::EXTSH,
          1142 => Opcode::EXTSH8,
          1143 => Opcode::EXTSH8_32_64,
          1144 => Opcode::EXTSH8_rec,
          1145 => Opcode::EXTSH_rec,
          1146 => Opcode::EXTSW,
          1147 => Opcode::EXTSWSLI,
          1148 => Opcode::EXTSWSLI_32_64,
          1149 => Opcode::EXTSWSLI_32_64_rec,
          1150 => Opcode::EXTSWSLI_rec,
          1151 => Opcode::EXTSW_32,
          1152 => Opcode::EXTSW_32_64,
          1153 => Opcode::EXTSW_32_64_rec,
          1154 => Opcode::EXTSW_rec,
          1155 => Opcode::EnforceIEIO,
          1156 => Opcode::FABSD,
          1157 => Opcode::FABSD_rec,
          1158 => Opcode::FABSS,
          1159 => Opcode::FABSS_rec,
          1160 => Opcode::FADD,
          1161 => Opcode::FADDS,
          1162 => Opcode::FADDS_rec,
          1163 => Opcode::FADD_rec,
          1164 => Opcode::FADDrtz,
          1165 => Opcode::FCFID,
          1166 => Opcode::FCFIDS,
          1167 => Opcode::FCFIDS_rec,
          1168 => Opcode::FCFIDU,
          1169 => Opcode::FCFIDUS,
          1170 => Opcode::FCFIDUS_rec,
          1171 => Opcode::FCFIDU_rec,
          1172 => Opcode::FCFID_rec,
          1173 => Opcode::FCMPOD,
          1174 => Opcode::FCMPOS,
          1175 => Opcode::FCMPUD,
          1176 => Opcode::FCMPUS,
          1177 => Opcode::FCPSGND,
          1178 => Opcode::FCPSGND_rec,
          1179 => Opcode::FCPSGNS,
          1180 => Opcode::FCPSGNS_rec,
          1181 => Opcode::FCTID,
          1182 => Opcode::FCTIDU,
          1183 => Opcode::FCTIDUZ,
          1184 => Opcode::FCTIDUZ_rec,
          1185 => Opcode::FCTIDU_rec,
          1186 => Opcode::FCTIDZ,
          1187 => Opcode::FCTIDZ_rec,
          1188 => Opcode::FCTID_rec,
          1189 => Opcode::FCTIW,
          1190 => Opcode::FCTIWU,
          1191 => Opcode::FCTIWUZ,
          1192 => Opcode::FCTIWUZ_rec,
          1193 => Opcode::FCTIWU_rec,
          1194 => Opcode::FCTIWZ,
          1195 => Opcode::FCTIWZ_rec,
          1196 => Opcode::FCTIW_rec,
          1197 => Opcode::FDIV,
          1198 => Opcode::FDIVS,
          1199 => Opcode::FDIVS_rec,
          1200 => Opcode::FDIV_rec,
          1201 => Opcode::FENCE,
          1202 => Opcode::FMADD,
          1203 => Opcode::FMADDS,
          1204 => Opcode::FMADDS_rec,
          1205 => Opcode::FMADD_rec,
          1206 => Opcode::FMR,
          1207 => Opcode::FMR_rec,
          1208 => Opcode::FMSUB,
          1209 => Opcode::FMSUBS,
          1210 => Opcode::FMSUBS_rec,
          1211 => Opcode::FMSUB_rec,
          1212 => Opcode::FMUL,
          1213 => Opcode::FMULS,
          1214 => Opcode::FMULS_rec,
          1215 => Opcode::FMUL_rec,
          1216 => Opcode::FNABSD,
          1217 => Opcode::FNABSD_rec,
          1218 => Opcode::FNABSS,
          1219 => Opcode::FNABSS_rec,
          1220 => Opcode::FNEGD,
          1221 => Opcode::FNEGD_rec,
          1222 => Opcode::FNEGS,
          1223 => Opcode::FNEGS_rec,
          1224 => Opcode::FNMADD,
          1225 => Opcode::FNMADDS,
          1226 => Opcode::FNMADDS_rec,
          1227 => Opcode::FNMADD_rec,
          1228 => Opcode::FNMSUB,
          1229 => Opcode::FNMSUBS,
          1230 => Opcode::FNMSUBS_rec,
          1231 => Opcode::FNMSUB_rec,
          1232 => Opcode::FRE,
          1233 => Opcode::FRES,
          1234 => Opcode::FRES_rec,
          1235 => Opcode::FRE_rec,
          1236 => Opcode::FRIMD,
          1237 => Opcode::FRIMD_rec,
          1238 => Opcode::FRIMS,
          1239 => Opcode::FRIMS_rec,
          1240 => Opcode::FRIND,
          1241 => Opcode::FRIND_rec,
          1242 => Opcode::FRINS,
          1243 => Opcode::FRINS_rec,
          1244 => Opcode::FRIPD,
          1245 => Opcode::FRIPD_rec,
          1246 => Opcode::FRIPS,
          1247 => Opcode::FRIPS_rec,
          1248 => Opcode::FRIZD,
          1249 => Opcode::FRIZD_rec,
          1250 => Opcode::FRIZS,
          1251 => Opcode::FRIZS_rec,
          1252 => Opcode::FRSP,
          1253 => Opcode::FRSP_rec,
          1254 => Opcode::FRSQRTE,
          1255 => Opcode::FRSQRTES,
          1256 => Opcode::FRSQRTES_rec,
          1257 => Opcode::FRSQRTE_rec,
          1258 => Opcode::FSELD,
          1259 => Opcode::FSELD_rec,
          1260 => Opcode::FSELS,
          1261 => Opcode::FSELS_rec,
          1262 => Opcode::FSQRT,
          1263 => Opcode::FSQRTS,
          1264 => Opcode::FSQRTS_rec,
          1265 => Opcode::FSQRT_rec,
          1266 => Opcode::FSUB,
          1267 => Opcode::FSUBS,
          1268 => Opcode::FSUBS_rec,
          1269 => Opcode::FSUB_rec,
          1270 => Opcode::FTDIV,
          1271 => Opcode::FTSQRT,
          1272 => Opcode::GETtlsADDR,
          1273 => Opcode::GETtlsADDR32,
          1274 => Opcode::GETtlsADDR32AIX,
          1275 => Opcode::GETtlsADDR64AIX,
          1276 => Opcode::GETtlsADDRPCREL,
          1277 => Opcode::GETtlsMOD32AIX,
          1278 => Opcode::GETtlsMOD64AIX,
          1279 => Opcode::GETtlsTpointer32AIX,
          1280 => Opcode::GETtlsldADDR,
          1281 => Opcode::GETtlsldADDR32,
          1282 => Opcode::GETtlsldADDRPCREL,
          1283 => Opcode::HASHCHK,
          1284 => Opcode::HASHCHK8,
          1285 => Opcode::HASHCHKP,
          1286 => Opcode::HASHCHKP8,
          1287 => Opcode::HASHST,
          1288 => Opcode::HASHST8,
          1289 => Opcode::HASHSTP,
          1290 => Opcode::HASHSTP8,
          1291 => Opcode::HRFID,
          1292 => Opcode::ICBI,
          1293 => Opcode::ICBIEP,
          1294 => Opcode::ICBLC,
          1295 => Opcode::ICBLQ,
          1296 => Opcode::ICBT,
          1297 => Opcode::ICBTLS,
          1298 => Opcode::ICCCI,
          1299 => Opcode::ISEL,
          1300 => Opcode::ISEL8,
          1301 => Opcode::ISYNC,
          1302 => Opcode::LA,
          1303 => Opcode::LA8,
          1304 => Opcode::LBARX,
          1305 => Opcode::LBARXL,
          1306 => Opcode::LBEPX,
          1307 => Opcode::LBZ,
          1308 => Opcode::LBZ8,
          1309 => Opcode::LBZCIX,
          1310 => Opcode::LBZU,
          1311 => Opcode::LBZU8,
          1312 => Opcode::LBZUX,
          1313 => Opcode::LBZUX8,
          1314 => Opcode::LBZX,
          1315 => Opcode::LBZX8,
          1316 => Opcode::LBZXTLS,
          1317 => Opcode::LBZXTLS_,
          1318 => Opcode::LBZXTLS_32,
          1319 => Opcode::LD,
          1320 => Opcode::LDARX,
          1321 => Opcode::LDARXL,
          1322 => Opcode::LDAT,
          1323 => Opcode::LDBRX,
          1324 => Opcode::LDCIX,
          1325 => Opcode::LDU,
          1326 => Opcode::LDUX,
          1327 => Opcode::LDX,
          1328 => Opcode::LDXTLS,
          1329 => Opcode::LDXTLS_,
          1330 => Opcode::LDgotTprelL,
          1331 => Opcode::LDgotTprelL32,
          1332 => Opcode::LDtoc,
          1333 => Opcode::LDtocBA,
          1334 => Opcode::LDtocCPT,
          1335 => Opcode::LDtocJTI,
          1336 => Opcode::LDtocL,
          1337 => Opcode::LFD,
          1338 => Opcode::LFDEPX,
          1339 => Opcode::LFDU,
          1340 => Opcode::LFDUX,
          1341 => Opcode::LFDX,
          1342 => Opcode::LFDXTLS,
          1343 => Opcode::LFDXTLS_,
          1344 => Opcode::LFIWAX,
          1345 => Opcode::LFIWZX,
          1346 => Opcode::LFS,
          1347 => Opcode::LFSU,
          1348 => Opcode::LFSUX,
          1349 => Opcode::LFSX,
          1350 => Opcode::LFSXTLS,
          1351 => Opcode::LFSXTLS_,
          1352 => Opcode::LHA,
          1353 => Opcode::LHA8,
          1354 => Opcode::LHARX,
          1355 => Opcode::LHARXL,
          1356 => Opcode::LHAU,
          1357 => Opcode::LHAU8,
          1358 => Opcode::LHAUX,
          1359 => Opcode::LHAUX8,
          1360 => Opcode::LHAX,
          1361 => Opcode::LHAX8,
          1362 => Opcode::LHAXTLS,
          1363 => Opcode::LHAXTLS_,
          1364 => Opcode::LHAXTLS_32,
          1365 => Opcode::LHBRX,
          1366 => Opcode::LHBRX8,
          1367 => Opcode::LHEPX,
          1368 => Opcode::LHZ,
          1369 => Opcode::LHZ8,
          1370 => Opcode::LHZCIX,
          1371 => Opcode::LHZU,
          1372 => Opcode::LHZU8,
          1373 => Opcode::LHZUX,
          1374 => Opcode::LHZUX8,
          1375 => Opcode::LHZX,
          1376 => Opcode::LHZX8,
          1377 => Opcode::LHZXTLS,
          1378 => Opcode::LHZXTLS_,
          1379 => Opcode::LHZXTLS_32,
          1380 => Opcode::LI,
          1381 => Opcode::LI8,
          1382 => Opcode::LIS,
          1383 => Opcode::LIS8,
          1384 => Opcode::LMW,
          1385 => Opcode::LQ,
          1386 => Opcode::LQARX,
          1387 => Opcode::LQARXL,
          1388 => Opcode::LQX_PSEUDO,
          1389 => Opcode::LSWI,
          1390 => Opcode::LVEBX,
          1391 => Opcode::LVEHX,
          1392 => Opcode::LVEWX,
          1393 => Opcode::LVSL,
          1394 => Opcode::LVSR,
          1395 => Opcode::LVX,
          1396 => Opcode::LVXL,
          1397 => Opcode::LWA,
          1398 => Opcode::LWARX,
          1399 => Opcode::LWARXL,
          1400 => Opcode::LWAT,
          1401 => Opcode::LWAUX,
          1402 => Opcode::LWAX,
          1403 => Opcode::LWAXTLS,
          1404 => Opcode::LWAXTLS_,
          1405 => Opcode::LWAXTLS_32,
          1406 => Opcode::LWAX_32,
          1407 => Opcode::LWA_32,
          1408 => Opcode::LWBRX,
          1409 => Opcode::LWBRX8,
          1410 => Opcode::LWEPX,
          1411 => Opcode::LWZ,
          1412 => Opcode::LWZ8,
          1413 => Opcode::LWZCIX,
          1414 => Opcode::LWZU,
          1415 => Opcode::LWZU8,
          1416 => Opcode::LWZUX,
          1417 => Opcode::LWZUX8,
          1418 => Opcode::LWZX,
          1419 => Opcode::LWZX8,
          1420 => Opcode::LWZXTLS,
          1421 => Opcode::LWZXTLS_,
          1422 => Opcode::LWZXTLS_32,
          1423 => Opcode::LWZtoc,
          1424 => Opcode::LWZtocL,
          1425 => Opcode::LXSD,
          1426 => Opcode::LXSDX,
          1427 => Opcode::LXSIBZX,
          1428 => Opcode::LXSIHZX,
          1429 => Opcode::LXSIWAX,
          1430 => Opcode::LXSIWZX,
          1431 => Opcode::LXSSP,
          1432 => Opcode::LXSSPX,
          1433 => Opcode::LXV,
          1434 => Opcode::LXVB16X,
          1435 => Opcode::LXVD2X,
          1436 => Opcode::LXVDSX,
          1437 => Opcode::LXVH8X,
          1438 => Opcode::LXVKQ,
          1439 => Opcode::LXVL,
          1440 => Opcode::LXVLL,
          1441 => Opcode::LXVP,
          1442 => Opcode::LXVPRL,
          1443 => Opcode::LXVPRLL,
          1444 => Opcode::LXVPX,
          1445 => Opcode::LXVRBX,
          1446 => Opcode::LXVRDX,
          1447 => Opcode::LXVRHX,
          1448 => Opcode::LXVRL,
          1449 => Opcode::LXVRLL,
          1450 => Opcode::LXVRWX,
          1451 => Opcode::LXVW4X,
          1452 => Opcode::LXVWSX,
          1453 => Opcode::LXVX,
          1454 => Opcode::MADDHD,
          1455 => Opcode::MADDHDU,
          1456 => Opcode::MADDLD,
          1457 => Opcode::MADDLD8,
          1458 => Opcode::MBAR,
          1459 => Opcode::MCRF,
          1460 => Opcode::MCRFS,
          1461 => Opcode::MCRXRX,
          1462 => Opcode::MFBHRBE,
          1463 => Opcode::MFCR,
          1464 => Opcode::MFCR8,
          1465 => Opcode::MFCTR,
          1466 => Opcode::MFCTR8,
          1467 => Opcode::MFDCR,
          1468 => Opcode::MFFS,
          1469 => Opcode::MFFSCDRN,
          1470 => Opcode::MFFSCDRNI,
          1471 => Opcode::MFFSCE,
          1472 => Opcode::MFFSCRN,
          1473 => Opcode::MFFSCRNI,
          1474 => Opcode::MFFSL,
          1475 => Opcode::MFFS_rec,
          1476 => Opcode::MFLR,
          1477 => Opcode::MFLR8,
          1478 => Opcode::MFMSR,
          1479 => Opcode::MFOCRF,
          1480 => Opcode::MFOCRF8,
          1481 => Opcode::MFPMR,
          1482 => Opcode::MFSPR,
          1483 => Opcode::MFSPR8,
          1484 => Opcode::MFSR,
          1485 => Opcode::MFSRIN,
          1486 => Opcode::MFTB,
          1487 => Opcode::MFTB8,
          1488 => Opcode::MFUDSCR,
          1489 => Opcode::MFVRD,
          1490 => Opcode::MFVRSAVE,
          1491 => Opcode::MFVRSAVEv,
          1492 => Opcode::MFVRWZ,
          1493 => Opcode::MFVSCR,
          1494 => Opcode::MFVSRD,
          1495 => Opcode::MFVSRLD,
          1496 => Opcode::MFVSRWZ,
          1497 => Opcode::MODSD,
          1498 => Opcode::MODSW,
          1499 => Opcode::MODUD,
          1500 => Opcode::MODUW,
          1501 => Opcode::MSGSYNC,
          1502 => Opcode::MSYNC,
          1503 => Opcode::MTCRF,
          1504 => Opcode::MTCRF8,
          1505 => Opcode::MTCTR,
          1506 => Opcode::MTCTR8,
          1507 => Opcode::MTCTR8loop,
          1508 => Opcode::MTCTRloop,
          1509 => Opcode::MTDCR,
          1510 => Opcode::MTFSB0,
          1511 => Opcode::MTFSB1,
          1512 => Opcode::MTFSF,
          1513 => Opcode::MTFSFI,
          1514 => Opcode::MTFSFI_rec,
          1515 => Opcode::MTFSFIb,
          1516 => Opcode::MTFSF_rec,
          1517 => Opcode::MTFSFb,
          1518 => Opcode::MTLR,
          1519 => Opcode::MTLR8,
          1520 => Opcode::MTMSR,
          1521 => Opcode::MTMSRD,
          1522 => Opcode::MTOCRF,
          1523 => Opcode::MTOCRF8,
          1524 => Opcode::MTPMR,
          1525 => Opcode::MTSPR,
          1526 => Opcode::MTSPR8,
          1527 => Opcode::MTSR,
          1528 => Opcode::MTSRIN,
          1529 => Opcode::MTUDSCR,
          1530 => Opcode::MTVRD,
          1531 => Opcode::MTVRSAVE,
          1532 => Opcode::MTVRSAVEv,
          1533 => Opcode::MTVRWA,
          1534 => Opcode::MTVRWZ,
          1535 => Opcode::MTVSCR,
          1536 => Opcode::MTVSRBM,
          1537 => Opcode::MTVSRBMI,
          1538 => Opcode::MTVSRD,
          1539 => Opcode::MTVSRDD,
          1540 => Opcode::MTVSRDM,
          1541 => Opcode::MTVSRHM,
          1542 => Opcode::MTVSRQM,
          1543 => Opcode::MTVSRWA,
          1544 => Opcode::MTVSRWM,
          1545 => Opcode::MTVSRWS,
          1546 => Opcode::MTVSRWZ,
          1547 => Opcode::MULHD,
          1548 => Opcode::MULHDU,
          1549 => Opcode::MULHDU_rec,
          1550 => Opcode::MULHD_rec,
          1551 => Opcode::MULHW,
          1552 => Opcode::MULHWU,
          1553 => Opcode::MULHWU_rec,
          1554 => Opcode::MULHW_rec,
          1555 => Opcode::MULLD,
          1556 => Opcode::MULLDO,
          1557 => Opcode::MULLDO_rec,
          1558 => Opcode::MULLD_rec,
          1559 => Opcode::MULLI,
          1560 => Opcode::MULLI8,
          1561 => Opcode::MULLW,
          1562 => Opcode::MULLWO,
          1563 => Opcode::MULLWO_rec,
          1564 => Opcode::MULLW_rec,
          1565 => Opcode::MoveGOTtoLR,
          1566 => Opcode::MovePCtoLR,
          1567 => Opcode::MovePCtoLR8,
          1568 => Opcode::NAND,
          1569 => Opcode::NAND8,
          1570 => Opcode::NAND8_rec,
          1571 => Opcode::NAND_rec,
          1572 => Opcode::NAP,
          1573 => Opcode::NEG,
          1574 => Opcode::NEG8,
          1575 => Opcode::NEG8O,
          1576 => Opcode::NEG8O_rec,
          1577 => Opcode::NEG8_rec,
          1578 => Opcode::NEGO,
          1579 => Opcode::NEGO_rec,
          1580 => Opcode::NEG_rec,
          1581 => Opcode::NOP,
          1582 => Opcode::NOP_GT_PWR6,
          1583 => Opcode::NOP_GT_PWR7,
          1584 => Opcode::NOR,
          1585 => Opcode::NOR8,
          1586 => Opcode::NOR8_rec,
          1587 => Opcode::NOR_rec,
          1588 => Opcode::OR,
          1589 => Opcode::OR8,
          1590 => Opcode::OR8_rec,
          1591 => Opcode::ORC,
          1592 => Opcode::ORC8,
          1593 => Opcode::ORC8_rec,
          1594 => Opcode::ORC_rec,
          1595 => Opcode::ORI,
          1596 => Opcode::ORI8,
          1597 => Opcode::ORIS,
          1598 => Opcode::ORIS8,
          1599 => Opcode::OR_rec,
          1600 => Opcode::PADDI,
          1601 => Opcode::PADDI8,
          1602 => Opcode::PADDI8pc,
          1603 => Opcode::PADDIdtprel,
          1604 => Opcode::PADDIpc,
          1605 => Opcode::PDEPD,
          1606 => Opcode::PEXTD,
          1607 => Opcode::PLA,
          1608 => Opcode::PLA8,
          1609 => Opcode::PLA8pc,
          1610 => Opcode::PLApc,
          1611 => Opcode::PLBZ,
          1612 => Opcode::PLBZ8,
          1613 => Opcode::PLBZ8nopc,
          1614 => Opcode::PLBZ8onlypc,
          1615 => Opcode::PLBZ8pc,
          1616 => Opcode::PLBZnopc,
          1617 => Opcode::PLBZonlypc,
          1618 => Opcode::PLBZpc,
          1619 => Opcode::PLD,
          1620 => Opcode::PLDnopc,
          1621 => Opcode::PLDonlypc,
          1622 => Opcode::PLDpc,
          1623 => Opcode::PLFD,
          1624 => Opcode::PLFDnopc,
          1625 => Opcode::PLFDonlypc,
          1626 => Opcode::PLFDpc,
          1627 => Opcode::PLFS,
          1628 => Opcode::PLFSnopc,
          1629 => Opcode::PLFSonlypc,
          1630 => Opcode::PLFSpc,
          1631 => Opcode::PLHA,
          1632 => Opcode::PLHA8,
          1633 => Opcode::PLHA8nopc,
          1634 => Opcode::PLHA8onlypc,
          1635 => Opcode::PLHA8pc,
          1636 => Opcode::PLHAnopc,
          1637 => Opcode::PLHAonlypc,
          1638 => Opcode::PLHApc,
          1639 => Opcode::PLHZ,
          1640 => Opcode::PLHZ8,
          1641 => Opcode::PLHZ8nopc,
          1642 => Opcode::PLHZ8onlypc,
          1643 => Opcode::PLHZ8pc,
          1644 => Opcode::PLHZnopc,
          1645 => Opcode::PLHZonlypc,
          1646 => Opcode::PLHZpc,
          1647 => Opcode::PLI,
          1648 => Opcode::PLI8,
          1649 => Opcode::PLWA,
          1650 => Opcode::PLWA8,
          1651 => Opcode::PLWA8nopc,
          1652 => Opcode::PLWA8onlypc,
          1653 => Opcode::PLWA8pc,
          1654 => Opcode::PLWAnopc,
          1655 => Opcode::PLWAonlypc,
          1656 => Opcode::PLWApc,
          1657 => Opcode::PLWZ,
          1658 => Opcode::PLWZ8,
          1659 => Opcode::PLWZ8nopc,
          1660 => Opcode::PLWZ8onlypc,
          1661 => Opcode::PLWZ8pc,
          1662 => Opcode::PLWZnopc,
          1663 => Opcode::PLWZonlypc,
          1664 => Opcode::PLWZpc,
          1665 => Opcode::PLXSD,
          1666 => Opcode::PLXSDnopc,
          1667 => Opcode::PLXSDonlypc,
          1668 => Opcode::PLXSDpc,
          1669 => Opcode::PLXSSP,
          1670 => Opcode::PLXSSPnopc,
          1671 => Opcode::PLXSSPonlypc,
          1672 => Opcode::PLXSSPpc,
          1673 => Opcode::PLXV,
          1674 => Opcode::PLXVP,
          1675 => Opcode::PLXVPnopc,
          1676 => Opcode::PLXVPonlypc,
          1677 => Opcode::PLXVPpc,
          1678 => Opcode::PLXVnopc,
          1679 => Opcode::PLXVonlypc,
          1680 => Opcode::PLXVpc,
          1681 => Opcode::PMXVBF16GER2,
          1682 => Opcode::PMXVBF16GER2NN,
          1683 => Opcode::PMXVBF16GER2NP,
          1684 => Opcode::PMXVBF16GER2PN,
          1685 => Opcode::PMXVBF16GER2PP,
          1686 => Opcode::PMXVBF16GER2W,
          1687 => Opcode::PMXVBF16GER2WNN,
          1688 => Opcode::PMXVBF16GER2WNP,
          1689 => Opcode::PMXVBF16GER2WPN,
          1690 => Opcode::PMXVBF16GER2WPP,
          1691 => Opcode::PMXVF16GER2,
          1692 => Opcode::PMXVF16GER2NN,
          1693 => Opcode::PMXVF16GER2NP,
          1694 => Opcode::PMXVF16GER2PN,
          1695 => Opcode::PMXVF16GER2PP,
          1696 => Opcode::PMXVF16GER2W,
          1697 => Opcode::PMXVF16GER2WNN,
          1698 => Opcode::PMXVF16GER2WNP,
          1699 => Opcode::PMXVF16GER2WPN,
          1700 => Opcode::PMXVF16GER2WPP,
          1701 => Opcode::PMXVF32GER,
          1702 => Opcode::PMXVF32GERNN,
          1703 => Opcode::PMXVF32GERNP,
          1704 => Opcode::PMXVF32GERPN,
          1705 => Opcode::PMXVF32GERPP,
          1706 => Opcode::PMXVF32GERW,
          1707 => Opcode::PMXVF32GERWNN,
          1708 => Opcode::PMXVF32GERWNP,
          1709 => Opcode::PMXVF32GERWPN,
          1710 => Opcode::PMXVF32GERWPP,
          1711 => Opcode::PMXVF64GER,
          1712 => Opcode::PMXVF64GERNN,
          1713 => Opcode::PMXVF64GERNP,
          1714 => Opcode::PMXVF64GERPN,
          1715 => Opcode::PMXVF64GERPP,
          1716 => Opcode::PMXVF64GERW,
          1717 => Opcode::PMXVF64GERWNN,
          1718 => Opcode::PMXVF64GERWNP,
          1719 => Opcode::PMXVF64GERWPN,
          1720 => Opcode::PMXVF64GERWPP,
          1721 => Opcode::PMXVI16GER2,
          1722 => Opcode::PMXVI16GER2PP,
          1723 => Opcode::PMXVI16GER2S,
          1724 => Opcode::PMXVI16GER2SPP,
          1725 => Opcode::PMXVI16GER2SW,
          1726 => Opcode::PMXVI16GER2SWPP,
          1727 => Opcode::PMXVI16GER2W,
          1728 => Opcode::PMXVI16GER2WPP,
          1729 => Opcode::PMXVI4GER8,
          1730 => Opcode::PMXVI4GER8PP,
          1731 => Opcode::PMXVI4GER8W,
          1732 => Opcode::PMXVI4GER8WPP,
          1733 => Opcode::PMXVI8GER4,
          1734 => Opcode::PMXVI8GER4PP,
          1735 => Opcode::PMXVI8GER4SPP,
          1736 => Opcode::PMXVI8GER4W,
          1737 => Opcode::PMXVI8GER4WPP,
          1738 => Opcode::PMXVI8GER4WSPP,
          1739 => Opcode::POPCNTB,
          1740 => Opcode::POPCNTB8,
          1741 => Opcode::POPCNTD,
          1742 => Opcode::POPCNTW,
          1743 => Opcode::PPC32GOT,
          1744 => Opcode::PPC32PICGOT,
          1745 => Opcode::PREPARE_PROBED_ALLOCA_32,
          1746 => Opcode::PREPARE_PROBED_ALLOCA_64,
          1747 => Opcode::PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_32,
          1748 => Opcode::PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_64,
          1749 => Opcode::PROBED_ALLOCA_32,
          1750 => Opcode::PROBED_ALLOCA_64,
          1751 => Opcode::PROBED_STACKALLOC_32,
          1752 => Opcode::PROBED_STACKALLOC_64,
          1753 => Opcode::PSTB,
          1754 => Opcode::PSTB8,
          1755 => Opcode::PSTB8nopc,
          1756 => Opcode::PSTB8onlypc,
          1757 => Opcode::PSTB8pc,
          1758 => Opcode::PSTBnopc,
          1759 => Opcode::PSTBonlypc,
          1760 => Opcode::PSTBpc,
          1761 => Opcode::PSTD,
          1762 => Opcode::PSTDnopc,
          1763 => Opcode::PSTDonlypc,
          1764 => Opcode::PSTDpc,
          1765 => Opcode::PSTFD,
          1766 => Opcode::PSTFDnopc,
          1767 => Opcode::PSTFDonlypc,
          1768 => Opcode::PSTFDpc,
          1769 => Opcode::PSTFS,
          1770 => Opcode::PSTFSnopc,
          1771 => Opcode::PSTFSonlypc,
          1772 => Opcode::PSTFSpc,
          1773 => Opcode::PSTH,
          1774 => Opcode::PSTH8,
          1775 => Opcode::PSTH8nopc,
          1776 => Opcode::PSTH8onlypc,
          1777 => Opcode::PSTH8pc,
          1778 => Opcode::PSTHnopc,
          1779 => Opcode::PSTHonlypc,
          1780 => Opcode::PSTHpc,
          1781 => Opcode::PSTW,
          1782 => Opcode::PSTW8,
          1783 => Opcode::PSTW8nopc,
          1784 => Opcode::PSTW8onlypc,
          1785 => Opcode::PSTW8pc,
          1786 => Opcode::PSTWnopc,
          1787 => Opcode::PSTWonlypc,
          1788 => Opcode::PSTWpc,
          1789 => Opcode::PSTXSD,
          1790 => Opcode::PSTXSDnopc,
          1791 => Opcode::PSTXSDonlypc,
          1792 => Opcode::PSTXSDpc,
          1793 => Opcode::PSTXSSP,
          1794 => Opcode::PSTXSSPnopc,
          1795 => Opcode::PSTXSSPonlypc,
          1796 => Opcode::PSTXSSPpc,
          1797 => Opcode::PSTXV,
          1798 => Opcode::PSTXVP,
          1799 => Opcode::PSTXVPnopc,
          1800 => Opcode::PSTXVPonlypc,
          1801 => Opcode::PSTXVPpc,
          1802 => Opcode::PSTXVnopc,
          1803 => Opcode::PSTXVonlypc,
          1804 => Opcode::PSTXVpc,
          1805 => Opcode::PseudoEIEIO,
          1806 => Opcode::RESTORE_ACC,
          1807 => Opcode::RESTORE_CR,
          1808 => Opcode::RESTORE_CRBIT,
          1809 => Opcode::RESTORE_QUADWORD,
          1810 => Opcode::RESTORE_UACC,
          1811 => Opcode::RESTORE_WACC,
          1812 => Opcode::RFCI,
          1813 => Opcode::RFDI,
          1814 => Opcode::RFEBB,
          1815 => Opcode::RFI,
          1816 => Opcode::RFID,
          1817 => Opcode::RFMCI,
          1818 => Opcode::RLDCL,
          1819 => Opcode::RLDCL_rec,
          1820 => Opcode::RLDCR,
          1821 => Opcode::RLDCR_rec,
          1822 => Opcode::RLDIC,
          1823 => Opcode::RLDICL,
          1824 => Opcode::RLDICL_32,
          1825 => Opcode::RLDICL_32_64,
          1826 => Opcode::RLDICL_32_rec,
          1827 => Opcode::RLDICL_rec,
          1828 => Opcode::RLDICR,
          1829 => Opcode::RLDICR_32,
          1830 => Opcode::RLDICR_rec,
          1831 => Opcode::RLDIC_rec,
          1832 => Opcode::RLDIMI,
          1833 => Opcode::RLDIMI_rec,
          1834 => Opcode::RLWIMI,
          1835 => Opcode::RLWIMI8,
          1836 => Opcode::RLWIMI8_rec,
          1837 => Opcode::RLWIMI_rec,
          1838 => Opcode::RLWINM,
          1839 => Opcode::RLWINM8,
          1840 => Opcode::RLWINM8_rec,
          1841 => Opcode::RLWINM_rec,
          1842 => Opcode::RLWNM,
          1843 => Opcode::RLWNM8,
          1844 => Opcode::RLWNM8_rec,
          1845 => Opcode::RLWNM_rec,
          1846 => Opcode::ReadTB,
          1847 => Opcode::SC,
          1848 => Opcode::SCV,
          1849 => Opcode::SELECT_CC_F16,
          1850 => Opcode::SELECT_CC_F4,
          1851 => Opcode::SELECT_CC_F8,
          1852 => Opcode::SELECT_CC_I4,
          1853 => Opcode::SELECT_CC_I8,
          1854 => Opcode::SELECT_CC_SPE,
          1855 => Opcode::SELECT_CC_SPE4,
          1856 => Opcode::SELECT_CC_VRRC,
          1857 => Opcode::SELECT_CC_VSFRC,
          1858 => Opcode::SELECT_CC_VSRC,
          1859 => Opcode::SELECT_CC_VSSRC,
          1860 => Opcode::SELECT_F16,
          1861 => Opcode::SELECT_F4,
          1862 => Opcode::SELECT_F8,
          1863 => Opcode::SELECT_I4,
          1864 => Opcode::SELECT_I8,
          1865 => Opcode::SELECT_SPE,
          1866 => Opcode::SELECT_SPE4,
          1867 => Opcode::SELECT_VRRC,
          1868 => Opcode::SELECT_VSFRC,
          1869 => Opcode::SELECT_VSRC,
          1870 => Opcode::SELECT_VSSRC,
          1871 => Opcode::SETB,
          1872 => Opcode::SETB8,
          1873 => Opcode::SETBC,
          1874 => Opcode::SETBC8,
          1875 => Opcode::SETBCR,
          1876 => Opcode::SETBCR8,
          1877 => Opcode::SETFLM,
          1878 => Opcode::SETNBC,
          1879 => Opcode::SETNBC8,
          1880 => Opcode::SETNBCR,
          1881 => Opcode::SETNBCR8,
          1882 => Opcode::SETRND,
          1883 => Opcode::SETRNDi,
          1884 => Opcode::SLBFEE_rec,
          1885 => Opcode::SLBIA,
          1886 => Opcode::SLBIE,
          1887 => Opcode::SLBIEG,
          1888 => Opcode::SLBMFEE,
          1889 => Opcode::SLBMFEV,
          1890 => Opcode::SLBMTE,
          1891 => Opcode::SLBSYNC,
          1892 => Opcode::SLD,
          1893 => Opcode::SLD_rec,
          1894 => Opcode::SLW,
          1895 => Opcode::SLW8,
          1896 => Opcode::SLW8_rec,
          1897 => Opcode::SLW_rec,
          1898 => Opcode::SPELWZ,
          1899 => Opcode::SPELWZX,
          1900 => Opcode::SPESTW,
          1901 => Opcode::SPESTWX,
          1902 => Opcode::SPILL_ACC,
          1903 => Opcode::SPILL_CR,
          1904 => Opcode::SPILL_CRBIT,
          1905 => Opcode::SPILL_QUADWORD,
          1906 => Opcode::SPILL_UACC,
          1907 => Opcode::SPILL_WACC,
          1908 => Opcode::SPLIT_QUADWORD,
          1909 => Opcode::SRAD,
          1910 => Opcode::SRADI,
          1911 => Opcode::SRADI_32,
          1912 => Opcode::SRADI_rec,
          1913 => Opcode::SRAD_rec,
          1914 => Opcode::SRAW,
          1915 => Opcode::SRAW8,
          1916 => Opcode::SRAW8_rec,
          1917 => Opcode::SRAWI,
          1918 => Opcode::SRAWI8,
          1919 => Opcode::SRAWI8_rec,
          1920 => Opcode::SRAWI_rec,
          1921 => Opcode::SRAW_rec,
          1922 => Opcode::SRD,
          1923 => Opcode::SRD_rec,
          1924 => Opcode::SRW,
          1925 => Opcode::SRW8,
          1926 => Opcode::SRW8_rec,
          1927 => Opcode::SRW_rec,
          1928 => Opcode::STB,
          1929 => Opcode::STB8,
          1930 => Opcode::STBCIX,
          1931 => Opcode::STBCX,
          1932 => Opcode::STBEPX,
          1933 => Opcode::STBU,
          1934 => Opcode::STBU8,
          1935 => Opcode::STBUX,
          1936 => Opcode::STBUX8,
          1937 => Opcode::STBX,
          1938 => Opcode::STBX8,
          1939 => Opcode::STBXTLS,
          1940 => Opcode::STBXTLS_,
          1941 => Opcode::STBXTLS_32,
          1942 => Opcode::STD,
          1943 => Opcode::STDAT,
          1944 => Opcode::STDBRX,
          1945 => Opcode::STDCIX,
          1946 => Opcode::STDCX,
          1947 => Opcode::STDU,
          1948 => Opcode::STDUX,
          1949 => Opcode::STDX,
          1950 => Opcode::STDXTLS,
          1951 => Opcode::STDXTLS_,
          1952 => Opcode::STFD,
          1953 => Opcode::STFDEPX,
          1954 => Opcode::STFDU,
          1955 => Opcode::STFDUX,
          1956 => Opcode::STFDX,
          1957 => Opcode::STFDXTLS,
          1958 => Opcode::STFDXTLS_,
          1959 => Opcode::STFIWX,
          1960 => Opcode::STFS,
          1961 => Opcode::STFSU,
          1962 => Opcode::STFSUX,
          1963 => Opcode::STFSX,
          1964 => Opcode::STFSXTLS,
          1965 => Opcode::STFSXTLS_,
          1966 => Opcode::STH,
          1967 => Opcode::STH8,
          1968 => Opcode::STHBRX,
          1969 => Opcode::STHCIX,
          1970 => Opcode::STHCX,
          1971 => Opcode::STHEPX,
          1972 => Opcode::STHU,
          1973 => Opcode::STHU8,
          1974 => Opcode::STHUX,
          1975 => Opcode::STHUX8,
          1976 => Opcode::STHX,
          1977 => Opcode::STHX8,
          1978 => Opcode::STHXTLS,
          1979 => Opcode::STHXTLS_,
          1980 => Opcode::STHXTLS_32,
          1981 => Opcode::STMW,
          1982 => Opcode::STOP,
          1983 => Opcode::STQ,
          1984 => Opcode::STQCX,
          1985 => Opcode::STQX_PSEUDO,
          1986 => Opcode::STSWI,
          1987 => Opcode::STVEBX,
          1988 => Opcode::STVEHX,
          1989 => Opcode::STVEWX,
          1990 => Opcode::STVX,
          1991 => Opcode::STVXL,
          1992 => Opcode::STW,
          1993 => Opcode::STW8,
          1994 => Opcode::STWAT,
          1995 => Opcode::STWBRX,
          1996 => Opcode::STWCIX,
          1997 => Opcode::STWCX,
          1998 => Opcode::STWEPX,
          1999 => Opcode::STWU,
          2000 => Opcode::STWU8,
          2001 => Opcode::STWUX,
          2002 => Opcode::STWUX8,
          2003 => Opcode::STWX,
          2004 => Opcode::STWX8,
          2005 => Opcode::STWXTLS,
          2006 => Opcode::STWXTLS_,
          2007 => Opcode::STWXTLS_32,
          2008 => Opcode::STXSD,
          2009 => Opcode::STXSDX,
          2010 => Opcode::STXSIBX,
          2011 => Opcode::STXSIBXv,
          2012 => Opcode::STXSIHX,
          2013 => Opcode::STXSIHXv,
          2014 => Opcode::STXSIWX,
          2015 => Opcode::STXSSP,
          2016 => Opcode::STXSSPX,
          2017 => Opcode::STXV,
          2018 => Opcode::STXVB16X,
          2019 => Opcode::STXVD2X,
          2020 => Opcode::STXVH8X,
          2021 => Opcode::STXVL,
          2022 => Opcode::STXVLL,
          2023 => Opcode::STXVP,
          2024 => Opcode::STXVPRL,
          2025 => Opcode::STXVPRLL,
          2026 => Opcode::STXVPX,
          2027 => Opcode::STXVRBX,
          2028 => Opcode::STXVRDX,
          2029 => Opcode::STXVRHX,
          2030 => Opcode::STXVRL,
          2031 => Opcode::STXVRLL,
          2032 => Opcode::STXVRWX,
          2033 => Opcode::STXVW4X,
          2034 => Opcode::STXVX,
          2035 => Opcode::SUBF,
          2036 => Opcode::SUBF8,
          2037 => Opcode::SUBF8O,
          2038 => Opcode::SUBF8O_rec,
          2039 => Opcode::SUBF8_rec,
          2040 => Opcode::SUBFC,
          2041 => Opcode::SUBFC8,
          2042 => Opcode::SUBFC8O,
          2043 => Opcode::SUBFC8O_rec,
          2044 => Opcode::SUBFC8_rec,
          2045 => Opcode::SUBFCO,
          2046 => Opcode::SUBFCO_rec,
          2047 => Opcode::SUBFC_rec,
          2048 => Opcode::SUBFE,
          2049 => Opcode::SUBFE8,
          2050 => Opcode::SUBFE8O,
          2051 => Opcode::SUBFE8O_rec,
          2052 => Opcode::SUBFE8_rec,
          2053 => Opcode::SUBFEO,
          2054 => Opcode::SUBFEO_rec,
          2055 => Opcode::SUBFE_rec,
          2056 => Opcode::SUBFIC,
          2057 => Opcode::SUBFIC8,
          2058 => Opcode::SUBFME,
          2059 => Opcode::SUBFME8,
          2060 => Opcode::SUBFME8O,
          2061 => Opcode::SUBFME8O_rec,
          2062 => Opcode::SUBFME8_rec,
          2063 => Opcode::SUBFMEO,
          2064 => Opcode::SUBFMEO_rec,
          2065 => Opcode::SUBFME_rec,
          2066 => Opcode::SUBFO,
          2067 => Opcode::SUBFO_rec,
          2068 => Opcode::SUBFUS,
          2069 => Opcode::SUBFUS_rec,
          2070 => Opcode::SUBFZE,
          2071 => Opcode::SUBFZE8,
          2072 => Opcode::SUBFZE8O,
          2073 => Opcode::SUBFZE8O_rec,
          2074 => Opcode::SUBFZE8_rec,
          2075 => Opcode::SUBFZEO,
          2076 => Opcode::SUBFZEO_rec,
          2077 => Opcode::SUBFZE_rec,
          2078 => Opcode::SUBF_rec,
          2079 => Opcode::SYNC,
          2080 => Opcode::SYNCP10,
          2081 => Opcode::TABORT,
          2082 => Opcode::TABORTDC,
          2083 => Opcode::TABORTDCI,
          2084 => Opcode::TABORTWC,
          2085 => Opcode::TABORTWCI,
          2086 => Opcode::TAILB,
          2087 => Opcode::TAILB8,
          2088 => Opcode::TAILBA,
          2089 => Opcode::TAILBA8,
          2090 => Opcode::TAILBCTR,
          2091 => Opcode::TAILBCTR8,
          2092 => Opcode::TBEGIN,
          2093 => Opcode::TBEGIN_RET,
          2094 => Opcode::TCHECK,
          2095 => Opcode::TCHECK_RET,
          2096 => Opcode::TCRETURNai,
          2097 => Opcode::TCRETURNai8,
          2098 => Opcode::TCRETURNdi,
          2099 => Opcode::TCRETURNdi8,
          2100 => Opcode::TCRETURNri,
          2101 => Opcode::TCRETURNri8,
          2102 => Opcode::TD,
          2103 => Opcode::TDI,
          2104 => Opcode::TEND,
          2105 => Opcode::TLBIA,
          2106 => Opcode::TLBIE,
          2107 => Opcode::TLBIEL,
          2108 => Opcode::TLBILX,
          2109 => Opcode::TLBIVAX,
          2110 => Opcode::TLBLD,
          2111 => Opcode::TLBLI,
          2112 => Opcode::TLBRE,
          2113 => Opcode::TLBRE2,
          2114 => Opcode::TLBSX,
          2115 => Opcode::TLBSX2,
          2116 => Opcode::TLBSX2D,
          2117 => Opcode::TLBSYNC,
          2118 => Opcode::TLBWE,
          2119 => Opcode::TLBWE2,
          2120 => Opcode::TLSGDAIX,
          2121 => Opcode::TLSGDAIX8,
          2122 => Opcode::TLSLDAIX,
          2123 => Opcode::TLSLDAIX8,
          2124 => Opcode::TRAP,
          2125 => Opcode::TRECHKPT,
          2126 => Opcode::TRECLAIM,
          2127 => Opcode::TSR,
          2128 => Opcode::TW,
          2129 => Opcode::TWI,
          2130 => Opcode::UNENCODED_NOP,
          2131 => Opcode::UpdateGBR,
          2132 => Opcode::VABSDUB,
          2133 => Opcode::VABSDUH,
          2134 => Opcode::VABSDUW,
          2135 => Opcode::VADDCUQ,
          2136 => Opcode::VADDCUW,
          2137 => Opcode::VADDECUQ,
          2138 => Opcode::VADDEUQM,
          2139 => Opcode::VADDFP,
          2140 => Opcode::VADDSBS,
          2141 => Opcode::VADDSHS,
          2142 => Opcode::VADDSWS,
          2143 => Opcode::VADDUBM,
          2144 => Opcode::VADDUBS,
          2145 => Opcode::VADDUDM,
          2146 => Opcode::VADDUHM,
          2147 => Opcode::VADDUHS,
          2148 => Opcode::VADDUQM,
          2149 => Opcode::VADDUWM,
          2150 => Opcode::VADDUWS,
          2151 => Opcode::VAND,
          2152 => Opcode::VANDC,
          2153 => Opcode::VAVGSB,
          2154 => Opcode::VAVGSH,
          2155 => Opcode::VAVGSW,
          2156 => Opcode::VAVGUB,
          2157 => Opcode::VAVGUH,
          2158 => Opcode::VAVGUW,
          2159 => Opcode::VBPERMD,
          2160 => Opcode::VBPERMQ,
          2161 => Opcode::VCFSX,
          2162 => Opcode::VCFSX_0,
          2163 => Opcode::VCFUGED,
          2164 => Opcode::VCFUX,
          2165 => Opcode::VCFUX_0,
          2166 => Opcode::VCIPHER,
          2167 => Opcode::VCIPHERLAST,
          2168 => Opcode::VCLRLB,
          2169 => Opcode::VCLRRB,
          2170 => Opcode::VCLZB,
          2171 => Opcode::VCLZD,
          2172 => Opcode::VCLZDM,
          2173 => Opcode::VCLZH,
          2174 => Opcode::VCLZLSBB,
          2175 => Opcode::VCLZW,
          2176 => Opcode::VCMPBFP,
          2177 => Opcode::VCMPBFP_rec,
          2178 => Opcode::VCMPEQFP,
          2179 => Opcode::VCMPEQFP_rec,
          2180 => Opcode::VCMPEQUB,
          2181 => Opcode::VCMPEQUB_rec,
          2182 => Opcode::VCMPEQUD,
          2183 => Opcode::VCMPEQUD_rec,
          2184 => Opcode::VCMPEQUH,
          2185 => Opcode::VCMPEQUH_rec,
          2186 => Opcode::VCMPEQUQ,
          2187 => Opcode::VCMPEQUQ_rec,
          2188 => Opcode::VCMPEQUW,
          2189 => Opcode::VCMPEQUW_rec,
          2190 => Opcode::VCMPGEFP,
          2191 => Opcode::VCMPGEFP_rec,
          2192 => Opcode::VCMPGTFP,
          2193 => Opcode::VCMPGTFP_rec,
          2194 => Opcode::VCMPGTSB,
          2195 => Opcode::VCMPGTSB_rec,
          2196 => Opcode::VCMPGTSD,
          2197 => Opcode::VCMPGTSD_rec,
          2198 => Opcode::VCMPGTSH,
          2199 => Opcode::VCMPGTSH_rec,
          2200 => Opcode::VCMPGTSQ,
          2201 => Opcode::VCMPGTSQ_rec,
          2202 => Opcode::VCMPGTSW,
          2203 => Opcode::VCMPGTSW_rec,
          2204 => Opcode::VCMPGTUB,
          2205 => Opcode::VCMPGTUB_rec,
          2206 => Opcode::VCMPGTUD,
          2207 => Opcode::VCMPGTUD_rec,
          2208 => Opcode::VCMPGTUH,
          2209 => Opcode::VCMPGTUH_rec,
          2210 => Opcode::VCMPGTUQ,
          2211 => Opcode::VCMPGTUQ_rec,
          2212 => Opcode::VCMPGTUW,
          2213 => Opcode::VCMPGTUW_rec,
          2214 => Opcode::VCMPNEB,
          2215 => Opcode::VCMPNEB_rec,
          2216 => Opcode::VCMPNEH,
          2217 => Opcode::VCMPNEH_rec,
          2218 => Opcode::VCMPNEW,
          2219 => Opcode::VCMPNEW_rec,
          2220 => Opcode::VCMPNEZB,
          2221 => Opcode::VCMPNEZB_rec,
          2222 => Opcode::VCMPNEZH,
          2223 => Opcode::VCMPNEZH_rec,
          2224 => Opcode::VCMPNEZW,
          2225 => Opcode::VCMPNEZW_rec,
          2226 => Opcode::VCMPSQ,
          2227 => Opcode::VCMPUQ,
          2228 => Opcode::VCNTMBB,
          2229 => Opcode::VCNTMBD,
          2230 => Opcode::VCNTMBH,
          2231 => Opcode::VCNTMBW,
          2232 => Opcode::VCTSXS,
          2233 => Opcode::VCTSXS_0,
          2234 => Opcode::VCTUXS,
          2235 => Opcode::VCTUXS_0,
          2236 => Opcode::VCTZB,
          2237 => Opcode::VCTZD,
          2238 => Opcode::VCTZDM,
          2239 => Opcode::VCTZH,
          2240 => Opcode::VCTZLSBB,
          2241 => Opcode::VCTZW,
          2242 => Opcode::VDIVESD,
          2243 => Opcode::VDIVESQ,
          2244 => Opcode::VDIVESW,
          2245 => Opcode::VDIVEUD,
          2246 => Opcode::VDIVEUQ,
          2247 => Opcode::VDIVEUW,
          2248 => Opcode::VDIVSD,
          2249 => Opcode::VDIVSQ,
          2250 => Opcode::VDIVSW,
          2251 => Opcode::VDIVUD,
          2252 => Opcode::VDIVUQ,
          2253 => Opcode::VDIVUW,
          2254 => Opcode::VEQV,
          2255 => Opcode::VEXPANDBM,
          2256 => Opcode::VEXPANDDM,
          2257 => Opcode::VEXPANDHM,
          2258 => Opcode::VEXPANDQM,
          2259 => Opcode::VEXPANDWM,
          2260 => Opcode::VEXPTEFP,
          2261 => Opcode::VEXTDDVLX,
          2262 => Opcode::VEXTDDVRX,
          2263 => Opcode::VEXTDUBVLX,
          2264 => Opcode::VEXTDUBVRX,
          2265 => Opcode::VEXTDUHVLX,
          2266 => Opcode::VEXTDUHVRX,
          2267 => Opcode::VEXTDUWVLX,
          2268 => Opcode::VEXTDUWVRX,
          2269 => Opcode::VEXTRACTBM,
          2270 => Opcode::VEXTRACTD,
          2271 => Opcode::VEXTRACTDM,
          2272 => Opcode::VEXTRACTHM,
          2273 => Opcode::VEXTRACTQM,
          2274 => Opcode::VEXTRACTUB,
          2275 => Opcode::VEXTRACTUH,
          2276 => Opcode::VEXTRACTUW,
          2277 => Opcode::VEXTRACTWM,
          2278 => Opcode::VEXTSB2D,
          2279 => Opcode::VEXTSB2Ds,
          2280 => Opcode::VEXTSB2W,
          2281 => Opcode::VEXTSB2Ws,
          2282 => Opcode::VEXTSD2Q,
          2283 => Opcode::VEXTSH2D,
          2284 => Opcode::VEXTSH2Ds,
          2285 => Opcode::VEXTSH2W,
          2286 => Opcode::VEXTSH2Ws,
          2287 => Opcode::VEXTSW2D,
          2288 => Opcode::VEXTSW2Ds,
          2289 => Opcode::VEXTUBLX,
          2290 => Opcode::VEXTUBRX,
          2291 => Opcode::VEXTUHLX,
          2292 => Opcode::VEXTUHRX,
          2293 => Opcode::VEXTUWLX,
          2294 => Opcode::VEXTUWRX,
          2295 => Opcode::VGBBD,
          2296 => Opcode::VGNB,
          2297 => Opcode::VINSBLX,
          2298 => Opcode::VINSBRX,
          2299 => Opcode::VINSBVLX,
          2300 => Opcode::VINSBVRX,
          2301 => Opcode::VINSD,
          2302 => Opcode::VINSDLX,
          2303 => Opcode::VINSDRX,
          2304 => Opcode::VINSERTB,
          2305 => Opcode::VINSERTD,
          2306 => Opcode::VINSERTH,
          2307 => Opcode::VINSERTW,
          2308 => Opcode::VINSHLX,
          2309 => Opcode::VINSHRX,
          2310 => Opcode::VINSHVLX,
          2311 => Opcode::VINSHVRX,
          2312 => Opcode::VINSW,
          2313 => Opcode::VINSWLX,
          2314 => Opcode::VINSWRX,
          2315 => Opcode::VINSWVLX,
          2316 => Opcode::VINSWVRX,
          2317 => Opcode::VLOGEFP,
          2318 => Opcode::VMADDFP,
          2319 => Opcode::VMAXFP,
          2320 => Opcode::VMAXSB,
          2321 => Opcode::VMAXSD,
          2322 => Opcode::VMAXSH,
          2323 => Opcode::VMAXSW,
          2324 => Opcode::VMAXUB,
          2325 => Opcode::VMAXUD,
          2326 => Opcode::VMAXUH,
          2327 => Opcode::VMAXUW,
          2328 => Opcode::VMHADDSHS,
          2329 => Opcode::VMHRADDSHS,
          2330 => Opcode::VMINFP,
          2331 => Opcode::VMINSB,
          2332 => Opcode::VMINSD,
          2333 => Opcode::VMINSH,
          2334 => Opcode::VMINSW,
          2335 => Opcode::VMINUB,
          2336 => Opcode::VMINUD,
          2337 => Opcode::VMINUH,
          2338 => Opcode::VMINUW,
          2339 => Opcode::VMLADDUHM,
          2340 => Opcode::VMODSD,
          2341 => Opcode::VMODSQ,
          2342 => Opcode::VMODSW,
          2343 => Opcode::VMODUD,
          2344 => Opcode::VMODUQ,
          2345 => Opcode::VMODUW,
          2346 => Opcode::VMRGEW,
          2347 => Opcode::VMRGHB,
          2348 => Opcode::VMRGHH,
          2349 => Opcode::VMRGHW,
          2350 => Opcode::VMRGLB,
          2351 => Opcode::VMRGLH,
          2352 => Opcode::VMRGLW,
          2353 => Opcode::VMRGOW,
          2354 => Opcode::VMSUMCUD,
          2355 => Opcode::VMSUMMBM,
          2356 => Opcode::VMSUMSHM,
          2357 => Opcode::VMSUMSHS,
          2358 => Opcode::VMSUMUBM,
          2359 => Opcode::VMSUMUDM,
          2360 => Opcode::VMSUMUHM,
          2361 => Opcode::VMSUMUHS,
          2362 => Opcode::VMUL10CUQ,
          2363 => Opcode::VMUL10ECUQ,
          2364 => Opcode::VMUL10EUQ,
          2365 => Opcode::VMUL10UQ,
          2366 => Opcode::VMULESB,
          2367 => Opcode::VMULESD,
          2368 => Opcode::VMULESH,
          2369 => Opcode::VMULESW,
          2370 => Opcode::VMULEUB,
          2371 => Opcode::VMULEUD,
          2372 => Opcode::VMULEUH,
          2373 => Opcode::VMULEUW,
          2374 => Opcode::VMULHSD,
          2375 => Opcode::VMULHSW,
          2376 => Opcode::VMULHUD,
          2377 => Opcode::VMULHUW,
          2378 => Opcode::VMULLD,
          2379 => Opcode::VMULOSB,
          2380 => Opcode::VMULOSD,
          2381 => Opcode::VMULOSH,
          2382 => Opcode::VMULOSW,
          2383 => Opcode::VMULOUB,
          2384 => Opcode::VMULOUD,
          2385 => Opcode::VMULOUH,
          2386 => Opcode::VMULOUW,
          2387 => Opcode::VMULUWM,
          2388 => Opcode::VNAND,
          2389 => Opcode::VNCIPHER,
          2390 => Opcode::VNCIPHERLAST,
          2391 => Opcode::VNEGD,
          2392 => Opcode::VNEGW,
          2393 => Opcode::VNMSUBFP,
          2394 => Opcode::VNOR,
          2395 => Opcode::VOR,
          2396 => Opcode::VORC,
          2397 => Opcode::VPDEPD,
          2398 => Opcode::VPERM,
          2399 => Opcode::VPERMR,
          2400 => Opcode::VPERMXOR,
          2401 => Opcode::VPEXTD,
          2402 => Opcode::VPKPX,
          2403 => Opcode::VPKSDSS,
          2404 => Opcode::VPKSDUS,
          2405 => Opcode::VPKSHSS,
          2406 => Opcode::VPKSHUS,
          2407 => Opcode::VPKSWSS,
          2408 => Opcode::VPKSWUS,
          2409 => Opcode::VPKUDUM,
          2410 => Opcode::VPKUDUS,
          2411 => Opcode::VPKUHUM,
          2412 => Opcode::VPKUHUS,
          2413 => Opcode::VPKUWUM,
          2414 => Opcode::VPKUWUS,
          2415 => Opcode::VPMSUMB,
          2416 => Opcode::VPMSUMD,
          2417 => Opcode::VPMSUMH,
          2418 => Opcode::VPMSUMW,
          2419 => Opcode::VPOPCNTB,
          2420 => Opcode::VPOPCNTD,
          2421 => Opcode::VPOPCNTH,
          2422 => Opcode::VPOPCNTW,
          2423 => Opcode::VPRTYBD,
          2424 => Opcode::VPRTYBQ,
          2425 => Opcode::VPRTYBW,
          2426 => Opcode::VREFP,
          2427 => Opcode::VRFIM,
          2428 => Opcode::VRFIN,
          2429 => Opcode::VRFIP,
          2430 => Opcode::VRFIZ,
          2431 => Opcode::VRLB,
          2432 => Opcode::VRLD,
          2433 => Opcode::VRLDMI,
          2434 => Opcode::VRLDNM,
          2435 => Opcode::VRLH,
          2436 => Opcode::VRLQ,
          2437 => Opcode::VRLQMI,
          2438 => Opcode::VRLQNM,
          2439 => Opcode::VRLW,
          2440 => Opcode::VRLWMI,
          2441 => Opcode::VRLWNM,
          2442 => Opcode::VRSQRTEFP,
          2443 => Opcode::VSBOX,
          2444 => Opcode::VSEL,
          2445 => Opcode::VSHASIGMAD,
          2446 => Opcode::VSHASIGMAW,
          2447 => Opcode::VSL,
          2448 => Opcode::VSLB,
          2449 => Opcode::VSLD,
          2450 => Opcode::VSLDBI,
          2451 => Opcode::VSLDOI,
          2452 => Opcode::VSLH,
          2453 => Opcode::VSLO,
          2454 => Opcode::VSLQ,
          2455 => Opcode::VSLV,
          2456 => Opcode::VSLW,
          2457 => Opcode::VSPLTB,
          2458 => Opcode::VSPLTBs,
          2459 => Opcode::VSPLTH,
          2460 => Opcode::VSPLTHs,
          2461 => Opcode::VSPLTISB,
          2462 => Opcode::VSPLTISH,
          2463 => Opcode::VSPLTISW,
          2464 => Opcode::VSPLTW,
          2465 => Opcode::VSR,
          2466 => Opcode::VSRAB,
          2467 => Opcode::VSRAD,
          2468 => Opcode::VSRAH,
          2469 => Opcode::VSRAQ,
          2470 => Opcode::VSRAW,
          2471 => Opcode::VSRB,
          2472 => Opcode::VSRD,
          2473 => Opcode::VSRDBI,
          2474 => Opcode::VSRH,
          2475 => Opcode::VSRO,
          2476 => Opcode::VSRQ,
          2477 => Opcode::VSRV,
          2478 => Opcode::VSRW,
          2479 => Opcode::VSTRIBL,
          2480 => Opcode::VSTRIBL_rec,
          2481 => Opcode::VSTRIBR,
          2482 => Opcode::VSTRIBR_rec,
          2483 => Opcode::VSTRIHL,
          2484 => Opcode::VSTRIHL_rec,
          2485 => Opcode::VSTRIHR,
          2486 => Opcode::VSTRIHR_rec,
          2487 => Opcode::VSUBCUQ,
          2488 => Opcode::VSUBCUW,
          2489 => Opcode::VSUBECUQ,
          2490 => Opcode::VSUBEUQM,
          2491 => Opcode::VSUBFP,
          2492 => Opcode::VSUBSBS,
          2493 => Opcode::VSUBSHS,
          2494 => Opcode::VSUBSWS,
          2495 => Opcode::VSUBUBM,
          2496 => Opcode::VSUBUBS,
          2497 => Opcode::VSUBUDM,
          2498 => Opcode::VSUBUHM,
          2499 => Opcode::VSUBUHS,
          2500 => Opcode::VSUBUQM,
          2501 => Opcode::VSUBUWM,
          2502 => Opcode::VSUBUWS,
          2503 => Opcode::VSUM2SWS,
          2504 => Opcode::VSUM4SBS,
          2505 => Opcode::VSUM4SHS,
          2506 => Opcode::VSUM4UBS,
          2507 => Opcode::VSUMSWS,
          2508 => Opcode::VUPKHPX,
          2509 => Opcode::VUPKHSB,
          2510 => Opcode::VUPKHSH,
          2511 => Opcode::VUPKHSW,
          2512 => Opcode::VUPKLPX,
          2513 => Opcode::VUPKLSB,
          2514 => Opcode::VUPKLSH,
          2515 => Opcode::VUPKLSW,
          2516 => Opcode::VXOR,
          2517 => Opcode::V_SET0,
          2518 => Opcode::V_SET0B,
          2519 => Opcode::V_SET0H,
          2520 => Opcode::V_SETALLONES,
          2521 => Opcode::V_SETALLONESB,
          2522 => Opcode::V_SETALLONESH,
          2523 => Opcode::WAIT,
          2524 => Opcode::WAITP10,
          2525 => Opcode::WRTEE,
          2526 => Opcode::WRTEEI,
          2527 => Opcode::XOR,
          2528 => Opcode::XOR8,
          2529 => Opcode::XOR8_rec,
          2530 => Opcode::XORI,
          2531 => Opcode::XORI8,
          2532 => Opcode::XORIS,
          2533 => Opcode::XORIS8,
          2534 => Opcode::XOR_rec,
          2535 => Opcode::XSABSDP,
          2536 => Opcode::XSABSQP,
          2537 => Opcode::XSADDDP,
          2538 => Opcode::XSADDQP,
          2539 => Opcode::XSADDQPO,
          2540 => Opcode::XSADDSP,
          2541 => Opcode::XSCMPEQDP,
          2542 => Opcode::XSCMPEQQP,
          2543 => Opcode::XSCMPEXPDP,
          2544 => Opcode::XSCMPEXPQP,
          2545 => Opcode::XSCMPGEDP,
          2546 => Opcode::XSCMPGEQP,
          2547 => Opcode::XSCMPGTDP,
          2548 => Opcode::XSCMPGTQP,
          2549 => Opcode::XSCMPODP,
          2550 => Opcode::XSCMPOQP,
          2551 => Opcode::XSCMPUDP,
          2552 => Opcode::XSCMPUQP,
          2553 => Opcode::XSCPSGNDP,
          2554 => Opcode::XSCPSGNQP,
          2555 => Opcode::XSCVDPHP,
          2556 => Opcode::XSCVDPQP,
          2557 => Opcode::XSCVDPSP,
          2558 => Opcode::XSCVDPSPN,
          2559 => Opcode::XSCVDPSXDS,
          2560 => Opcode::XSCVDPSXDSs,
          2561 => Opcode::XSCVDPSXWS,
          2562 => Opcode::XSCVDPSXWSs,
          2563 => Opcode::XSCVDPUXDS,
          2564 => Opcode::XSCVDPUXDSs,
          2565 => Opcode::XSCVDPUXWS,
          2566 => Opcode::XSCVDPUXWSs,
          2567 => Opcode::XSCVHPDP,
          2568 => Opcode::XSCVQPDP,
          2569 => Opcode::XSCVQPDPO,
          2570 => Opcode::XSCVQPSDZ,
          2571 => Opcode::XSCVQPSQZ,
          2572 => Opcode::XSCVQPSWZ,
          2573 => Opcode::XSCVQPUDZ,
          2574 => Opcode::XSCVQPUQZ,
          2575 => Opcode::XSCVQPUWZ,
          2576 => Opcode::XSCVSDQP,
          2577 => Opcode::XSCVSPDP,
          2578 => Opcode::XSCVSPDPN,
          2579 => Opcode::XSCVSQQP,
          2580 => Opcode::XSCVSXDDP,
          2581 => Opcode::XSCVSXDSP,
          2582 => Opcode::XSCVUDQP,
          2583 => Opcode::XSCVUQQP,
          2584 => Opcode::XSCVUXDDP,
          2585 => Opcode::XSCVUXDSP,
          2586 => Opcode::XSDIVDP,
          2587 => Opcode::XSDIVQP,
          2588 => Opcode::XSDIVQPO,
          2589 => Opcode::XSDIVSP,
          2590 => Opcode::XSIEXPDP,
          2591 => Opcode::XSIEXPQP,
          2592 => Opcode::XSMADDADP,
          2593 => Opcode::XSMADDASP,
          2594 => Opcode::XSMADDMDP,
          2595 => Opcode::XSMADDMSP,
          2596 => Opcode::XSMADDQP,
          2597 => Opcode::XSMADDQPO,
          2598 => Opcode::XSMAXCDP,
          2599 => Opcode::XSMAXCQP,
          2600 => Opcode::XSMAXDP,
          2601 => Opcode::XSMAXJDP,
          2602 => Opcode::XSMINCDP,
          2603 => Opcode::XSMINCQP,
          2604 => Opcode::XSMINDP,
          2605 => Opcode::XSMINJDP,
          2606 => Opcode::XSMSUBADP,
          2607 => Opcode::XSMSUBASP,
          2608 => Opcode::XSMSUBMDP,
          2609 => Opcode::XSMSUBMSP,
          2610 => Opcode::XSMSUBQP,
          2611 => Opcode::XSMSUBQPO,
          2612 => Opcode::XSMULDP,
          2613 => Opcode::XSMULQP,
          2614 => Opcode::XSMULQPO,
          2615 => Opcode::XSMULSP,
          2616 => Opcode::XSNABSDP,
          2617 => Opcode::XSNABSDPs,
          2618 => Opcode::XSNABSQP,
          2619 => Opcode::XSNEGDP,
          2620 => Opcode::XSNEGQP,
          2621 => Opcode::XSNMADDADP,
          2622 => Opcode::XSNMADDASP,
          2623 => Opcode::XSNMADDMDP,
          2624 => Opcode::XSNMADDMSP,
          2625 => Opcode::XSNMADDQP,
          2626 => Opcode::XSNMADDQPO,
          2627 => Opcode::XSNMSUBADP,
          2628 => Opcode::XSNMSUBASP,
          2629 => Opcode::XSNMSUBMDP,
          2630 => Opcode::XSNMSUBMSP,
          2631 => Opcode::XSNMSUBQP,
          2632 => Opcode::XSNMSUBQPO,
          2633 => Opcode::XSRDPI,
          2634 => Opcode::XSRDPIC,
          2635 => Opcode::XSRDPIM,
          2636 => Opcode::XSRDPIP,
          2637 => Opcode::XSRDPIZ,
          2638 => Opcode::XSREDP,
          2639 => Opcode::XSRESP,
          2640 => Opcode::XSRQPI,
          2641 => Opcode::XSRQPIX,
          2642 => Opcode::XSRQPXP,
          2643 => Opcode::XSRSP,
          2644 => Opcode::XSRSQRTEDP,
          2645 => Opcode::XSRSQRTESP,
          2646 => Opcode::XSSQRTDP,
          2647 => Opcode::XSSQRTQP,
          2648 => Opcode::XSSQRTQPO,
          2649 => Opcode::XSSQRTSP,
          2650 => Opcode::XSSUBDP,
          2651 => Opcode::XSSUBQP,
          2652 => Opcode::XSSUBQPO,
          2653 => Opcode::XSSUBSP,
          2654 => Opcode::XSTDIVDP,
          2655 => Opcode::XSTSQRTDP,
          2656 => Opcode::XSTSTDCDP,
          2657 => Opcode::XSTSTDCQP,
          2658 => Opcode::XSTSTDCSP,
          2659 => Opcode::XSXEXPDP,
          2660 => Opcode::XSXEXPQP,
          2661 => Opcode::XSXSIGDP,
          2662 => Opcode::XSXSIGQP,
          2663 => Opcode::XVABSDP,
          2664 => Opcode::XVABSSP,
          2665 => Opcode::XVADDDP,
          2666 => Opcode::XVADDSP,
          2667 => Opcode::XVBF16GER2,
          2668 => Opcode::XVBF16GER2NN,
          2669 => Opcode::XVBF16GER2NP,
          2670 => Opcode::XVBF16GER2PN,
          2671 => Opcode::XVBF16GER2PP,
          2672 => Opcode::XVBF16GER2W,
          2673 => Opcode::XVBF16GER2WNN,
          2674 => Opcode::XVBF16GER2WNP,
          2675 => Opcode::XVBF16GER2WPN,
          2676 => Opcode::XVBF16GER2WPP,
          2677 => Opcode::XVCMPEQDP,
          2678 => Opcode::XVCMPEQDP_rec,
          2679 => Opcode::XVCMPEQSP,
          2680 => Opcode::XVCMPEQSP_rec,
          2681 => Opcode::XVCMPGEDP,
          2682 => Opcode::XVCMPGEDP_rec,
          2683 => Opcode::XVCMPGESP,
          2684 => Opcode::XVCMPGESP_rec,
          2685 => Opcode::XVCMPGTDP,
          2686 => Opcode::XVCMPGTDP_rec,
          2687 => Opcode::XVCMPGTSP,
          2688 => Opcode::XVCMPGTSP_rec,
          2689 => Opcode::XVCPSGNDP,
          2690 => Opcode::XVCPSGNSP,
          2691 => Opcode::XVCVBF16SPN,
          2692 => Opcode::XVCVDPSP,
          2693 => Opcode::XVCVDPSXDS,
          2694 => Opcode::XVCVDPSXWS,
          2695 => Opcode::XVCVDPUXDS,
          2696 => Opcode::XVCVDPUXWS,
          2697 => Opcode::XVCVHPSP,
          2698 => Opcode::XVCVSPBF16,
          2699 => Opcode::XVCVSPDP,
          2700 => Opcode::XVCVSPHP,
          2701 => Opcode::XVCVSPSXDS,
          2702 => Opcode::XVCVSPSXWS,
          2703 => Opcode::XVCVSPUXDS,
          2704 => Opcode::XVCVSPUXWS,
          2705 => Opcode::XVCVSXDDP,
          2706 => Opcode::XVCVSXDSP,
          2707 => Opcode::XVCVSXWDP,
          2708 => Opcode::XVCVSXWSP,
          2709 => Opcode::XVCVUXDDP,
          2710 => Opcode::XVCVUXDSP,
          2711 => Opcode::XVCVUXWDP,
          2712 => Opcode::XVCVUXWSP,
          2713 => Opcode::XVDIVDP,
          2714 => Opcode::XVDIVSP,
          2715 => Opcode::XVF16GER2,
          2716 => Opcode::XVF16GER2NN,
          2717 => Opcode::XVF16GER2NP,
          2718 => Opcode::XVF16GER2PN,
          2719 => Opcode::XVF16GER2PP,
          2720 => Opcode::XVF16GER2W,
          2721 => Opcode::XVF16GER2WNN,
          2722 => Opcode::XVF16GER2WNP,
          2723 => Opcode::XVF16GER2WPN,
          2724 => Opcode::XVF16GER2WPP,
          2725 => Opcode::XVF32GER,
          2726 => Opcode::XVF32GERNN,
          2727 => Opcode::XVF32GERNP,
          2728 => Opcode::XVF32GERPN,
          2729 => Opcode::XVF32GERPP,
          2730 => Opcode::XVF32GERW,
          2731 => Opcode::XVF32GERWNN,
          2732 => Opcode::XVF32GERWNP,
          2733 => Opcode::XVF32GERWPN,
          2734 => Opcode::XVF32GERWPP,
          2735 => Opcode::XVF64GER,
          2736 => Opcode::XVF64GERNN,
          2737 => Opcode::XVF64GERNP,
          2738 => Opcode::XVF64GERPN,
          2739 => Opcode::XVF64GERPP,
          2740 => Opcode::XVF64GERW,
          2741 => Opcode::XVF64GERWNN,
          2742 => Opcode::XVF64GERWNP,
          2743 => Opcode::XVF64GERWPN,
          2744 => Opcode::XVF64GERWPP,
          2745 => Opcode::XVI16GER2,
          2746 => Opcode::XVI16GER2PP,
          2747 => Opcode::XVI16GER2S,
          2748 => Opcode::XVI16GER2SPP,
          2749 => Opcode::XVI16GER2SW,
          2750 => Opcode::XVI16GER2SWPP,
          2751 => Opcode::XVI16GER2W,
          2752 => Opcode::XVI16GER2WPP,
          2753 => Opcode::XVI4GER8,
          2754 => Opcode::XVI4GER8PP,
          2755 => Opcode::XVI4GER8W,
          2756 => Opcode::XVI4GER8WPP,
          2757 => Opcode::XVI8GER4,
          2758 => Opcode::XVI8GER4PP,
          2759 => Opcode::XVI8GER4SPP,
          2760 => Opcode::XVI8GER4W,
          2761 => Opcode::XVI8GER4WPP,
          2762 => Opcode::XVI8GER4WSPP,
          2763 => Opcode::XVIEXPDP,
          2764 => Opcode::XVIEXPSP,
          2765 => Opcode::XVMADDADP,
          2766 => Opcode::XVMADDASP,
          2767 => Opcode::XVMADDMDP,
          2768 => Opcode::XVMADDMSP,
          2769 => Opcode::XVMAXDP,
          2770 => Opcode::XVMAXSP,
          2771 => Opcode::XVMINDP,
          2772 => Opcode::XVMINSP,
          2773 => Opcode::XVMSUBADP,
          2774 => Opcode::XVMSUBASP,
          2775 => Opcode::XVMSUBMDP,
          2776 => Opcode::XVMSUBMSP,
          2777 => Opcode::XVMULDP,
          2778 => Opcode::XVMULSP,
          2779 => Opcode::XVNABSDP,
          2780 => Opcode::XVNABSSP,
          2781 => Opcode::XVNEGDP,
          2782 => Opcode::XVNEGSP,
          2783 => Opcode::XVNMADDADP,
          2784 => Opcode::XVNMADDASP,
          2785 => Opcode::XVNMADDMDP,
          2786 => Opcode::XVNMADDMSP,
          2787 => Opcode::XVNMSUBADP,
          2788 => Opcode::XVNMSUBASP,
          2789 => Opcode::XVNMSUBMDP,
          2790 => Opcode::XVNMSUBMSP,
          2791 => Opcode::XVRDPI,
          2792 => Opcode::XVRDPIC,
          2793 => Opcode::XVRDPIM,
          2794 => Opcode::XVRDPIP,
          2795 => Opcode::XVRDPIZ,
          2796 => Opcode::XVREDP,
          2797 => Opcode::XVRESP,
          2798 => Opcode::XVRSPI,
          2799 => Opcode::XVRSPIC,
          2800 => Opcode::XVRSPIM,
          2801 => Opcode::XVRSPIP,
          2802 => Opcode::XVRSPIZ,
          2803 => Opcode::XVRSQRTEDP,
          2804 => Opcode::XVRSQRTESP,
          2805 => Opcode::XVSQRTDP,
          2806 => Opcode::XVSQRTSP,
          2807 => Opcode::XVSUBDP,
          2808 => Opcode::XVSUBSP,
          2809 => Opcode::XVTDIVDP,
          2810 => Opcode::XVTDIVSP,
          2811 => Opcode::XVTLSBB,
          2812 => Opcode::XVTSQRTDP,
          2813 => Opcode::XVTSQRTSP,
          2814 => Opcode::XVTSTDCDP,
          2815 => Opcode::XVTSTDCSP,
          2816 => Opcode::XVXEXPDP,
          2817 => Opcode::XVXEXPSP,
          2818 => Opcode::XVXSIGDP,
          2819 => Opcode::XVXSIGSP,
          2820 => Opcode::XXBLENDVB,
          2821 => Opcode::XXBLENDVD,
          2822 => Opcode::XXBLENDVH,
          2823 => Opcode::XXBLENDVW,
          2824 => Opcode::XXBRD,
          2825 => Opcode::XXBRH,
          2826 => Opcode::XXBRQ,
          2827 => Opcode::XXBRW,
          2828 => Opcode::XXEVAL,
          2829 => Opcode::XXEXTRACTUW,
          2830 => Opcode::XXGENPCVBM,
          2831 => Opcode::XXGENPCVDM,
          2832 => Opcode::XXGENPCVHM,
          2833 => Opcode::XXGENPCVWM,
          2834 => Opcode::XXINSERTW,
          2835 => Opcode::XXLAND,
          2836 => Opcode::XXLANDC,
          2837 => Opcode::XXLEQV,
          2838 => Opcode::XXLEQVOnes,
          2839 => Opcode::XXLNAND,
          2840 => Opcode::XXLNOR,
          2841 => Opcode::XXLOR,
          2842 => Opcode::XXLORC,
          2843 => Opcode::XXLORf,
          2844 => Opcode::XXLXOR,
          2845 => Opcode::XXLXORdpz,
          2846 => Opcode::XXLXORspz,
          2847 => Opcode::XXLXORz,
          2848 => Opcode::XXMFACC,
          2849 => Opcode::XXMFACCW,
          2850 => Opcode::XXMRGHW,
          2851 => Opcode::XXMRGLW,
          2852 => Opcode::XXMTACC,
          2853 => Opcode::XXMTACCW,
          2854 => Opcode::XXPERM,
          2855 => Opcode::XXPERMDI,
          2856 => Opcode::XXPERMDIs,
          2857 => Opcode::XXPERMR,
          2858 => Opcode::XXPERMX,
          2859 => Opcode::XXSEL,
          2860 => Opcode::XXSETACCZ,
          2861 => Opcode::XXSETACCZW,
          2862 => Opcode::XXSLDWI,
          2863 => Opcode::XXSLDWIs,
          2864 => Opcode::XXSPLTI32DX,
          2865 => Opcode::XXSPLTIB,
          2866 => Opcode::XXSPLTIDP,
          2867 => Opcode::XXSPLTIW,
          2868 => Opcode::XXSPLTW,
          2869 => Opcode::XXSPLTWs,
          2870 => Opcode::gBC,
          2871 => Opcode::gBCA,
          2872 => Opcode::gBCAat,
          2873 => Opcode::gBCCTR,
          2874 => Opcode::gBCCTRL,
          2875 => Opcode::gBCL,
          2876 => Opcode::gBCLA,
          2877 => Opcode::gBCLAat,
          2878 => Opcode::gBCLR,
          2879 => Opcode::gBCLRL,
          2880 => Opcode::gBCLat,
          2881 => Opcode::gBCat,
          2882 => Opcode::INSTRUCTION_LIST_END,
          _ => Opcode::UNKNOWN(value),
        }
    }
}
