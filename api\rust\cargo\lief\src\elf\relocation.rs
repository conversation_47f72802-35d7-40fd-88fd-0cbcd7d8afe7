use std::marker::PhantomData;

use lief_ffi as ffi;

use crate::Error;
use crate::generic;
use crate::elf::Section;

use crate::common::into_optional;
use crate::common::FromFFI;
use crate::{declare_iterator, to_result};

use super::Symbol;

/// Structure which reprents an ELF relocation
pub struct Relocation<'a> {
    ptr: cxx::UniquePtr<ffi::ELF_Relocation>,
    _owner: PhantomData<&'a ffi::ELF_Binary>
}


#[allow(non_camel_case_types)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
/// The different types of the relocation
pub enum Type {
    X86_64_NONE,
    X86_64_64,
    X86_64_PC32,
    X86_64_GOT32,
    X86_64_PLT32,
    X86_64_COPY,
    X86_64_GLOB_DAT,
    X86_64_JUMP_SLOT,
    X86_64_RELATIVE,
    X86_64_GOTPCREL,
    X86_64_32,
    X86_64_32S,
    X86_64_16,
    X86_64_PC16,
    X86_64_8,
    X86_64_PC8,
    X86_64_DTPMOD64,
    X86_64_DTPOFF64,
    X86_64_TPOFF64,
    X86_64_TLSGD,
    X86_64_TLSLD,
    X86_64_DTPOFF32,
    X86_64_GOTTPOFF,
    X86_64_TPOFF32,
    X86_64_PC64,
    X86_64_GOTOFF64,
    X86_64_GOTPC32,
    X86_64_GOT64,
    X86_64_GOTPCREL64,
    X86_64_GOTPC64,
    X86_64_GOTPLT64,
    X86_64_PLTOFF64,
    X86_64_SIZE32,
    X86_64_SIZE64,
    X86_64_GOTPC32_TLSDESC,
    X86_64_TLSDESC_CALL,
    X86_64_TLSDESC,
    X86_64_IRELATIVE,
    X86_64_RELATIVE64,
    X86_64_PC32_BND,
    X86_64_PLT32_BND,
    X86_64_GOTPCRELX,
    X86_64_REX_GOTPCRELX,
    AARCH64_NONE,
    AARCH64_ABS64,
    AARCH64_ABS32,
    AARCH64_ABS16,
    AARCH64_PREL64,
    AARCH64_PREL32,
    AARCH64_PREL16,
    AARCH64_MOVW_UABS_G0,
    AARCH64_MOVW_UABS_G0_NC,
    AARCH64_MOVW_UABS_G1,
    AARCH64_MOVW_UABS_G1_NC,
    AARCH64_MOVW_UABS_G2,
    AARCH64_MOVW_UABS_G2_NC,
    AARCH64_MOVW_UABS_G3,
    AARCH64_MOVW_SABS_G0,
    AARCH64_MOVW_SABS_G1,
    AARCH64_MOVW_SABS_G2,
    AARCH64_LD_PREL_LO19,
    AARCH64_ADR_PREL_LO21,
    AARCH64_ADR_PREL_PG_HI21,
    AARCH64_ADR_PREL_PG_HI21_NC,
    AARCH64_ADD_ABS_LO12_NC,
    AARCH64_LDST8_ABS_LO12_NC,
    AARCH64_TSTBR14,
    AARCH64_CONDBR19,
    AARCH64_JUMP26,
    AARCH64_CALL26,
    AARCH64_LDST16_ABS_LO12_NC,
    AARCH64_LDST32_ABS_LO12_NC,
    AARCH64_LDST64_ABS_LO12_NC,
    AARCH64_MOVW_PREL_G0,
    AARCH64_MOVW_PREL_G0_NC,
    AARCH64_MOVW_PREL_G1,
    AARCH64_MOVW_PREL_G1_NC,
    AARCH64_MOVW_PREL_G2,
    AARCH64_MOVW_PREL_G2_NC,
    AARCH64_MOVW_PREL_G3,
    AARCH64_LDST128_ABS_LO12_NC,
    AARCH64_MOVW_GOTOFF_G0,
    AARCH64_MOVW_GOTOFF_G0_NC,
    AARCH64_MOVW_GOTOFF_G1,
    AARCH64_MOVW_GOTOFF_G1_NC,
    AARCH64_MOVW_GOTOFF_G2,
    AARCH64_MOVW_GOTOFF_G2_NC,
    AARCH64_MOVW_GOTOFF_G3,
    AARCH64_GOTREL64,
    AARCH64_GOTREL32,
    AARCH64_GOT_LD_PREL19,
    AARCH64_LD64_GOTOFF_LO15,
    AARCH64_ADR_GOT_PAGE,
    AARCH64_LD64_GOT_LO12_NC,
    AARCH64_LD64_GOTPAGE_LO15,
    AARCH64_TLSGD_ADR_PREL21,
    AARCH64_TLSGD_ADR_PAGE21,
    AARCH64_TLSGD_ADD_LO12_NC,
    AARCH64_TLSGD_MOVW_G1,
    AARCH64_TLSGD_MOVW_G0_NC,
    AARCH64_TLSLD_ADR_PREL21,
    AARCH64_TLSLD_ADR_PAGE21,
    AARCH64_TLSLD_ADD_LO12_NC,
    AARCH64_TLSLD_MOVW_G1,
    AARCH64_TLSLD_MOVW_G0_NC,
    AARCH64_TLSLD_LD_PREL19,
    AARCH64_TLSLD_MOVW_DTPREL_G2,
    AARCH64_TLSLD_MOVW_DTPREL_G1,
    AARCH64_TLSLD_MOVW_DTPREL_G1_NC,
    AARCH64_TLSLD_MOVW_DTPREL_G0,
    AARCH64_TLSLD_MOVW_DTPREL_G0_NC,
    AARCH64_TLSLD_ADD_DTPREL_HI12,
    AARCH64_TLSLD_ADD_DTPREL_LO12,
    AARCH64_TLSLD_ADD_DTPREL_LO12_NC,
    AARCH64_TLSLD_LDST8_DTPREL_LO12,
    AARCH64_TLSLD_LDST8_DTPREL_LO12_NC,
    AARCH64_TLSLD_LDST16_DTPREL_LO12,
    AARCH64_TLSLD_LDST16_DTPREL_LO12_NC,
    AARCH64_TLSLD_LDST32_DTPREL_LO12,
    AARCH64_TLSLD_LDST32_DTPREL_LO12_NC,
    AARCH64_TLSLD_LDST64_DTPREL_LO12,
    AARCH64_TLSLD_LDST64_DTPREL_LO12_NC,
    AARCH64_TLSIE_MOVW_GOTTPREL_G1,
    AARCH64_TLSIE_MOVW_GOTTPREL_G0_NC,
    AARCH64_TLSIE_ADR_GOTTPREL_PAGE21,
    AARCH64_TLSIE_LD64_GOTTPREL_LO12_NC,
    AARCH64_TLSIE_LD_GOTTPREL_PREL19,
    AARCH64_TLSLE_MOVW_TPREL_G2,
    AARCH64_TLSLE_MOVW_TPREL_G1,
    AARCH64_TLSLE_MOVW_TPREL_G1_NC,
    AARCH64_TLSLE_MOVW_TPREL_G0,
    AARCH64_TLSLE_MOVW_TPREL_G0_NC,
    AARCH64_TLSLE_ADD_TPREL_HI12,
    AARCH64_TLSLE_ADD_TPREL_LO12,
    AARCH64_TLSLE_ADD_TPREL_LO12_NC,
    AARCH64_TLSLE_LDST8_TPREL_LO12,
    AARCH64_TLSLE_LDST8_TPREL_LO12_NC,
    AARCH64_TLSLE_LDST16_TPREL_LO12,
    AARCH64_TLSLE_LDST16_TPREL_LO12_NC,
    AARCH64_TLSLE_LDST32_TPREL_LO12,
    AARCH64_TLSLE_LDST32_TPREL_LO12_NC,
    AARCH64_TLSLE_LDST64_TPREL_LO12,
    AARCH64_TLSLE_LDST64_TPREL_LO12_NC,
    AARCH64_TLSDESC_LD_PREL19,
    AARCH64_TLSDESC_ADR_PREL21,
    AARCH64_TLSDESC_ADR_PAGE21,
    AARCH64_TLSDESC_LD64_LO12_NC,
    AARCH64_TLSDESC_ADD_LO12_NC,
    AARCH64_TLSDESC_OFF_G1,
    AARCH64_TLSDESC_OFF_G0_NC,
    AARCH64_TLSDESC_LDR,
    AARCH64_TLSDESC_ADD,
    AARCH64_TLSDESC_CALL,
    AARCH64_TLSLE_LDST128_TPREL_LO12,
    AARCH64_TLSLE_LDST128_TPREL_LO12_NC,
    AARCH64_TLSLD_LDST128_DTPREL_LO12,
    AARCH64_TLSLD_LDST128_DTPREL_LO12_NC,
    AARCH64_COPY,
    AARCH64_GLOB_DAT,
    AARCH64_JUMP_SLOT,
    AARCH64_RELATIVE,
    AARCH64_TLS_DTPREL64,
    AARCH64_TLS_DTPMOD64,
    AARCH64_TLS_TPREL64,
    AARCH64_TLSDESC,
    AARCH64_IRELATIVE,
    ARM_NONE,
    ARM_PC24,
    ARM_ABS32,
    ARM_REL32,
    ARM_LDR_PC_G0,
    ARM_ABS16,
    ARM_ABS12,
    ARM_THM_ABS5,
    ARM_ABS8,
    ARM_SBREL32,
    ARM_THM_CALL,
    ARM_THM_PC8,
    ARM_BREL_ADJ,
    ARM_TLS_DESC,
    ARM_THM_SWI8,
    ARM_XPC25,
    ARM_THM_XPC22,
    ARM_TLS_DTPMOD32,
    ARM_TLS_DTPOFF32,
    ARM_TLS_TPOFF32,
    ARM_COPY,
    ARM_GLOB_DAT,
    ARM_JUMP_SLOT,
    ARM_RELATIVE,
    ARM_GOTOFF32,
    ARM_BASE_PREL,
    ARM_GOT_BREL,
    ARM_PLT32,
    ARM_CALL,
    ARM_JUMP24,
    ARM_THM_JUMP24,
    ARM_BASE_ABS,
    ARM_ALU_PCREL_7_0,
    ARM_ALU_PCREL_15_8,
    ARM_ALU_PCREL_23_15,
    ARM_LDR_SBREL_11_0_NC,
    ARM_ALU_SBREL_19_12_NC,
    ARM_ALU_SBREL_27_20_CK,
    ARM_TARGET1,
    ARM_SBREL31,
    ARM_V4BX,
    ARM_TARGET2,
    ARM_PREL31,
    ARM_MOVW_ABS_NC,
    ARM_MOVT_ABS,
    ARM_MOVW_PREL_NC,
    ARM_MOVT_PREL,
    ARM_THM_MOVW_ABS_NC,
    ARM_THM_MOVT_ABS,
    ARM_THM_MOVW_PREL_NC,
    ARM_THM_MOVT_PREL,
    ARM_THM_JUMP19,
    ARM_THM_JUMP6,
    ARM_THM_ALU_PREL_11_0,
    ARM_THM_PC12,
    ARM_ABS32_NOI,
    ARM_REL32_NOI,
    ARM_ALU_PC_G0_NC,
    ARM_ALU_PC_G0,
    ARM_ALU_PC_G1_NC,
    ARM_ALU_PC_G1,
    ARM_ALU_PC_G2,
    ARM_LDR_PC_G1,
    ARM_LDR_PC_G2,
    ARM_LDRS_PC_G0,
    ARM_LDRS_PC_G1,
    ARM_LDRS_PC_G2,
    ARM_LDC_PC_G0,
    ARM_LDC_PC_G1,
    ARM_LDC_PC_G2,
    ARM_ALU_SB_G0_NC,
    ARM_ALU_SB_G0,
    ARM_ALU_SB_G1_NC,
    ARM_ALU_SB_G1,
    ARM_ALU_SB_G2,
    ARM_LDR_SB_G0,
    ARM_LDR_SB_G1,
    ARM_LDR_SB_G2,
    ARM_LDRS_SB_G0,
    ARM_LDRS_SB_G1,
    ARM_LDRS_SB_G2,
    ARM_LDC_SB_G0,
    ARM_LDC_SB_G1,
    ARM_LDC_SB_G2,
    ARM_MOVW_BREL_NC,
    ARM_MOVT_BREL,
    ARM_MOVW_BREL,
    ARM_THM_MOVW_BREL_NC,
    ARM_THM_MOVT_BREL,
    ARM_THM_MOVW_BREL,
    ARM_TLS_GOTDESC,
    ARM_TLS_CALL,
    ARM_TLS_DESCSEQ,
    ARM_THM_TLS_CALL,
    ARM_PLT32_ABS,
    ARM_GOT_ABS,
    ARM_GOT_PREL,
    ARM_GOT_BREL12,
    ARM_GOTOFF12,
    ARM_GOTRELAX,
    ARM_GNU_VTENTRY,
    ARM_GNU_VTINHERIT,
    ARM_THM_JUMP11,
    ARM_THM_JUMP8,
    ARM_TLS_GD32,
    ARM_TLS_LDM32,
    ARM_TLS_LDO32,
    ARM_TLS_IE32,
    ARM_TLS_LE32,
    ARM_TLS_LDO12,
    ARM_TLS_LE12,
    ARM_TLS_IE12GP,
    ARM_PRIVATE_0,
    ARM_PRIVATE_1,
    ARM_PRIVATE_2,
    ARM_PRIVATE_3,
    ARM_PRIVATE_4,
    ARM_PRIVATE_5,
    ARM_PRIVATE_6,
    ARM_PRIVATE_7,
    ARM_PRIVATE_8,
    ARM_PRIVATE_9,
    ARM_PRIVATE_10,
    ARM_PRIVATE_11,
    ARM_PRIVATE_12,
    ARM_PRIVATE_13,
    ARM_PRIVATE_14,
    ARM_PRIVATE_15,
    ARM_ME_TOO,
    ARM_THM_TLS_DESCSEQ16,
    ARM_THM_TLS_DESCSEQ32,
    ARM_IRELATIVE,
    ARM_RXPC25,
    ARM_RSBREL32,
    ARM_THM_RPC22,
    ARM_RREL32,
    ARM_RPC24,
    ARM_RBASE,
    HEX_NONE,
    HEX_B22_PCREL,
    HEX_B15_PCREL,
    HEX_B7_PCREL,
    HEX_LO16,
    HEX_HI16,
    HEX_32,
    HEX_16,
    HEX_8,
    HEX_GPREL16_0,
    HEX_GPREL16_1,
    HEX_GPREL16_2,
    HEX_GPREL16_3,
    HEX_HL16,
    HEX_B13_PCREL,
    HEX_B9_PCREL,
    HEX_B32_PCREL_X,
    HEX_32_6_X,
    HEX_B22_PCREL_X,
    HEX_B15_PCREL_X,
    HEX_B13_PCREL_X,
    HEX_B9_PCREL_X,
    HEX_B7_PCREL_X,
    HEX_16_X,
    HEX_12_X,
    HEX_11_X,
    HEX_10_X,
    HEX_9_X,
    HEX_8_X,
    HEX_7_X,
    HEX_6_X,
    HEX_32_PCREL,
    HEX_COPY,
    HEX_GLOB_DAT,
    HEX_JMP_SLOT,
    HEX_RELATIVE,
    HEX_PLT_B22_PCREL,
    HEX_GOTREL_LO16,
    HEX_GOTREL_HI16,
    HEX_GOTREL_32,
    HEX_GOT_LO16,
    HEX_GOT_HI16,
    HEX_GOT_32,
    HEX_GOT_16,
    HEX_DTPMOD_32,
    HEX_DTPREL_LO16,
    HEX_DTPREL_HI16,
    HEX_DTPREL_32,
    HEX_DTPREL_16,
    HEX_GD_PLT_B22_PCREL,
    HEX_GD_GOT_LO16,
    HEX_GD_GOT_HI16,
    HEX_GD_GOT_32,
    HEX_GD_GOT_16,
    HEX_IE_LO16,
    HEX_IE_HI16,
    HEX_IE_32,
    HEX_IE_GOT_LO16,
    HEX_IE_GOT_HI16,
    HEX_IE_GOT_32,
    HEX_IE_GOT_16,
    HEX_TPREL_LO16,
    HEX_TPREL_HI16,
    HEX_TPREL_32,
    HEX_TPREL_16,
    HEX_6_PCREL_X,
    HEX_GOTREL_32_6_X,
    HEX_GOTREL_16_X,
    HEX_GOTREL_11_X,
    HEX_GOT_32_6_X,
    HEX_GOT_16_X,
    HEX_GOT_11_X,
    HEX_DTPREL_32_6_X,
    HEX_DTPREL_16_X,
    HEX_DTPREL_11_X,
    HEX_GD_GOT_32_6_X,
    HEX_GD_GOT_16_X,
    HEX_GD_GOT_11_X,
    HEX_IE_32_6_X,
    HEX_IE_16_X,
    HEX_IE_GOT_32_6_X,
    HEX_IE_GOT_16_X,
    HEX_IE_GOT_11_X,
    HEX_TPREL_32_6_X,
    HEX_TPREL_16_X,
    HEX_TPREL_11_X,
    HEX_LD_PLT_B22_PCREL,
    HEX_LD_GOT_LO16,
    HEX_LD_GOT_HI16,
    HEX_LD_GOT_32,
    HEX_LD_GOT_16,
    HEX_LD_GOT_32_6_X,
    HEX_LD_GOT_16_X,
    HEX_LD_GOT_11_X,
    X86_NONE,
    X86_32,
    X86_PC32,
    X86_GOT32,
    X86_PLT32,
    X86_COPY,
    X86_GLOB_DAT,
    X86_JUMP_SLOT,
    X86_RELATIVE,
    X86_GOTOFF,
    X86_GOTPC,
    X86_32PLT,
    X86_TLS_TPOFF,
    X86_TLS_IE,
    X86_TLS_GOTIE,
    X86_TLS_LE,
    X86_TLS_GD,
    X86_TLS_LDM,
    X86_16,
    X86_PC16,
    X86_8,
    X86_PC8,
    X86_TLS_GD_32,
    X86_TLS_GD_PUSH,
    X86_TLS_GD_CALL,
    X86_TLS_GD_POP,
    X86_TLS_LDM_32,
    X86_TLS_LDM_PUSH,
    X86_TLS_LDM_CALL,
    X86_TLS_LDM_POP,
    X86_TLS_LDO_32,
    X86_TLS_IE_32,
    X86_TLS_LE_32,
    X86_TLS_DTPMOD32,
    X86_TLS_DTPOFF32,
    X86_TLS_TPOFF32,
    X86_TLS_GOTDESC,
    X86_TLS_DESC_CALL,
    X86_TLS_DESC,
    X86_IRELATIVE,
    LARCH_NONE,
    LARCH_32,
    LARCH_64,
    LARCH_RELATIVE,
    LARCH_COPY,
    LARCH_JUMP_SLOT,
    LARCH_TLS_DTPMOD32,
    LARCH_TLS_DTPMOD64,
    LARCH_TLS_DTPREL32,
    LARCH_TLS_DTPREL64,
    LARCH_TLS_TPREL32,
    LARCH_TLS_TPREL64,
    LARCH_IRELATIVE,
    LARCH_MARK_LA,
    LARCH_MARK_PCREL,
    LARCH_SOP_PUSH_PCREL,
    LARCH_SOP_PUSH_ABSOLUTE,
    LARCH_SOP_PUSH_DUP,
    LARCH_SOP_PUSH_GPREL,
    LARCH_SOP_PUSH_TLS_TPREL,
    LARCH_SOP_PUSH_TLS_GOT,
    LARCH_SOP_PUSH_TLS_GD,
    LARCH_SOP_PUSH_PLT_PCREL,
    LARCH_SOP_ASSERT,
    LARCH_SOP_NOT,
    LARCH_SOP_SUB,
    LARCH_SOP_SL,
    LARCH_SOP_SR,
    LARCH_SOP_ADD,
    LARCH_SOP_AND,
    LARCH_SOP_IF_ELSE,
    LARCH_SOP_POP_32_S_10_5,
    LARCH_SOP_POP_32_U_10_12,
    LARCH_SOP_POP_32_S_10_12,
    LARCH_SOP_POP_32_S_10_16,
    LARCH_SOP_POP_32_S_10_16_S2,
    LARCH_SOP_POP_32_S_5_20,
    LARCH_SOP_POP_32_S_0_5_10_16_S2,
    LARCH_SOP_POP_32_S_0_10_10_16_S2,
    LARCH_SOP_POP_32_U,
    LARCH_ADD8,
    LARCH_ADD16,
    LARCH_ADD24,
    LARCH_ADD32,
    LARCH_ADD64,
    LARCH_SUB8,
    LARCH_SUB16,
    LARCH_SUB24,
    LARCH_SUB32,
    LARCH_SUB64,
    LARCH_GNU_VTINHERIT,
    LARCH_GNU_VTENTRY,
    LARCH_B16,
    LARCH_B21,
    LARCH_B26,
    LARCH_ABS_HI20,
    LARCH_ABS_LO12,
    LARCH_ABS64_LO20,
    LARCH_ABS64_HI12,
    LARCH_PCALA_HI20,
    LARCH_PCALA_LO12,
    LARCH_PCALA64_LO20,
    LARCH_PCALA64_HI12,
    LARCH_GOT_PC_HI20,
    LARCH_GOT_PC_LO12,
    LARCH_GOT64_PC_LO20,
    LARCH_GOT64_PC_HI12,
    LARCH_GOT_HI20,
    LARCH_GOT_LO12,
    LARCH_GOT64_LO20,
    LARCH_GOT64_HI12,
    LARCH_TLS_LE_HI20,
    LARCH_TLS_LE_LO12,
    LARCH_TLS_LE64_LO20,
    LARCH_TLS_LE64_HI12,
    LARCH_TLS_IE_PC_HI20,
    LARCH_TLS_IE_PC_LO12,
    LARCH_TLS_IE64_PC_LO20,
    LARCH_TLS_IE64_PC_HI12,
    LARCH_TLS_IE_HI20,
    LARCH_TLS_IE_LO12,
    LARCH_TLS_IE64_LO20,
    LARCH_TLS_IE64_HI12,
    LARCH_TLS_LD_PC_HI20,
    LARCH_TLS_LD_HI20,
    LARCH_TLS_GD_PC_HI20,
    LARCH_TLS_GD_HI20,
    LARCH_32_PCREL,
    LARCH_RELAX,
    LARCH_ALIGN,
    LARCH_PCREL20_S2,
    LARCH_ADD6,
    LARCH_SUB6,
    LARCH_ADD_ULEB128,
    LARCH_SUB_ULEB128,
    LARCH_64_PCREL,
    LARCH_CALL36,
    LARCH_TLS_DESC32,
    LARCH_TLS_DESC64,
    LARCH_TLS_DESC_PC_HI20,
    LARCH_TLS_DESC_PC_LO12,
    LARCH_TLS_DESC64_PC_LO20,
    LARCH_TLS_DESC64_PC_HI12,
    LARCH_TLS_DESC_HI20,
    LARCH_TLS_DESC_LO12,
    LARCH_TLS_DESC64_LO20,
    LARCH_TLS_DESC64_HI12,
    LARCH_TLS_DESC_LD,
    LARCH_TLS_DESC_CALL,
    LARCH_TLS_LE_HI20_R,
    LARCH_TLS_LE_ADD_R,
    LARCH_TLS_LE_LO12_R,
    LARCH_TLS_LD_PCREL20_S2,
    LARCH_TLS_GD_PCREL20_S2,
    LARCH_TLS_DESC_PCREL20_S2,
    MIPS_NONE,
    MIPS_16,
    MIPS_32,
    MIPS_REL32,
    MIPS_26,
    MIPS_HI16,
    MIPS_LO16,
    MIPS_GPREL16,
    MIPS_LITERAL,
    MIPS_GOT16,
    MIPS_PC16,
    MIPS_CALL16,
    MIPS_GPREL32,
    MIPS_UNUSED1,
    MIPS_UNUSED2,
    MIPS_UNUSED3,
    MIPS_SHIFT5,
    MIPS_SHIFT6,
    MIPS_64,
    MIPS_GOT_DISP,
    MIPS_GOT_PAGE,
    MIPS_GOT_OFST,
    MIPS_GOT_HI16,
    MIPS_GOT_LO16,
    MIPS_SUB,
    MIPS_INSERT_A,
    MIPS_INSERT_B,
    MIPS_DELETE,
    MIPS_HIGHER,
    MIPS_HIGHEST,
    MIPS_CALL_HI16,
    MIPS_CALL_LO16,
    MIPS_SCN_DISP,
    MIPS_REL16,
    MIPS_ADD_IMMEDIATE,
    MIPS_PJUMP,
    MIPS_RELGOT,
    MIPS_JALR,
    MIPS_TLS_DTPMOD32,
    MIPS_TLS_DTPREL32,
    MIPS_TLS_DTPMOD64,
    MIPS_TLS_DTPREL64,
    MIPS_TLS_GD,
    MIPS_TLS_LDM,
    MIPS_TLS_DTPREL_HI16,
    MIPS_TLS_DTPREL_LO16,
    MIPS_TLS_GOTTPREL,
    MIPS_TLS_TPREL32,
    MIPS_TLS_TPREL64,
    MIPS_TLS_TPREL_HI16,
    MIPS_TLS_TPREL_LO16,
    MIPS_GLOB_DAT,
    MIPS_PC21_S2,
    MIPS_PC26_S2,
    MIPS_PC18_S3,
    MIPS_PC19_S2,
    MIPS_PCHI16,
    MIPS_PCLO16,
    MIPS16_26,
    MIPS16_GPREL,
    MIPS16_GOT16,
    MIPS16_CALL16,
    MIPS16_HI16,
    MIPS16_LO16,
    MIPS16_TLS_GD,
    MIPS16_TLS_LDM,
    MIPS16_TLS_DTPREL_HI16,
    MIPS16_TLS_DTPREL_LO16,
    MIPS16_TLS_GOTTPREL,
    MIPS16_TLS_TPREL_HI16,
    MIPS16_TLS_TPREL_LO16,
    MIPS_COPY,
    MIPS_JUMP_SLOT,
    MICROMIPS_26_S1,
    MICROMIPS_HI16,
    MICROMIPS_LO16,
    MICROMIPS_GPREL16,
    MICROMIPS_LITERAL,
    MICROMIPS_GOT16,
    MICROMIPS_PC7_S1,
    MICROMIPS_PC10_S1,
    MICROMIPS_PC16_S1,
    MICROMIPS_CALL16,
    MICROMIPS_GOT_DISP,
    MICROMIPS_GOT_PAGE,
    MICROMIPS_GOT_OFST,
    MICROMIPS_GOT_HI16,
    MICROMIPS_GOT_LO16,
    MICROMIPS_SUB,
    MICROMIPS_HIGHER,
    MICROMIPS_HIGHEST,
    MICROMIPS_CALL_HI16,
    MICROMIPS_CALL_LO16,
    MICROMIPS_SCN_DISP,
    MICROMIPS_JALR,
    MICROMIPS_HI0_LO16,
    MICROMIPS_TLS_GD,
    MICROMIPS_TLS_LDM,
    MICROMIPS_TLS_DTPREL_HI16,
    MICROMIPS_TLS_DTPREL_LO16,
    MICROMIPS_TLS_GOTTPREL,
    MICROMIPS_TLS_TPREL_HI16,
    MICROMIPS_TLS_TPREL_LO16,
    MICROMIPS_GPREL7_S2,
    MICROMIPS_PC23_S2,
    MICROMIPS_PC21_S2,
    MICROMIPS_PC26_S2,
    MICROMIPS_PC18_S3,
    MICROMIPS_PC19_S2,
    MIPS_NUM,
    MIPS_PC32,
    MIPS_EH,
    PPC_NONE,
    PPC_ADDR32,
    PPC_ADDR24,
    PPC_ADDR16,
    PPC_ADDR16_LO,
    PPC_ADDR16_HI,
    PPC_ADDR16_HA,
    PPC_ADDR14,
    PPC_ADDR14_BRTAKEN,
    PPC_ADDR14_BRNTAKEN,
    PPC_REL24,
    PPC_REL14,
    PPC_REL14_BRTAKEN,
    PPC_REL14_BRNTAKEN,
    PPC_GOT16,
    PPC_GOT16_LO,
    PPC_GOT16_HI,
    PPC_GOT16_HA,
    PPC_PLTREL24,
    PPC_JMP_SLOT,
    PPC_RELATIVE,
    PPC_LOCAL24PC,
    PPC_REL32,
    PPC_TLS,
    PPC_DTPMOD32,
    PPC_TPREL16,
    PPC_TPREL16_LO,
    PPC_TPREL16_HI,
    PPC_TPREL16_HA,
    PPC_TPREL32,
    PPC_DTPREL16,
    PPC_DTPREL16_LO,
    PPC_DTPREL16_HI,
    PPC_DTPREL16_HA,
    PPC_DTPREL32,
    PPC_GOT_TLSGD16,
    PPC_GOT_TLSGD16_LO,
    PPC_GOT_TLSGD16_HI,
    PPC_GOT_TLSGD16_HA,
    PPC_GOT_TLSLD16,
    PPC_GOT_TLSLD16_LO,
    PPC_GOT_TLSLD16_HI,
    PPC_GOT_TLSLD16_HA,
    PPC_GOT_TPREL16,
    PPC_GOT_TPREL16_LO,
    PPC_GOT_TPREL16_HI,
    PPC_GOT_TPREL16_HA,
    PPC_GOT_DTPREL16,
    PPC_GOT_DTPREL16_LO,
    PPC_GOT_DTPREL16_HI,
    PPC_GOT_DTPREL16_HA,
    PPC_TLSGD,
    PPC_TLSLD,
    PPC_REL16,
    PPC_REL16_LO,
    PPC_REL16_HI,
    PPC_REL16_HA,
    PPC64_NONE,
    PPC64_ADDR32,
    PPC64_ADDR24,
    PPC64_ADDR16,
    PPC64_ADDR16_LO,
    PPC64_ADDR16_HI,
    PPC64_ADDR16_HA,
    PPC64_ADDR14,
    PPC64_ADDR14_BRTAKEN,
    PPC64_ADDR14_BRNTAKEN,
    PPC64_REL24,
    PPC64_REL14,
    PPC64_REL14_BRTAKEN,
    PPC64_REL14_BRNTAKEN,
    PPC64_GOT16,
    PPC64_GOT16_LO,
    PPC64_GOT16_HI,
    PPC64_GOT16_HA,
    PPC64_JMP_SLOT,
    PPC64_RELATIVE,
    PPC64_REL32,
    PPC64_ADDR64,
    PPC64_ADDR16_HIGHER,
    PPC64_ADDR16_HIGHERA,
    PPC64_ADDR16_HIGHEST,
    PPC64_ADDR16_HIGHESTA,
    PPC64_REL64,
    PPC64_TOC16,
    PPC64_TOC16_LO,
    PPC64_TOC16_HI,
    PPC64_TOC16_HA,
    PPC64_TOC,
    PPC64_ADDR16_DS,
    PPC64_ADDR16_LO_DS,
    PPC64_GOT16_DS,
    PPC64_GOT16_LO_DS,
    PPC64_TOC16_DS,
    PPC64_TOC16_LO_DS,
    PPC64_TLS,
    PPC64_DTPMOD64,
    PPC64_TPREL16,
    PPC64_TPREL16_LO,
    PPC64_TPREL16_HI,
    PPC64_TPREL16_HA,
    PPC64_TPREL64,
    PPC64_DTPREL16,
    PPC64_DTPREL16_LO,
    PPC64_DTPREL16_HI,
    PPC64_DTPREL16_HA,
    PPC64_DTPREL64,
    PPC64_GOT_TLSGD16,
    PPC64_GOT_TLSGD16_LO,
    PPC64_GOT_TLSGD16_HI,
    PPC64_GOT_TLSGD16_HA,
    PPC64_GOT_TLSLD16,
    PPC64_GOT_TLSLD16_LO,
    PPC64_GOT_TLSLD16_HI,
    PPC64_GOT_TLSLD16_HA,
    PPC64_GOT_TPREL16_DS,
    PPC64_GOT_TPREL16_LO_DS,
    PPC64_GOT_TPREL16_HI,
    PPC64_GOT_TPREL16_HA,
    PPC64_GOT_DTPREL16_DS,
    PPC64_GOT_DTPREL16_LO_DS,
    PPC64_GOT_DTPREL16_HI,
    PPC64_GOT_DTPREL16_HA,
    PPC64_TPREL16_DS,
    PPC64_TPREL16_LO_DS,
    PPC64_TPREL16_HIGHER,
    PPC64_TPREL16_HIGHERA,
    PPC64_TPREL16_HIGHEST,
    PPC64_TPREL16_HIGHESTA,
    PPC64_DTPREL16_DS,
    PPC64_DTPREL16_LO_DS,
    PPC64_DTPREL16_HIGHER,
    PPC64_DTPREL16_HIGHERA,
    PPC64_DTPREL16_HIGHEST,
    PPC64_DTPREL16_HIGHESTA,
    PPC64_TLSGD,
    PPC64_TLSLD,
    PPC64_REL16,
    PPC64_REL16_LO,
    PPC64_REL16_HI,
    PPC64_REL16_HA,
    SPARC_NONE,
    SPARC_8,
    SPARC_16,
    SPARC_32,
    SPARC_DISP8,
    SPARC_DISP16,
    SPARC_DISP32,
    SPARC_WDISP30,
    SPARC_WDISP22,
    SPARC_HI22,
    SPARC_22,
    SPARC_13,
    SPARC_LO10,
    SPARC_GOT10,
    SPARC_GOT13,
    SPARC_GOT22,
    SPARC_PC10,
    SPARC_PC22,
    SPARC_WPLT30,
    SPARC_COPY,
    SPARC_GLOB_DAT,
    SPARC_JMP_SLOT,
    SPARC_RELATIVE,
    SPARC_UA32,
    SPARC_PLT32,
    SPARC_HIPLT22,
    SPARC_LOPLT10,
    SPARC_PCPLT32,
    SPARC_PCPLT22,
    SPARC_PCPLT10,
    SPARC_10,
    SPARC_11,
    SPARC_64,
    SPARC_OLO10,
    SPARC_HH22,
    SPARC_HM10,
    SPARC_LM22,
    SPARC_PC_HH22,
    SPARC_PC_HM10,
    SPARC_PC_LM22,
    SPARC_WDISP16,
    SPARC_WDISP19,
    SPARC_7,
    SPARC_5,
    SPARC_6,
    SPARC_DISP64,
    SPARC_PLT64,
    SPARC_HIX22,
    SPARC_LOX10,
    SPARC_H44,
    SPARC_M44,
    SPARC_L44,
    SPARC_REGISTER,
    SPARC_UA64,
    SPARC_UA16,
    SPARC_TLS_GD_HI22,
    SPARC_TLS_GD_LO10,
    SPARC_TLS_GD_ADD,
    SPARC_TLS_GD_CALL,
    SPARC_TLS_LDM_HI22,
    SPARC_TLS_LDM_LO10,
    SPARC_TLS_LDM_ADD,
    SPARC_TLS_LDM_CALL,
    SPARC_TLS_LDO_HIX22,
    SPARC_TLS_LDO_LOX10,
    SPARC_TLS_LDO_ADD,
    SPARC_TLS_IE_HI22,
    SPARC_TLS_IE_LO10,
    SPARC_TLS_IE_LD,
    SPARC_TLS_IE_LDX,
    SPARC_TLS_IE_ADD,
    SPARC_TLS_LE_HIX22,
    SPARC_TLS_LE_LOX10,
    SPARC_TLS_DTPMOD32,
    SPARC_TLS_DTPMOD64,
    SPARC_TLS_DTPOFF32,
    SPARC_TLS_DTPOFF64,
    SPARC_TLS_TPOFF32,
    SPARC_TLS_TPOFF64,
    SPARC_GOTDATA_HIX22,
    SPARC_GOTDATA_LOX10,
    SPARC_GOTDATA_OP_HIX22,
    SPARC_GOTDATA_OP_LOX10,
    SPARC_GOTDATA_OP,
    SYSZ_NONE,
    SYSZ_8,
    SYSZ_12,
    SYSZ_16,
    SYSZ_32,
    SYSZ_PC32,
    SYSZ_GOT12,
    SYSZ_GOT32,
    SYSZ_PLT32,
    SYSZ_COPY,
    SYSZ_GLOB_DAT,
    SYSZ_JMP_SLOT,
    SYSZ_RELATIVE,
    SYSZ_GOTOFF,
    SYSZ_GOTPC,
    SYSZ_GOT16,
    SYSZ_PC16,
    SYSZ_PC16DBL,
    SYSZ_PLT16DBL,
    SYSZ_PC32DBL,
    SYSZ_PLT32DBL,
    SYSZ_GOTPCDBL,
    SYSZ_64,
    SYSZ_PC64,
    SYSZ_GOT64,
    SYSZ_PLT64,
    SYSZ_GOTENT,
    SYSZ_GOTOFF16,
    SYSZ_GOTOFF64,
    SYSZ_GOTPLT12,
    SYSZ_GOTPLT16,
    SYSZ_GOTPLT32,
    SYSZ_GOTPLT64,
    SYSZ_GOTPLTENT,
    SYSZ_PLTOFF16,
    SYSZ_PLTOFF32,
    SYSZ_PLTOFF64,
    SYSZ_TLS_LOAD,
    SYSZ_TLS_GDCALL,
    SYSZ_TLS_LDCALL,
    SYSZ_TLS_GD32,
    SYSZ_TLS_GD64,
    SYSZ_TLS_GOTIE12,
    SYSZ_TLS_GOTIE32,
    SYSZ_TLS_GOTIE64,
    SYSZ_TLS_LDM32,
    SYSZ_TLS_LDM64,
    SYSZ_TLS_IE32,
    SYSZ_TLS_IE64,
    SYSZ_TLS_IEENT,
    SYSZ_TLS_LE32,
    SYSZ_TLS_LE64,
    SYSZ_TLS_LDO32,
    SYSZ_TLS_LDO64,
    SYSZ_TLS_DTPMOD,
    SYSZ_TLS_DTPOFF,
    SYSZ_TLS_TPOFF,
    SYSZ_20,
    SYSZ_GOT20,
    SYSZ_GOTPLT20,
    SYSZ_TLS_GOTIE20,
    SYSZ_IRELATIVE,
    RISCV_NONE,
    RISCV_32,
    RISCV_64,
    RISCV_RELATIVE,
    RISCV_COPY,
    RISCV_JUMP_SLOT,
    RISCV_TLS_DTPMOD32,
    RISCV_TLS_DTPMOD64,
    RISCV_TLS_DTPREL32,
    RISCV_TLS_DTPREL64,
    RISCV_TLS_TPREL32,
    RISCV_TLS_TPREL64,
    RISCV_TLSDESC,
    RISCV_BRANCH,
    RISCV_JAL,
    RISCV_CALL,
    RISCV_CALL_PLT,
    RISCV_GOT_HI20,
    RISCV_TLS_GOT_HI20,
    RISCV_TLS_GD_HI20,
    RISCV_PCREL_HI20,
    RISCV_PCREL_LO12_I,
    RISCV_PCREL_LO12_S,
    RISCV_HI20,
    RISCV_LO12_I,
    RISCV_LO12_S,
    RISCV_TPREL_HI20,
    RISCV_TPREL_LO12_I,
    RISCV_TPREL_LO12_S,
    RISCV_TPREL_ADD,
    RISCV_ADD8,
    RISCV_ADD16,
    RISCV_ADD32,
    RISCV_ADD64,
    RISCV_SUB8,
    RISCV_SUB16,
    RISCV_SUB32,
    RISCV_SUB64,
    RISCV_GOT32_PCREL,
    RISCV_ALIGN,
    RISCV_RVC_BRANCH,
    RISCV_RVC_JUMP,
    RISCV_RVC_LUI,
    RISCV_RELAX,
    RISCV_SUB6,
    RISCV_SET6,
    RISCV_SET8,
    RISCV_SET16,
    RISCV_SET32,
    RISCV_32_PCREL,
    RISCV_IRELATIVE,
    RISCV_PLT32,
    RISCV_SET_ULEB128,
    RISCV_SUB_ULEB128,
    RISCV_TLSDESC_HI20,
    RISCV_TLSDESC_LOAD_LO12,
    RISCV_TLSDESC_ADD_LO12,
    RISCV_TLSDESC_CALL,
    BPF_NONE,
    BPF_64_64,
    BPF_64_ABS64,
    BPF_64_ABS32,
    BPF_64_NODYLD32,
    BPF_64_32,
    UNKNOWN(u32),
}

impl From<u32> for Type {
    fn from(value: u32) -> Self {
        match value {
            0x08000000 => Type::X86_64_NONE,
            0x08000001 => Type::X86_64_64,
            0x08000002 => Type::X86_64_PC32,
            0x08000003 => Type::X86_64_GOT32,
            0x08000004 => Type::X86_64_PLT32,
            0x08000005 => Type::X86_64_COPY,
            0x08000006 => Type::X86_64_GLOB_DAT,
            0x08000007 => Type::X86_64_JUMP_SLOT,
            0x08000008 => Type::X86_64_RELATIVE,
            0x08000009 => Type::X86_64_GOTPCREL,
            0x0800000a => Type::X86_64_32,
            0x0800000b => Type::X86_64_32S,
            0x0800000c => Type::X86_64_16,
            0x0800000d => Type::X86_64_PC16,
            0x0800000e => Type::X86_64_8,
            0x0800000f => Type::X86_64_PC8,
            0x08000010 => Type::X86_64_DTPMOD64,
            0x08000011 => Type::X86_64_DTPOFF64,
            0x08000012 => Type::X86_64_TPOFF64,
            0x08000013 => Type::X86_64_TLSGD,
            0x08000014 => Type::X86_64_TLSLD,
            0x08000015 => Type::X86_64_DTPOFF32,
            0x08000016 => Type::X86_64_GOTTPOFF,
            0x08000017 => Type::X86_64_TPOFF32,
            0x08000018 => Type::X86_64_PC64,
            0x08000019 => Type::X86_64_GOTOFF64,
            0x0800001a => Type::X86_64_GOTPC32,
            0x0800001b => Type::X86_64_GOT64,
            0x0800001c => Type::X86_64_GOTPCREL64,
            0x0800001d => Type::X86_64_GOTPC64,
            0x0800001e => Type::X86_64_GOTPLT64,
            0x0800001f => Type::X86_64_PLTOFF64,
            0x08000020 => Type::X86_64_SIZE32,
            0x08000021 => Type::X86_64_SIZE64,
            0x08000022 => Type::X86_64_GOTPC32_TLSDESC,
            0x08000023 => Type::X86_64_TLSDESC_CALL,
            0x08000024 => Type::X86_64_TLSDESC,
            0x08000025 => Type::X86_64_IRELATIVE,
            0x08000026 => Type::X86_64_RELATIVE64,
            0x08000027 => Type::X86_64_PC32_BND,
            0x08000028 => Type::X86_64_PLT32_BND,
            0x08000029 => Type::X86_64_GOTPCRELX,
            0x0800002a => Type::X86_64_REX_GOTPCRELX,
            0x10000000 => Type::AARCH64_NONE,
            0x10000101 => Type::AARCH64_ABS64,
            0x10000102 => Type::AARCH64_ABS32,
            0x10000103 => Type::AARCH64_ABS16,
            0x10000104 => Type::AARCH64_PREL64,
            0x10000105 => Type::AARCH64_PREL32,
            0x10000106 => Type::AARCH64_PREL16,
            0x10000107 => Type::AARCH64_MOVW_UABS_G0,
            0x10000108 => Type::AARCH64_MOVW_UABS_G0_NC,
            0x10000109 => Type::AARCH64_MOVW_UABS_G1,
            0x1000010a => Type::AARCH64_MOVW_UABS_G1_NC,
            0x1000010b => Type::AARCH64_MOVW_UABS_G2,
            0x1000010c => Type::AARCH64_MOVW_UABS_G2_NC,
            0x1000010d => Type::AARCH64_MOVW_UABS_G3,
            0x1000010e => Type::AARCH64_MOVW_SABS_G0,
            0x1000010f => Type::AARCH64_MOVW_SABS_G1,
            0x10000110 => Type::AARCH64_MOVW_SABS_G2,
            0x10000111 => Type::AARCH64_LD_PREL_LO19,
            0x10000112 => Type::AARCH64_ADR_PREL_LO21,
            0x10000113 => Type::AARCH64_ADR_PREL_PG_HI21,
            0x10000114 => Type::AARCH64_ADR_PREL_PG_HI21_NC,
            0x10000115 => Type::AARCH64_ADD_ABS_LO12_NC,
            0x10000116 => Type::AARCH64_LDST8_ABS_LO12_NC,
            0x10000117 => Type::AARCH64_TSTBR14,
            0x10000118 => Type::AARCH64_CONDBR19,
            0x1000011a => Type::AARCH64_JUMP26,
            0x1000011b => Type::AARCH64_CALL26,
            0x1000011c => Type::AARCH64_LDST16_ABS_LO12_NC,
            0x1000011d => Type::AARCH64_LDST32_ABS_LO12_NC,
            0x1000011e => Type::AARCH64_LDST64_ABS_LO12_NC,
            0x1000011f => Type::AARCH64_MOVW_PREL_G0,
            0x10000120 => Type::AARCH64_MOVW_PREL_G0_NC,
            0x10000121 => Type::AARCH64_MOVW_PREL_G1,
            0x10000122 => Type::AARCH64_MOVW_PREL_G1_NC,
            0x10000123 => Type::AARCH64_MOVW_PREL_G2,
            0x10000124 => Type::AARCH64_MOVW_PREL_G2_NC,
            0x10000125 => Type::AARCH64_MOVW_PREL_G3,
            0x1000012b => Type::AARCH64_LDST128_ABS_LO12_NC,
            0x1000012c => Type::AARCH64_MOVW_GOTOFF_G0,
            0x1000012d => Type::AARCH64_MOVW_GOTOFF_G0_NC,
            0x1000012e => Type::AARCH64_MOVW_GOTOFF_G1,
            0x1000012f => Type::AARCH64_MOVW_GOTOFF_G1_NC,
            0x10000130 => Type::AARCH64_MOVW_GOTOFF_G2,
            0x10000131 => Type::AARCH64_MOVW_GOTOFF_G2_NC,
            0x10000132 => Type::AARCH64_MOVW_GOTOFF_G3,
            0x10000133 => Type::AARCH64_GOTREL64,
            0x10000134 => Type::AARCH64_GOTREL32,
            0x10000135 => Type::AARCH64_GOT_LD_PREL19,
            0x10000136 => Type::AARCH64_LD64_GOTOFF_LO15,
            0x10000137 => Type::AARCH64_ADR_GOT_PAGE,
            0x10000138 => Type::AARCH64_LD64_GOT_LO12_NC,
            0x10000139 => Type::AARCH64_LD64_GOTPAGE_LO15,
            0x10000200 => Type::AARCH64_TLSGD_ADR_PREL21,
            0x10000201 => Type::AARCH64_TLSGD_ADR_PAGE21,
            0x10000202 => Type::AARCH64_TLSGD_ADD_LO12_NC,
            0x10000203 => Type::AARCH64_TLSGD_MOVW_G1,
            0x10000204 => Type::AARCH64_TLSGD_MOVW_G0_NC,
            0x10000205 => Type::AARCH64_TLSLD_ADR_PREL21,
            0x10000206 => Type::AARCH64_TLSLD_ADR_PAGE21,
            0x10000207 => Type::AARCH64_TLSLD_ADD_LO12_NC,
            0x10000208 => Type::AARCH64_TLSLD_MOVW_G1,
            0x10000209 => Type::AARCH64_TLSLD_MOVW_G0_NC,
            0x1000020a => Type::AARCH64_TLSLD_LD_PREL19,
            0x1000020b => Type::AARCH64_TLSLD_MOVW_DTPREL_G2,
            0x1000020c => Type::AARCH64_TLSLD_MOVW_DTPREL_G1,
            0x1000020d => Type::AARCH64_TLSLD_MOVW_DTPREL_G1_NC,
            0x1000020e => Type::AARCH64_TLSLD_MOVW_DTPREL_G0,
            0x1000020f => Type::AARCH64_TLSLD_MOVW_DTPREL_G0_NC,
            0x10000210 => Type::AARCH64_TLSLD_ADD_DTPREL_HI12,
            0x10000211 => Type::AARCH64_TLSLD_ADD_DTPREL_LO12,
            0x10000212 => Type::AARCH64_TLSLD_ADD_DTPREL_LO12_NC,
            0x10000213 => Type::AARCH64_TLSLD_LDST8_DTPREL_LO12,
            0x10000214 => Type::AARCH64_TLSLD_LDST8_DTPREL_LO12_NC,
            0x10000215 => Type::AARCH64_TLSLD_LDST16_DTPREL_LO12,
            0x10000216 => Type::AARCH64_TLSLD_LDST16_DTPREL_LO12_NC,
            0x10000217 => Type::AARCH64_TLSLD_LDST32_DTPREL_LO12,
            0x10000218 => Type::AARCH64_TLSLD_LDST32_DTPREL_LO12_NC,
            0x10000219 => Type::AARCH64_TLSLD_LDST64_DTPREL_LO12,
            0x1000021a => Type::AARCH64_TLSLD_LDST64_DTPREL_LO12_NC,
            0x1000021b => Type::AARCH64_TLSIE_MOVW_GOTTPREL_G1,
            0x1000021c => Type::AARCH64_TLSIE_MOVW_GOTTPREL_G0_NC,
            0x1000021d => Type::AARCH64_TLSIE_ADR_GOTTPREL_PAGE21,
            0x1000021e => Type::AARCH64_TLSIE_LD64_GOTTPREL_LO12_NC,
            0x1000021f => Type::AARCH64_TLSIE_LD_GOTTPREL_PREL19,
            0x10000220 => Type::AARCH64_TLSLE_MOVW_TPREL_G2,
            0x10000221 => Type::AARCH64_TLSLE_MOVW_TPREL_G1,
            0x10000222 => Type::AARCH64_TLSLE_MOVW_TPREL_G1_NC,
            0x10000223 => Type::AARCH64_TLSLE_MOVW_TPREL_G0,
            0x10000224 => Type::AARCH64_TLSLE_MOVW_TPREL_G0_NC,
            0x10000225 => Type::AARCH64_TLSLE_ADD_TPREL_HI12,
            0x10000226 => Type::AARCH64_TLSLE_ADD_TPREL_LO12,
            0x10000227 => Type::AARCH64_TLSLE_ADD_TPREL_LO12_NC,
            0x10000228 => Type::AARCH64_TLSLE_LDST8_TPREL_LO12,
            0x10000229 => Type::AARCH64_TLSLE_LDST8_TPREL_LO12_NC,
            0x1000022a => Type::AARCH64_TLSLE_LDST16_TPREL_LO12,
            0x1000022b => Type::AARCH64_TLSLE_LDST16_TPREL_LO12_NC,
            0x1000022c => Type::AARCH64_TLSLE_LDST32_TPREL_LO12,
            0x1000022d => Type::AARCH64_TLSLE_LDST32_TPREL_LO12_NC,
            0x1000022e => Type::AARCH64_TLSLE_LDST64_TPREL_LO12,
            0x1000022f => Type::AARCH64_TLSLE_LDST64_TPREL_LO12_NC,
            0x10000230 => Type::AARCH64_TLSDESC_LD_PREL19,
            0x10000231 => Type::AARCH64_TLSDESC_ADR_PREL21,
            0x10000232 => Type::AARCH64_TLSDESC_ADR_PAGE21,
            0x10000233 => Type::AARCH64_TLSDESC_LD64_LO12_NC,
            0x10000234 => Type::AARCH64_TLSDESC_ADD_LO12_NC,
            0x10000235 => Type::AARCH64_TLSDESC_OFF_G1,
            0x10000236 => Type::AARCH64_TLSDESC_OFF_G0_NC,
            0x10000237 => Type::AARCH64_TLSDESC_LDR,
            0x10000238 => Type::AARCH64_TLSDESC_ADD,
            0x10000239 => Type::AARCH64_TLSDESC_CALL,
            0x1000023a => Type::AARCH64_TLSLE_LDST128_TPREL_LO12,
            0x1000023b => Type::AARCH64_TLSLE_LDST128_TPREL_LO12_NC,
            0x1000023c => Type::AARCH64_TLSLD_LDST128_DTPREL_LO12,
            0x1000023d => Type::AARCH64_TLSLD_LDST128_DTPREL_LO12_NC,
            0x10000400 => Type::AARCH64_COPY,
            0x10000401 => Type::AARCH64_GLOB_DAT,
            0x10000402 => Type::AARCH64_JUMP_SLOT,
            0x10000403 => Type::AARCH64_RELATIVE,
            0x10000404 => Type::AARCH64_TLS_DTPREL64,
            0x10000405 => Type::AARCH64_TLS_DTPMOD64,
            0x10000406 => Type::AARCH64_TLS_TPREL64,
            0x10000407 => Type::AARCH64_TLSDESC,
            0x10000408 => Type::AARCH64_IRELATIVE,
            0x18000000 => Type::ARM_NONE,
            0x18000001 => Type::ARM_PC24,
            0x18000002 => Type::ARM_ABS32,
            0x18000003 => Type::ARM_REL32,
            0x18000004 => Type::ARM_LDR_PC_G0,
            0x18000005 => Type::ARM_ABS16,
            0x18000006 => Type::ARM_ABS12,
            0x18000007 => Type::ARM_THM_ABS5,
            0x18000008 => Type::ARM_ABS8,
            0x18000009 => Type::ARM_SBREL32,
            0x1800000a => Type::ARM_THM_CALL,
            0x1800000b => Type::ARM_THM_PC8,
            0x1800000c => Type::ARM_BREL_ADJ,
            0x1800000d => Type::ARM_TLS_DESC,
            0x1800000e => Type::ARM_THM_SWI8,
            0x1800000f => Type::ARM_XPC25,
            0x18000010 => Type::ARM_THM_XPC22,
            0x18000011 => Type::ARM_TLS_DTPMOD32,
            0x18000012 => Type::ARM_TLS_DTPOFF32,
            0x18000013 => Type::ARM_TLS_TPOFF32,
            0x18000014 => Type::ARM_COPY,
            0x18000015 => Type::ARM_GLOB_DAT,
            0x18000016 => Type::ARM_JUMP_SLOT,
            0x18000017 => Type::ARM_RELATIVE,
            0x18000018 => Type::ARM_GOTOFF32,
            0x18000019 => Type::ARM_BASE_PREL,
            0x1800001a => Type::ARM_GOT_BREL,
            0x1800001b => Type::ARM_PLT32,
            0x1800001c => Type::ARM_CALL,
            0x1800001d => Type::ARM_JUMP24,
            0x1800001e => Type::ARM_THM_JUMP24,
            0x1800001f => Type::ARM_BASE_ABS,
            0x18000020 => Type::ARM_ALU_PCREL_7_0,
            0x18000021 => Type::ARM_ALU_PCREL_15_8,
            0x18000022 => Type::ARM_ALU_PCREL_23_15,
            0x18000023 => Type::ARM_LDR_SBREL_11_0_NC,
            0x18000024 => Type::ARM_ALU_SBREL_19_12_NC,
            0x18000025 => Type::ARM_ALU_SBREL_27_20_CK,
            0x18000026 => Type::ARM_TARGET1,
            0x18000027 => Type::ARM_SBREL31,
            0x18000028 => Type::ARM_V4BX,
            0x18000029 => Type::ARM_TARGET2,
            0x1800002a => Type::ARM_PREL31,
            0x1800002b => Type::ARM_MOVW_ABS_NC,
            0x1800002c => Type::ARM_MOVT_ABS,
            0x1800002d => Type::ARM_MOVW_PREL_NC,
            0x1800002e => Type::ARM_MOVT_PREL,
            0x1800002f => Type::ARM_THM_MOVW_ABS_NC,
            0x18000030 => Type::ARM_THM_MOVT_ABS,
            0x18000031 => Type::ARM_THM_MOVW_PREL_NC,
            0x18000032 => Type::ARM_THM_MOVT_PREL,
            0x18000033 => Type::ARM_THM_JUMP19,
            0x18000034 => Type::ARM_THM_JUMP6,
            0x18000035 => Type::ARM_THM_ALU_PREL_11_0,
            0x18000036 => Type::ARM_THM_PC12,
            0x18000037 => Type::ARM_ABS32_NOI,
            0x18000038 => Type::ARM_REL32_NOI,
            0x18000039 => Type::ARM_ALU_PC_G0_NC,
            0x1800003a => Type::ARM_ALU_PC_G0,
            0x1800003b => Type::ARM_ALU_PC_G1_NC,
            0x1800003c => Type::ARM_ALU_PC_G1,
            0x1800003d => Type::ARM_ALU_PC_G2,
            0x1800003e => Type::ARM_LDR_PC_G1,
            0x1800003f => Type::ARM_LDR_PC_G2,
            0x18000040 => Type::ARM_LDRS_PC_G0,
            0x18000041 => Type::ARM_LDRS_PC_G1,
            0x18000042 => Type::ARM_LDRS_PC_G2,
            0x18000043 => Type::ARM_LDC_PC_G0,
            0x18000044 => Type::ARM_LDC_PC_G1,
            0x18000045 => Type::ARM_LDC_PC_G2,
            0x18000046 => Type::ARM_ALU_SB_G0_NC,
            0x18000047 => Type::ARM_ALU_SB_G0,
            0x18000048 => Type::ARM_ALU_SB_G1_NC,
            0x18000049 => Type::ARM_ALU_SB_G1,
            0x1800004a => Type::ARM_ALU_SB_G2,
            0x1800004b => Type::ARM_LDR_SB_G0,
            0x1800004c => Type::ARM_LDR_SB_G1,
            0x1800004d => Type::ARM_LDR_SB_G2,
            0x1800004e => Type::ARM_LDRS_SB_G0,
            0x1800004f => Type::ARM_LDRS_SB_G1,
            0x18000050 => Type::ARM_LDRS_SB_G2,
            0x18000051 => Type::ARM_LDC_SB_G0,
            0x18000052 => Type::ARM_LDC_SB_G1,
            0x18000053 => Type::ARM_LDC_SB_G2,
            0x18000054 => Type::ARM_MOVW_BREL_NC,
            0x18000055 => Type::ARM_MOVT_BREL,
            0x18000056 => Type::ARM_MOVW_BREL,
            0x18000057 => Type::ARM_THM_MOVW_BREL_NC,
            0x18000058 => Type::ARM_THM_MOVT_BREL,
            0x18000059 => Type::ARM_THM_MOVW_BREL,
            0x1800005a => Type::ARM_TLS_GOTDESC,
            0x1800005b => Type::ARM_TLS_CALL,
            0x1800005c => Type::ARM_TLS_DESCSEQ,
            0x1800005d => Type::ARM_THM_TLS_CALL,
            0x1800005e => Type::ARM_PLT32_ABS,
            0x1800005f => Type::ARM_GOT_ABS,
            0x18000060 => Type::ARM_GOT_PREL,
            0x18000061 => Type::ARM_GOT_BREL12,
            0x18000062 => Type::ARM_GOTOFF12,
            0x18000063 => Type::ARM_GOTRELAX,
            0x18000064 => Type::ARM_GNU_VTENTRY,
            0x18000065 => Type::ARM_GNU_VTINHERIT,
            0x18000066 => Type::ARM_THM_JUMP11,
            0x18000067 => Type::ARM_THM_JUMP8,
            0x18000068 => Type::ARM_TLS_GD32,
            0x18000069 => Type::ARM_TLS_LDM32,
            0x1800006a => Type::ARM_TLS_LDO32,
            0x1800006b => Type::ARM_TLS_IE32,
            0x1800006c => Type::ARM_TLS_LE32,
            0x1800006d => Type::ARM_TLS_LDO12,
            0x1800006e => Type::ARM_TLS_LE12,
            0x1800006f => Type::ARM_TLS_IE12GP,
            0x18000070 => Type::ARM_PRIVATE_0,
            0x18000071 => Type::ARM_PRIVATE_1,
            0x18000072 => Type::ARM_PRIVATE_2,
            0x18000073 => Type::ARM_PRIVATE_3,
            0x18000074 => Type::ARM_PRIVATE_4,
            0x18000075 => Type::ARM_PRIVATE_5,
            0x18000076 => Type::ARM_PRIVATE_6,
            0x18000077 => Type::ARM_PRIVATE_7,
            0x18000078 => Type::ARM_PRIVATE_8,
            0x18000079 => Type::ARM_PRIVATE_9,
            0x1800007a => Type::ARM_PRIVATE_10,
            0x1800007b => Type::ARM_PRIVATE_11,
            0x1800007c => Type::ARM_PRIVATE_12,
            0x1800007d => Type::ARM_PRIVATE_13,
            0x1800007e => Type::ARM_PRIVATE_14,
            0x1800007f => Type::ARM_PRIVATE_15,
            0x18000080 => Type::ARM_ME_TOO,
            0x18000081 => Type::ARM_THM_TLS_DESCSEQ16,
            0x18000082 => Type::ARM_THM_TLS_DESCSEQ32,
            0x180000a0 => Type::ARM_IRELATIVE,
            0x180000f9 => Type::ARM_RXPC25,
            0x180000fa => Type::ARM_RSBREL32,
            0x180000fb => Type::ARM_THM_RPC22,
            0x180000fc => Type::ARM_RREL32,
            0x180000fd => Type::ARM_RPC24,
            0x180000fe => Type::ARM_RBASE,
            0x20000000 => Type::HEX_NONE,
            0x20000001 => Type::HEX_B22_PCREL,
            0x20000002 => Type::HEX_B15_PCREL,
            0x20000003 => Type::HEX_B7_PCREL,
            0x20000004 => Type::HEX_LO16,
            0x20000005 => Type::HEX_HI16,
            0x20000006 => Type::HEX_32,
            0x20000007 => Type::HEX_16,
            0x20000008 => Type::HEX_8,
            0x20000009 => Type::HEX_GPREL16_0,
            0x2000000a => Type::HEX_GPREL16_1,
            0x2000000b => Type::HEX_GPREL16_2,
            0x2000000c => Type::HEX_GPREL16_3,
            0x2000000d => Type::HEX_HL16,
            0x2000000e => Type::HEX_B13_PCREL,
            0x2000000f => Type::HEX_B9_PCREL,
            0x20000010 => Type::HEX_B32_PCREL_X,
            0x20000011 => Type::HEX_32_6_X,
            0x20000012 => Type::HEX_B22_PCREL_X,
            0x20000013 => Type::HEX_B15_PCREL_X,
            0x20000014 => Type::HEX_B13_PCREL_X,
            0x20000015 => Type::HEX_B9_PCREL_X,
            0x20000016 => Type::HEX_B7_PCREL_X,
            0x20000017 => Type::HEX_16_X,
            0x20000018 => Type::HEX_12_X,
            0x20000019 => Type::HEX_11_X,
            0x2000001a => Type::HEX_10_X,
            0x2000001b => Type::HEX_9_X,
            0x2000001c => Type::HEX_8_X,
            0x2000001d => Type::HEX_7_X,
            0x2000001e => Type::HEX_6_X,
            0x2000001f => Type::HEX_32_PCREL,
            0x20000020 => Type::HEX_COPY,
            0x20000021 => Type::HEX_GLOB_DAT,
            0x20000022 => Type::HEX_JMP_SLOT,
            0x20000023 => Type::HEX_RELATIVE,
            0x20000024 => Type::HEX_PLT_B22_PCREL,
            0x20000025 => Type::HEX_GOTREL_LO16,
            0x20000026 => Type::HEX_GOTREL_HI16,
            0x20000027 => Type::HEX_GOTREL_32,
            0x20000028 => Type::HEX_GOT_LO16,
            0x20000029 => Type::HEX_GOT_HI16,
            0x2000002a => Type::HEX_GOT_32,
            0x2000002b => Type::HEX_GOT_16,
            0x2000002c => Type::HEX_DTPMOD_32,
            0x2000002d => Type::HEX_DTPREL_LO16,
            0x2000002e => Type::HEX_DTPREL_HI16,
            0x2000002f => Type::HEX_DTPREL_32,
            0x20000030 => Type::HEX_DTPREL_16,
            0x20000031 => Type::HEX_GD_PLT_B22_PCREL,
            0x20000032 => Type::HEX_GD_GOT_LO16,
            0x20000033 => Type::HEX_GD_GOT_HI16,
            0x20000034 => Type::HEX_GD_GOT_32,
            0x20000035 => Type::HEX_GD_GOT_16,
            0x20000036 => Type::HEX_IE_LO16,
            0x20000037 => Type::HEX_IE_HI16,
            0x20000038 => Type::HEX_IE_32,
            0x20000039 => Type::HEX_IE_GOT_LO16,
            0x2000003a => Type::HEX_IE_GOT_HI16,
            0x2000003b => Type::HEX_IE_GOT_32,
            0x2000003c => Type::HEX_IE_GOT_16,
            0x2000003d => Type::HEX_TPREL_LO16,
            0x2000003e => Type::HEX_TPREL_HI16,
            0x2000003f => Type::HEX_TPREL_32,
            0x20000040 => Type::HEX_TPREL_16,
            0x20000041 => Type::HEX_6_PCREL_X,
            0x20000042 => Type::HEX_GOTREL_32_6_X,
            0x20000043 => Type::HEX_GOTREL_16_X,
            0x20000044 => Type::HEX_GOTREL_11_X,
            0x20000045 => Type::HEX_GOT_32_6_X,
            0x20000046 => Type::HEX_GOT_16_X,
            0x20000047 => Type::HEX_GOT_11_X,
            0x20000048 => Type::HEX_DTPREL_32_6_X,
            0x20000049 => Type::HEX_DTPREL_16_X,
            0x2000004a => Type::HEX_DTPREL_11_X,
            0x2000004b => Type::HEX_GD_GOT_32_6_X,
            0x2000004c => Type::HEX_GD_GOT_16_X,
            0x2000004d => Type::HEX_GD_GOT_11_X,
            0x2000004e => Type::HEX_IE_32_6_X,
            0x2000004f => Type::HEX_IE_16_X,
            0x20000050 => Type::HEX_IE_GOT_32_6_X,
            0x20000051 => Type::HEX_IE_GOT_16_X,
            0x20000052 => Type::HEX_IE_GOT_11_X,
            0x20000053 => Type::HEX_TPREL_32_6_X,
            0x20000054 => Type::HEX_TPREL_16_X,
            0x20000055 => Type::HEX_TPREL_11_X,
            0x20000056 => Type::HEX_LD_PLT_B22_PCREL,
            0x20000057 => Type::HEX_LD_GOT_LO16,
            0x20000058 => Type::HEX_LD_GOT_HI16,
            0x20000059 => Type::HEX_LD_GOT_32,
            0x2000005a => Type::HEX_LD_GOT_16,
            0x2000005b => Type::HEX_LD_GOT_32_6_X,
            0x2000005c => Type::HEX_LD_GOT_16_X,
            0x2000005d => Type::HEX_LD_GOT_11_X,
            0x28000000 => Type::X86_NONE,
            0x28000001 => Type::X86_32,
            0x28000002 => Type::X86_PC32,
            0x28000003 => Type::X86_GOT32,
            0x28000004 => Type::X86_PLT32,
            0x28000005 => Type::X86_COPY,
            0x28000006 => Type::X86_GLOB_DAT,
            0x28000007 => Type::X86_JUMP_SLOT,
            0x28000008 => Type::X86_RELATIVE,
            0x28000009 => Type::X86_GOTOFF,
            0x2800000a => Type::X86_GOTPC,
            0x2800000b => Type::X86_32PLT,
            0x2800000e => Type::X86_TLS_TPOFF,
            0x2800000f => Type::X86_TLS_IE,
            0x28000010 => Type::X86_TLS_GOTIE,
            0x28000011 => Type::X86_TLS_LE,
            0x28000012 => Type::X86_TLS_GD,
            0x28000013 => Type::X86_TLS_LDM,
            0x28000014 => Type::X86_16,
            0x28000015 => Type::X86_PC16,
            0x28000016 => Type::X86_8,
            0x28000017 => Type::X86_PC8,
            0x28000018 => Type::X86_TLS_GD_32,
            0x28000019 => Type::X86_TLS_GD_PUSH,
            0x2800001a => Type::X86_TLS_GD_CALL,
            0x2800001b => Type::X86_TLS_GD_POP,
            0x2800001c => Type::X86_TLS_LDM_32,
            0x2800001d => Type::X86_TLS_LDM_PUSH,
            0x2800001e => Type::X86_TLS_LDM_CALL,
            0x2800001f => Type::X86_TLS_LDM_POP,
            0x28000020 => Type::X86_TLS_LDO_32,
            0x28000021 => Type::X86_TLS_IE_32,
            0x28000022 => Type::X86_TLS_LE_32,
            0x28000023 => Type::X86_TLS_DTPMOD32,
            0x28000024 => Type::X86_TLS_DTPOFF32,
            0x28000025 => Type::X86_TLS_TPOFF32,
            0x28000027 => Type::X86_TLS_GOTDESC,
            0x28000028 => Type::X86_TLS_DESC_CALL,
            0x28000029 => Type::X86_TLS_DESC,
            0x2800002a => Type::X86_IRELATIVE,
            0x30000000 => Type::LARCH_NONE,
            0x30000001 => Type::LARCH_32,
            0x30000002 => Type::LARCH_64,
            0x30000003 => Type::LARCH_RELATIVE,
            0x30000004 => Type::LARCH_COPY,
            0x30000005 => Type::LARCH_JUMP_SLOT,
            0x30000006 => Type::LARCH_TLS_DTPMOD32,
            0x30000007 => Type::LARCH_TLS_DTPMOD64,
            0x30000008 => Type::LARCH_TLS_DTPREL32,
            0x30000009 => Type::LARCH_TLS_DTPREL64,
            0x3000000a => Type::LARCH_TLS_TPREL32,
            0x3000000b => Type::LARCH_TLS_TPREL64,
            0x3000000c => Type::LARCH_IRELATIVE,
            0x30000014 => Type::LARCH_MARK_LA,
            0x30000015 => Type::LARCH_MARK_PCREL,
            0x30000016 => Type::LARCH_SOP_PUSH_PCREL,
            0x30000017 => Type::LARCH_SOP_PUSH_ABSOLUTE,
            0x30000018 => Type::LARCH_SOP_PUSH_DUP,
            0x30000019 => Type::LARCH_SOP_PUSH_GPREL,
            0x3000001a => Type::LARCH_SOP_PUSH_TLS_TPREL,
            0x3000001b => Type::LARCH_SOP_PUSH_TLS_GOT,
            0x3000001c => Type::LARCH_SOP_PUSH_TLS_GD,
            0x3000001d => Type::LARCH_SOP_PUSH_PLT_PCREL,
            0x3000001e => Type::LARCH_SOP_ASSERT,
            0x3000001f => Type::LARCH_SOP_NOT,
            0x30000020 => Type::LARCH_SOP_SUB,
            0x30000021 => Type::LARCH_SOP_SL,
            0x30000022 => Type::LARCH_SOP_SR,
            0x30000023 => Type::LARCH_SOP_ADD,
            0x30000024 => Type::LARCH_SOP_AND,
            0x30000025 => Type::LARCH_SOP_IF_ELSE,
            0x30000026 => Type::LARCH_SOP_POP_32_S_10_5,
            0x30000027 => Type::LARCH_SOP_POP_32_U_10_12,
            0x30000028 => Type::LARCH_SOP_POP_32_S_10_12,
            0x30000029 => Type::LARCH_SOP_POP_32_S_10_16,
            0x3000002a => Type::LARCH_SOP_POP_32_S_10_16_S2,
            0x3000002b => Type::LARCH_SOP_POP_32_S_5_20,
            0x3000002c => Type::LARCH_SOP_POP_32_S_0_5_10_16_S2,
            0x3000002d => Type::LARCH_SOP_POP_32_S_0_10_10_16_S2,
            0x3000002e => Type::LARCH_SOP_POP_32_U,
            0x3000002f => Type::LARCH_ADD8,
            0x30000030 => Type::LARCH_ADD16,
            0x30000031 => Type::LARCH_ADD24,
            0x30000032 => Type::LARCH_ADD32,
            0x30000033 => Type::LARCH_ADD64,
            0x30000034 => Type::LARCH_SUB8,
            0x30000035 => Type::LARCH_SUB16,
            0x30000036 => Type::LARCH_SUB24,
            0x30000037 => Type::LARCH_SUB32,
            0x30000038 => Type::LARCH_SUB64,
            0x30000039 => Type::LARCH_GNU_VTINHERIT,
            0x3000003a => Type::LARCH_GNU_VTENTRY,
            0x30000040 => Type::LARCH_B16,
            0x30000041 => Type::LARCH_B21,
            0x30000042 => Type::LARCH_B26,
            0x30000043 => Type::LARCH_ABS_HI20,
            0x30000044 => Type::LARCH_ABS_LO12,
            0x30000045 => Type::LARCH_ABS64_LO20,
            0x30000046 => Type::LARCH_ABS64_HI12,
            0x30000047 => Type::LARCH_PCALA_HI20,
            0x30000048 => Type::LARCH_PCALA_LO12,
            0x30000049 => Type::LARCH_PCALA64_LO20,
            0x3000004a => Type::LARCH_PCALA64_HI12,
            0x3000004b => Type::LARCH_GOT_PC_HI20,
            0x3000004c => Type::LARCH_GOT_PC_LO12,
            0x3000004d => Type::LARCH_GOT64_PC_LO20,
            0x3000004e => Type::LARCH_GOT64_PC_HI12,
            0x3000004f => Type::LARCH_GOT_HI20,
            0x30000050 => Type::LARCH_GOT_LO12,
            0x30000051 => Type::LARCH_GOT64_LO20,
            0x30000052 => Type::LARCH_GOT64_HI12,
            0x30000053 => Type::LARCH_TLS_LE_HI20,
            0x30000054 => Type::LARCH_TLS_LE_LO12,
            0x30000055 => Type::LARCH_TLS_LE64_LO20,
            0x30000056 => Type::LARCH_TLS_LE64_HI12,
            0x30000057 => Type::LARCH_TLS_IE_PC_HI20,
            0x30000058 => Type::LARCH_TLS_IE_PC_LO12,
            0x30000059 => Type::LARCH_TLS_IE64_PC_LO20,
            0x3000005a => Type::LARCH_TLS_IE64_PC_HI12,
            0x3000005b => Type::LARCH_TLS_IE_HI20,
            0x3000005c => Type::LARCH_TLS_IE_LO12,
            0x3000005d => Type::LARCH_TLS_IE64_LO20,
            0x3000005e => Type::LARCH_TLS_IE64_HI12,
            0x3000005f => Type::LARCH_TLS_LD_PC_HI20,
            0x30000060 => Type::LARCH_TLS_LD_HI20,
            0x30000061 => Type::LARCH_TLS_GD_PC_HI20,
            0x30000062 => Type::LARCH_TLS_GD_HI20,
            0x30000063 => Type::LARCH_32_PCREL,
            0x30000064 => Type::LARCH_RELAX,
            0x30000066 => Type::LARCH_ALIGN,
            0x30000067 => Type::LARCH_PCREL20_S2,
            0x30000069 => Type::LARCH_ADD6,
            0x3000006a => Type::LARCH_SUB6,
            0x3000006b => Type::LARCH_ADD_ULEB128,
            0x3000006c => Type::LARCH_SUB_ULEB128,
            0x3000006d => Type::LARCH_64_PCREL,
            0x3000006e => Type::LARCH_CALL36,
            0x3000000d => Type::LARCH_TLS_DESC32,
            0x3000000e => Type::LARCH_TLS_DESC64,
            0x3000006f => Type::LARCH_TLS_DESC_PC_HI20,
            0x30000070 => Type::LARCH_TLS_DESC_PC_LO12,
            0x30000071 => Type::LARCH_TLS_DESC64_PC_LO20,
            0x30000072 => Type::LARCH_TLS_DESC64_PC_HI12,
            0x30000073 => Type::LARCH_TLS_DESC_HI20,
            0x30000074 => Type::LARCH_TLS_DESC_LO12,
            0x30000075 => Type::LARCH_TLS_DESC64_LO20,
            0x30000076 => Type::LARCH_TLS_DESC64_HI12,
            0x30000077 => Type::LARCH_TLS_DESC_LD,
            0x30000078 => Type::LARCH_TLS_DESC_CALL,
            0x30000079 => Type::LARCH_TLS_LE_HI20_R,
            0x3000007a => Type::LARCH_TLS_LE_ADD_R,
            0x3000007b => Type::LARCH_TLS_LE_LO12_R,
            0x3000007c => Type::LARCH_TLS_LD_PCREL20_S2,
            0x3000007d => Type::LARCH_TLS_GD_PCREL20_S2,
            0x3000007e => Type::LARCH_TLS_DESC_PCREL20_S2,
            0x38000000 => Type::MIPS_NONE,
            0x38000001 => Type::MIPS_16,
            0x38000002 => Type::MIPS_32,
            0x38000003 => Type::MIPS_REL32,
            0x38000004 => Type::MIPS_26,
            0x38000005 => Type::MIPS_HI16,
            0x38000006 => Type::MIPS_LO16,
            0x38000007 => Type::MIPS_GPREL16,
            0x38000008 => Type::MIPS_LITERAL,
            0x38000009 => Type::MIPS_GOT16,
            0x3800000a => Type::MIPS_PC16,
            0x3800000b => Type::MIPS_CALL16,
            0x3800000c => Type::MIPS_GPREL32,
            0x3800000d => Type::MIPS_UNUSED1,
            0x3800000e => Type::MIPS_UNUSED2,
            0x3800000f => Type::MIPS_UNUSED3,
            0x38000010 => Type::MIPS_SHIFT5,
            0x38000011 => Type::MIPS_SHIFT6,
            0x38000012 => Type::MIPS_64,
            0x38000013 => Type::MIPS_GOT_DISP,
            0x38000014 => Type::MIPS_GOT_PAGE,
            0x38000015 => Type::MIPS_GOT_OFST,
            0x38000016 => Type::MIPS_GOT_HI16,
            0x38000017 => Type::MIPS_GOT_LO16,
            0x38000018 => Type::MIPS_SUB,
            0x38000019 => Type::MIPS_INSERT_A,
            0x3800001a => Type::MIPS_INSERT_B,
            0x3800001b => Type::MIPS_DELETE,
            0x3800001c => Type::MIPS_HIGHER,
            0x3800001d => Type::MIPS_HIGHEST,
            0x3800001e => Type::MIPS_CALL_HI16,
            0x3800001f => Type::MIPS_CALL_LO16,
            0x38000020 => Type::MIPS_SCN_DISP,
            0x38000021 => Type::MIPS_REL16,
            0x38000022 => Type::MIPS_ADD_IMMEDIATE,
            0x38000023 => Type::MIPS_PJUMP,
            0x38000024 => Type::MIPS_RELGOT,
            0x38000025 => Type::MIPS_JALR,
            0x38000026 => Type::MIPS_TLS_DTPMOD32,
            0x38000027 => Type::MIPS_TLS_DTPREL32,
            0x38000028 => Type::MIPS_TLS_DTPMOD64,
            0x38000029 => Type::MIPS_TLS_DTPREL64,
            0x3800002a => Type::MIPS_TLS_GD,
            0x3800002b => Type::MIPS_TLS_LDM,
            0x3800002c => Type::MIPS_TLS_DTPREL_HI16,
            0x3800002d => Type::MIPS_TLS_DTPREL_LO16,
            0x3800002e => Type::MIPS_TLS_GOTTPREL,
            0x3800002f => Type::MIPS_TLS_TPREL32,
            0x38000030 => Type::MIPS_TLS_TPREL64,
            0x38000031 => Type::MIPS_TLS_TPREL_HI16,
            0x38000032 => Type::MIPS_TLS_TPREL_LO16,
            0x38000033 => Type::MIPS_GLOB_DAT,
            0x3800003c => Type::MIPS_PC21_S2,
            0x3800003d => Type::MIPS_PC26_S2,
            0x3800003e => Type::MIPS_PC18_S3,
            0x3800003f => Type::MIPS_PC19_S2,
            0x38000040 => Type::MIPS_PCHI16,
            0x38000041 => Type::MIPS_PCLO16,
            0x38000064 => Type::MIPS16_26,
            0x38000065 => Type::MIPS16_GPREL,
            0x38000066 => Type::MIPS16_GOT16,
            0x38000067 => Type::MIPS16_CALL16,
            0x38000068 => Type::MIPS16_HI16,
            0x38000069 => Type::MIPS16_LO16,
            0x3800006a => Type::MIPS16_TLS_GD,
            0x3800006b => Type::MIPS16_TLS_LDM,
            0x3800006c => Type::MIPS16_TLS_DTPREL_HI16,
            0x3800006d => Type::MIPS16_TLS_DTPREL_LO16,
            0x3800006e => Type::MIPS16_TLS_GOTTPREL,
            0x3800006f => Type::MIPS16_TLS_TPREL_HI16,
            0x38000070 => Type::MIPS16_TLS_TPREL_LO16,
            0x3800007e => Type::MIPS_COPY,
            0x3800007f => Type::MIPS_JUMP_SLOT,
            0x38000085 => Type::MICROMIPS_26_S1,
            0x38000086 => Type::MICROMIPS_HI16,
            0x38000087 => Type::MICROMIPS_LO16,
            0x38000088 => Type::MICROMIPS_GPREL16,
            0x38000089 => Type::MICROMIPS_LITERAL,
            0x3800008a => Type::MICROMIPS_GOT16,
            0x3800008b => Type::MICROMIPS_PC7_S1,
            0x3800008c => Type::MICROMIPS_PC10_S1,
            0x3800008d => Type::MICROMIPS_PC16_S1,
            0x3800008e => Type::MICROMIPS_CALL16,
            0x38000091 => Type::MICROMIPS_GOT_DISP,
            0x38000092 => Type::MICROMIPS_GOT_PAGE,
            0x38000093 => Type::MICROMIPS_GOT_OFST,
            0x38000094 => Type::MICROMIPS_GOT_HI16,
            0x38000095 => Type::MICROMIPS_GOT_LO16,
            0x38000096 => Type::MICROMIPS_SUB,
            0x38000097 => Type::MICROMIPS_HIGHER,
            0x38000098 => Type::MICROMIPS_HIGHEST,
            0x38000099 => Type::MICROMIPS_CALL_HI16,
            0x3800009a => Type::MICROMIPS_CALL_LO16,
            0x3800009b => Type::MICROMIPS_SCN_DISP,
            0x3800009c => Type::MICROMIPS_JALR,
            0x3800009d => Type::MICROMIPS_HI0_LO16,
            0x380000a2 => Type::MICROMIPS_TLS_GD,
            0x380000a3 => Type::MICROMIPS_TLS_LDM,
            0x380000a4 => Type::MICROMIPS_TLS_DTPREL_HI16,
            0x380000a5 => Type::MICROMIPS_TLS_DTPREL_LO16,
            0x380000a6 => Type::MICROMIPS_TLS_GOTTPREL,
            0x380000a9 => Type::MICROMIPS_TLS_TPREL_HI16,
            0x380000aa => Type::MICROMIPS_TLS_TPREL_LO16,
            0x380000ac => Type::MICROMIPS_GPREL7_S2,
            0x380000ad => Type::MICROMIPS_PC23_S2,
            0x380000ae => Type::MICROMIPS_PC21_S2,
            0x380000af => Type::MICROMIPS_PC26_S2,
            0x380000b0 => Type::MICROMIPS_PC18_S3,
            0x380000b1 => Type::MICROMIPS_PC19_S2,
            0x380000da => Type::MIPS_NUM,
            0x380000f8 => Type::MIPS_PC32,
            0x380000f9 => Type::MIPS_EH,
            0x40000000 => Type::PPC_NONE,
            0x40000001 => Type::PPC_ADDR32,
            0x40000002 => Type::PPC_ADDR24,
            0x40000003 => Type::PPC_ADDR16,
            0x40000004 => Type::PPC_ADDR16_LO,
            0x40000005 => Type::PPC_ADDR16_HI,
            0x40000006 => Type::PPC_ADDR16_HA,
            0x40000007 => Type::PPC_ADDR14,
            0x40000008 => Type::PPC_ADDR14_BRTAKEN,
            0x40000009 => Type::PPC_ADDR14_BRNTAKEN,
            0x4000000a => Type::PPC_REL24,
            0x4000000b => Type::PPC_REL14,
            0x4000000c => Type::PPC_REL14_BRTAKEN,
            0x4000000d => Type::PPC_REL14_BRNTAKEN,
            0x4000000e => Type::PPC_GOT16,
            0x4000000f => Type::PPC_GOT16_LO,
            0x40000010 => Type::PPC_GOT16_HI,
            0x40000011 => Type::PPC_GOT16_HA,
            0x40000012 => Type::PPC_PLTREL24,
            0x40000015 => Type::PPC_JMP_SLOT,
            0x40000016 => Type::PPC_RELATIVE,
            0x40000017 => Type::PPC_LOCAL24PC,
            0x4000001a => Type::PPC_REL32,
            0x40000043 => Type::PPC_TLS,
            0x40000044 => Type::PPC_DTPMOD32,
            0x40000045 => Type::PPC_TPREL16,
            0x40000046 => Type::PPC_TPREL16_LO,
            0x40000047 => Type::PPC_TPREL16_HI,
            0x40000048 => Type::PPC_TPREL16_HA,
            0x40000049 => Type::PPC_TPREL32,
            0x4000004a => Type::PPC_DTPREL16,
            0x4000004b => Type::PPC_DTPREL16_LO,
            0x4000004c => Type::PPC_DTPREL16_HI,
            0x4000004d => Type::PPC_DTPREL16_HA,
            0x4000004e => Type::PPC_DTPREL32,
            0x4000004f => Type::PPC_GOT_TLSGD16,
            0x40000050 => Type::PPC_GOT_TLSGD16_LO,
            0x40000051 => Type::PPC_GOT_TLSGD16_HI,
            0x40000052 => Type::PPC_GOT_TLSGD16_HA,
            0x40000053 => Type::PPC_GOT_TLSLD16,
            0x40000054 => Type::PPC_GOT_TLSLD16_LO,
            0x40000055 => Type::PPC_GOT_TLSLD16_HI,
            0x40000056 => Type::PPC_GOT_TLSLD16_HA,
            0x40000057 => Type::PPC_GOT_TPREL16,
            0x40000058 => Type::PPC_GOT_TPREL16_LO,
            0x40000059 => Type::PPC_GOT_TPREL16_HI,
            0x4000005a => Type::PPC_GOT_TPREL16_HA,
            0x4000005b => Type::PPC_GOT_DTPREL16,
            0x4000005c => Type::PPC_GOT_DTPREL16_LO,
            0x4000005d => Type::PPC_GOT_DTPREL16_HI,
            0x4000005e => Type::PPC_GOT_DTPREL16_HA,
            0x4000005f => Type::PPC_TLSGD,
            0x40000060 => Type::PPC_TLSLD,
            0x400000f9 => Type::PPC_REL16,
            0x400000fa => Type::PPC_REL16_LO,
            0x400000fb => Type::PPC_REL16_HI,
            0x400000fc => Type::PPC_REL16_HA,
            0x48000000 => Type::PPC64_NONE,
            0x48000001 => Type::PPC64_ADDR32,
            0x48000002 => Type::PPC64_ADDR24,
            0x48000003 => Type::PPC64_ADDR16,
            0x48000004 => Type::PPC64_ADDR16_LO,
            0x48000005 => Type::PPC64_ADDR16_HI,
            0x48000006 => Type::PPC64_ADDR16_HA,
            0x48000007 => Type::PPC64_ADDR14,
            0x48000008 => Type::PPC64_ADDR14_BRTAKEN,
            0x48000009 => Type::PPC64_ADDR14_BRNTAKEN,
            0x4800000a => Type::PPC64_REL24,
            0x4800000b => Type::PPC64_REL14,
            0x4800000c => Type::PPC64_REL14_BRTAKEN,
            0x4800000d => Type::PPC64_REL14_BRNTAKEN,
            0x4800000e => Type::PPC64_GOT16,
            0x4800000f => Type::PPC64_GOT16_LO,
            0x48000010 => Type::PPC64_GOT16_HI,
            0x48000011 => Type::PPC64_GOT16_HA,
            0x48000015 => Type::PPC64_JMP_SLOT,
            0x48000016 => Type::PPC64_RELATIVE,
            0x4800001a => Type::PPC64_REL32,
            0x48000026 => Type::PPC64_ADDR64,
            0x48000027 => Type::PPC64_ADDR16_HIGHER,
            0x48000028 => Type::PPC64_ADDR16_HIGHERA,
            0x48000029 => Type::PPC64_ADDR16_HIGHEST,
            0x4800002a => Type::PPC64_ADDR16_HIGHESTA,
            0x4800002c => Type::PPC64_REL64,
            0x4800002f => Type::PPC64_TOC16,
            0x48000030 => Type::PPC64_TOC16_LO,
            0x48000031 => Type::PPC64_TOC16_HI,
            0x48000032 => Type::PPC64_TOC16_HA,
            0x48000033 => Type::PPC64_TOC,
            0x48000038 => Type::PPC64_ADDR16_DS,
            0x48000039 => Type::PPC64_ADDR16_LO_DS,
            0x4800003a => Type::PPC64_GOT16_DS,
            0x4800003b => Type::PPC64_GOT16_LO_DS,
            0x4800003f => Type::PPC64_TOC16_DS,
            0x48000040 => Type::PPC64_TOC16_LO_DS,
            0x48000043 => Type::PPC64_TLS,
            0x48000044 => Type::PPC64_DTPMOD64,
            0x48000045 => Type::PPC64_TPREL16,
            0x48000046 => Type::PPC64_TPREL16_LO,
            0x48000047 => Type::PPC64_TPREL16_HI,
            0x48000048 => Type::PPC64_TPREL16_HA,
            0x48000049 => Type::PPC64_TPREL64,
            0x4800004a => Type::PPC64_DTPREL16,
            0x4800004b => Type::PPC64_DTPREL16_LO,
            0x4800004c => Type::PPC64_DTPREL16_HI,
            0x4800004d => Type::PPC64_DTPREL16_HA,
            0x4800004e => Type::PPC64_DTPREL64,
            0x4800004f => Type::PPC64_GOT_TLSGD16,
            0x48000050 => Type::PPC64_GOT_TLSGD16_LO,
            0x48000051 => Type::PPC64_GOT_TLSGD16_HI,
            0x48000052 => Type::PPC64_GOT_TLSGD16_HA,
            0x48000053 => Type::PPC64_GOT_TLSLD16,
            0x48000054 => Type::PPC64_GOT_TLSLD16_LO,
            0x48000055 => Type::PPC64_GOT_TLSLD16_HI,
            0x48000056 => Type::PPC64_GOT_TLSLD16_HA,
            0x48000057 => Type::PPC64_GOT_TPREL16_DS,
            0x48000058 => Type::PPC64_GOT_TPREL16_LO_DS,
            0x48000059 => Type::PPC64_GOT_TPREL16_HI,
            0x4800005a => Type::PPC64_GOT_TPREL16_HA,
            0x4800005b => Type::PPC64_GOT_DTPREL16_DS,
            0x4800005c => Type::PPC64_GOT_DTPREL16_LO_DS,
            0x4800005d => Type::PPC64_GOT_DTPREL16_HI,
            0x4800005e => Type::PPC64_GOT_DTPREL16_HA,
            0x4800005f => Type::PPC64_TPREL16_DS,
            0x48000060 => Type::PPC64_TPREL16_LO_DS,
            0x48000061 => Type::PPC64_TPREL16_HIGHER,
            0x48000062 => Type::PPC64_TPREL16_HIGHERA,
            0x48000063 => Type::PPC64_TPREL16_HIGHEST,
            0x48000064 => Type::PPC64_TPREL16_HIGHESTA,
            0x48000065 => Type::PPC64_DTPREL16_DS,
            0x48000066 => Type::PPC64_DTPREL16_LO_DS,
            0x48000067 => Type::PPC64_DTPREL16_HIGHER,
            0x48000068 => Type::PPC64_DTPREL16_HIGHERA,
            0x48000069 => Type::PPC64_DTPREL16_HIGHEST,
            0x4800006a => Type::PPC64_DTPREL16_HIGHESTA,
            0x4800006b => Type::PPC64_TLSGD,
            0x4800006c => Type::PPC64_TLSLD,
            0x480000f9 => Type::PPC64_REL16,
            0x480000fa => Type::PPC64_REL16_LO,
            0x480000fb => Type::PPC64_REL16_HI,
            0x480000fc => Type::PPC64_REL16_HA,
            0x50000000 => Type::SPARC_NONE,
            0x50000001 => Type::SPARC_8,
            0x50000002 => Type::SPARC_16,
            0x50000003 => Type::SPARC_32,
            0x50000004 => Type::SPARC_DISP8,
            0x50000005 => Type::SPARC_DISP16,
            0x50000006 => Type::SPARC_DISP32,
            0x50000007 => Type::SPARC_WDISP30,
            0x50000008 => Type::SPARC_WDISP22,
            0x50000009 => Type::SPARC_HI22,
            0x5000000a => Type::SPARC_22,
            0x5000000b => Type::SPARC_13,
            0x5000000c => Type::SPARC_LO10,
            0x5000000d => Type::SPARC_GOT10,
            0x5000000e => Type::SPARC_GOT13,
            0x5000000f => Type::SPARC_GOT22,
            0x50000010 => Type::SPARC_PC10,
            0x50000011 => Type::SPARC_PC22,
            0x50000012 => Type::SPARC_WPLT30,
            0x50000013 => Type::SPARC_COPY,
            0x50000014 => Type::SPARC_GLOB_DAT,
            0x50000015 => Type::SPARC_JMP_SLOT,
            0x50000016 => Type::SPARC_RELATIVE,
            0x50000017 => Type::SPARC_UA32,
            0x50000018 => Type::SPARC_PLT32,
            0x50000019 => Type::SPARC_HIPLT22,
            0x5000001a => Type::SPARC_LOPLT10,
            0x5000001b => Type::SPARC_PCPLT32,
            0x5000001c => Type::SPARC_PCPLT22,
            0x5000001d => Type::SPARC_PCPLT10,
            0x5000001e => Type::SPARC_10,
            0x5000001f => Type::SPARC_11,
            0x50000020 => Type::SPARC_64,
            0x50000021 => Type::SPARC_OLO10,
            0x50000022 => Type::SPARC_HH22,
            0x50000023 => Type::SPARC_HM10,
            0x50000024 => Type::SPARC_LM22,
            0x50000025 => Type::SPARC_PC_HH22,
            0x50000026 => Type::SPARC_PC_HM10,
            0x50000027 => Type::SPARC_PC_LM22,
            0x50000028 => Type::SPARC_WDISP16,
            0x50000029 => Type::SPARC_WDISP19,
            0x5000002b => Type::SPARC_7,
            0x5000002c => Type::SPARC_5,
            0x5000002d => Type::SPARC_6,
            0x5000002e => Type::SPARC_DISP64,
            0x5000002f => Type::SPARC_PLT64,
            0x50000030 => Type::SPARC_HIX22,
            0x50000031 => Type::SPARC_LOX10,
            0x50000032 => Type::SPARC_H44,
            0x50000033 => Type::SPARC_M44,
            0x50000034 => Type::SPARC_L44,
            0x50000035 => Type::SPARC_REGISTER,
            0x50000036 => Type::SPARC_UA64,
            0x50000037 => Type::SPARC_UA16,
            0x50000038 => Type::SPARC_TLS_GD_HI22,
            0x50000039 => Type::SPARC_TLS_GD_LO10,
            0x5000003a => Type::SPARC_TLS_GD_ADD,
            0x5000003b => Type::SPARC_TLS_GD_CALL,
            0x5000003c => Type::SPARC_TLS_LDM_HI22,
            0x5000003d => Type::SPARC_TLS_LDM_LO10,
            0x5000003e => Type::SPARC_TLS_LDM_ADD,
            0x5000003f => Type::SPARC_TLS_LDM_CALL,
            0x50000040 => Type::SPARC_TLS_LDO_HIX22,
            0x50000041 => Type::SPARC_TLS_LDO_LOX10,
            0x50000042 => Type::SPARC_TLS_LDO_ADD,
            0x50000043 => Type::SPARC_TLS_IE_HI22,
            0x50000044 => Type::SPARC_TLS_IE_LO10,
            0x50000045 => Type::SPARC_TLS_IE_LD,
            0x50000046 => Type::SPARC_TLS_IE_LDX,
            0x50000047 => Type::SPARC_TLS_IE_ADD,
            0x50000048 => Type::SPARC_TLS_LE_HIX22,
            0x50000049 => Type::SPARC_TLS_LE_LOX10,
            0x5000004a => Type::SPARC_TLS_DTPMOD32,
            0x5000004b => Type::SPARC_TLS_DTPMOD64,
            0x5000004c => Type::SPARC_TLS_DTPOFF32,
            0x5000004d => Type::SPARC_TLS_DTPOFF64,
            0x5000004e => Type::SPARC_TLS_TPOFF32,
            0x5000004f => Type::SPARC_TLS_TPOFF64,
            0x50000050 => Type::SPARC_GOTDATA_HIX22,
            0x50000051 => Type::SPARC_GOTDATA_LOX10,
            0x50000052 => Type::SPARC_GOTDATA_OP_HIX22,
            0x50000053 => Type::SPARC_GOTDATA_OP_LOX10,
            0x50000054 => Type::SPARC_GOTDATA_OP,
            0x58000000 => Type::SYSZ_NONE,
            0x58000001 => Type::SYSZ_8,
            0x58000002 => Type::SYSZ_12,
            0x58000003 => Type::SYSZ_16,
            0x58000004 => Type::SYSZ_32,
            0x58000005 => Type::SYSZ_PC32,
            0x58000006 => Type::SYSZ_GOT12,
            0x58000007 => Type::SYSZ_GOT32,
            0x58000008 => Type::SYSZ_PLT32,
            0x58000009 => Type::SYSZ_COPY,
            0x5800000a => Type::SYSZ_GLOB_DAT,
            0x5800000b => Type::SYSZ_JMP_SLOT,
            0x5800000c => Type::SYSZ_RELATIVE,
            0x5800000d => Type::SYSZ_GOTOFF,
            0x5800000e => Type::SYSZ_GOTPC,
            0x5800000f => Type::SYSZ_GOT16,
            0x58000010 => Type::SYSZ_PC16,
            0x58000011 => Type::SYSZ_PC16DBL,
            0x58000012 => Type::SYSZ_PLT16DBL,
            0x58000013 => Type::SYSZ_PC32DBL,
            0x58000014 => Type::SYSZ_PLT32DBL,
            0x58000015 => Type::SYSZ_GOTPCDBL,
            0x58000016 => Type::SYSZ_64,
            0x58000017 => Type::SYSZ_PC64,
            0x58000018 => Type::SYSZ_GOT64,
            0x58000019 => Type::SYSZ_PLT64,
            0x5800001a => Type::SYSZ_GOTENT,
            0x5800001b => Type::SYSZ_GOTOFF16,
            0x5800001c => Type::SYSZ_GOTOFF64,
            0x5800001d => Type::SYSZ_GOTPLT12,
            0x5800001e => Type::SYSZ_GOTPLT16,
            0x5800001f => Type::SYSZ_GOTPLT32,
            0x58000020 => Type::SYSZ_GOTPLT64,
            0x58000021 => Type::SYSZ_GOTPLTENT,
            0x58000022 => Type::SYSZ_PLTOFF16,
            0x58000023 => Type::SYSZ_PLTOFF32,
            0x58000024 => Type::SYSZ_PLTOFF64,
            0x58000025 => Type::SYSZ_TLS_LOAD,
            0x58000026 => Type::SYSZ_TLS_GDCALL,
            0x58000027 => Type::SYSZ_TLS_LDCALL,
            0x58000028 => Type::SYSZ_TLS_GD32,
            0x58000029 => Type::SYSZ_TLS_GD64,
            0x5800002a => Type::SYSZ_TLS_GOTIE12,
            0x5800002b => Type::SYSZ_TLS_GOTIE32,
            0x5800002c => Type::SYSZ_TLS_GOTIE64,
            0x5800002d => Type::SYSZ_TLS_LDM32,
            0x5800002e => Type::SYSZ_TLS_LDM64,
            0x5800002f => Type::SYSZ_TLS_IE32,
            0x58000030 => Type::SYSZ_TLS_IE64,
            0x58000031 => Type::SYSZ_TLS_IEENT,
            0x58000032 => Type::SYSZ_TLS_LE32,
            0x58000033 => Type::SYSZ_TLS_LE64,
            0x58000034 => Type::SYSZ_TLS_LDO32,
            0x58000035 => Type::SYSZ_TLS_LDO64,
            0x58000036 => Type::SYSZ_TLS_DTPMOD,
            0x58000037 => Type::SYSZ_TLS_DTPOFF,
            0x58000038 => Type::SYSZ_TLS_TPOFF,
            0x58000039 => Type::SYSZ_20,
            0x5800003a => Type::SYSZ_GOT20,
            0x5800003b => Type::SYSZ_GOTPLT20,
            0x5800003c => Type::SYSZ_TLS_GOTIE20,
            0x5800003d => Type::SYSZ_IRELATIVE,
            0x60000000 => Type::RISCV_NONE,
            0x60000001 => Type::RISCV_32,
            0x60000002 => Type::RISCV_64,
            0x60000003 => Type::RISCV_RELATIVE,
            0x60000004 => Type::RISCV_COPY,
            0x60000005 => Type::RISCV_JUMP_SLOT,
            0x60000006 => Type::RISCV_TLS_DTPMOD32,
            0x60000007 => Type::RISCV_TLS_DTPMOD64,
            0x60000008 => Type::RISCV_TLS_DTPREL32,
            0x60000009 => Type::RISCV_TLS_DTPREL64,
            0x6000000a => Type::RISCV_TLS_TPREL32,
            0x6000000b => Type::RISCV_TLS_TPREL64,
            0x6000000c => Type::RISCV_TLSDESC,
            0x60000010 => Type::RISCV_BRANCH,
            0x60000011 => Type::RISCV_JAL,
            0x60000012 => Type::RISCV_CALL,
            0x60000013 => Type::RISCV_CALL_PLT,
            0x60000014 => Type::RISCV_GOT_HI20,
            0x60000015 => Type::RISCV_TLS_GOT_HI20,
            0x60000016 => Type::RISCV_TLS_GD_HI20,
            0x60000017 => Type::RISCV_PCREL_HI20,
            0x60000018 => Type::RISCV_PCREL_LO12_I,
            0x60000019 => Type::RISCV_PCREL_LO12_S,
            0x6000001a => Type::RISCV_HI20,
            0x6000001b => Type::RISCV_LO12_I,
            0x6000001c => Type::RISCV_LO12_S,
            0x6000001d => Type::RISCV_TPREL_HI20,
            0x6000001e => Type::RISCV_TPREL_LO12_I,
            0x6000001f => Type::RISCV_TPREL_LO12_S,
            0x60000020 => Type::RISCV_TPREL_ADD,
            0x60000021 => Type::RISCV_ADD8,
            0x60000022 => Type::RISCV_ADD16,
            0x60000023 => Type::RISCV_ADD32,
            0x60000024 => Type::RISCV_ADD64,
            0x60000025 => Type::RISCV_SUB8,
            0x60000026 => Type::RISCV_SUB16,
            0x60000027 => Type::RISCV_SUB32,
            0x60000028 => Type::RISCV_SUB64,
            0x60000029 => Type::RISCV_GOT32_PCREL,
            0x6000002b => Type::RISCV_ALIGN,
            0x6000002c => Type::RISCV_RVC_BRANCH,
            0x6000002d => Type::RISCV_RVC_JUMP,
            0x6000002e => Type::RISCV_RVC_LUI,
            0x60000033 => Type::RISCV_RELAX,
            0x60000034 => Type::RISCV_SUB6,
            0x60000035 => Type::RISCV_SET6,
            0x60000036 => Type::RISCV_SET8,
            0x60000037 => Type::RISCV_SET16,
            0x60000038 => Type::RISCV_SET32,
            0x60000039 => Type::RISCV_32_PCREL,
            0x6000003a => Type::RISCV_IRELATIVE,
            0x6000003b => Type::RISCV_PLT32,
            0x6000003c => Type::RISCV_SET_ULEB128,
            0x6000003d => Type::RISCV_SUB_ULEB128,
            0x6000003e => Type::RISCV_TLSDESC_HI20,
            0x6000003f => Type::RISCV_TLSDESC_LOAD_LO12,
            0x60000040 => Type::RISCV_TLSDESC_ADD_LO12,
            0x60000041 => Type::RISCV_TLSDESC_CALL,
            0x68000000 => Type::BPF_NONE,
            0x68000001 => Type::BPF_64_64,
            0x68000002 => Type::BPF_64_ABS64,
            0x68000003 => Type::BPF_64_ABS32,
            0x68000004 => Type::BPF_64_NODYLD32,
            0x6800000a => Type::BPF_64_32,
            _ => Type::UNKNOWN(value),

        }
    }
}

impl From<Type> for u32 {
    fn from(value: Type) -> u32 {
        match value {
            Type::X86_64_NONE => 0x08000000,
            Type::X86_64_64 => 0x08000001,
            Type::X86_64_PC32 => 0x08000002,
            Type::X86_64_GOT32 => 0x08000003,
            Type::X86_64_PLT32 => 0x08000004,
            Type::X86_64_COPY => 0x08000005,
            Type::X86_64_GLOB_DAT => 0x08000006,
            Type::X86_64_JUMP_SLOT => 0x08000007,
            Type::X86_64_RELATIVE => 0x08000008,
            Type::X86_64_GOTPCREL => 0x08000009,
            Type::X86_64_32 => 0x0800000a,
            Type::X86_64_32S => 0x0800000b,
            Type::X86_64_16 => 0x0800000c,
            Type::X86_64_PC16 => 0x0800000d,
            Type::X86_64_8 => 0x0800000e,
            Type::X86_64_PC8 => 0x0800000f,
            Type::X86_64_DTPMOD64 => 0x08000010,
            Type::X86_64_DTPOFF64 => 0x08000011,
            Type::X86_64_TPOFF64 => 0x08000012,
            Type::X86_64_TLSGD => 0x08000013,
            Type::X86_64_TLSLD => 0x08000014,
            Type::X86_64_DTPOFF32 => 0x08000015,
            Type::X86_64_GOTTPOFF => 0x08000016,
            Type::X86_64_TPOFF32 => 0x08000017,
            Type::X86_64_PC64 => 0x08000018,
            Type::X86_64_GOTOFF64 => 0x08000019,
            Type::X86_64_GOTPC32 => 0x0800001a,
            Type::X86_64_GOT64 => 0x0800001b,
            Type::X86_64_GOTPCREL64 => 0x0800001c,
            Type::X86_64_GOTPC64 => 0x0800001d,
            Type::X86_64_GOTPLT64 => 0x0800001e,
            Type::X86_64_PLTOFF64 => 0x0800001f,
            Type::X86_64_SIZE32 => 0x08000020,
            Type::X86_64_SIZE64 => 0x08000021,
            Type::X86_64_GOTPC32_TLSDESC => 0x08000022,
            Type::X86_64_TLSDESC_CALL => 0x08000023,
            Type::X86_64_TLSDESC => 0x08000024,
            Type::X86_64_IRELATIVE => 0x08000025,
            Type::X86_64_RELATIVE64 => 0x08000026,
            Type::X86_64_PC32_BND => 0x08000027,
            Type::X86_64_PLT32_BND => 0x08000028,
            Type::X86_64_GOTPCRELX => 0x08000029,
            Type::X86_64_REX_GOTPCRELX => 0x0800002a,
            Type::AARCH64_NONE => 0x10000000,
            Type::AARCH64_ABS64 => 0x10000101,
            Type::AARCH64_ABS32 => 0x10000102,
            Type::AARCH64_ABS16 => 0x10000103,
            Type::AARCH64_PREL64 => 0x10000104,
            Type::AARCH64_PREL32 => 0x10000105,
            Type::AARCH64_PREL16 => 0x10000106,
            Type::AARCH64_MOVW_UABS_G0 => 0x10000107,
            Type::AARCH64_MOVW_UABS_G0_NC => 0x10000108,
            Type::AARCH64_MOVW_UABS_G1 => 0x10000109,
            Type::AARCH64_MOVW_UABS_G1_NC => 0x1000010a,
            Type::AARCH64_MOVW_UABS_G2 => 0x1000010b,
            Type::AARCH64_MOVW_UABS_G2_NC => 0x1000010c,
            Type::AARCH64_MOVW_UABS_G3 => 0x1000010d,
            Type::AARCH64_MOVW_SABS_G0 => 0x1000010e,
            Type::AARCH64_MOVW_SABS_G1 => 0x1000010f,
            Type::AARCH64_MOVW_SABS_G2 => 0x10000110,
            Type::AARCH64_LD_PREL_LO19 => 0x10000111,
            Type::AARCH64_ADR_PREL_LO21 => 0x10000112,
            Type::AARCH64_ADR_PREL_PG_HI21 => 0x10000113,
            Type::AARCH64_ADR_PREL_PG_HI21_NC => 0x10000114,
            Type::AARCH64_ADD_ABS_LO12_NC => 0x10000115,
            Type::AARCH64_LDST8_ABS_LO12_NC => 0x10000116,
            Type::AARCH64_TSTBR14 => 0x10000117,
            Type::AARCH64_CONDBR19 => 0x10000118,
            Type::AARCH64_JUMP26 => 0x1000011a,
            Type::AARCH64_CALL26 => 0x1000011b,
            Type::AARCH64_LDST16_ABS_LO12_NC => 0x1000011c,
            Type::AARCH64_LDST32_ABS_LO12_NC => 0x1000011d,
            Type::AARCH64_LDST64_ABS_LO12_NC => 0x1000011e,
            Type::AARCH64_MOVW_PREL_G0 => 0x1000011f,
            Type::AARCH64_MOVW_PREL_G0_NC => 0x10000120,
            Type::AARCH64_MOVW_PREL_G1 => 0x10000121,
            Type::AARCH64_MOVW_PREL_G1_NC => 0x10000122,
            Type::AARCH64_MOVW_PREL_G2 => 0x10000123,
            Type::AARCH64_MOVW_PREL_G2_NC => 0x10000124,
            Type::AARCH64_MOVW_PREL_G3 => 0x10000125,
            Type::AARCH64_LDST128_ABS_LO12_NC => 0x1000012b,
            Type::AARCH64_MOVW_GOTOFF_G0 => 0x1000012c,
            Type::AARCH64_MOVW_GOTOFF_G0_NC => 0x1000012d,
            Type::AARCH64_MOVW_GOTOFF_G1 => 0x1000012e,
            Type::AARCH64_MOVW_GOTOFF_G1_NC => 0x1000012f,
            Type::AARCH64_MOVW_GOTOFF_G2 => 0x10000130,
            Type::AARCH64_MOVW_GOTOFF_G2_NC => 0x10000131,
            Type::AARCH64_MOVW_GOTOFF_G3 => 0x10000132,
            Type::AARCH64_GOTREL64 => 0x10000133,
            Type::AARCH64_GOTREL32 => 0x10000134,
            Type::AARCH64_GOT_LD_PREL19 => 0x10000135,
            Type::AARCH64_LD64_GOTOFF_LO15 => 0x10000136,
            Type::AARCH64_ADR_GOT_PAGE => 0x10000137,
            Type::AARCH64_LD64_GOT_LO12_NC => 0x10000138,
            Type::AARCH64_LD64_GOTPAGE_LO15 => 0x10000139,
            Type::AARCH64_TLSGD_ADR_PREL21 => 0x10000200,
            Type::AARCH64_TLSGD_ADR_PAGE21 => 0x10000201,
            Type::AARCH64_TLSGD_ADD_LO12_NC => 0x10000202,
            Type::AARCH64_TLSGD_MOVW_G1 => 0x10000203,
            Type::AARCH64_TLSGD_MOVW_G0_NC => 0x10000204,
            Type::AARCH64_TLSLD_ADR_PREL21 => 0x10000205,
            Type::AARCH64_TLSLD_ADR_PAGE21 => 0x10000206,
            Type::AARCH64_TLSLD_ADD_LO12_NC => 0x10000207,
            Type::AARCH64_TLSLD_MOVW_G1 => 0x10000208,
            Type::AARCH64_TLSLD_MOVW_G0_NC => 0x10000209,
            Type::AARCH64_TLSLD_LD_PREL19 => 0x1000020a,
            Type::AARCH64_TLSLD_MOVW_DTPREL_G2 => 0x1000020b,
            Type::AARCH64_TLSLD_MOVW_DTPREL_G1 => 0x1000020c,
            Type::AARCH64_TLSLD_MOVW_DTPREL_G1_NC => 0x1000020d,
            Type::AARCH64_TLSLD_MOVW_DTPREL_G0 => 0x1000020e,
            Type::AARCH64_TLSLD_MOVW_DTPREL_G0_NC => 0x1000020f,
            Type::AARCH64_TLSLD_ADD_DTPREL_HI12 => 0x10000210,
            Type::AARCH64_TLSLD_ADD_DTPREL_LO12 => 0x10000211,
            Type::AARCH64_TLSLD_ADD_DTPREL_LO12_NC => 0x10000212,
            Type::AARCH64_TLSLD_LDST8_DTPREL_LO12 => 0x10000213,
            Type::AARCH64_TLSLD_LDST8_DTPREL_LO12_NC => 0x10000214,
            Type::AARCH64_TLSLD_LDST16_DTPREL_LO12 => 0x10000215,
            Type::AARCH64_TLSLD_LDST16_DTPREL_LO12_NC => 0x10000216,
            Type::AARCH64_TLSLD_LDST32_DTPREL_LO12 => 0x10000217,
            Type::AARCH64_TLSLD_LDST32_DTPREL_LO12_NC => 0x10000218,
            Type::AARCH64_TLSLD_LDST64_DTPREL_LO12 => 0x10000219,
            Type::AARCH64_TLSLD_LDST64_DTPREL_LO12_NC => 0x1000021a,
            Type::AARCH64_TLSIE_MOVW_GOTTPREL_G1 => 0x1000021b,
            Type::AARCH64_TLSIE_MOVW_GOTTPREL_G0_NC => 0x1000021c,
            Type::AARCH64_TLSIE_ADR_GOTTPREL_PAGE21 => 0x1000021d,
            Type::AARCH64_TLSIE_LD64_GOTTPREL_LO12_NC => 0x1000021e,
            Type::AARCH64_TLSIE_LD_GOTTPREL_PREL19 => 0x1000021f,
            Type::AARCH64_TLSLE_MOVW_TPREL_G2 => 0x10000220,
            Type::AARCH64_TLSLE_MOVW_TPREL_G1 => 0x10000221,
            Type::AARCH64_TLSLE_MOVW_TPREL_G1_NC => 0x10000222,
            Type::AARCH64_TLSLE_MOVW_TPREL_G0 => 0x10000223,
            Type::AARCH64_TLSLE_MOVW_TPREL_G0_NC => 0x10000224,
            Type::AARCH64_TLSLE_ADD_TPREL_HI12 => 0x10000225,
            Type::AARCH64_TLSLE_ADD_TPREL_LO12 => 0x10000226,
            Type::AARCH64_TLSLE_ADD_TPREL_LO12_NC => 0x10000227,
            Type::AARCH64_TLSLE_LDST8_TPREL_LO12 => 0x10000228,
            Type::AARCH64_TLSLE_LDST8_TPREL_LO12_NC => 0x10000229,
            Type::AARCH64_TLSLE_LDST16_TPREL_LO12 => 0x1000022a,
            Type::AARCH64_TLSLE_LDST16_TPREL_LO12_NC => 0x1000022b,
            Type::AARCH64_TLSLE_LDST32_TPREL_LO12 => 0x1000022c,
            Type::AARCH64_TLSLE_LDST32_TPREL_LO12_NC => 0x1000022d,
            Type::AARCH64_TLSLE_LDST64_TPREL_LO12 => 0x1000022e,
            Type::AARCH64_TLSLE_LDST64_TPREL_LO12_NC => 0x1000022f,
            Type::AARCH64_TLSDESC_LD_PREL19 => 0x10000230,
            Type::AARCH64_TLSDESC_ADR_PREL21 => 0x10000231,
            Type::AARCH64_TLSDESC_ADR_PAGE21 => 0x10000232,
            Type::AARCH64_TLSDESC_LD64_LO12_NC => 0x10000233,
            Type::AARCH64_TLSDESC_ADD_LO12_NC => 0x10000234,
            Type::AARCH64_TLSDESC_OFF_G1 => 0x10000235,
            Type::AARCH64_TLSDESC_OFF_G0_NC => 0x10000236,
            Type::AARCH64_TLSDESC_LDR => 0x10000237,
            Type::AARCH64_TLSDESC_ADD => 0x10000238,
            Type::AARCH64_TLSDESC_CALL => 0x10000239,
            Type::AARCH64_TLSLE_LDST128_TPREL_LO12 => 0x1000023a,
            Type::AARCH64_TLSLE_LDST128_TPREL_LO12_NC => 0x1000023b,
            Type::AARCH64_TLSLD_LDST128_DTPREL_LO12 => 0x1000023c,
            Type::AARCH64_TLSLD_LDST128_DTPREL_LO12_NC => 0x1000023d,
            Type::AARCH64_COPY => 0x10000400,
            Type::AARCH64_GLOB_DAT => 0x10000401,
            Type::AARCH64_JUMP_SLOT => 0x10000402,
            Type::AARCH64_RELATIVE => 0x10000403,
            Type::AARCH64_TLS_DTPREL64 => 0x10000404,
            Type::AARCH64_TLS_DTPMOD64 => 0x10000405,
            Type::AARCH64_TLS_TPREL64 => 0x10000406,
            Type::AARCH64_TLSDESC => 0x10000407,
            Type::AARCH64_IRELATIVE => 0x10000408,
            Type::ARM_NONE => 0x18000000,
            Type::ARM_PC24 => 0x18000001,
            Type::ARM_ABS32 => 0x18000002,
            Type::ARM_REL32 => 0x18000003,
            Type::ARM_LDR_PC_G0 => 0x18000004,
            Type::ARM_ABS16 => 0x18000005,
            Type::ARM_ABS12 => 0x18000006,
            Type::ARM_THM_ABS5 => 0x18000007,
            Type::ARM_ABS8 => 0x18000008,
            Type::ARM_SBREL32 => 0x18000009,
            Type::ARM_THM_CALL => 0x1800000a,
            Type::ARM_THM_PC8 => 0x1800000b,
            Type::ARM_BREL_ADJ => 0x1800000c,
            Type::ARM_TLS_DESC => 0x1800000d,
            Type::ARM_THM_SWI8 => 0x1800000e,
            Type::ARM_XPC25 => 0x1800000f,
            Type::ARM_THM_XPC22 => 0x18000010,
            Type::ARM_TLS_DTPMOD32 => 0x18000011,
            Type::ARM_TLS_DTPOFF32 => 0x18000012,
            Type::ARM_TLS_TPOFF32 => 0x18000013,
            Type::ARM_COPY => 0x18000014,
            Type::ARM_GLOB_DAT => 0x18000015,
            Type::ARM_JUMP_SLOT => 0x18000016,
            Type::ARM_RELATIVE => 0x18000017,
            Type::ARM_GOTOFF32 => 0x18000018,
            Type::ARM_BASE_PREL => 0x18000019,
            Type::ARM_GOT_BREL => 0x1800001a,
            Type::ARM_PLT32 => 0x1800001b,
            Type::ARM_CALL => 0x1800001c,
            Type::ARM_JUMP24 => 0x1800001d,
            Type::ARM_THM_JUMP24 => 0x1800001e,
            Type::ARM_BASE_ABS => 0x1800001f,
            Type::ARM_ALU_PCREL_7_0 => 0x18000020,
            Type::ARM_ALU_PCREL_15_8 => 0x18000021,
            Type::ARM_ALU_PCREL_23_15 => 0x18000022,
            Type::ARM_LDR_SBREL_11_0_NC => 0x18000023,
            Type::ARM_ALU_SBREL_19_12_NC => 0x18000024,
            Type::ARM_ALU_SBREL_27_20_CK => 0x18000025,
            Type::ARM_TARGET1 => 0x18000026,
            Type::ARM_SBREL31 => 0x18000027,
            Type::ARM_V4BX => 0x18000028,
            Type::ARM_TARGET2 => 0x18000029,
            Type::ARM_PREL31 => 0x1800002a,
            Type::ARM_MOVW_ABS_NC => 0x1800002b,
            Type::ARM_MOVT_ABS => 0x1800002c,
            Type::ARM_MOVW_PREL_NC => 0x1800002d,
            Type::ARM_MOVT_PREL => 0x1800002e,
            Type::ARM_THM_MOVW_ABS_NC => 0x1800002f,
            Type::ARM_THM_MOVT_ABS => 0x18000030,
            Type::ARM_THM_MOVW_PREL_NC => 0x18000031,
            Type::ARM_THM_MOVT_PREL => 0x18000032,
            Type::ARM_THM_JUMP19 => 0x18000033,
            Type::ARM_THM_JUMP6 => 0x18000034,
            Type::ARM_THM_ALU_PREL_11_0 => 0x18000035,
            Type::ARM_THM_PC12 => 0x18000036,
            Type::ARM_ABS32_NOI => 0x18000037,
            Type::ARM_REL32_NOI => 0x18000038,
            Type::ARM_ALU_PC_G0_NC => 0x18000039,
            Type::ARM_ALU_PC_G0 => 0x1800003a,
            Type::ARM_ALU_PC_G1_NC => 0x1800003b,
            Type::ARM_ALU_PC_G1 => 0x1800003c,
            Type::ARM_ALU_PC_G2 => 0x1800003d,
            Type::ARM_LDR_PC_G1 => 0x1800003e,
            Type::ARM_LDR_PC_G2 => 0x1800003f,
            Type::ARM_LDRS_PC_G0 => 0x18000040,
            Type::ARM_LDRS_PC_G1 => 0x18000041,
            Type::ARM_LDRS_PC_G2 => 0x18000042,
            Type::ARM_LDC_PC_G0 => 0x18000043,
            Type::ARM_LDC_PC_G1 => 0x18000044,
            Type::ARM_LDC_PC_G2 => 0x18000045,
            Type::ARM_ALU_SB_G0_NC => 0x18000046,
            Type::ARM_ALU_SB_G0 => 0x18000047,
            Type::ARM_ALU_SB_G1_NC => 0x18000048,
            Type::ARM_ALU_SB_G1 => 0x18000049,
            Type::ARM_ALU_SB_G2 => 0x1800004a,
            Type::ARM_LDR_SB_G0 => 0x1800004b,
            Type::ARM_LDR_SB_G1 => 0x1800004c,
            Type::ARM_LDR_SB_G2 => 0x1800004d,
            Type::ARM_LDRS_SB_G0 => 0x1800004e,
            Type::ARM_LDRS_SB_G1 => 0x1800004f,
            Type::ARM_LDRS_SB_G2 => 0x18000050,
            Type::ARM_LDC_SB_G0 => 0x18000051,
            Type::ARM_LDC_SB_G1 => 0x18000052,
            Type::ARM_LDC_SB_G2 => 0x18000053,
            Type::ARM_MOVW_BREL_NC => 0x18000054,
            Type::ARM_MOVT_BREL => 0x18000055,
            Type::ARM_MOVW_BREL => 0x18000056,
            Type::ARM_THM_MOVW_BREL_NC => 0x18000057,
            Type::ARM_THM_MOVT_BREL => 0x18000058,
            Type::ARM_THM_MOVW_BREL => 0x18000059,
            Type::ARM_TLS_GOTDESC => 0x1800005a,
            Type::ARM_TLS_CALL => 0x1800005b,
            Type::ARM_TLS_DESCSEQ => 0x1800005c,
            Type::ARM_THM_TLS_CALL => 0x1800005d,
            Type::ARM_PLT32_ABS => 0x1800005e,
            Type::ARM_GOT_ABS => 0x1800005f,
            Type::ARM_GOT_PREL => 0x18000060,
            Type::ARM_GOT_BREL12 => 0x18000061,
            Type::ARM_GOTOFF12 => 0x18000062,
            Type::ARM_GOTRELAX => 0x18000063,
            Type::ARM_GNU_VTENTRY => 0x18000064,
            Type::ARM_GNU_VTINHERIT => 0x18000065,
            Type::ARM_THM_JUMP11 => 0x18000066,
            Type::ARM_THM_JUMP8 => 0x18000067,
            Type::ARM_TLS_GD32 => 0x18000068,
            Type::ARM_TLS_LDM32 => 0x18000069,
            Type::ARM_TLS_LDO32 => 0x1800006a,
            Type::ARM_TLS_IE32 => 0x1800006b,
            Type::ARM_TLS_LE32 => 0x1800006c,
            Type::ARM_TLS_LDO12 => 0x1800006d,
            Type::ARM_TLS_LE12 => 0x1800006e,
            Type::ARM_TLS_IE12GP => 0x1800006f,
            Type::ARM_PRIVATE_0 => 0x18000070,
            Type::ARM_PRIVATE_1 => 0x18000071,
            Type::ARM_PRIVATE_2 => 0x18000072,
            Type::ARM_PRIVATE_3 => 0x18000073,
            Type::ARM_PRIVATE_4 => 0x18000074,
            Type::ARM_PRIVATE_5 => 0x18000075,
            Type::ARM_PRIVATE_6 => 0x18000076,
            Type::ARM_PRIVATE_7 => 0x18000077,
            Type::ARM_PRIVATE_8 => 0x18000078,
            Type::ARM_PRIVATE_9 => 0x18000079,
            Type::ARM_PRIVATE_10 => 0x1800007a,
            Type::ARM_PRIVATE_11 => 0x1800007b,
            Type::ARM_PRIVATE_12 => 0x1800007c,
            Type::ARM_PRIVATE_13 => 0x1800007d,
            Type::ARM_PRIVATE_14 => 0x1800007e,
            Type::ARM_PRIVATE_15 => 0x1800007f,
            Type::ARM_ME_TOO => 0x18000080,
            Type::ARM_THM_TLS_DESCSEQ16 => 0x18000081,
            Type::ARM_THM_TLS_DESCSEQ32 => 0x18000082,
            Type::ARM_IRELATIVE => 0x180000a0,
            Type::ARM_RXPC25 => 0x180000f9,
            Type::ARM_RSBREL32 => 0x180000fa,
            Type::ARM_THM_RPC22 => 0x180000fb,
            Type::ARM_RREL32 => 0x180000fc,
            Type::ARM_RPC24 => 0x180000fd,
            Type::ARM_RBASE => 0x180000fe,
            Type::HEX_NONE => 0x20000000,
            Type::HEX_B22_PCREL => 0x20000001,
            Type::HEX_B15_PCREL => 0x20000002,
            Type::HEX_B7_PCREL => 0x20000003,
            Type::HEX_LO16 => 0x20000004,
            Type::HEX_HI16 => 0x20000005,
            Type::HEX_32 => 0x20000006,
            Type::HEX_16 => 0x20000007,
            Type::HEX_8 => 0x20000008,
            Type::HEX_GPREL16_0 => 0x20000009,
            Type::HEX_GPREL16_1 => 0x2000000a,
            Type::HEX_GPREL16_2 => 0x2000000b,
            Type::HEX_GPREL16_3 => 0x2000000c,
            Type::HEX_HL16 => 0x2000000d,
            Type::HEX_B13_PCREL => 0x2000000e,
            Type::HEX_B9_PCREL => 0x2000000f,
            Type::HEX_B32_PCREL_X => 0x20000010,
            Type::HEX_32_6_X => 0x20000011,
            Type::HEX_B22_PCREL_X => 0x20000012,
            Type::HEX_B15_PCREL_X => 0x20000013,
            Type::HEX_B13_PCREL_X => 0x20000014,
            Type::HEX_B9_PCREL_X => 0x20000015,
            Type::HEX_B7_PCREL_X => 0x20000016,
            Type::HEX_16_X => 0x20000017,
            Type::HEX_12_X => 0x20000018,
            Type::HEX_11_X => 0x20000019,
            Type::HEX_10_X => 0x2000001a,
            Type::HEX_9_X => 0x2000001b,
            Type::HEX_8_X => 0x2000001c,
            Type::HEX_7_X => 0x2000001d,
            Type::HEX_6_X => 0x2000001e,
            Type::HEX_32_PCREL => 0x2000001f,
            Type::HEX_COPY => 0x20000020,
            Type::HEX_GLOB_DAT => 0x20000021,
            Type::HEX_JMP_SLOT => 0x20000022,
            Type::HEX_RELATIVE => 0x20000023,
            Type::HEX_PLT_B22_PCREL => 0x20000024,
            Type::HEX_GOTREL_LO16 => 0x20000025,
            Type::HEX_GOTREL_HI16 => 0x20000026,
            Type::HEX_GOTREL_32 => 0x20000027,
            Type::HEX_GOT_LO16 => 0x20000028,
            Type::HEX_GOT_HI16 => 0x20000029,
            Type::HEX_GOT_32 => 0x2000002a,
            Type::HEX_GOT_16 => 0x2000002b,
            Type::HEX_DTPMOD_32 => 0x2000002c,
            Type::HEX_DTPREL_LO16 => 0x2000002d,
            Type::HEX_DTPREL_HI16 => 0x2000002e,
            Type::HEX_DTPREL_32 => 0x2000002f,
            Type::HEX_DTPREL_16 => 0x20000030,
            Type::HEX_GD_PLT_B22_PCREL => 0x20000031,
            Type::HEX_GD_GOT_LO16 => 0x20000032,
            Type::HEX_GD_GOT_HI16 => 0x20000033,
            Type::HEX_GD_GOT_32 => 0x20000034,
            Type::HEX_GD_GOT_16 => 0x20000035,
            Type::HEX_IE_LO16 => 0x20000036,
            Type::HEX_IE_HI16 => 0x20000037,
            Type::HEX_IE_32 => 0x20000038,
            Type::HEX_IE_GOT_LO16 => 0x20000039,
            Type::HEX_IE_GOT_HI16 => 0x2000003a,
            Type::HEX_IE_GOT_32 => 0x2000003b,
            Type::HEX_IE_GOT_16 => 0x2000003c,
            Type::HEX_TPREL_LO16 => 0x2000003d,
            Type::HEX_TPREL_HI16 => 0x2000003e,
            Type::HEX_TPREL_32 => 0x2000003f,
            Type::HEX_TPREL_16 => 0x20000040,
            Type::HEX_6_PCREL_X => 0x20000041,
            Type::HEX_GOTREL_32_6_X => 0x20000042,
            Type::HEX_GOTREL_16_X => 0x20000043,
            Type::HEX_GOTREL_11_X => 0x20000044,
            Type::HEX_GOT_32_6_X => 0x20000045,
            Type::HEX_GOT_16_X => 0x20000046,
            Type::HEX_GOT_11_X => 0x20000047,
            Type::HEX_DTPREL_32_6_X => 0x20000048,
            Type::HEX_DTPREL_16_X => 0x20000049,
            Type::HEX_DTPREL_11_X => 0x2000004a,
            Type::HEX_GD_GOT_32_6_X => 0x2000004b,
            Type::HEX_GD_GOT_16_X => 0x2000004c,
            Type::HEX_GD_GOT_11_X => 0x2000004d,
            Type::HEX_IE_32_6_X => 0x2000004e,
            Type::HEX_IE_16_X => 0x2000004f,
            Type::HEX_IE_GOT_32_6_X => 0x20000050,
            Type::HEX_IE_GOT_16_X => 0x20000051,
            Type::HEX_IE_GOT_11_X => 0x20000052,
            Type::HEX_TPREL_32_6_X => 0x20000053,
            Type::HEX_TPREL_16_X => 0x20000054,
            Type::HEX_TPREL_11_X => 0x20000055,
            Type::HEX_LD_PLT_B22_PCREL => 0x20000056,
            Type::HEX_LD_GOT_LO16 => 0x20000057,
            Type::HEX_LD_GOT_HI16 => 0x20000058,
            Type::HEX_LD_GOT_32 => 0x20000059,
            Type::HEX_LD_GOT_16 => 0x2000005a,
            Type::HEX_LD_GOT_32_6_X => 0x2000005b,
            Type::HEX_LD_GOT_16_X => 0x2000005c,
            Type::HEX_LD_GOT_11_X => 0x2000005d,
            Type::X86_NONE => 0x28000000,
            Type::X86_32 => 0x28000001,
            Type::X86_PC32 => 0x28000002,
            Type::X86_GOT32 => 0x28000003,
            Type::X86_PLT32 => 0x28000004,
            Type::X86_COPY => 0x28000005,
            Type::X86_GLOB_DAT => 0x28000006,
            Type::X86_JUMP_SLOT => 0x28000007,
            Type::X86_RELATIVE => 0x28000008,
            Type::X86_GOTOFF => 0x28000009,
            Type::X86_GOTPC => 0x2800000a,
            Type::X86_32PLT => 0x2800000b,
            Type::X86_TLS_TPOFF => 0x2800000e,
            Type::X86_TLS_IE => 0x2800000f,
            Type::X86_TLS_GOTIE => 0x28000010,
            Type::X86_TLS_LE => 0x28000011,
            Type::X86_TLS_GD => 0x28000012,
            Type::X86_TLS_LDM => 0x28000013,
            Type::X86_16 => 0x28000014,
            Type::X86_PC16 => 0x28000015,
            Type::X86_8 => 0x28000016,
            Type::X86_PC8 => 0x28000017,
            Type::X86_TLS_GD_32 => 0x28000018,
            Type::X86_TLS_GD_PUSH => 0x28000019,
            Type::X86_TLS_GD_CALL => 0x2800001a,
            Type::X86_TLS_GD_POP => 0x2800001b,
            Type::X86_TLS_LDM_32 => 0x2800001c,
            Type::X86_TLS_LDM_PUSH => 0x2800001d,
            Type::X86_TLS_LDM_CALL => 0x2800001e,
            Type::X86_TLS_LDM_POP => 0x2800001f,
            Type::X86_TLS_LDO_32 => 0x28000020,
            Type::X86_TLS_IE_32 => 0x28000021,
            Type::X86_TLS_LE_32 => 0x28000022,
            Type::X86_TLS_DTPMOD32 => 0x28000023,
            Type::X86_TLS_DTPOFF32 => 0x28000024,
            Type::X86_TLS_TPOFF32 => 0x28000025,
            Type::X86_TLS_GOTDESC => 0x28000027,
            Type::X86_TLS_DESC_CALL => 0x28000028,
            Type::X86_TLS_DESC => 0x28000029,
            Type::X86_IRELATIVE => 0x2800002a,
            Type::LARCH_NONE => 0x30000000,
            Type::LARCH_32 => 0x30000001,
            Type::LARCH_64 => 0x30000002,
            Type::LARCH_RELATIVE => 0x30000003,
            Type::LARCH_COPY => 0x30000004,
            Type::LARCH_JUMP_SLOT => 0x30000005,
            Type::LARCH_TLS_DTPMOD32 => 0x30000006,
            Type::LARCH_TLS_DTPMOD64 => 0x30000007,
            Type::LARCH_TLS_DTPREL32 => 0x30000008,
            Type::LARCH_TLS_DTPREL64 => 0x30000009,
            Type::LARCH_TLS_TPREL32 => 0x3000000a,
            Type::LARCH_TLS_TPREL64 => 0x3000000b,
            Type::LARCH_IRELATIVE => 0x3000000c,
            Type::LARCH_MARK_LA => 0x30000014,
            Type::LARCH_MARK_PCREL => 0x30000015,
            Type::LARCH_SOP_PUSH_PCREL => 0x30000016,
            Type::LARCH_SOP_PUSH_ABSOLUTE => 0x30000017,
            Type::LARCH_SOP_PUSH_DUP => 0x30000018,
            Type::LARCH_SOP_PUSH_GPREL => 0x30000019,
            Type::LARCH_SOP_PUSH_TLS_TPREL => 0x3000001a,
            Type::LARCH_SOP_PUSH_TLS_GOT => 0x3000001b,
            Type::LARCH_SOP_PUSH_TLS_GD => 0x3000001c,
            Type::LARCH_SOP_PUSH_PLT_PCREL => 0x3000001d,
            Type::LARCH_SOP_ASSERT => 0x3000001e,
            Type::LARCH_SOP_NOT => 0x3000001f,
            Type::LARCH_SOP_SUB => 0x30000020,
            Type::LARCH_SOP_SL => 0x30000021,
            Type::LARCH_SOP_SR => 0x30000022,
            Type::LARCH_SOP_ADD => 0x30000023,
            Type::LARCH_SOP_AND => 0x30000024,
            Type::LARCH_SOP_IF_ELSE => 0x30000025,
            Type::LARCH_SOP_POP_32_S_10_5 => 0x30000026,
            Type::LARCH_SOP_POP_32_U_10_12 => 0x30000027,
            Type::LARCH_SOP_POP_32_S_10_12 => 0x30000028,
            Type::LARCH_SOP_POP_32_S_10_16 => 0x30000029,
            Type::LARCH_SOP_POP_32_S_10_16_S2 => 0x3000002a,
            Type::LARCH_SOP_POP_32_S_5_20 => 0x3000002b,
            Type::LARCH_SOP_POP_32_S_0_5_10_16_S2 => 0x3000002c,
            Type::LARCH_SOP_POP_32_S_0_10_10_16_S2 => 0x3000002d,
            Type::LARCH_SOP_POP_32_U => 0x3000002e,
            Type::LARCH_ADD8 => 0x3000002f,
            Type::LARCH_ADD16 => 0x30000030,
            Type::LARCH_ADD24 => 0x30000031,
            Type::LARCH_ADD32 => 0x30000032,
            Type::LARCH_ADD64 => 0x30000033,
            Type::LARCH_SUB8 => 0x30000034,
            Type::LARCH_SUB16 => 0x30000035,
            Type::LARCH_SUB24 => 0x30000036,
            Type::LARCH_SUB32 => 0x30000037,
            Type::LARCH_SUB64 => 0x30000038,
            Type::LARCH_GNU_VTINHERIT => 0x30000039,
            Type::LARCH_GNU_VTENTRY => 0x3000003a,
            Type::LARCH_B16 => 0x30000040,
            Type::LARCH_B21 => 0x30000041,
            Type::LARCH_B26 => 0x30000042,
            Type::LARCH_ABS_HI20 => 0x30000043,
            Type::LARCH_ABS_LO12 => 0x30000044,
            Type::LARCH_ABS64_LO20 => 0x30000045,
            Type::LARCH_ABS64_HI12 => 0x30000046,
            Type::LARCH_PCALA_HI20 => 0x30000047,
            Type::LARCH_PCALA_LO12 => 0x30000048,
            Type::LARCH_PCALA64_LO20 => 0x30000049,
            Type::LARCH_PCALA64_HI12 => 0x3000004a,
            Type::LARCH_GOT_PC_HI20 => 0x3000004b,
            Type::LARCH_GOT_PC_LO12 => 0x3000004c,
            Type::LARCH_GOT64_PC_LO20 => 0x3000004d,
            Type::LARCH_GOT64_PC_HI12 => 0x3000004e,
            Type::LARCH_GOT_HI20 => 0x3000004f,
            Type::LARCH_GOT_LO12 => 0x30000050,
            Type::LARCH_GOT64_LO20 => 0x30000051,
            Type::LARCH_GOT64_HI12 => 0x30000052,
            Type::LARCH_TLS_LE_HI20 => 0x30000053,
            Type::LARCH_TLS_LE_LO12 => 0x30000054,
            Type::LARCH_TLS_LE64_LO20 => 0x30000055,
            Type::LARCH_TLS_LE64_HI12 => 0x30000056,
            Type::LARCH_TLS_IE_PC_HI20 => 0x30000057,
            Type::LARCH_TLS_IE_PC_LO12 => 0x30000058,
            Type::LARCH_TLS_IE64_PC_LO20 => 0x30000059,
            Type::LARCH_TLS_IE64_PC_HI12 => 0x3000005a,
            Type::LARCH_TLS_IE_HI20 => 0x3000005b,
            Type::LARCH_TLS_IE_LO12 => 0x3000005c,
            Type::LARCH_TLS_IE64_LO20 => 0x3000005d,
            Type::LARCH_TLS_IE64_HI12 => 0x3000005e,
            Type::LARCH_TLS_LD_PC_HI20 => 0x3000005f,
            Type::LARCH_TLS_LD_HI20 => 0x30000060,
            Type::LARCH_TLS_GD_PC_HI20 => 0x30000061,
            Type::LARCH_TLS_GD_HI20 => 0x30000062,
            Type::LARCH_32_PCREL => 0x30000063,
            Type::LARCH_RELAX => 0x30000064,
            Type::LARCH_ALIGN => 0x30000066,
            Type::LARCH_PCREL20_S2 => 0x30000067,
            Type::LARCH_ADD6 => 0x30000069,
            Type::LARCH_SUB6 => 0x3000006a,
            Type::LARCH_ADD_ULEB128 => 0x3000006b,
            Type::LARCH_SUB_ULEB128 => 0x3000006c,
            Type::LARCH_64_PCREL => 0x3000006d,
            Type::LARCH_CALL36 => 0x3000006e,
            Type::LARCH_TLS_DESC32 => 0x3000000d,
            Type::LARCH_TLS_DESC64 => 0x3000000e,
            Type::LARCH_TLS_DESC_PC_HI20 => 0x3000006f,
            Type::LARCH_TLS_DESC_PC_LO12 => 0x30000070,
            Type::LARCH_TLS_DESC64_PC_LO20 => 0x30000071,
            Type::LARCH_TLS_DESC64_PC_HI12 => 0x30000072,
            Type::LARCH_TLS_DESC_HI20 => 0x30000073,
            Type::LARCH_TLS_DESC_LO12 => 0x30000074,
            Type::LARCH_TLS_DESC64_LO20 => 0x30000075,
            Type::LARCH_TLS_DESC64_HI12 => 0x30000076,
            Type::LARCH_TLS_DESC_LD => 0x30000077,
            Type::LARCH_TLS_DESC_CALL => 0x30000078,
            Type::LARCH_TLS_LE_HI20_R => 0x30000079,
            Type::LARCH_TLS_LE_ADD_R => 0x3000007a,
            Type::LARCH_TLS_LE_LO12_R => 0x3000007b,
            Type::LARCH_TLS_LD_PCREL20_S2 => 0x3000007c,
            Type::LARCH_TLS_GD_PCREL20_S2 => 0x3000007d,
            Type::LARCH_TLS_DESC_PCREL20_S2 => 0x3000007e,
            Type::MIPS_NONE => 0x38000000,
            Type::MIPS_16 => 0x38000001,
            Type::MIPS_32 => 0x38000002,
            Type::MIPS_REL32 => 0x38000003,
            Type::MIPS_26 => 0x38000004,
            Type::MIPS_HI16 => 0x38000005,
            Type::MIPS_LO16 => 0x38000006,
            Type::MIPS_GPREL16 => 0x38000007,
            Type::MIPS_LITERAL => 0x38000008,
            Type::MIPS_GOT16 => 0x38000009,
            Type::MIPS_PC16 => 0x3800000a,
            Type::MIPS_CALL16 => 0x3800000b,
            Type::MIPS_GPREL32 => 0x3800000c,
            Type::MIPS_UNUSED1 => 0x3800000d,
            Type::MIPS_UNUSED2 => 0x3800000e,
            Type::MIPS_UNUSED3 => 0x3800000f,
            Type::MIPS_SHIFT5 => 0x38000010,
            Type::MIPS_SHIFT6 => 0x38000011,
            Type::MIPS_64 => 0x38000012,
            Type::MIPS_GOT_DISP => 0x38000013,
            Type::MIPS_GOT_PAGE => 0x38000014,
            Type::MIPS_GOT_OFST => 0x38000015,
            Type::MIPS_GOT_HI16 => 0x38000016,
            Type::MIPS_GOT_LO16 => 0x38000017,
            Type::MIPS_SUB => 0x38000018,
            Type::MIPS_INSERT_A => 0x38000019,
            Type::MIPS_INSERT_B => 0x3800001a,
            Type::MIPS_DELETE => 0x3800001b,
            Type::MIPS_HIGHER => 0x3800001c,
            Type::MIPS_HIGHEST => 0x3800001d,
            Type::MIPS_CALL_HI16 => 0x3800001e,
            Type::MIPS_CALL_LO16 => 0x3800001f,
            Type::MIPS_SCN_DISP => 0x38000020,
            Type::MIPS_REL16 => 0x38000021,
            Type::MIPS_ADD_IMMEDIATE => 0x38000022,
            Type::MIPS_PJUMP => 0x38000023,
            Type::MIPS_RELGOT => 0x38000024,
            Type::MIPS_JALR => 0x38000025,
            Type::MIPS_TLS_DTPMOD32 => 0x38000026,
            Type::MIPS_TLS_DTPREL32 => 0x38000027,
            Type::MIPS_TLS_DTPMOD64 => 0x38000028,
            Type::MIPS_TLS_DTPREL64 => 0x38000029,
            Type::MIPS_TLS_GD => 0x3800002a,
            Type::MIPS_TLS_LDM => 0x3800002b,
            Type::MIPS_TLS_DTPREL_HI16 => 0x3800002c,
            Type::MIPS_TLS_DTPREL_LO16 => 0x3800002d,
            Type::MIPS_TLS_GOTTPREL => 0x3800002e,
            Type::MIPS_TLS_TPREL32 => 0x3800002f,
            Type::MIPS_TLS_TPREL64 => 0x38000030,
            Type::MIPS_TLS_TPREL_HI16 => 0x38000031,
            Type::MIPS_TLS_TPREL_LO16 => 0x38000032,
            Type::MIPS_GLOB_DAT => 0x38000033,
            Type::MIPS_PC21_S2 => 0x3800003c,
            Type::MIPS_PC26_S2 => 0x3800003d,
            Type::MIPS_PC18_S3 => 0x3800003e,
            Type::MIPS_PC19_S2 => 0x3800003f,
            Type::MIPS_PCHI16 => 0x38000040,
            Type::MIPS_PCLO16 => 0x38000041,
            Type::MIPS16_26 => 0x38000064,
            Type::MIPS16_GPREL => 0x38000065,
            Type::MIPS16_GOT16 => 0x38000066,
            Type::MIPS16_CALL16 => 0x38000067,
            Type::MIPS16_HI16 => 0x38000068,
            Type::MIPS16_LO16 => 0x38000069,
            Type::MIPS16_TLS_GD => 0x3800006a,
            Type::MIPS16_TLS_LDM => 0x3800006b,
            Type::MIPS16_TLS_DTPREL_HI16 => 0x3800006c,
            Type::MIPS16_TLS_DTPREL_LO16 => 0x3800006d,
            Type::MIPS16_TLS_GOTTPREL => 0x3800006e,
            Type::MIPS16_TLS_TPREL_HI16 => 0x3800006f,
            Type::MIPS16_TLS_TPREL_LO16 => 0x38000070,
            Type::MIPS_COPY => 0x3800007e,
            Type::MIPS_JUMP_SLOT => 0x3800007f,
            Type::MICROMIPS_26_S1 => 0x38000085,
            Type::MICROMIPS_HI16 => 0x38000086,
            Type::MICROMIPS_LO16 => 0x38000087,
            Type::MICROMIPS_GPREL16 => 0x38000088,
            Type::MICROMIPS_LITERAL => 0x38000089,
            Type::MICROMIPS_GOT16 => 0x3800008a,
            Type::MICROMIPS_PC7_S1 => 0x3800008b,
            Type::MICROMIPS_PC10_S1 => 0x3800008c,
            Type::MICROMIPS_PC16_S1 => 0x3800008d,
            Type::MICROMIPS_CALL16 => 0x3800008e,
            Type::MICROMIPS_GOT_DISP => 0x38000091,
            Type::MICROMIPS_GOT_PAGE => 0x38000092,
            Type::MICROMIPS_GOT_OFST => 0x38000093,
            Type::MICROMIPS_GOT_HI16 => 0x38000094,
            Type::MICROMIPS_GOT_LO16 => 0x38000095,
            Type::MICROMIPS_SUB => 0x38000096,
            Type::MICROMIPS_HIGHER => 0x38000097,
            Type::MICROMIPS_HIGHEST => 0x38000098,
            Type::MICROMIPS_CALL_HI16 => 0x38000099,
            Type::MICROMIPS_CALL_LO16 => 0x3800009a,
            Type::MICROMIPS_SCN_DISP => 0x3800009b,
            Type::MICROMIPS_JALR => 0x3800009c,
            Type::MICROMIPS_HI0_LO16 => 0x3800009d,
            Type::MICROMIPS_TLS_GD => 0x380000a2,
            Type::MICROMIPS_TLS_LDM => 0x380000a3,
            Type::MICROMIPS_TLS_DTPREL_HI16 => 0x380000a4,
            Type::MICROMIPS_TLS_DTPREL_LO16 => 0x380000a5,
            Type::MICROMIPS_TLS_GOTTPREL => 0x380000a6,
            Type::MICROMIPS_TLS_TPREL_HI16 => 0x380000a9,
            Type::MICROMIPS_TLS_TPREL_LO16 => 0x380000aa,
            Type::MICROMIPS_GPREL7_S2 => 0x380000ac,
            Type::MICROMIPS_PC23_S2 => 0x380000ad,
            Type::MICROMIPS_PC21_S2 => 0x380000ae,
            Type::MICROMIPS_PC26_S2 => 0x380000af,
            Type::MICROMIPS_PC18_S3 => 0x380000b0,
            Type::MICROMIPS_PC19_S2 => 0x380000b1,
            Type::MIPS_NUM => 0x380000da,
            Type::MIPS_PC32 => 0x380000f8,
            Type::MIPS_EH => 0x380000f9,
            Type::PPC_NONE => 0x40000000,
            Type::PPC_ADDR32 => 0x40000001,
            Type::PPC_ADDR24 => 0x40000002,
            Type::PPC_ADDR16 => 0x40000003,
            Type::PPC_ADDR16_LO => 0x40000004,
            Type::PPC_ADDR16_HI => 0x40000005,
            Type::PPC_ADDR16_HA => 0x40000006,
            Type::PPC_ADDR14 => 0x40000007,
            Type::PPC_ADDR14_BRTAKEN => 0x40000008,
            Type::PPC_ADDR14_BRNTAKEN => 0x40000009,
            Type::PPC_REL24 => 0x4000000a,
            Type::PPC_REL14 => 0x4000000b,
            Type::PPC_REL14_BRTAKEN => 0x4000000c,
            Type::PPC_REL14_BRNTAKEN => 0x4000000d,
            Type::PPC_GOT16 => 0x4000000e,
            Type::PPC_GOT16_LO => 0x4000000f,
            Type::PPC_GOT16_HI => 0x40000010,
            Type::PPC_GOT16_HA => 0x40000011,
            Type::PPC_PLTREL24 => 0x40000012,
            Type::PPC_JMP_SLOT => 0x40000015,
            Type::PPC_RELATIVE => 0x40000016,
            Type::PPC_LOCAL24PC => 0x40000017,
            Type::PPC_REL32 => 0x4000001a,
            Type::PPC_TLS => 0x40000043,
            Type::PPC_DTPMOD32 => 0x40000044,
            Type::PPC_TPREL16 => 0x40000045,
            Type::PPC_TPREL16_LO => 0x40000046,
            Type::PPC_TPREL16_HI => 0x40000047,
            Type::PPC_TPREL16_HA => 0x40000048,
            Type::PPC_TPREL32 => 0x40000049,
            Type::PPC_DTPREL16 => 0x4000004a,
            Type::PPC_DTPREL16_LO => 0x4000004b,
            Type::PPC_DTPREL16_HI => 0x4000004c,
            Type::PPC_DTPREL16_HA => 0x4000004d,
            Type::PPC_DTPREL32 => 0x4000004e,
            Type::PPC_GOT_TLSGD16 => 0x4000004f,
            Type::PPC_GOT_TLSGD16_LO => 0x40000050,
            Type::PPC_GOT_TLSGD16_HI => 0x40000051,
            Type::PPC_GOT_TLSGD16_HA => 0x40000052,
            Type::PPC_GOT_TLSLD16 => 0x40000053,
            Type::PPC_GOT_TLSLD16_LO => 0x40000054,
            Type::PPC_GOT_TLSLD16_HI => 0x40000055,
            Type::PPC_GOT_TLSLD16_HA => 0x40000056,
            Type::PPC_GOT_TPREL16 => 0x40000057,
            Type::PPC_GOT_TPREL16_LO => 0x40000058,
            Type::PPC_GOT_TPREL16_HI => 0x40000059,
            Type::PPC_GOT_TPREL16_HA => 0x4000005a,
            Type::PPC_GOT_DTPREL16 => 0x4000005b,
            Type::PPC_GOT_DTPREL16_LO => 0x4000005c,
            Type::PPC_GOT_DTPREL16_HI => 0x4000005d,
            Type::PPC_GOT_DTPREL16_HA => 0x4000005e,
            Type::PPC_TLSGD => 0x4000005f,
            Type::PPC_TLSLD => 0x40000060,
            Type::PPC_REL16 => 0x400000f9,
            Type::PPC_REL16_LO => 0x400000fa,
            Type::PPC_REL16_HI => 0x400000fb,
            Type::PPC_REL16_HA => 0x400000fc,
            Type::PPC64_NONE => 0x48000000,
            Type::PPC64_ADDR32 => 0x48000001,
            Type::PPC64_ADDR24 => 0x48000002,
            Type::PPC64_ADDR16 => 0x48000003,
            Type::PPC64_ADDR16_LO => 0x48000004,
            Type::PPC64_ADDR16_HI => 0x48000005,
            Type::PPC64_ADDR16_HA => 0x48000006,
            Type::PPC64_ADDR14 => 0x48000007,
            Type::PPC64_ADDR14_BRTAKEN => 0x48000008,
            Type::PPC64_ADDR14_BRNTAKEN => 0x48000009,
            Type::PPC64_REL24 => 0x4800000a,
            Type::PPC64_REL14 => 0x4800000b,
            Type::PPC64_REL14_BRTAKEN => 0x4800000c,
            Type::PPC64_REL14_BRNTAKEN => 0x4800000d,
            Type::PPC64_GOT16 => 0x4800000e,
            Type::PPC64_GOT16_LO => 0x4800000f,
            Type::PPC64_GOT16_HI => 0x48000010,
            Type::PPC64_GOT16_HA => 0x48000011,
            Type::PPC64_JMP_SLOT => 0x48000015,
            Type::PPC64_RELATIVE => 0x48000016,
            Type::PPC64_REL32 => 0x4800001a,
            Type::PPC64_ADDR64 => 0x48000026,
            Type::PPC64_ADDR16_HIGHER => 0x48000027,
            Type::PPC64_ADDR16_HIGHERA => 0x48000028,
            Type::PPC64_ADDR16_HIGHEST => 0x48000029,
            Type::PPC64_ADDR16_HIGHESTA => 0x4800002a,
            Type::PPC64_REL64 => 0x4800002c,
            Type::PPC64_TOC16 => 0x4800002f,
            Type::PPC64_TOC16_LO => 0x48000030,
            Type::PPC64_TOC16_HI => 0x48000031,
            Type::PPC64_TOC16_HA => 0x48000032,
            Type::PPC64_TOC => 0x48000033,
            Type::PPC64_ADDR16_DS => 0x48000038,
            Type::PPC64_ADDR16_LO_DS => 0x48000039,
            Type::PPC64_GOT16_DS => 0x4800003a,
            Type::PPC64_GOT16_LO_DS => 0x4800003b,
            Type::PPC64_TOC16_DS => 0x4800003f,
            Type::PPC64_TOC16_LO_DS => 0x48000040,
            Type::PPC64_TLS => 0x48000043,
            Type::PPC64_DTPMOD64 => 0x48000044,
            Type::PPC64_TPREL16 => 0x48000045,
            Type::PPC64_TPREL16_LO => 0x48000046,
            Type::PPC64_TPREL16_HI => 0x48000047,
            Type::PPC64_TPREL16_HA => 0x48000048,
            Type::PPC64_TPREL64 => 0x48000049,
            Type::PPC64_DTPREL16 => 0x4800004a,
            Type::PPC64_DTPREL16_LO => 0x4800004b,
            Type::PPC64_DTPREL16_HI => 0x4800004c,
            Type::PPC64_DTPREL16_HA => 0x4800004d,
            Type::PPC64_DTPREL64 => 0x4800004e,
            Type::PPC64_GOT_TLSGD16 => 0x4800004f,
            Type::PPC64_GOT_TLSGD16_LO => 0x48000050,
            Type::PPC64_GOT_TLSGD16_HI => 0x48000051,
            Type::PPC64_GOT_TLSGD16_HA => 0x48000052,
            Type::PPC64_GOT_TLSLD16 => 0x48000053,
            Type::PPC64_GOT_TLSLD16_LO => 0x48000054,
            Type::PPC64_GOT_TLSLD16_HI => 0x48000055,
            Type::PPC64_GOT_TLSLD16_HA => 0x48000056,
            Type::PPC64_GOT_TPREL16_DS => 0x48000057,
            Type::PPC64_GOT_TPREL16_LO_DS => 0x48000058,
            Type::PPC64_GOT_TPREL16_HI => 0x48000059,
            Type::PPC64_GOT_TPREL16_HA => 0x4800005a,
            Type::PPC64_GOT_DTPREL16_DS => 0x4800005b,
            Type::PPC64_GOT_DTPREL16_LO_DS => 0x4800005c,
            Type::PPC64_GOT_DTPREL16_HI => 0x4800005d,
            Type::PPC64_GOT_DTPREL16_HA => 0x4800005e,
            Type::PPC64_TPREL16_DS => 0x4800005f,
            Type::PPC64_TPREL16_LO_DS => 0x48000060,
            Type::PPC64_TPREL16_HIGHER => 0x48000061,
            Type::PPC64_TPREL16_HIGHERA => 0x48000062,
            Type::PPC64_TPREL16_HIGHEST => 0x48000063,
            Type::PPC64_TPREL16_HIGHESTA => 0x48000064,
            Type::PPC64_DTPREL16_DS => 0x48000065,
            Type::PPC64_DTPREL16_LO_DS => 0x48000066,
            Type::PPC64_DTPREL16_HIGHER => 0x48000067,
            Type::PPC64_DTPREL16_HIGHERA => 0x48000068,
            Type::PPC64_DTPREL16_HIGHEST => 0x48000069,
            Type::PPC64_DTPREL16_HIGHESTA => 0x4800006a,
            Type::PPC64_TLSGD => 0x4800006b,
            Type::PPC64_TLSLD => 0x4800006c,
            Type::PPC64_REL16 => 0x480000f9,
            Type::PPC64_REL16_LO => 0x480000fa,
            Type::PPC64_REL16_HI => 0x480000fb,
            Type::PPC64_REL16_HA => 0x480000fc,
            Type::SPARC_NONE => 0x50000000,
            Type::SPARC_8 => 0x50000001,
            Type::SPARC_16 => 0x50000002,
            Type::SPARC_32 => 0x50000003,
            Type::SPARC_DISP8 => 0x50000004,
            Type::SPARC_DISP16 => 0x50000005,
            Type::SPARC_DISP32 => 0x50000006,
            Type::SPARC_WDISP30 => 0x50000007,
            Type::SPARC_WDISP22 => 0x50000008,
            Type::SPARC_HI22 => 0x50000009,
            Type::SPARC_22 => 0x5000000a,
            Type::SPARC_13 => 0x5000000b,
            Type::SPARC_LO10 => 0x5000000c,
            Type::SPARC_GOT10 => 0x5000000d,
            Type::SPARC_GOT13 => 0x5000000e,
            Type::SPARC_GOT22 => 0x5000000f,
            Type::SPARC_PC10 => 0x50000010,
            Type::SPARC_PC22 => 0x50000011,
            Type::SPARC_WPLT30 => 0x50000012,
            Type::SPARC_COPY => 0x50000013,
            Type::SPARC_GLOB_DAT => 0x50000014,
            Type::SPARC_JMP_SLOT => 0x50000015,
            Type::SPARC_RELATIVE => 0x50000016,
            Type::SPARC_UA32 => 0x50000017,
            Type::SPARC_PLT32 => 0x50000018,
            Type::SPARC_HIPLT22 => 0x50000019,
            Type::SPARC_LOPLT10 => 0x5000001a,
            Type::SPARC_PCPLT32 => 0x5000001b,
            Type::SPARC_PCPLT22 => 0x5000001c,
            Type::SPARC_PCPLT10 => 0x5000001d,
            Type::SPARC_10 => 0x5000001e,
            Type::SPARC_11 => 0x5000001f,
            Type::SPARC_64 => 0x50000020,
            Type::SPARC_OLO10 => 0x50000021,
            Type::SPARC_HH22 => 0x50000022,
            Type::SPARC_HM10 => 0x50000023,
            Type::SPARC_LM22 => 0x50000024,
            Type::SPARC_PC_HH22 => 0x50000025,
            Type::SPARC_PC_HM10 => 0x50000026,
            Type::SPARC_PC_LM22 => 0x50000027,
            Type::SPARC_WDISP16 => 0x50000028,
            Type::SPARC_WDISP19 => 0x50000029,
            Type::SPARC_7 => 0x5000002b,
            Type::SPARC_5 => 0x5000002c,
            Type::SPARC_6 => 0x5000002d,
            Type::SPARC_DISP64 => 0x5000002e,
            Type::SPARC_PLT64 => 0x5000002f,
            Type::SPARC_HIX22 => 0x50000030,
            Type::SPARC_LOX10 => 0x50000031,
            Type::SPARC_H44 => 0x50000032,
            Type::SPARC_M44 => 0x50000033,
            Type::SPARC_L44 => 0x50000034,
            Type::SPARC_REGISTER => 0x50000035,
            Type::SPARC_UA64 => 0x50000036,
            Type::SPARC_UA16 => 0x50000037,
            Type::SPARC_TLS_GD_HI22 => 0x50000038,
            Type::SPARC_TLS_GD_LO10 => 0x50000039,
            Type::SPARC_TLS_GD_ADD => 0x5000003a,
            Type::SPARC_TLS_GD_CALL => 0x5000003b,
            Type::SPARC_TLS_LDM_HI22 => 0x5000003c,
            Type::SPARC_TLS_LDM_LO10 => 0x5000003d,
            Type::SPARC_TLS_LDM_ADD => 0x5000003e,
            Type::SPARC_TLS_LDM_CALL => 0x5000003f,
            Type::SPARC_TLS_LDO_HIX22 => 0x50000040,
            Type::SPARC_TLS_LDO_LOX10 => 0x50000041,
            Type::SPARC_TLS_LDO_ADD => 0x50000042,
            Type::SPARC_TLS_IE_HI22 => 0x50000043,
            Type::SPARC_TLS_IE_LO10 => 0x50000044,
            Type::SPARC_TLS_IE_LD => 0x50000045,
            Type::SPARC_TLS_IE_LDX => 0x50000046,
            Type::SPARC_TLS_IE_ADD => 0x50000047,
            Type::SPARC_TLS_LE_HIX22 => 0x50000048,
            Type::SPARC_TLS_LE_LOX10 => 0x50000049,
            Type::SPARC_TLS_DTPMOD32 => 0x5000004a,
            Type::SPARC_TLS_DTPMOD64 => 0x5000004b,
            Type::SPARC_TLS_DTPOFF32 => 0x5000004c,
            Type::SPARC_TLS_DTPOFF64 => 0x5000004d,
            Type::SPARC_TLS_TPOFF32 => 0x5000004e,
            Type::SPARC_TLS_TPOFF64 => 0x5000004f,
            Type::SPARC_GOTDATA_HIX22 => 0x50000050,
            Type::SPARC_GOTDATA_LOX10 => 0x50000051,
            Type::SPARC_GOTDATA_OP_HIX22 => 0x50000052,
            Type::SPARC_GOTDATA_OP_LOX10 => 0x50000053,
            Type::SPARC_GOTDATA_OP => 0x50000054,
            Type::SYSZ_NONE => 0x58000000,
            Type::SYSZ_8 => 0x58000001,
            Type::SYSZ_12 => 0x58000002,
            Type::SYSZ_16 => 0x58000003,
            Type::SYSZ_32 => 0x58000004,
            Type::SYSZ_PC32 => 0x58000005,
            Type::SYSZ_GOT12 => 0x58000006,
            Type::SYSZ_GOT32 => 0x58000007,
            Type::SYSZ_PLT32 => 0x58000008,
            Type::SYSZ_COPY => 0x58000009,
            Type::SYSZ_GLOB_DAT => 0x5800000a,
            Type::SYSZ_JMP_SLOT => 0x5800000b,
            Type::SYSZ_RELATIVE => 0x5800000c,
            Type::SYSZ_GOTOFF => 0x5800000d,
            Type::SYSZ_GOTPC => 0x5800000e,
            Type::SYSZ_GOT16 => 0x5800000f,
            Type::SYSZ_PC16 => 0x58000010,
            Type::SYSZ_PC16DBL => 0x58000011,
            Type::SYSZ_PLT16DBL => 0x58000012,
            Type::SYSZ_PC32DBL => 0x58000013,
            Type::SYSZ_PLT32DBL => 0x58000014,
            Type::SYSZ_GOTPCDBL => 0x58000015,
            Type::SYSZ_64 => 0x58000016,
            Type::SYSZ_PC64 => 0x58000017,
            Type::SYSZ_GOT64 => 0x58000018,
            Type::SYSZ_PLT64 => 0x58000019,
            Type::SYSZ_GOTENT => 0x5800001a,
            Type::SYSZ_GOTOFF16 => 0x5800001b,
            Type::SYSZ_GOTOFF64 => 0x5800001c,
            Type::SYSZ_GOTPLT12 => 0x5800001d,
            Type::SYSZ_GOTPLT16 => 0x5800001e,
            Type::SYSZ_GOTPLT32 => 0x5800001f,
            Type::SYSZ_GOTPLT64 => 0x58000020,
            Type::SYSZ_GOTPLTENT => 0x58000021,
            Type::SYSZ_PLTOFF16 => 0x58000022,
            Type::SYSZ_PLTOFF32 => 0x58000023,
            Type::SYSZ_PLTOFF64 => 0x58000024,
            Type::SYSZ_TLS_LOAD => 0x58000025,
            Type::SYSZ_TLS_GDCALL => 0x58000026,
            Type::SYSZ_TLS_LDCALL => 0x58000027,
            Type::SYSZ_TLS_GD32 => 0x58000028,
            Type::SYSZ_TLS_GD64 => 0x58000029,
            Type::SYSZ_TLS_GOTIE12 => 0x5800002a,
            Type::SYSZ_TLS_GOTIE32 => 0x5800002b,
            Type::SYSZ_TLS_GOTIE64 => 0x5800002c,
            Type::SYSZ_TLS_LDM32 => 0x5800002d,
            Type::SYSZ_TLS_LDM64 => 0x5800002e,
            Type::SYSZ_TLS_IE32 => 0x5800002f,
            Type::SYSZ_TLS_IE64 => 0x58000030,
            Type::SYSZ_TLS_IEENT => 0x58000031,
            Type::SYSZ_TLS_LE32 => 0x58000032,
            Type::SYSZ_TLS_LE64 => 0x58000033,
            Type::SYSZ_TLS_LDO32 => 0x58000034,
            Type::SYSZ_TLS_LDO64 => 0x58000035,
            Type::SYSZ_TLS_DTPMOD => 0x58000036,
            Type::SYSZ_TLS_DTPOFF => 0x58000037,
            Type::SYSZ_TLS_TPOFF => 0x58000038,
            Type::SYSZ_20 => 0x58000039,
            Type::SYSZ_GOT20 => 0x5800003a,
            Type::SYSZ_GOTPLT20 => 0x5800003b,
            Type::SYSZ_TLS_GOTIE20 => 0x5800003c,
            Type::SYSZ_IRELATIVE => 0x5800003d,
            Type::RISCV_NONE => 0x60000000,
            Type::RISCV_32 => 0x60000001,
            Type::RISCV_64 => 0x60000002,
            Type::RISCV_RELATIVE => 0x60000003,
            Type::RISCV_COPY => 0x60000004,
            Type::RISCV_JUMP_SLOT => 0x60000005,
            Type::RISCV_TLS_DTPMOD32 => 0x60000006,
            Type::RISCV_TLS_DTPMOD64 => 0x60000007,
            Type::RISCV_TLS_DTPREL32 => 0x60000008,
            Type::RISCV_TLS_DTPREL64 => 0x60000009,
            Type::RISCV_TLS_TPREL32 => 0x6000000a,
            Type::RISCV_TLS_TPREL64 => 0x6000000b,
            Type::RISCV_TLSDESC => 0x6000000c,
            Type::RISCV_BRANCH => 0x60000010,
            Type::RISCV_JAL => 0x60000011,
            Type::RISCV_CALL => 0x60000012,
            Type::RISCV_CALL_PLT => 0x60000013,
            Type::RISCV_GOT_HI20 => 0x60000014,
            Type::RISCV_TLS_GOT_HI20 => 0x60000015,
            Type::RISCV_TLS_GD_HI20 => 0x60000016,
            Type::RISCV_PCREL_HI20 => 0x60000017,
            Type::RISCV_PCREL_LO12_I => 0x60000018,
            Type::RISCV_PCREL_LO12_S => 0x60000019,
            Type::RISCV_HI20 => 0x6000001a,
            Type::RISCV_LO12_I => 0x6000001b,
            Type::RISCV_LO12_S => 0x6000001c,
            Type::RISCV_TPREL_HI20 => 0x6000001d,
            Type::RISCV_TPREL_LO12_I => 0x6000001e,
            Type::RISCV_TPREL_LO12_S => 0x6000001f,
            Type::RISCV_TPREL_ADD => 0x60000020,
            Type::RISCV_ADD8 => 0x60000021,
            Type::RISCV_ADD16 => 0x60000022,
            Type::RISCV_ADD32 => 0x60000023,
            Type::RISCV_ADD64 => 0x60000024,
            Type::RISCV_SUB8 => 0x60000025,
            Type::RISCV_SUB16 => 0x60000026,
            Type::RISCV_SUB32 => 0x60000027,
            Type::RISCV_SUB64 => 0x60000028,
            Type::RISCV_GOT32_PCREL => 0x60000029,
            Type::RISCV_ALIGN => 0x6000002b,
            Type::RISCV_RVC_BRANCH => 0x6000002c,
            Type::RISCV_RVC_JUMP => 0x6000002d,
            Type::RISCV_RVC_LUI => 0x6000002e,
            Type::RISCV_RELAX => 0x60000033,
            Type::RISCV_SUB6 => 0x60000034,
            Type::RISCV_SET6 => 0x60000035,
            Type::RISCV_SET8 => 0x60000036,
            Type::RISCV_SET16 => 0x60000037,
            Type::RISCV_SET32 => 0x60000038,
            Type::RISCV_32_PCREL => 0x60000039,
            Type::RISCV_IRELATIVE => 0x6000003a,
            Type::RISCV_PLT32 => 0x6000003b,
            Type::RISCV_SET_ULEB128 => 0x6000003c,
            Type::RISCV_SUB_ULEB128 => 0x6000003d,
            Type::RISCV_TLSDESC_HI20 => 0x6000003e,
            Type::RISCV_TLSDESC_LOAD_LO12 => 0x6000003f,
            Type::RISCV_TLSDESC_ADD_LO12 => 0x60000040,
            Type::RISCV_TLSDESC_CALL => 0x60000041,
            Type::BPF_NONE => 0x68000000,
            Type::BPF_64_64 => 0x68000001,
            Type::BPF_64_ABS64 => 0x68000002,
            Type::BPF_64_ABS32 => 0x68000003,
            Type::BPF_64_NODYLD32 => 0x68000004,
            Type::BPF_64_32 => 0x6800000a,
            Type::UNKNOWN(value) => value,

        }
    }
}

#[allow(non_camel_case_types)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
/// The *purpose* of a relocation defines how this relocation is used by the
/// loader.
pub enum Purpose {
    NONE,
    /// The relocation is associated with the PLT/GOT resolution
    PLTGOT,
    /// The relocation is used for regulard data/code relocation
    DYNAMIC,
    /// The relocation is used in an object file
    OBJECT,
    UNKNOWN(u32),
}

impl Purpose {
    pub fn from_value(value: u32) -> Self {
        match value {
            0x00000000 => Purpose::NONE,
            0x00000001 => Purpose::PLTGOT,
            0x00000002 => Purpose::DYNAMIC,
            0x00000003 => Purpose::OBJECT,
            _ => Purpose::UNKNOWN(value),

        }
    }
}


#[allow(non_camel_case_types)]
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum Encoding {
    /// The relocation is using the regular Elf_Rel structure
    REL,
    /// The relocation is using the regular Elf_Rela structure
    RELA,
    /// The relocation is using the relative relocation format
    RELR,
    /// The relocation is using the packed Android-SLEB128 format
    ANDROID_SLEB,
    UNKNOWN(u32),
}

impl Encoding {
    pub fn from_value(value: u32) -> Self {
        match value {
            0x00000001 => Encoding::REL,
            0x00000002 => Encoding::RELA,
            0x00000003 => Encoding::RELR,
            0x00000004 => Encoding::ANDROID_SLEB,
            _ => Encoding::UNKNOWN(value),

        }
    }
}

impl Relocation<'_> {
    /// Additional value that can be involved in the relocation processing
    pub fn addend(&self) -> i64 {
        self.ptr.addend()
    }

    /// Type of the relocation
    pub fn get_type(&self) -> Type {
        Type::from(self.ptr.get_type())
    }

    /// Check if the relocation uses the explicit [`Relocation::addend`] field
    /// (this is usually the case for 64 bits binaries)
    pub fn is_rela(&self) -> bool {
        self.ptr.is_rela()
    }

    /// Check if the relocation uses the implicit addend
    /// (i.e. not present in the ELF structure)
    pub fn is_rel(&self) -> bool {
        self.ptr.is_rel()
    }

    /// Relocation info which contains, for instance, the symbol index
    pub fn info(&self) -> u32 {
        self.ptr.info()
    }

    /// Target architecture for this relocation
    pub fn architecture(&self) -> u32 {
        self.ptr.architecture()
    }

    /// The purpose of the relocation
    pub fn purpose(&self) -> Purpose {
        Purpose::from_value(self.ptr.purpose())
    }

    /// The encoding of the relocation
    pub fn encoding(&self) -> Encoding {
        Encoding::from_value(self.ptr.encoding())
    }

    /// Symbol associated with the relocation (if any)
    pub fn symbol(&self) -> Option<Symbol> {
        into_optional(self.ptr.symbol())
    }

    /// The section in which the relocation is applied (if any)
    pub fn section(&self) -> Option<Section> {
        into_optional(self.ptr.section())
    }

    /// The associated symbol table (if any)
    pub fn symbol_table(&self) -> Option<Section> {
        into_optional(self.ptr.symbol_table())
    }

    /// Try to resolve the value of the relocation with the provided base address.
    ///
    /// The returned value could be used such as: `*address = resolve_with_base_address(...)`
    ///
    /// See: [`Relocation::resolve`]
    pub fn resolve_with_base_address(&self, base_address: u64) -> Result<u64, Error> {
        to_result!(ffi::ELF_Relocation::resolve, &self, base_address);
    }

    /// Try to resolve the value of the relocation.
    /// The returned value could be used such as: `*address = resolve(...)`
    ///
    /// See: [`Relocation::resolve_with_base_address`]
    pub fn resolve(&self) -> Result<u64, Error> {
        self.resolve_with_base_address(0)
    }
}

impl std::fmt::Debug for Relocation<'_> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let base = self as &dyn generic::Relocation;
        f.debug_struct("Relocation")
            .field("base", &base)
            .field("addend", &self.addend())
            .field("type", &self.get_type())
            .field("info", &self.info())
            .field("architecture", &self.architecture())
            .field("purpose", &self.purpose())
            .field("encoding", &self.encoding())
            .field("symbol", &self.symbol())
            .field("section", &self.section())
            .field("symbol_table", &self.symbol_table())
            .finish()
    }
}

impl FromFFI<ffi::ELF_Relocation> for Relocation<'_> {
    fn from_ffi(ptr: cxx::UniquePtr<ffi::ELF_Relocation>) -> Self {
        Relocation {
            ptr,
            _owner: PhantomData
        }
    }
}

impl generic::Relocation for Relocation<'_> {
    fn as_generic(&self) -> &ffi::AbstractRelocation {
        self.ptr.as_ref().unwrap().as_ref()
    }
}

declare_iterator!(Relocations, Relocation<'a>, ffi::ELF_Relocation, ffi::ELF_Binary, ffi::ELF_Binary_it_relocations);
declare_iterator!(PltGotRelocations, Relocation<'a>, ffi::ELF_Relocation, ffi::ELF_Binary, ffi::ELF_Binary_it_pltgot_relocations);
declare_iterator!(DynamicRelocations, Relocation<'a>, ffi::ELF_Relocation, ffi::ELF_Binary, ffi::ELF_Binary_it_dynamic_relocations);
declare_iterator!(ObjectRelocations, Relocation<'a>, ffi::ELF_Relocation, ffi::ELF_Binary, ffi::ELF_Binary_it_object_relocations);
