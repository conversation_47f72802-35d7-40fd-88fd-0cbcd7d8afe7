#include "asm/mips/init.hpp"

#include "LIEF/asm/mips/registers.hpp"

namespace LIEF::assembly::mips::py {
template<>
void create<LIEF::assembly::mips::REG>(nb::module_& m) {
  nb::enum_<LIEF::assembly::mips::REG> reg(m, "REG");
  reg.value("NoRegister", LIEF::assembly::mips::REG::NoRegister)
  .value("AT", LIEF::assembly::mips::REG::AT)
  .value("DSPCCond", LIEF::assembly::mips::REG::DSPCCond)
  .value("DSPCarry", LIEF::assembly::mips::REG::DSPCarry)
  .value("DSPEFI", LIEF::assembly::mips::REG::DSPEFI)
  .value("DSPOutFlag", LIEF::assembly::mips::REG::DSPOutFlag)
  .value("DSPPos", LIEF::assembly::mips::REG::DSPPos)
  .value("DSPSCount", LIEF::assembly::mips::REG::DSPSCount)
  .value("FP", LIEF::assembly::mips::REG::FP)
  .value("GP", LIEF::assembly::mips::REG::GP)
  .value("MSAAccess", LIEF::assembly::mips::REG::MSAAccess)
  .value("MSACSR", LIEF::assembly::mips::REG::MSACSR)
  .value("MSAIR", LIEF::assembly::mips::REG::MSAIR)
  .value("MSAMap", LIEF::assembly::mips::REG::MSAMap)
  .value("MSAModify", LIEF::assembly::mips::REG::MSAModify)
  .value("MSARequest", LIEF::assembly::mips::REG::MSARequest)
  .value("MSASave", LIEF::assembly::mips::REG::MSASave)
  .value("MSAUnmap", LIEF::assembly::mips::REG::MSAUnmap)
  .value("PC", LIEF::assembly::mips::REG::PC)
  .value("RA", LIEF::assembly::mips::REG::RA)
  .value("SP", LIEF::assembly::mips::REG::SP)
  .value("ZERO", LIEF::assembly::mips::REG::ZERO)
  .value("A0", LIEF::assembly::mips::REG::A0)
  .value("A1", LIEF::assembly::mips::REG::A1)
  .value("A2", LIEF::assembly::mips::REG::A2)
  .value("A3", LIEF::assembly::mips::REG::A3)
  .value("AC0", LIEF::assembly::mips::REG::AC0)
  .value("AC1", LIEF::assembly::mips::REG::AC1)
  .value("AC2", LIEF::assembly::mips::REG::AC2)
  .value("AC3", LIEF::assembly::mips::REG::AC3)
  .value("AT_64", LIEF::assembly::mips::REG::AT_64)
  .value("COP00", LIEF::assembly::mips::REG::COP00)
  .value("COP01", LIEF::assembly::mips::REG::COP01)
  .value("COP02", LIEF::assembly::mips::REG::COP02)
  .value("COP03", LIEF::assembly::mips::REG::COP03)
  .value("COP04", LIEF::assembly::mips::REG::COP04)
  .value("COP05", LIEF::assembly::mips::REG::COP05)
  .value("COP06", LIEF::assembly::mips::REG::COP06)
  .value("COP07", LIEF::assembly::mips::REG::COP07)
  .value("COP08", LIEF::assembly::mips::REG::COP08)
  .value("COP09", LIEF::assembly::mips::REG::COP09)
  .value("COP20", LIEF::assembly::mips::REG::COP20)
  .value("COP21", LIEF::assembly::mips::REG::COP21)
  .value("COP22", LIEF::assembly::mips::REG::COP22)
  .value("COP23", LIEF::assembly::mips::REG::COP23)
  .value("COP24", LIEF::assembly::mips::REG::COP24)
  .value("COP25", LIEF::assembly::mips::REG::COP25)
  .value("COP26", LIEF::assembly::mips::REG::COP26)
  .value("COP27", LIEF::assembly::mips::REG::COP27)
  .value("COP28", LIEF::assembly::mips::REG::COP28)
  .value("COP29", LIEF::assembly::mips::REG::COP29)
  .value("COP30", LIEF::assembly::mips::REG::COP30)
  .value("COP31", LIEF::assembly::mips::REG::COP31)
  .value("COP32", LIEF::assembly::mips::REG::COP32)
  .value("COP33", LIEF::assembly::mips::REG::COP33)
  .value("COP34", LIEF::assembly::mips::REG::COP34)
  .value("COP35", LIEF::assembly::mips::REG::COP35)
  .value("COP36", LIEF::assembly::mips::REG::COP36)
  .value("COP37", LIEF::assembly::mips::REG::COP37)
  .value("COP38", LIEF::assembly::mips::REG::COP38)
  .value("COP39", LIEF::assembly::mips::REG::COP39)
  .value("COP010", LIEF::assembly::mips::REG::COP010)
  .value("COP011", LIEF::assembly::mips::REG::COP011)
  .value("COP012", LIEF::assembly::mips::REG::COP012)
  .value("COP013", LIEF::assembly::mips::REG::COP013)
  .value("COP014", LIEF::assembly::mips::REG::COP014)
  .value("COP015", LIEF::assembly::mips::REG::COP015)
  .value("COP016", LIEF::assembly::mips::REG::COP016)
  .value("COP017", LIEF::assembly::mips::REG::COP017)
  .value("COP018", LIEF::assembly::mips::REG::COP018)
  .value("COP019", LIEF::assembly::mips::REG::COP019)
  .value("COP020", LIEF::assembly::mips::REG::COP020)
  .value("COP021", LIEF::assembly::mips::REG::COP021)
  .value("COP022", LIEF::assembly::mips::REG::COP022)
  .value("COP023", LIEF::assembly::mips::REG::COP023)
  .value("COP024", LIEF::assembly::mips::REG::COP024)
  .value("COP025", LIEF::assembly::mips::REG::COP025)
  .value("COP026", LIEF::assembly::mips::REG::COP026)
  .value("COP027", LIEF::assembly::mips::REG::COP027)
  .value("COP028", LIEF::assembly::mips::REG::COP028)
  .value("COP029", LIEF::assembly::mips::REG::COP029)
  .value("COP030", LIEF::assembly::mips::REG::COP030)
  .value("COP031", LIEF::assembly::mips::REG::COP031)
  .value("COP210", LIEF::assembly::mips::REG::COP210)
  .value("COP211", LIEF::assembly::mips::REG::COP211)
  .value("COP212", LIEF::assembly::mips::REG::COP212)
  .value("COP213", LIEF::assembly::mips::REG::COP213)
  .value("COP214", LIEF::assembly::mips::REG::COP214)
  .value("COP215", LIEF::assembly::mips::REG::COP215)
  .value("COP216", LIEF::assembly::mips::REG::COP216)
  .value("COP217", LIEF::assembly::mips::REG::COP217)
  .value("COP218", LIEF::assembly::mips::REG::COP218)
  .value("COP219", LIEF::assembly::mips::REG::COP219)
  .value("COP220", LIEF::assembly::mips::REG::COP220)
  .value("COP221", LIEF::assembly::mips::REG::COP221)
  .value("COP222", LIEF::assembly::mips::REG::COP222)
  .value("COP223", LIEF::assembly::mips::REG::COP223)
  .value("COP224", LIEF::assembly::mips::REG::COP224)
  .value("COP225", LIEF::assembly::mips::REG::COP225)
  .value("COP226", LIEF::assembly::mips::REG::COP226)
  .value("COP227", LIEF::assembly::mips::REG::COP227)
  .value("COP228", LIEF::assembly::mips::REG::COP228)
  .value("COP229", LIEF::assembly::mips::REG::COP229)
  .value("COP230", LIEF::assembly::mips::REG::COP230)
  .value("COP231", LIEF::assembly::mips::REG::COP231)
  .value("COP310", LIEF::assembly::mips::REG::COP310)
  .value("COP311", LIEF::assembly::mips::REG::COP311)
  .value("COP312", LIEF::assembly::mips::REG::COP312)
  .value("COP313", LIEF::assembly::mips::REG::COP313)
  .value("COP314", LIEF::assembly::mips::REG::COP314)
  .value("COP315", LIEF::assembly::mips::REG::COP315)
  .value("COP316", LIEF::assembly::mips::REG::COP316)
  .value("COP317", LIEF::assembly::mips::REG::COP317)
  .value("COP318", LIEF::assembly::mips::REG::COP318)
  .value("COP319", LIEF::assembly::mips::REG::COP319)
  .value("COP320", LIEF::assembly::mips::REG::COP320)
  .value("COP321", LIEF::assembly::mips::REG::COP321)
  .value("COP322", LIEF::assembly::mips::REG::COP322)
  .value("COP323", LIEF::assembly::mips::REG::COP323)
  .value("COP324", LIEF::assembly::mips::REG::COP324)
  .value("COP325", LIEF::assembly::mips::REG::COP325)
  .value("COP326", LIEF::assembly::mips::REG::COP326)
  .value("COP327", LIEF::assembly::mips::REG::COP327)
  .value("COP328", LIEF::assembly::mips::REG::COP328)
  .value("COP329", LIEF::assembly::mips::REG::COP329)
  .value("COP330", LIEF::assembly::mips::REG::COP330)
  .value("COP331", LIEF::assembly::mips::REG::COP331)
  .value("D0", LIEF::assembly::mips::REG::D0)
  .value("D1", LIEF::assembly::mips::REG::D1)
  .value("D2", LIEF::assembly::mips::REG::D2)
  .value("D3", LIEF::assembly::mips::REG::D3)
  .value("D4", LIEF::assembly::mips::REG::D4)
  .value("D5", LIEF::assembly::mips::REG::D5)
  .value("D6", LIEF::assembly::mips::REG::D6)
  .value("D7", LIEF::assembly::mips::REG::D7)
  .value("D8", LIEF::assembly::mips::REG::D8)
  .value("D9", LIEF::assembly::mips::REG::D9)
  .value("D10", LIEF::assembly::mips::REG::D10)
  .value("D11", LIEF::assembly::mips::REG::D11)
  .value("D12", LIEF::assembly::mips::REG::D12)
  .value("D13", LIEF::assembly::mips::REG::D13)
  .value("D14", LIEF::assembly::mips::REG::D14)
  .value("D15", LIEF::assembly::mips::REG::D15)
  .value("DSPOutFlag20", LIEF::assembly::mips::REG::DSPOutFlag20)
  .value("DSPOutFlag21", LIEF::assembly::mips::REG::DSPOutFlag21)
  .value("DSPOutFlag22", LIEF::assembly::mips::REG::DSPOutFlag22)
  .value("DSPOutFlag23", LIEF::assembly::mips::REG::DSPOutFlag23)
  .value("F0", LIEF::assembly::mips::REG::F0)
  .value("F1", LIEF::assembly::mips::REG::F1)
  .value("F2", LIEF::assembly::mips::REG::F2)
  .value("F3", LIEF::assembly::mips::REG::F3)
  .value("F4", LIEF::assembly::mips::REG::F4)
  .value("F5", LIEF::assembly::mips::REG::F5)
  .value("F6", LIEF::assembly::mips::REG::F6)
  .value("F7", LIEF::assembly::mips::REG::F7)
  .value("F8", LIEF::assembly::mips::REG::F8)
  .value("F9", LIEF::assembly::mips::REG::F9)
  .value("F10", LIEF::assembly::mips::REG::F10)
  .value("F11", LIEF::assembly::mips::REG::F11)
  .value("F12", LIEF::assembly::mips::REG::F12)
  .value("F13", LIEF::assembly::mips::REG::F13)
  .value("F14", LIEF::assembly::mips::REG::F14)
  .value("F15", LIEF::assembly::mips::REG::F15)
  .value("F16", LIEF::assembly::mips::REG::F16)
  .value("F17", LIEF::assembly::mips::REG::F17)
  .value("F18", LIEF::assembly::mips::REG::F18)
  .value("F19", LIEF::assembly::mips::REG::F19)
  .value("F20", LIEF::assembly::mips::REG::F20)
  .value("F21", LIEF::assembly::mips::REG::F21)
  .value("F22", LIEF::assembly::mips::REG::F22)
  .value("F23", LIEF::assembly::mips::REG::F23)
  .value("F24", LIEF::assembly::mips::REG::F24)
  .value("F25", LIEF::assembly::mips::REG::F25)
  .value("F26", LIEF::assembly::mips::REG::F26)
  .value("F27", LIEF::assembly::mips::REG::F27)
  .value("F28", LIEF::assembly::mips::REG::F28)
  .value("F29", LIEF::assembly::mips::REG::F29)
  .value("F30", LIEF::assembly::mips::REG::F30)
  .value("F31", LIEF::assembly::mips::REG::F31)
  .value("FCC0", LIEF::assembly::mips::REG::FCC0)
  .value("FCC1", LIEF::assembly::mips::REG::FCC1)
  .value("FCC2", LIEF::assembly::mips::REG::FCC2)
  .value("FCC3", LIEF::assembly::mips::REG::FCC3)
  .value("FCC4", LIEF::assembly::mips::REG::FCC4)
  .value("FCC5", LIEF::assembly::mips::REG::FCC5)
  .value("FCC6", LIEF::assembly::mips::REG::FCC6)
  .value("FCC7", LIEF::assembly::mips::REG::FCC7)
  .value("FCR0", LIEF::assembly::mips::REG::FCR0)
  .value("FCR1", LIEF::assembly::mips::REG::FCR1)
  .value("FCR2", LIEF::assembly::mips::REG::FCR2)
  .value("FCR3", LIEF::assembly::mips::REG::FCR3)
  .value("FCR4", LIEF::assembly::mips::REG::FCR4)
  .value("FCR5", LIEF::assembly::mips::REG::FCR5)
  .value("FCR6", LIEF::assembly::mips::REG::FCR6)
  .value("FCR7", LIEF::assembly::mips::REG::FCR7)
  .value("FCR8", LIEF::assembly::mips::REG::FCR8)
  .value("FCR9", LIEF::assembly::mips::REG::FCR9)
  .value("FCR10", LIEF::assembly::mips::REG::FCR10)
  .value("FCR11", LIEF::assembly::mips::REG::FCR11)
  .value("FCR12", LIEF::assembly::mips::REG::FCR12)
  .value("FCR13", LIEF::assembly::mips::REG::FCR13)
  .value("FCR14", LIEF::assembly::mips::REG::FCR14)
  .value("FCR15", LIEF::assembly::mips::REG::FCR15)
  .value("FCR16", LIEF::assembly::mips::REG::FCR16)
  .value("FCR17", LIEF::assembly::mips::REG::FCR17)
  .value("FCR18", LIEF::assembly::mips::REG::FCR18)
  .value("FCR19", LIEF::assembly::mips::REG::FCR19)
  .value("FCR20", LIEF::assembly::mips::REG::FCR20)
  .value("FCR21", LIEF::assembly::mips::REG::FCR21)
  .value("FCR22", LIEF::assembly::mips::REG::FCR22)
  .value("FCR23", LIEF::assembly::mips::REG::FCR23)
  .value("FCR24", LIEF::assembly::mips::REG::FCR24)
  .value("FCR25", LIEF::assembly::mips::REG::FCR25)
  .value("FCR26", LIEF::assembly::mips::REG::FCR26)
  .value("FCR27", LIEF::assembly::mips::REG::FCR27)
  .value("FCR28", LIEF::assembly::mips::REG::FCR28)
  .value("FCR29", LIEF::assembly::mips::REG::FCR29)
  .value("FCR30", LIEF::assembly::mips::REG::FCR30)
  .value("FCR31", LIEF::assembly::mips::REG::FCR31)
  .value("FP_64", LIEF::assembly::mips::REG::FP_64)
  .value("F_HI0", LIEF::assembly::mips::REG::F_HI0)
  .value("F_HI1", LIEF::assembly::mips::REG::F_HI1)
  .value("F_HI2", LIEF::assembly::mips::REG::F_HI2)
  .value("F_HI3", LIEF::assembly::mips::REG::F_HI3)
  .value("F_HI4", LIEF::assembly::mips::REG::F_HI4)
  .value("F_HI5", LIEF::assembly::mips::REG::F_HI5)
  .value("F_HI6", LIEF::assembly::mips::REG::F_HI6)
  .value("F_HI7", LIEF::assembly::mips::REG::F_HI7)
  .value("F_HI8", LIEF::assembly::mips::REG::F_HI8)
  .value("F_HI9", LIEF::assembly::mips::REG::F_HI9)
  .value("F_HI10", LIEF::assembly::mips::REG::F_HI10)
  .value("F_HI11", LIEF::assembly::mips::REG::F_HI11)
  .value("F_HI12", LIEF::assembly::mips::REG::F_HI12)
  .value("F_HI13", LIEF::assembly::mips::REG::F_HI13)
  .value("F_HI14", LIEF::assembly::mips::REG::F_HI14)
  .value("F_HI15", LIEF::assembly::mips::REG::F_HI15)
  .value("F_HI16", LIEF::assembly::mips::REG::F_HI16)
  .value("F_HI17", LIEF::assembly::mips::REG::F_HI17)
  .value("F_HI18", LIEF::assembly::mips::REG::F_HI18)
  .value("F_HI19", LIEF::assembly::mips::REG::F_HI19)
  .value("F_HI20", LIEF::assembly::mips::REG::F_HI20)
  .value("F_HI21", LIEF::assembly::mips::REG::F_HI21)
  .value("F_HI22", LIEF::assembly::mips::REG::F_HI22)
  .value("F_HI23", LIEF::assembly::mips::REG::F_HI23)
  .value("F_HI24", LIEF::assembly::mips::REG::F_HI24)
  .value("F_HI25", LIEF::assembly::mips::REG::F_HI25)
  .value("F_HI26", LIEF::assembly::mips::REG::F_HI26)
  .value("F_HI27", LIEF::assembly::mips::REG::F_HI27)
  .value("F_HI28", LIEF::assembly::mips::REG::F_HI28)
  .value("F_HI29", LIEF::assembly::mips::REG::F_HI29)
  .value("F_HI30", LIEF::assembly::mips::REG::F_HI30)
  .value("F_HI31", LIEF::assembly::mips::REG::F_HI31)
  .value("GP_64", LIEF::assembly::mips::REG::GP_64)
  .value("HI0", LIEF::assembly::mips::REG::HI0)
  .value("HI1", LIEF::assembly::mips::REG::HI1)
  .value("HI2", LIEF::assembly::mips::REG::HI2)
  .value("HI3", LIEF::assembly::mips::REG::HI3)
  .value("HWR0", LIEF::assembly::mips::REG::HWR0)
  .value("HWR1", LIEF::assembly::mips::REG::HWR1)
  .value("HWR2", LIEF::assembly::mips::REG::HWR2)
  .value("HWR3", LIEF::assembly::mips::REG::HWR3)
  .value("HWR4", LIEF::assembly::mips::REG::HWR4)
  .value("HWR5", LIEF::assembly::mips::REG::HWR5)
  .value("HWR6", LIEF::assembly::mips::REG::HWR6)
  .value("HWR7", LIEF::assembly::mips::REG::HWR7)
  .value("HWR8", LIEF::assembly::mips::REG::HWR8)
  .value("HWR9", LIEF::assembly::mips::REG::HWR9)
  .value("HWR10", LIEF::assembly::mips::REG::HWR10)
  .value("HWR11", LIEF::assembly::mips::REG::HWR11)
  .value("HWR12", LIEF::assembly::mips::REG::HWR12)
  .value("HWR13", LIEF::assembly::mips::REG::HWR13)
  .value("HWR14", LIEF::assembly::mips::REG::HWR14)
  .value("HWR15", LIEF::assembly::mips::REG::HWR15)
  .value("HWR16", LIEF::assembly::mips::REG::HWR16)
  .value("HWR17", LIEF::assembly::mips::REG::HWR17)
  .value("HWR18", LIEF::assembly::mips::REG::HWR18)
  .value("HWR19", LIEF::assembly::mips::REG::HWR19)
  .value("HWR20", LIEF::assembly::mips::REG::HWR20)
  .value("HWR21", LIEF::assembly::mips::REG::HWR21)
  .value("HWR22", LIEF::assembly::mips::REG::HWR22)
  .value("HWR23", LIEF::assembly::mips::REG::HWR23)
  .value("HWR24", LIEF::assembly::mips::REG::HWR24)
  .value("HWR25", LIEF::assembly::mips::REG::HWR25)
  .value("HWR26", LIEF::assembly::mips::REG::HWR26)
  .value("HWR27", LIEF::assembly::mips::REG::HWR27)
  .value("HWR28", LIEF::assembly::mips::REG::HWR28)
  .value("HWR29", LIEF::assembly::mips::REG::HWR29)
  .value("HWR30", LIEF::assembly::mips::REG::HWR30)
  .value("HWR31", LIEF::assembly::mips::REG::HWR31)
  .value("K0", LIEF::assembly::mips::REG::K0)
  .value("K1", LIEF::assembly::mips::REG::K1)
  .value("LO0", LIEF::assembly::mips::REG::LO0)
  .value("LO1", LIEF::assembly::mips::REG::LO1)
  .value("LO2", LIEF::assembly::mips::REG::LO2)
  .value("LO3", LIEF::assembly::mips::REG::LO3)
  .value("MPL0", LIEF::assembly::mips::REG::MPL0)
  .value("MPL1", LIEF::assembly::mips::REG::MPL1)
  .value("MPL2", LIEF::assembly::mips::REG::MPL2)
  .value("MSA8", LIEF::assembly::mips::REG::MSA8)
  .value("MSA9", LIEF::assembly::mips::REG::MSA9);
  reg.value("MSA10", LIEF::assembly::mips::REG::MSA10)
  .value("MSA11", LIEF::assembly::mips::REG::MSA11)
  .value("MSA12", LIEF::assembly::mips::REG::MSA12)
  .value("MSA13", LIEF::assembly::mips::REG::MSA13)
  .value("MSA14", LIEF::assembly::mips::REG::MSA14)
  .value("MSA15", LIEF::assembly::mips::REG::MSA15)
  .value("MSA16", LIEF::assembly::mips::REG::MSA16)
  .value("MSA17", LIEF::assembly::mips::REG::MSA17)
  .value("MSA18", LIEF::assembly::mips::REG::MSA18)
  .value("MSA19", LIEF::assembly::mips::REG::MSA19)
  .value("MSA20", LIEF::assembly::mips::REG::MSA20)
  .value("MSA21", LIEF::assembly::mips::REG::MSA21)
  .value("MSA22", LIEF::assembly::mips::REG::MSA22)
  .value("MSA23", LIEF::assembly::mips::REG::MSA23)
  .value("MSA24", LIEF::assembly::mips::REG::MSA24)
  .value("MSA25", LIEF::assembly::mips::REG::MSA25)
  .value("MSA26", LIEF::assembly::mips::REG::MSA26)
  .value("MSA27", LIEF::assembly::mips::REG::MSA27)
  .value("MSA28", LIEF::assembly::mips::REG::MSA28)
  .value("MSA29", LIEF::assembly::mips::REG::MSA29)
  .value("MSA30", LIEF::assembly::mips::REG::MSA30)
  .value("MSA31", LIEF::assembly::mips::REG::MSA31)
  .value("P0", LIEF::assembly::mips::REG::P0)
  .value("P1", LIEF::assembly::mips::REG::P1)
  .value("P2", LIEF::assembly::mips::REG::P2)
  .value("RA_64", LIEF::assembly::mips::REG::RA_64)
  .value("S0", LIEF::assembly::mips::REG::S0)
  .value("S1", LIEF::assembly::mips::REG::S1)
  .value("S2", LIEF::assembly::mips::REG::S2)
  .value("S3", LIEF::assembly::mips::REG::S3)
  .value("S4", LIEF::assembly::mips::REG::S4)
  .value("S5", LIEF::assembly::mips::REG::S5)
  .value("S6", LIEF::assembly::mips::REG::S6)
  .value("S7", LIEF::assembly::mips::REG::S7)
  .value("SP_64", LIEF::assembly::mips::REG::SP_64)
  .value("T0", LIEF::assembly::mips::REG::T0)
  .value("T1", LIEF::assembly::mips::REG::T1)
  .value("T2", LIEF::assembly::mips::REG::T2)
  .value("T3", LIEF::assembly::mips::REG::T3)
  .value("T4", LIEF::assembly::mips::REG::T4)
  .value("T5", LIEF::assembly::mips::REG::T5)
  .value("T6", LIEF::assembly::mips::REG::T6)
  .value("T7", LIEF::assembly::mips::REG::T7)
  .value("T8", LIEF::assembly::mips::REG::T8)
  .value("T9", LIEF::assembly::mips::REG::T9)
  .value("V0", LIEF::assembly::mips::REG::V0)
  .value("V1", LIEF::assembly::mips::REG::V1)
  .value("W0", LIEF::assembly::mips::REG::W0)
  .value("W1", LIEF::assembly::mips::REG::W1)
  .value("W2", LIEF::assembly::mips::REG::W2)
  .value("W3", LIEF::assembly::mips::REG::W3)
  .value("W4", LIEF::assembly::mips::REG::W4)
  .value("W5", LIEF::assembly::mips::REG::W5)
  .value("W6", LIEF::assembly::mips::REG::W6)
  .value("W7", LIEF::assembly::mips::REG::W7)
  .value("W8", LIEF::assembly::mips::REG::W8)
  .value("W9", LIEF::assembly::mips::REG::W9)
  .value("W10", LIEF::assembly::mips::REG::W10)
  .value("W11", LIEF::assembly::mips::REG::W11)
  .value("W12", LIEF::assembly::mips::REG::W12)
  .value("W13", LIEF::assembly::mips::REG::W13)
  .value("W14", LIEF::assembly::mips::REG::W14)
  .value("W15", LIEF::assembly::mips::REG::W15)
  .value("W16", LIEF::assembly::mips::REG::W16)
  .value("W17", LIEF::assembly::mips::REG::W17)
  .value("W18", LIEF::assembly::mips::REG::W18)
  .value("W19", LIEF::assembly::mips::REG::W19)
  .value("W20", LIEF::assembly::mips::REG::W20)
  .value("W21", LIEF::assembly::mips::REG::W21)
  .value("W22", LIEF::assembly::mips::REG::W22)
  .value("W23", LIEF::assembly::mips::REG::W23)
  .value("W24", LIEF::assembly::mips::REG::W24)
  .value("W25", LIEF::assembly::mips::REG::W25)
  .value("W26", LIEF::assembly::mips::REG::W26)
  .value("W27", LIEF::assembly::mips::REG::W27)
  .value("W28", LIEF::assembly::mips::REG::W28)
  .value("W29", LIEF::assembly::mips::REG::W29)
  .value("W30", LIEF::assembly::mips::REG::W30)
  .value("W31", LIEF::assembly::mips::REG::W31)
  .value("ZERO_64", LIEF::assembly::mips::REG::ZERO_64)
  .value("A0_64", LIEF::assembly::mips::REG::A0_64)
  .value("A1_64", LIEF::assembly::mips::REG::A1_64)
  .value("A2_64", LIEF::assembly::mips::REG::A2_64)
  .value("A3_64", LIEF::assembly::mips::REG::A3_64)
  .value("AC0_64", LIEF::assembly::mips::REG::AC0_64)
  .value("D0_64", LIEF::assembly::mips::REG::D0_64)
  .value("D1_64", LIEF::assembly::mips::REG::D1_64)
  .value("D2_64", LIEF::assembly::mips::REG::D2_64)
  .value("D3_64", LIEF::assembly::mips::REG::D3_64)
  .value("D4_64", LIEF::assembly::mips::REG::D4_64)
  .value("D5_64", LIEF::assembly::mips::REG::D5_64)
  .value("D6_64", LIEF::assembly::mips::REG::D6_64)
  .value("D7_64", LIEF::assembly::mips::REG::D7_64)
  .value("D8_64", LIEF::assembly::mips::REG::D8_64)
  .value("D9_64", LIEF::assembly::mips::REG::D9_64)
  .value("D10_64", LIEF::assembly::mips::REG::D10_64)
  .value("D11_64", LIEF::assembly::mips::REG::D11_64)
  .value("D12_64", LIEF::assembly::mips::REG::D12_64)
  .value("D13_64", LIEF::assembly::mips::REG::D13_64)
  .value("D14_64", LIEF::assembly::mips::REG::D14_64)
  .value("D15_64", LIEF::assembly::mips::REG::D15_64)
  .value("D16_64", LIEF::assembly::mips::REG::D16_64)
  .value("D17_64", LIEF::assembly::mips::REG::D17_64)
  .value("D18_64", LIEF::assembly::mips::REG::D18_64)
  .value("D19_64", LIEF::assembly::mips::REG::D19_64)
  .value("D20_64", LIEF::assembly::mips::REG::D20_64)
  .value("D21_64", LIEF::assembly::mips::REG::D21_64)
  .value("D22_64", LIEF::assembly::mips::REG::D22_64)
  .value("D23_64", LIEF::assembly::mips::REG::D23_64)
  .value("D24_64", LIEF::assembly::mips::REG::D24_64)
  .value("D25_64", LIEF::assembly::mips::REG::D25_64)
  .value("D26_64", LIEF::assembly::mips::REG::D26_64)
  .value("D27_64", LIEF::assembly::mips::REG::D27_64)
  .value("D28_64", LIEF::assembly::mips::REG::D28_64)
  .value("D29_64", LIEF::assembly::mips::REG::D29_64)
  .value("D30_64", LIEF::assembly::mips::REG::D30_64)
  .value("D31_64", LIEF::assembly::mips::REG::D31_64)
  .value("DSPOutFlag16_19", LIEF::assembly::mips::REG::DSPOutFlag16_19)
  .value("HI0_64", LIEF::assembly::mips::REG::HI0_64)
  .value("K0_64", LIEF::assembly::mips::REG::K0_64)
  .value("K1_64", LIEF::assembly::mips::REG::K1_64)
  .value("LO0_64", LIEF::assembly::mips::REG::LO0_64)
  .value("S0_64", LIEF::assembly::mips::REG::S0_64)
  .value("S1_64", LIEF::assembly::mips::REG::S1_64)
  .value("S2_64", LIEF::assembly::mips::REG::S2_64)
  .value("S3_64", LIEF::assembly::mips::REG::S3_64)
  .value("S4_64", LIEF::assembly::mips::REG::S4_64)
  .value("S5_64", LIEF::assembly::mips::REG::S5_64)
  .value("S6_64", LIEF::assembly::mips::REG::S6_64)
  .value("S7_64", LIEF::assembly::mips::REG::S7_64)
  .value("T0_64", LIEF::assembly::mips::REG::T0_64)
  .value("T1_64", LIEF::assembly::mips::REG::T1_64)
  .value("T2_64", LIEF::assembly::mips::REG::T2_64)
  .value("T3_64", LIEF::assembly::mips::REG::T3_64)
  .value("T4_64", LIEF::assembly::mips::REG::T4_64)
  .value("T5_64", LIEF::assembly::mips::REG::T5_64)
  .value("T6_64", LIEF::assembly::mips::REG::T6_64)
  .value("T7_64", LIEF::assembly::mips::REG::T7_64)
  .value("T8_64", LIEF::assembly::mips::REG::T8_64)
  .value("T9_64", LIEF::assembly::mips::REG::T9_64)
  .value("V0_64", LIEF::assembly::mips::REG::V0_64)
  .value("V1_64", LIEF::assembly::mips::REG::V1_64)
  .value("NUM_TARGET_REGS", LIEF::assembly::mips::REG::NUM_TARGET_REGS)
  ;
}
}
