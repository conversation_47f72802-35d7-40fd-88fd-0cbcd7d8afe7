#include "asm/x86/init.hpp"

#include "LIEF/asm/x86/registers.hpp"

namespace LIEF::assembly::x86::py {
template<>
void create<LIEF::assembly::x86::REG>(nb::module_& m) {
  nb::enum_<LIEF::assembly::x86::REG> reg(m, "REG");
  reg.value("NoRegister", LIEF::assembly::x86::REG::NoRegister)
  .value("AH", LIEF::assembly::x86::REG::AH)
  .value("AL", LIEF::assembly::x86::REG::AL)
  .value("AX", LIEF::assembly::x86::REG::AX)
  .value("BH", LIEF::assembly::x86::REG::BH)
  .value("BL", LIEF::assembly::x86::REG::BL)
  .value("BP", LIEF::assembly::x86::REG::BP)
  .value("BPH", LIEF::assembly::x86::REG::BPH)
  .value("BPL", LIEF::assembly::x86::REG::BPL)
  .value("BX", LIEF::assembly::x86::REG::BX)
  .value("CH", LIEF::assembly::x86::REG::CH)
  .value("CL", LIEF::assembly::x86::REG::CL)
  .value("CS", LIEF::assembly::x86::REG::CS)
  .value("CX", LIEF::assembly::x86::REG::CX)
  .value("DF", LIEF::assembly::x86::REG::DF)
  .value("DH", LIEF::assembly::x86::REG::DH)
  .value("DI", LIEF::assembly::x86::REG::DI)
  .value("DIH", LIEF::assembly::x86::REG::DIH)
  .value("DIL", LIEF::assembly::x86::REG::DIL)
  .value("DL", LIEF::assembly::x86::REG::DL)
  .value("DS", LIEF::assembly::x86::REG::DS)
  .value("DX", LIEF::assembly::x86::REG::DX)
  .value("EAX", LIEF::assembly::x86::REG::EAX)
  .value("EBP", LIEF::assembly::x86::REG::EBP)
  .value("EBX", LIEF::assembly::x86::REG::EBX)
  .value("ECX", LIEF::assembly::x86::REG::ECX)
  .value("EDI", LIEF::assembly::x86::REG::EDI)
  .value("EDX", LIEF::assembly::x86::REG::EDX)
  .value("EFLAGS", LIEF::assembly::x86::REG::EFLAGS)
  .value("EIP", LIEF::assembly::x86::REG::EIP)
  .value("EIZ", LIEF::assembly::x86::REG::EIZ)
  .value("ES", LIEF::assembly::x86::REG::ES)
  .value("ESI", LIEF::assembly::x86::REG::ESI)
  .value("ESP", LIEF::assembly::x86::REG::ESP)
  .value("FPCW", LIEF::assembly::x86::REG::FPCW)
  .value("FPSW", LIEF::assembly::x86::REG::FPSW)
  .value("FS", LIEF::assembly::x86::REG::FS)
  .value("FS_BASE", LIEF::assembly::x86::REG::FS_BASE)
  .value("GS", LIEF::assembly::x86::REG::GS)
  .value("GS_BASE", LIEF::assembly::x86::REG::GS_BASE)
  .value("HAX", LIEF::assembly::x86::REG::HAX)
  .value("HBP", LIEF::assembly::x86::REG::HBP)
  .value("HBX", LIEF::assembly::x86::REG::HBX)
  .value("HCX", LIEF::assembly::x86::REG::HCX)
  .value("HDI", LIEF::assembly::x86::REG::HDI)
  .value("HDX", LIEF::assembly::x86::REG::HDX)
  .value("HIP", LIEF::assembly::x86::REG::HIP)
  .value("HSI", LIEF::assembly::x86::REG::HSI)
  .value("HSP", LIEF::assembly::x86::REG::HSP)
  .value("IP", LIEF::assembly::x86::REG::IP)
  .value("MXCSR", LIEF::assembly::x86::REG::MXCSR)
  .value("RAX", LIEF::assembly::x86::REG::RAX)
  .value("RBP", LIEF::assembly::x86::REG::RBP)
  .value("RBX", LIEF::assembly::x86::REG::RBX)
  .value("RCX", LIEF::assembly::x86::REG::RCX)
  .value("RDI", LIEF::assembly::x86::REG::RDI)
  .value("RDX", LIEF::assembly::x86::REG::RDX)
  .value("RFLAGS", LIEF::assembly::x86::REG::RFLAGS)
  .value("RIP", LIEF::assembly::x86::REG::RIP)
  .value("RIZ", LIEF::assembly::x86::REG::RIZ)
  .value("RSI", LIEF::assembly::x86::REG::RSI)
  .value("RSP", LIEF::assembly::x86::REG::RSP)
  .value("SI", LIEF::assembly::x86::REG::SI)
  .value("SIH", LIEF::assembly::x86::REG::SIH)
  .value("SIL", LIEF::assembly::x86::REG::SIL)
  .value("SP", LIEF::assembly::x86::REG::SP)
  .value("SPH", LIEF::assembly::x86::REG::SPH)
  .value("SPL", LIEF::assembly::x86::REG::SPL)
  .value("SS", LIEF::assembly::x86::REG::SS)
  .value("SSP", LIEF::assembly::x86::REG::SSP)
  .value("_EFLAGS", LIEF::assembly::x86::REG::_EFLAGS)
  .value("CR0", LIEF::assembly::x86::REG::CR0)
  .value("CR1", LIEF::assembly::x86::REG::CR1)
  .value("CR2", LIEF::assembly::x86::REG::CR2)
  .value("CR3", LIEF::assembly::x86::REG::CR3)
  .value("CR4", LIEF::assembly::x86::REG::CR4)
  .value("CR5", LIEF::assembly::x86::REG::CR5)
  .value("CR6", LIEF::assembly::x86::REG::CR6)
  .value("CR7", LIEF::assembly::x86::REG::CR7)
  .value("CR8", LIEF::assembly::x86::REG::CR8)
  .value("CR9", LIEF::assembly::x86::REG::CR9)
  .value("CR10", LIEF::assembly::x86::REG::CR10)
  .value("CR11", LIEF::assembly::x86::REG::CR11)
  .value("CR12", LIEF::assembly::x86::REG::CR12)
  .value("CR13", LIEF::assembly::x86::REG::CR13)
  .value("CR14", LIEF::assembly::x86::REG::CR14)
  .value("CR15", LIEF::assembly::x86::REG::CR15)
  .value("DR0", LIEF::assembly::x86::REG::DR0)
  .value("DR1", LIEF::assembly::x86::REG::DR1)
  .value("DR2", LIEF::assembly::x86::REG::DR2)
  .value("DR3", LIEF::assembly::x86::REG::DR3)
  .value("DR4", LIEF::assembly::x86::REG::DR4)
  .value("DR5", LIEF::assembly::x86::REG::DR5)
  .value("DR6", LIEF::assembly::x86::REG::DR6)
  .value("DR7", LIEF::assembly::x86::REG::DR7)
  .value("DR8", LIEF::assembly::x86::REG::DR8)
  .value("DR9", LIEF::assembly::x86::REG::DR9)
  .value("DR10", LIEF::assembly::x86::REG::DR10)
  .value("DR11", LIEF::assembly::x86::REG::DR11)
  .value("DR12", LIEF::assembly::x86::REG::DR12)
  .value("DR13", LIEF::assembly::x86::REG::DR13)
  .value("DR14", LIEF::assembly::x86::REG::DR14)
  .value("DR15", LIEF::assembly::x86::REG::DR15)
  .value("FP0", LIEF::assembly::x86::REG::FP0)
  .value("FP1", LIEF::assembly::x86::REG::FP1)
  .value("FP2", LIEF::assembly::x86::REG::FP2)
  .value("FP3", LIEF::assembly::x86::REG::FP3)
  .value("FP4", LIEF::assembly::x86::REG::FP4)
  .value("FP5", LIEF::assembly::x86::REG::FP5)
  .value("FP6", LIEF::assembly::x86::REG::FP6)
  .value("FP7", LIEF::assembly::x86::REG::FP7)
  .value("MM0", LIEF::assembly::x86::REG::MM0)
  .value("MM1", LIEF::assembly::x86::REG::MM1)
  .value("MM2", LIEF::assembly::x86::REG::MM2)
  .value("MM3", LIEF::assembly::x86::REG::MM3)
  .value("MM4", LIEF::assembly::x86::REG::MM4)
  .value("MM5", LIEF::assembly::x86::REG::MM5)
  .value("MM6", LIEF::assembly::x86::REG::MM6)
  .value("MM7", LIEF::assembly::x86::REG::MM7)
  .value("R8", LIEF::assembly::x86::REG::R8)
  .value("R9", LIEF::assembly::x86::REG::R9)
  .value("R10", LIEF::assembly::x86::REG::R10)
  .value("R11", LIEF::assembly::x86::REG::R11)
  .value("R12", LIEF::assembly::x86::REG::R12)
  .value("R13", LIEF::assembly::x86::REG::R13)
  .value("R14", LIEF::assembly::x86::REG::R14)
  .value("R15", LIEF::assembly::x86::REG::R15)
  .value("ST0", LIEF::assembly::x86::REG::ST0)
  .value("ST1", LIEF::assembly::x86::REG::ST1)
  .value("ST2", LIEF::assembly::x86::REG::ST2)
  .value("ST3", LIEF::assembly::x86::REG::ST3)
  .value("ST4", LIEF::assembly::x86::REG::ST4)
  .value("ST5", LIEF::assembly::x86::REG::ST5)
  .value("ST6", LIEF::assembly::x86::REG::ST6)
  .value("ST7", LIEF::assembly::x86::REG::ST7)
  .value("XMM0", LIEF::assembly::x86::REG::XMM0)
  .value("XMM1", LIEF::assembly::x86::REG::XMM1)
  .value("XMM2", LIEF::assembly::x86::REG::XMM2)
  .value("XMM3", LIEF::assembly::x86::REG::XMM3)
  .value("XMM4", LIEF::assembly::x86::REG::XMM4)
  .value("XMM5", LIEF::assembly::x86::REG::XMM5)
  .value("XMM6", LIEF::assembly::x86::REG::XMM6)
  .value("XMM7", LIEF::assembly::x86::REG::XMM7)
  .value("XMM8", LIEF::assembly::x86::REG::XMM8)
  .value("XMM9", LIEF::assembly::x86::REG::XMM9)
  .value("XMM10", LIEF::assembly::x86::REG::XMM10)
  .value("XMM11", LIEF::assembly::x86::REG::XMM11)
  .value("XMM12", LIEF::assembly::x86::REG::XMM12)
  .value("XMM13", LIEF::assembly::x86::REG::XMM13)
  .value("XMM14", LIEF::assembly::x86::REG::XMM14)
  .value("XMM15", LIEF::assembly::x86::REG::XMM15)
  .value("R8B", LIEF::assembly::x86::REG::R8B)
  .value("R9B", LIEF::assembly::x86::REG::R9B)
  .value("R10B", LIEF::assembly::x86::REG::R10B)
  .value("R11B", LIEF::assembly::x86::REG::R11B)
  .value("R12B", LIEF::assembly::x86::REG::R12B)
  .value("R13B", LIEF::assembly::x86::REG::R13B)
  .value("R14B", LIEF::assembly::x86::REG::R14B)
  .value("R15B", LIEF::assembly::x86::REG::R15B)
  .value("R8BH", LIEF::assembly::x86::REG::R8BH)
  .value("R9BH", LIEF::assembly::x86::REG::R9BH)
  .value("R10BH", LIEF::assembly::x86::REG::R10BH)
  .value("R11BH", LIEF::assembly::x86::REG::R11BH)
  .value("R12BH", LIEF::assembly::x86::REG::R12BH)
  .value("R13BH", LIEF::assembly::x86::REG::R13BH)
  .value("R14BH", LIEF::assembly::x86::REG::R14BH)
  .value("R15BH", LIEF::assembly::x86::REG::R15BH)
  .value("R8D", LIEF::assembly::x86::REG::R8D)
  .value("R9D", LIEF::assembly::x86::REG::R9D)
  .value("R10D", LIEF::assembly::x86::REG::R10D)
  .value("R11D", LIEF::assembly::x86::REG::R11D)
  .value("R12D", LIEF::assembly::x86::REG::R12D)
  .value("R13D", LIEF::assembly::x86::REG::R13D)
  .value("R14D", LIEF::assembly::x86::REG::R14D)
  .value("R15D", LIEF::assembly::x86::REG::R15D)
  .value("R8W", LIEF::assembly::x86::REG::R8W)
  .value("R9W", LIEF::assembly::x86::REG::R9W)
  .value("R10W", LIEF::assembly::x86::REG::R10W)
  .value("R11W", LIEF::assembly::x86::REG::R11W)
  .value("R12W", LIEF::assembly::x86::REG::R12W)
  .value("R13W", LIEF::assembly::x86::REG::R13W)
  .value("R14W", LIEF::assembly::x86::REG::R14W)
  .value("R15W", LIEF::assembly::x86::REG::R15W)
  .value("R8WH", LIEF::assembly::x86::REG::R8WH)
  .value("R9WH", LIEF::assembly::x86::REG::R9WH)
  .value("R10WH", LIEF::assembly::x86::REG::R10WH)
  .value("R11WH", LIEF::assembly::x86::REG::R11WH)
  .value("R12WH", LIEF::assembly::x86::REG::R12WH)
  .value("R13WH", LIEF::assembly::x86::REG::R13WH)
  .value("R14WH", LIEF::assembly::x86::REG::R14WH)
  .value("R15WH", LIEF::assembly::x86::REG::R15WH)
  .value("YMM0", LIEF::assembly::x86::REG::YMM0)
  .value("YMM1", LIEF::assembly::x86::REG::YMM1)
  .value("YMM2", LIEF::assembly::x86::REG::YMM2)
  .value("YMM3", LIEF::assembly::x86::REG::YMM3)
  .value("YMM4", LIEF::assembly::x86::REG::YMM4)
  .value("YMM5", LIEF::assembly::x86::REG::YMM5)
  .value("YMM6", LIEF::assembly::x86::REG::YMM6)
  .value("YMM7", LIEF::assembly::x86::REG::YMM7)
  .value("YMM8", LIEF::assembly::x86::REG::YMM8)
  .value("YMM9", LIEF::assembly::x86::REG::YMM9)
  .value("YMM10", LIEF::assembly::x86::REG::YMM10)
  .value("YMM11", LIEF::assembly::x86::REG::YMM11)
  .value("YMM12", LIEF::assembly::x86::REG::YMM12)
  .value("YMM13", LIEF::assembly::x86::REG::YMM13)
  .value("YMM14", LIEF::assembly::x86::REG::YMM14)
  .value("YMM15", LIEF::assembly::x86::REG::YMM15)
  .value("K0", LIEF::assembly::x86::REG::K0)
  .value("K1", LIEF::assembly::x86::REG::K1)
  .value("K2", LIEF::assembly::x86::REG::K2)
  .value("K3", LIEF::assembly::x86::REG::K3)
  .value("K4", LIEF::assembly::x86::REG::K4)
  .value("K5", LIEF::assembly::x86::REG::K5)
  .value("K6", LIEF::assembly::x86::REG::K6)
  .value("K7", LIEF::assembly::x86::REG::K7)
  .value("XMM16", LIEF::assembly::x86::REG::XMM16)
  .value("XMM17", LIEF::assembly::x86::REG::XMM17)
  .value("XMM18", LIEF::assembly::x86::REG::XMM18)
  .value("XMM19", LIEF::assembly::x86::REG::XMM19)
  .value("XMM20", LIEF::assembly::x86::REG::XMM20)
  .value("XMM21", LIEF::assembly::x86::REG::XMM21)
  .value("XMM22", LIEF::assembly::x86::REG::XMM22)
  .value("XMM23", LIEF::assembly::x86::REG::XMM23)
  .value("XMM24", LIEF::assembly::x86::REG::XMM24)
  .value("XMM25", LIEF::assembly::x86::REG::XMM25)
  .value("XMM26", LIEF::assembly::x86::REG::XMM26)
  .value("XMM27", LIEF::assembly::x86::REG::XMM27)
  .value("XMM28", LIEF::assembly::x86::REG::XMM28)
  .value("XMM29", LIEF::assembly::x86::REG::XMM29)
  .value("XMM30", LIEF::assembly::x86::REG::XMM30)
  .value("XMM31", LIEF::assembly::x86::REG::XMM31)
  .value("YMM16", LIEF::assembly::x86::REG::YMM16)
  .value("YMM17", LIEF::assembly::x86::REG::YMM17)
  .value("YMM18", LIEF::assembly::x86::REG::YMM18)
  .value("YMM19", LIEF::assembly::x86::REG::YMM19)
  .value("YMM20", LIEF::assembly::x86::REG::YMM20)
  .value("YMM21", LIEF::assembly::x86::REG::YMM21)
  .value("YMM22", LIEF::assembly::x86::REG::YMM22)
  .value("YMM23", LIEF::assembly::x86::REG::YMM23)
  .value("YMM24", LIEF::assembly::x86::REG::YMM24)
  .value("YMM25", LIEF::assembly::x86::REG::YMM25)
  .value("YMM26", LIEF::assembly::x86::REG::YMM26)
  .value("YMM27", LIEF::assembly::x86::REG::YMM27)
  .value("YMM28", LIEF::assembly::x86::REG::YMM28)
  .value("YMM29", LIEF::assembly::x86::REG::YMM29)
  .value("YMM30", LIEF::assembly::x86::REG::YMM30)
  .value("YMM31", LIEF::assembly::x86::REG::YMM31)
  .value("ZMM0", LIEF::assembly::x86::REG::ZMM0)
  .value("ZMM1", LIEF::assembly::x86::REG::ZMM1)
  .value("ZMM2", LIEF::assembly::x86::REG::ZMM2)
  .value("ZMM3", LIEF::assembly::x86::REG::ZMM3)
  .value("ZMM4", LIEF::assembly::x86::REG::ZMM4)
  .value("ZMM5", LIEF::assembly::x86::REG::ZMM5)
  .value("ZMM6", LIEF::assembly::x86::REG::ZMM6)
  .value("ZMM7", LIEF::assembly::x86::REG::ZMM7)
  .value("ZMM8", LIEF::assembly::x86::REG::ZMM8)
  .value("ZMM9", LIEF::assembly::x86::REG::ZMM9)
  .value("ZMM10", LIEF::assembly::x86::REG::ZMM10)
  .value("ZMM11", LIEF::assembly::x86::REG::ZMM11)
  .value("ZMM12", LIEF::assembly::x86::REG::ZMM12)
  .value("ZMM13", LIEF::assembly::x86::REG::ZMM13)
  .value("ZMM14", LIEF::assembly::x86::REG::ZMM14)
  .value("ZMM15", LIEF::assembly::x86::REG::ZMM15)
  .value("ZMM16", LIEF::assembly::x86::REG::ZMM16)
  .value("ZMM17", LIEF::assembly::x86::REG::ZMM17)
  .value("ZMM18", LIEF::assembly::x86::REG::ZMM18)
  .value("ZMM19", LIEF::assembly::x86::REG::ZMM19)
  .value("ZMM20", LIEF::assembly::x86::REG::ZMM20)
  .value("ZMM21", LIEF::assembly::x86::REG::ZMM21)
  .value("ZMM22", LIEF::assembly::x86::REG::ZMM22)
  .value("ZMM23", LIEF::assembly::x86::REG::ZMM23)
  .value("ZMM24", LIEF::assembly::x86::REG::ZMM24)
  .value("ZMM25", LIEF::assembly::x86::REG::ZMM25)
  .value("ZMM26", LIEF::assembly::x86::REG::ZMM26)
  .value("ZMM27", LIEF::assembly::x86::REG::ZMM27)
  .value("ZMM28", LIEF::assembly::x86::REG::ZMM28)
  .value("ZMM29", LIEF::assembly::x86::REG::ZMM29)
  .value("ZMM30", LIEF::assembly::x86::REG::ZMM30)
  .value("ZMM31", LIEF::assembly::x86::REG::ZMM31)
  .value("K0_K1", LIEF::assembly::x86::REG::K0_K1)
  .value("K2_K3", LIEF::assembly::x86::REG::K2_K3)
  .value("K4_K5", LIEF::assembly::x86::REG::K4_K5)
  .value("K6_K7", LIEF::assembly::x86::REG::K6_K7)
  .value("TMMCFG", LIEF::assembly::x86::REG::TMMCFG)
  .value("TMM0", LIEF::assembly::x86::REG::TMM0)
  .value("TMM1", LIEF::assembly::x86::REG::TMM1)
  .value("TMM2", LIEF::assembly::x86::REG::TMM2)
  .value("TMM3", LIEF::assembly::x86::REG::TMM3)
  .value("TMM4", LIEF::assembly::x86::REG::TMM4)
  .value("TMM5", LIEF::assembly::x86::REG::TMM5)
  .value("TMM6", LIEF::assembly::x86::REG::TMM6)
  .value("TMM7", LIEF::assembly::x86::REG::TMM7)
  .value("TMM0_TMM1", LIEF::assembly::x86::REG::TMM0_TMM1)
  .value("TMM2_TMM3", LIEF::assembly::x86::REG::TMM2_TMM3)
  .value("TMM4_TMM5", LIEF::assembly::x86::REG::TMM4_TMM5)
  .value("TMM6_TMM7", LIEF::assembly::x86::REG::TMM6_TMM7)
  .value("R16", LIEF::assembly::x86::REG::R16)
  .value("R17", LIEF::assembly::x86::REG::R17)
  .value("R18", LIEF::assembly::x86::REG::R18)
  .value("R19", LIEF::assembly::x86::REG::R19);
  reg.value("R20", LIEF::assembly::x86::REG::R20)
  .value("R21", LIEF::assembly::x86::REG::R21)
  .value("R22", LIEF::assembly::x86::REG::R22)
  .value("R23", LIEF::assembly::x86::REG::R23)
  .value("R24", LIEF::assembly::x86::REG::R24)
  .value("R25", LIEF::assembly::x86::REG::R25)
  .value("R26", LIEF::assembly::x86::REG::R26)
  .value("R27", LIEF::assembly::x86::REG::R27)
  .value("R28", LIEF::assembly::x86::REG::R28)
  .value("R29", LIEF::assembly::x86::REG::R29)
  .value("R30", LIEF::assembly::x86::REG::R30)
  .value("R31", LIEF::assembly::x86::REG::R31)
  .value("R16B", LIEF::assembly::x86::REG::R16B)
  .value("R17B", LIEF::assembly::x86::REG::R17B)
  .value("R18B", LIEF::assembly::x86::REG::R18B)
  .value("R19B", LIEF::assembly::x86::REG::R19B)
  .value("R20B", LIEF::assembly::x86::REG::R20B)
  .value("R21B", LIEF::assembly::x86::REG::R21B)
  .value("R22B", LIEF::assembly::x86::REG::R22B)
  .value("R23B", LIEF::assembly::x86::REG::R23B)
  .value("R24B", LIEF::assembly::x86::REG::R24B)
  .value("R25B", LIEF::assembly::x86::REG::R25B)
  .value("R26B", LIEF::assembly::x86::REG::R26B)
  .value("R27B", LIEF::assembly::x86::REG::R27B)
  .value("R28B", LIEF::assembly::x86::REG::R28B)
  .value("R29B", LIEF::assembly::x86::REG::R29B)
  .value("R30B", LIEF::assembly::x86::REG::R30B)
  .value("R31B", LIEF::assembly::x86::REG::R31B)
  .value("R16BH", LIEF::assembly::x86::REG::R16BH)
  .value("R17BH", LIEF::assembly::x86::REG::R17BH)
  .value("R18BH", LIEF::assembly::x86::REG::R18BH)
  .value("R19BH", LIEF::assembly::x86::REG::R19BH)
  .value("R20BH", LIEF::assembly::x86::REG::R20BH)
  .value("R21BH", LIEF::assembly::x86::REG::R21BH)
  .value("R22BH", LIEF::assembly::x86::REG::R22BH)
  .value("R23BH", LIEF::assembly::x86::REG::R23BH)
  .value("R24BH", LIEF::assembly::x86::REG::R24BH)
  .value("R25BH", LIEF::assembly::x86::REG::R25BH)
  .value("R26BH", LIEF::assembly::x86::REG::R26BH)
  .value("R27BH", LIEF::assembly::x86::REG::R27BH)
  .value("R28BH", LIEF::assembly::x86::REG::R28BH)
  .value("R29BH", LIEF::assembly::x86::REG::R29BH)
  .value("R30BH", LIEF::assembly::x86::REG::R30BH)
  .value("R31BH", LIEF::assembly::x86::REG::R31BH)
  .value("R16D", LIEF::assembly::x86::REG::R16D)
  .value("R17D", LIEF::assembly::x86::REG::R17D)
  .value("R18D", LIEF::assembly::x86::REG::R18D)
  .value("R19D", LIEF::assembly::x86::REG::R19D)
  .value("R20D", LIEF::assembly::x86::REG::R20D)
  .value("R21D", LIEF::assembly::x86::REG::R21D)
  .value("R22D", LIEF::assembly::x86::REG::R22D)
  .value("R23D", LIEF::assembly::x86::REG::R23D)
  .value("R24D", LIEF::assembly::x86::REG::R24D)
  .value("R25D", LIEF::assembly::x86::REG::R25D)
  .value("R26D", LIEF::assembly::x86::REG::R26D)
  .value("R27D", LIEF::assembly::x86::REG::R27D)
  .value("R28D", LIEF::assembly::x86::REG::R28D)
  .value("R29D", LIEF::assembly::x86::REG::R29D)
  .value("R30D", LIEF::assembly::x86::REG::R30D)
  .value("R31D", LIEF::assembly::x86::REG::R31D)
  .value("R16W", LIEF::assembly::x86::REG::R16W)
  .value("R17W", LIEF::assembly::x86::REG::R17W)
  .value("R18W", LIEF::assembly::x86::REG::R18W)
  .value("R19W", LIEF::assembly::x86::REG::R19W)
  .value("R20W", LIEF::assembly::x86::REG::R20W)
  .value("R21W", LIEF::assembly::x86::REG::R21W)
  .value("R22W", LIEF::assembly::x86::REG::R22W)
  .value("R23W", LIEF::assembly::x86::REG::R23W)
  .value("R24W", LIEF::assembly::x86::REG::R24W)
  .value("R25W", LIEF::assembly::x86::REG::R25W)
  .value("R26W", LIEF::assembly::x86::REG::R26W)
  .value("R27W", LIEF::assembly::x86::REG::R27W)
  .value("R28W", LIEF::assembly::x86::REG::R28W)
  .value("R29W", LIEF::assembly::x86::REG::R29W)
  .value("R30W", LIEF::assembly::x86::REG::R30W)
  .value("R31W", LIEF::assembly::x86::REG::R31W)
  .value("R16WH", LIEF::assembly::x86::REG::R16WH)
  .value("R17WH", LIEF::assembly::x86::REG::R17WH)
  .value("R18WH", LIEF::assembly::x86::REG::R18WH)
  .value("R19WH", LIEF::assembly::x86::REG::R19WH)
  .value("R20WH", LIEF::assembly::x86::REG::R20WH)
  .value("R21WH", LIEF::assembly::x86::REG::R21WH)
  .value("R22WH", LIEF::assembly::x86::REG::R22WH)
  .value("R23WH", LIEF::assembly::x86::REG::R23WH)
  .value("R24WH", LIEF::assembly::x86::REG::R24WH)
  .value("R25WH", LIEF::assembly::x86::REG::R25WH)
  .value("R26WH", LIEF::assembly::x86::REG::R26WH)
  .value("R27WH", LIEF::assembly::x86::REG::R27WH)
  .value("R28WH", LIEF::assembly::x86::REG::R28WH)
  .value("R29WH", LIEF::assembly::x86::REG::R29WH)
  .value("R30WH", LIEF::assembly::x86::REG::R30WH)
  .value("R31WH", LIEF::assembly::x86::REG::R31WH)
  .value("NUM_TARGET_REGS", LIEF::assembly::x86::REG::NUM_TARGET_REGS)
  ;
}
}
