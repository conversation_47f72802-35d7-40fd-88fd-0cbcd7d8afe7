from typing import Iterator, Optional, Union


class DeclOpt:
    def __init__(self) -> None: ...

    show_annotations: bool

class Metadata:
    def to_decl(self, opt: DeclOpt = ...) -> str: ...

    @property
    def classes(self) -> Iterator[Optional[Class]]: ...

    @property
    def protocols(self) -> Iterator[Optional[Protocol]]: ...

    def get_class(self, name: str) -> Optional[Class]: ...

    def get_protocol(self, name: str) -> Optional[Protocol]: ...

class Class:
    @property
    def name(self) -> str: ...

    @property
    def demangled_name(self) -> str: ...

    @property
    def super_class(self) -> Optional[Class]: ...

    @property
    def is_meta(self) -> bool: ...

    @property
    def methods(self) -> Iterator[Optional[Method]]: ...

    @property
    def protocols(self) -> Iterator[Optional[Protocol]]: ...

    @property
    def properties(self) -> Iterator[Optional[Property]]: ...

    @property
    def ivars(self) -> Iterator[Optional[IVar]]: ...

    def to_decl(self, opt: DeclOpt = ...) -> str: ...

class IVar:
    @property
    def name(self) -> str: ...

    @property
    def mangled_type(self) -> str: ...

class Protocol:
    @property
    def mangled_name(self) -> str: ...

    @property
    def optional_methods(self) -> Iterator[Optional[Method]]: ...

    @property
    def required_methods(self) -> Iterator[Optional[Method]]: ...

    @property
    def properties(self) -> Iterator[Optional[Property]]: ...

    def to_decl(self, opt: DeclOpt = ...) -> str: ...

class Method:
    @property
    def name(self) -> str: ...

    @property
    def mangled_type(self) -> str: ...

    @property
    def address(self) -> int: ...

    @property
    def is_instance(self) -> bool: ...

class Property:
    @property
    def name(self) -> str: ...

    @property
    def attribute(self) -> str: ...
