/* Copyright 2017 - 2025 <PERSON><PERSON>
 * Copyright 2017 - 2025 Quarkslab
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "LIEF/PE/resources/AcceleratorCodes.hpp"
#include "PE/pyPE.hpp"

#include "enums_wrapper.hpp"

namespace LIEF::PE::py {
template<>
void create<ACCELERATOR_CODES>(nb::module_& m) {
  #define ENTRY(X) .value(to_string(ACCELERATOR_CODES::X), ACCELERATOR_CODES::X)
  enum_<ACCELERATOR_CODES>(m, "ACCELERATOR_CODES")
    ENTRY(LBUTTON)
    ENTRY(RBUTTON)
    ENTRY(CANCEL)
    ENTRY(MBUTTON)
    ENTRY(XBUTTON1_K)
    ENTRY(XBUTTON2_K)
    ENTRY(BACK)
    ENTRY(TAB)
    ENTRY(CLEAR)
    ENTRY(RETURN)
    ENTRY(SHIFT)
    ENTRY(CONTROL)
    ENTRY(MENU)
    ENTRY(PAUSE)
    ENTRY(CAPITAL)
    ENTRY(KANA)
    ENTRY(IME_ON)
    ENTRY(JUNJA)
    ENTRY(FINAL)
    ENTRY(KANJI)
    ENTRY(IME_OFF)
    ENTRY(ESCAPE)
    ENTRY(CONVERT)
    ENTRY(NONCONVERT)
    ENTRY(ACCEPT)
    ENTRY(MODECHANGE)
    ENTRY(SPACE)
    ENTRY(PRIOR)
    ENTRY(NEXT)
    ENTRY(END)
    ENTRY(HOME)
    ENTRY(LEFT)
    ENTRY(UP)
    ENTRY(RIGHT)
    ENTRY(DOWN)
    ENTRY(SELECT)
    ENTRY(PRINT)
    ENTRY(EXECUTE)
    ENTRY(SNAPSHOT)
    ENTRY(INSERT)
    ENTRY(DELETE_K)
    ENTRY(HELP)
    ENTRY(NUM_0)
    ENTRY(NUM_1)
    ENTRY(NUM_2)
    ENTRY(NUM_3)
    ENTRY(NUM_4)
    ENTRY(NUM_5)
    ENTRY(NUM_6)
    ENTRY(NUM_7)
    ENTRY(NUM_8)
    ENTRY(NUM_9)
    ENTRY(A)
    ENTRY(B)
    ENTRY(C)
    ENTRY(D)
    ENTRY(E)
    ENTRY(F)
    ENTRY(G)
    ENTRY(H)
    ENTRY(I)
    ENTRY(J)
    ENTRY(K)
    ENTRY(L)
    ENTRY(M)
    ENTRY(N)
    ENTRY(O)
    ENTRY(P)
    ENTRY(Q)
    ENTRY(R)
    ENTRY(S)
    ENTRY(T)
    ENTRY(U)
    ENTRY(V)
    ENTRY(W)
    ENTRY(X)
    ENTRY(Y)
    ENTRY(Z)
    ENTRY(LWIN)
    ENTRY(RWIN)
    ENTRY(APPS)
    ENTRY(SLEEP)
    ENTRY(NUMPAD0)
    ENTRY(NUMPAD1)
    ENTRY(NUMPAD2)
    ENTRY(NUMPAD3)
    ENTRY(NUMPAD4)
    ENTRY(NUMPAD5)
    ENTRY(NUMPAD6)
    ENTRY(NUMPAD7)
    ENTRY(NUMPAD8)
    ENTRY(NUMPAD9)
    ENTRY(MULTIPLY)
    ENTRY(ADD)
    ENTRY(SEPARATOR)
    ENTRY(SUBTRACT)
    ENTRY(DECIMAL)
    ENTRY(DIVIDE)
    ENTRY(F1)
    ENTRY(F2)
    ENTRY(F3)
    ENTRY(F4)
    ENTRY(F5)
    ENTRY(F6)
    ENTRY(F7)
    ENTRY(F8)
    ENTRY(F9)
    ENTRY(F10)
    ENTRY(F11)
    ENTRY(F12)
    ENTRY(F13)
    ENTRY(F14)
    ENTRY(F15)
    ENTRY(F16)
    ENTRY(F17)
    ENTRY(F18)
    ENTRY(F19)
    ENTRY(F20)
    ENTRY(F21)
    ENTRY(F22)
    ENTRY(F23)
    ENTRY(F24)
    ENTRY(NUMLOCK)
    ENTRY(SCROLL)
    ENTRY(LSHIFT)
    ENTRY(RSHIFT)
    ENTRY(LCONTROL)
    ENTRY(RCONTROL)
    ENTRY(LMENU)
    ENTRY(RMENU)
    ENTRY(BROWSER_BACK)
    ENTRY(BROWSER_FORWARD)
    ENTRY(BROWSER_REFRESH)
    ENTRY(BROWSER_STOP)
    ENTRY(BROWSER_SEARCH)
    ENTRY(BROWSER_FAVORITES)
    ENTRY(BROWSER_HOME)
    ENTRY(VOLUME_MUTE)
    ENTRY(VOLUME_DOWN)
    ENTRY(VOLUME_UP)
    ENTRY(MEDIA_NEXT_TRACK)
    ENTRY(MEDIA_PREV_TRACK)
    ENTRY(MEDIA_STOP)
    ENTRY(MEDIA_PLAY_PAUSE)
    ENTRY(LAUNCH_MAIL)
    ENTRY(LAUNCH_MEDIA_SELECT)
    ENTRY(LAUNCH_APP1)
    ENTRY(LAUNCH_APP2)
    ENTRY(OEM_1)
    ENTRY(OEM_PLUS)
    ENTRY(OEM_COMMA)
    ENTRY(OEM_MINUS)
    ENTRY(OEM_PERIOD)
    ENTRY(OEM_2)
    ENTRY(OEM_4)
    ENTRY(OEM_5)
    ENTRY(OEM_6)
    ENTRY(OEM_7)
    ENTRY(OEM_8)
    ENTRY(OEM_102)
    ENTRY(PROCESSKEY)
    ENTRY(PACKET)
    ENTRY(ATTN)
    ENTRY(CRSEL)
    ENTRY(EXSEL)
    ENTRY(EREOF)
    ENTRY(PLAY)
    ENTRY(ZOOM)
    ENTRY(NONAME)
    ENTRY(PA1)
    ENTRY(OEM_CLEAR)
  ;
  #undef ENTRY
}
}

