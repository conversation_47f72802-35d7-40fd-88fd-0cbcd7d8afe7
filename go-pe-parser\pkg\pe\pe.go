// Package pe provides comprehensive parsing, analysis, and modification capabilities
// for Portable Executable (PE) files. This is a Go port of the LIEF PE module.
//
// The package follows Go idioms while maintaining compatibility with the original
// LIEF PE functionality. It supports parsing PE32 and PE32+ files, extracting
// headers, sections, imports, exports, resources, debug information, and more.
//
// Basic usage:
//
//	binary, err := pe.Parse("example.exe")
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	fmt.Printf("Entry point: 0x%x\n", binary.OptionalHeader().AddressOfEntryPoint)
//	for _, section := range binary.Sections() {
//		fmt.Printf("Section: %s, Size: %d\n", section.Name(), section.VirtualSize())
//	}
package pe

import (
	"errors"
	"io"
	"os"
)

// Common errors
var (
	ErrInvalidPE     = errors.New("invalid PE file")
	ErrUnsupported   = errors.New("unsupported PE feature")
	ErrCorrupted     = errors.New("corrupted PE file")
	ErrNotFound      = errors.New("element not found")
	ErrInvalidOffset = errors.New("invalid offset")
)

// Parse parses a PE file from the given file path.
func Parse(filename string) (*Binary, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return ParseReader(file)
}

// ParseBytes parses a PE file from a byte slice.
func ParseBytes(data []byte) (*Binary, error) {
	parser := NewParser(data)
	return parser.Parse()
}

// ParseReader parses a PE file from an io.Reader.
func ParseReader(r io.Reader) (*Binary, error) {
	data, err := io.ReadAll(r)
	if err != nil {
		return nil, err
	}
	return ParseBytes(data)
}

// IsPE checks if the given data represents a valid PE file.
func IsPE(data []byte) bool {
	if len(data) < 64 {
		return false
	}

	// Check DOS signature
	if data[0] != 'M' || data[1] != 'Z' {
		return false
	}

	// Get PE header offset
	peOffset := uint32(data[60]) | uint32(data[61])<<8 | uint32(data[62])<<16 | uint32(data[63])<<24
	if peOffset >= uint32(len(data)-4) {
		return false
	}

	// Check PE signature
	return data[peOffset] == 'P' && data[peOffset+1] == 'E' &&
		   data[peOffset+2] == 0 && data[peOffset+3] == 0
}

// GetType determines the PE type (PE32 or PE32+) from the given data.
func GetType(data []byte) (PEType, error) {
	if !IsPE(data) {
		return 0, ErrInvalidPE
	}

	// Get PE header offset
	peOffset := uint32(data[60]) | uint32(data[61])<<8 | uint32(data[62])<<16 | uint32(data[63])<<24

	// Skip PE signature (4 bytes) and COFF header (20 bytes) to get to optional header magic
	magicOffset := peOffset + 4 + 20
	if magicOffset+2 > uint32(len(data)) {
		return 0, ErrCorrupted
	}

	magic := uint16(data[magicOffset]) | uint16(data[magicOffset+1])<<8
	switch magic {
	case 0x10b:
		return PE32, nil
	case 0x20b:
		return PE32Plus, nil
	default:
		return 0, ErrInvalidPE
	}
}