name: 'Linux x86-64'
on: push

jobs:
  linux-sdk:
    runs-on: ubuntu-latest
    container:
      image: liefproject/manylinux_2_28_x86_64
      env:
        CCACHE_DIR: "/home/<USER>/.ccache"
        CXXFLAGS: "-ffunction-sections -fdata-sections -fvisibility-inlines-hidden -static-libstdc++ -static-libgcc"
        CFLAGS: "-ffunction-sections -fdata-sections -static-libstdc++ -static-libgcc"
        LDFLAGS: "-Wl,--gc-sections -Wl,--exclude-libs,ALL"
        BUILD_DIR_STATIC: "/tmp/build/linux-x86-64/static-release"
        BUILD_DIR_SHARED: "/tmp/build/linux-x86-64/shared-release"
        CLICOLOR_FORCE: "1"
        ACTIONS_RUNTIME_URL: "${{ env.ACTIONS_RUNTIME_URL }}"
        ACTIONS_RUNTIME_TOKEN: "${{ env.ACTIONS_RUNTIME_TOKEN }}"
        ACTIONS_CACHE_URL: "${{ env.ACTIONS_CACHE_URL }}"
      options: --user 1001:1001
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: Prepare ccache
        shell: bash
        run: |
          mkdir -p /home/<USER>/.ccache/tmp
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: /home/<USER>/.ccache
          key: linux-x86-64-${{ runner.os }}-sdk-${{ steps.get-date.outputs.date }}
          restore-keys: |
              linux-x86-64-${{ runner.os }}-sdk
      - name: CMake - Static
        shell: bash
        run: |
          mkdir -p ${BUILD_DIR_STATIC}
          cmake -S $GITHUB_WORKSPACE -B ${BUILD_DIR_STATIC} -GNinja \
                -DCMAKE_INSTALL_LIBDIR="lib"                 \
                -DCMAKE_LINK_WHAT_YOU_USE=on                  \
                -DBUILD_SHARED_LIBS=off                       \
                -DLIEF_INSTALL_COMPILED_EXAMPLES=on           \
                -DCMAKE_INSTALL_PREFIX=/install               \
                -DCMAKE_BUILD_TYPE=Release
      - name: Compile - Static
        shell: bash
        run: |
          cmake --build ${BUILD_DIR_STATIC} --target install
      - name: CMake - Shared
        shell: bash
        run: |
          mkdir -p ${BUILD_DIR_SHARED}
          cmake -S $GITHUB_WORKSPACE -B ${BUILD_DIR_SHARED} -GNinja \
                -DCMAKE_INSTALL_LIBDIR="lib"                 \
                -DCMAKE_LINK_WHAT_YOU_USE=on                 \
                -DBUILD_SHARED_LIBS=on                       \
                -DLIEF_INSTALL_COMPILED_EXAMPLES=off         \
                -DCMAKE_BUILD_TYPE=Release
      - name: Compile - Shared
        run: |
          cmake --build ${BUILD_DIR_SHARED} --target all
      - name: Package
        run: |
          cd /tmp/build/linux-x86-64
          cpack --config $GITHUB_WORKSPACE/cmake/cpack.config.cmake
      - name: 'Upload SDK Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-x86-64-sdk
          path: /tmp/build/linux-x86-64/*.tar.gz
          retention-days: 3
      - name: 'Upload Install Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-x86-64-install
          path: /install
          retention-days: 1
  testing:
    if: "!contains(github.event.commits[0].message, '[skip-test]')"
    runs-on: ubuntu-latest
    container:
      image: liefproject/manylinux_2_28_x86_64
      env:
        PYTHON_BINARY: "/opt/python/cp312-cp312/bin/python3.12"
        LIEF_SAMPLES_DIR: "/tmp/samples"
        LIEF_BUILD_DIR: "/tmp/lief-build"
        LIEF_VENV: "/tmp/venv"
        CLICOLOR_FORCE: "1"
        PYTEST_ADDOPTS: "--color=yes"
      options: --user 1001:1001
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: linux-x86-64-${{ runner.os }}-test-${{ steps.get-date.outputs.date }}
          restore-keys: |
              linux-x86-64-${{ runner.os }}-test
      - name: Prepare Python virtual env
        shell: bash
        run: |
          $PYTHON_BINARY -m venv ${LIEF_VENV}
          source ${LIEF_VENV}/bin/activate
          python -m pip install             \
                 -r tests/requirements.txt  \
                 -r api/python/build-requirements.txt
      - name: Build & install LIEF
        shell: bash
        run: |
          source ${LIEF_VENV}/bin/activate
          export PYLIEF_CONF="${GITHUB_WORKSPACE}/scripts/docker/config/pylinux-test-x64.toml"
          python -m pip -vvv install api/python
      - name: Prepare test samples
        shell: bash
        run: |
          source ${LIEF_VENV}/bin/activate
          mkdir -p ${LIEF_SAMPLES_DIR}
          python tests/dl_samples.py ${LIEF_SAMPLES_DIR}
      - name: Run tests
        timeout-minutes: 5
        shell: bash
        run: |
          source ${LIEF_VENV}/bin/activate
          python tests/run_pytest.py
          python tests/run_tools_check.py ${LIEF_BUILD_DIR}/
          ctest --output-on-failure --test-dir ${LIEF_BUILD_DIR}/
      - name: Fuzzing [ls x86-64]
        shell: bash
        timeout-minutes: 20
        run: |
          source ${LIEF_VENV}/bin/activate
          export PYTHONPATH="${GITHUB_WORKSPACE}/tests:$PYTHONPATH"
          python tests/elf/fuzzing.py                             \
                 ${LIEF_BUILD_DIR}/tests/Melkor/src/MELKOR/melkor \
                 --input-seed ${LIEF_SAMPLES_DIR}/ELF/ELF64_x86-64_binary_ls.bin -n 100
      - name: Fuzzing [openssl x86-64]
        shell: bash
        timeout-minutes: 20
        run: |
          source ${LIEF_VENV}/bin/activate
          export PYTHONPATH="${GITHUB_WORKSPACE}/tests:$PYTHONPATH"
          python tests/elf/fuzzing.py                             \
                 ${LIEF_BUILD_DIR}/tests/Melkor/src/MELKOR/melkor \
                 --input-seed ${LIEF_SAMPLES_DIR}/ELF/ELF64_x86-64_binary_openssl.bin -n 100
      - name: Fuzzing [nm x86-64]
        shell: bash
        timeout-minutes: 20
        run: |
          source ${LIEF_VENV}/bin/activate
          export PYTHONPATH="${GITHUB_WORKSPACE}/tests:$PYTHONPATH"
          python tests/elf/fuzzing.py                             \
                 ${LIEF_BUILD_DIR}/tests/Melkor/src/MELKOR/melkor \
                 --input-seed ${LIEF_SAMPLES_DIR}/ELF/ELF64_x86-64_binary_nm.bin -n 100

  pywheels:
    runs-on: ubuntu-latest
    needs: linux-sdk
    container:
      image: liefproject/manylinux_2_28_x86_64
      env:
        CCACHE_DIR: "/home/<USER>/.ccache"
        CXXFLAGS: "-ffunction-sections -fdata-sections -fvisibility-inlines-hidden -static-libgcc"
        CFLAGS: "-ffunction-sections -fdata-sections -static-libgcc"
        LDFLAGS: "-Wl,--gc-sections -Wl,--exclude-libs,ALL"
        CLICOLOR_FORCE: "1"
        FORCE_COLOR: "1"
        LIEF_BUILD_DIR: "/tmp/lief-build"
        ACTIONS_RUNTIME_URL: "${{ env.ACTIONS_RUNTIME_URL }}"
        ACTIONS_RUNTIME_TOKEN: "${{ env.ACTIONS_RUNTIME_TOKEN }}"
        ACTIONS_CACHE_URL: "${{ env.ACTIONS_CACHE_URL }}"
      options: --user 1001:1001
    strategy:
      matrix:
        python-version: [3.9, '3.10', '3.11', '3.12', '3.13']
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: Prepare ccache
        shell: bash
        run: |
          mkdir -p /home/<USER>/.ccache/tmp
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: /home/<USER>/.ccache
          key: linux-x86-64-${{ runner.os }}-${{ matrix.python-version }}-${{ steps.get-date.outputs.date }}
          restore-keys: |
              linux-x86-64-${{ runner.os }}-${{ matrix.python-version }}
      - name: Download LIEF install dir
        uses: actions/download-artifact@v4
        with:
          name: linux-x86-64-install
          path: /install
      - name: Generate wheel
        env:
          PYTHON_VERSION: ${{ matrix.python-version }}
        shell: bash
        run: |
          export PYLIEF_CONF=$GITHUB_WORKSPACE/scripts/docker/config/pylinux-x64.toml
          PYTHON_VERSION_ALT=$(echo "${PYTHON_VERSION}" | sed 's/\.//') # Transform 3.8 -> 38
          PYTHON_BINARY=/opt/python/cp${PYTHON_VERSION_ALT}-cp${PYTHON_VERSION_ALT}/bin/python${PYTHON_VERSION}
          $PYTHON_BINARY -m pip -vvv wheel --disable-pip-version-check \
                         --wheel-dir=$GITHUB_WORKSPACE/wheel_stage     \
                         $GITHUB_WORKSPACE/api/python
      - name: Run auditwheel
        shell: bash
        run: |
          ls -alh $GITHUB_WORKSPACE/wheel_stage
          find $GITHUB_WORKSPACE/wheel_stage -exec \
               auditwheel repair -w $GITHUB_WORKSPACE/dist {} \;
          ls -alh $GITHUB_WORKSPACE/dist
      - name: 'Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-x86-64-python${{ matrix.python-version }}-wheel
          path: dist/*.whl
          retention-days: 3
  deploy:
    runs-on: ubuntu-latest
    if: ${{ always() }}
    needs: [pywheels]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: linux-x86-64-*
          merge-multiple: true
          path: ${{ runner.temp }}/artifacts
      - name: Display artifacts
        run: ls -R
        working-directory: ${{ runner.temp }}/artifacts
      - name: Deploy
        env:
          LIEF_S3_KEY: ${{ secrets.LIEF_S3_KEY }}
          LIEF_S3_SECRET: ${{ secrets.LIEF_S3_SECRET }}
        shell: bash
        run: |
          docker run --rm                                     \
            -v $GITHUB_WORKSPACE:/src                         \
            -v $RUNNER_TEMP:/runner_tmp                       \
            -e GITHUB_ACTIONS="true"                          \
            -e GITHUB_WORKSPACE=$GITHUB_WORKSPACE             \
            -e GITHUB_REF=$GITHUB_REF                         \
            -e GITHUB_REPOSITORY=$GITHUB_REPOSITORY           \
            -e LIEF_S3_KEY=$LIEF_S3_KEY                       \
            -e LIEF_S3_SECRET=$LIEF_S3_SECRET                 \
            liefproject/deploy                                \
            python3 /src/.github/deploy.py --skip-sdk /runner_tmp/artifacts
