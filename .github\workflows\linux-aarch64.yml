name: 'Linux AArch64'
on: push

jobs:
  linux-sdk:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: linux-aarch64-${{ runner.os }}-sdk-${{ steps.get-date.outputs.date }}
          restore-keys: |
              linux-aarch64-${{ runner.os }}-sdk
      - name: Build SDK
        shell: bash
        run: |
          mkdir -p $RUNNER_TEMP/lief-install
          mkdir -p $HOME/.ccache

          chmod -R 777 $HOME/.ccache

          docker run --name lief-sdk -u 1001:1001 --rm \
            -e CCACHE_COMPRESS=1                       \
            -e CCACHE_MAXSIZE=10.0G                    \
            -e CCACHE_DIR=/ccache                      \
            -v $GITHUB_WORKSPACE:/src                  \
            -v $RUNNER_TEMP/lief-install:/install      \
            -v $HOME/.ccache:/ccache                   \
            liefproject/manylinux2014-aarch64          \
            bash /src/scripts/docker/linux-sdk-aarch64
      - name: 'Upload SDK Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-aarch64-sdk
          path: build/*.tar.gz
          retention-days: 3
      - name: 'Upload Install Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-aarch64-install
          path: ${{ runner.temp }}/lief-install
          retention-days: 1
  wheels:
    runs-on: ubuntu-latest
    needs: linux-sdk
    strategy:
      matrix:
        python-version: [3.9, '3.10', '3.11', '3.12']
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: linux-aarch64-${{ runner.os }}-${{ matrix.python-version }}-${{ steps.get-date.outputs.date }}
          restore-keys: |
              linux-aarch64-${{ runner.os }}-${{ matrix.python-version }}
      - name: Download LIEF install dir
        uses: actions/download-artifact@v4
        with:
          name: linux-aarch64-install
          path: ${{ runner.temp }}/lief-install
      - name: Python ${{ matrix.python-version }} -- Wheel
        shell: bash
        env:
          PYTHON_VERSION: ${{ matrix.python-version }}
        run: |
          mkdir -p $HOME/.ccache
          chmod -R 777 $HOME/.ccache

          PYTHON_VERSION_ALT=$(echo "${PYTHON_VERSION}" | sed 's/\.//') # Transform 3.8 -> 38
          PYTHON_BINARY="/opt/python/cp${PYTHON_VERSION_ALT}-cp${PYTHON_VERSION_ALT}/bin/python${PYTHON_VERSION}"
          docker run -u 1001:1001 --rm           \
           -e CCACHE_COMPRESS=1                  \
           -e CCACHE_MAXSIZE=10.0G               \
           -e CCACHE_CPP2=1                      \
           -e CCACHE_DIR=/ccache                 \
           -e PYTHON_VERSION=$PYTHON_VERSION_ALT \
           -e PYTHON_BINARY=$PYTHON_BINARY       \
           -v $GITHUB_WORKSPACE:/src             \
           -v $HOME/.ccache:/ccache              \
           -v $RUNNER_TEMP/lief-install:/install \
           liefproject/manylinux2014-aarch64     \
           bash /src/scripts/docker/linux-py-aarch64
      - name: 'Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: linux-aarch64-python${{ matrix.python-version }}-wheel
          path: dist/*.whl
          retention-days: 3
  deploy:
    runs-on: ubuntu-latest
    if: ${{ always() }}
    needs: [linux-sdk, wheels]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: linux-aarch64-*
          merge-multiple: true
          path: ${{ runner.temp }}/artifacts
      - name: Display artifacts
        run: ls -R
        working-directory: ${{ runner.temp }}/artifacts
      - name: Deploy
        env:
          LIEF_S3_KEY: ${{ secrets.LIEF_S3_KEY }}
          LIEF_S3_SECRET: ${{ secrets.LIEF_S3_SECRET }}
        shell: bash
        run: |
          docker run --rm                                     \
            -v $GITHUB_WORKSPACE:/src                         \
            -v $RUNNER_TEMP:/runner_tmp                       \
            -e GITHUB_ACTIONS="true"                          \
            -e GITHUB_WORKSPACE=$GITHUB_WORKSPACE             \
            -e GITHUB_REF=$GITHUB_REF                         \
            -e GITHUB_REPOSITORY=$GITHUB_REPOSITORY           \
            -e LIEF_S3_KEY=$LIEF_S3_KEY                       \
            -e LIEF_S3_SECRET=$LIEF_S3_SECRET                 \
            liefproject/deploy                                \
            python3 /src/.github/deploy.py /runner_tmp/artifacts


