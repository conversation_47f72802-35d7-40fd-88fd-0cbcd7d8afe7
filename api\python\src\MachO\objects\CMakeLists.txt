target_sources(pyLIEF PRIVATE
  pyAtomInfo.cpp
  pyBinary.cpp
  pyBindingInfo.cpp
  pyBuildVersion.cpp
  pyBuilder.cpp
  pyChainedBindingInfo.cpp
  pyChainedPointerAnalysis.cpp
  pyCodeSignature.cpp
  pyCodeSignatureDir.cpp
  pyDataCodeEntry.cpp
  pyDataInCode.cpp
  pyDyldBindingInfo.cpp
  pyDyldChainedFixups.cpp
  pyDyldEnvironment.cpp
  pyDyldExportsTrie.cpp
  pyDyldInfo.cpp
  pyDylibCommand.cpp
  pyDylinker.cpp
  pyDynamicSymbolCommand.cpp
  pyEncryptionInfo.cpp
  pyExportInfo.cpp
  pyFatBinary.cpp
  pyFilesetCommand.cpp
  pyFunctionStarts.cpp
  pyHeader.cpp
  pyIndirectBindingInfo.cpp
  pyLinkerOptHint.cpp
  pyLoadCommand.cpp
  pyMainCommand.cpp
  pyParser.cpp
  pyParserConfig.cpp
  pyRPathCommand.cpp
  pyRelocation.cpp
  pyRelocationDyld.cpp
  pyRelocationFixup.cpp
  pyRelocationObject.cpp
  pyRoutine.cpp
  pySection.cpp
  pySegmentCommand.cpp
  pySegmentSplitInfo.cpp
  pySourceVersion.cpp
  pyStub.cpp
  pySubClient.cpp
  pySubFramework.cpp
  pySymbol.cpp
  pySymbolCommand.cpp
  pyThreadCommand.cpp
  pyTwoLevelHints.cpp
  pyUUID.cpp
  pyUnknownCommand.cpp
  pyVersionMin.cpp
)
