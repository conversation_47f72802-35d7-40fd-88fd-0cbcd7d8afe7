#[allow(non_camel_case_types)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, <PERSON><PERSON>Ord, Or<PERSON>, Hash)]
pub enum Reg {
  NoRegister,
  APSR,
  APSR_NZCV,
  CPSR,
  FPCXTNS,
  FPCXTS,
  FPEXC,
  FPINST,
  FPSCR,
  FPSCR_NZCV,
  FPSCR_NZCVQC,
  FPSID,
  ITSTATE,
  LR,
  PC,
  RA_AUTH_CODE,
  SP,
  SPSR,
  VPR,
  ZR,
  D0,
  D1,
  D2,
  D3,
  D4,
  D5,
  D6,
  D7,
  D8,
  D9,
  D10,
  D11,
  D12,
  D13,
  D14,
  D15,
  D16,
  D17,
  D18,
  D19,
  D20,
  D21,
  D22,
  D23,
  D24,
  D25,
  D26,
  D27,
  D28,
  D29,
  D30,
  D31,
  FPINST2,
  MVFR0,
  MVFR1,
  MVFR2,
  P0,
  Q0,
  Q1,
  Q2,
  Q3,
  Q4,
  Q5,
  Q6,
  Q7,
  Q8,
  Q9,
  Q10,
  Q11,
  Q12,
  Q13,
  Q14,
  Q15,
  R0,
  R1,
  R2,
  R3,
  R4,
  R5,
  R6,
  R7,
  R8,
  R9,
  R10,
  R11,
  R12,
  S0,
  S1,
  S2,
  S3,
  S4,
  S5,
  S6,
  S7,
  S8,
  S9,
  S10,
  S11,
  S12,
  S13,
  S14,
  S15,
  S16,
  S17,
  S18,
  S19,
  S20,
  S21,
  S22,
  S23,
  S24,
  S25,
  S26,
  S27,
  S28,
  S29,
  S30,
  S31,
  D0_D2,
  D1_D3,
  D2_D4,
  D3_D5,
  D4_D6,
  D5_D7,
  D6_D8,
  D7_D9,
  D8_D10,
  D9_D11,
  D10_D12,
  D11_D13,
  D12_D14,
  D13_D15,
  D14_D16,
  D15_D17,
  D16_D18,
  D17_D19,
  D18_D20,
  D19_D21,
  D20_D22,
  D21_D23,
  D22_D24,
  D23_D25,
  D24_D26,
  D25_D27,
  D26_D28,
  D27_D29,
  D28_D30,
  D29_D31,
  Q0_Q1,
  Q1_Q2,
  Q2_Q3,
  Q3_Q4,
  Q4_Q5,
  Q5_Q6,
  Q6_Q7,
  Q7_Q8,
  Q8_Q9,
  Q9_Q10,
  Q10_Q11,
  Q11_Q12,
  Q12_Q13,
  Q13_Q14,
  Q14_Q15,
  Q0_Q1_Q2_Q3,
  Q1_Q2_Q3_Q4,
  Q2_Q3_Q4_Q5,
  Q3_Q4_Q5_Q6,
  Q4_Q5_Q6_Q7,
  Q5_Q6_Q7_Q8,
  Q6_Q7_Q8_Q9,
  Q7_Q8_Q9_Q10,
  Q8_Q9_Q10_Q11,
  Q9_Q10_Q11_Q12,
  Q10_Q11_Q12_Q13,
  Q11_Q12_Q13_Q14,
  Q12_Q13_Q14_Q15,
  R0_R1,
  R2_R3,
  R4_R5,
  R6_R7,
  R8_R9,
  R10_R11,
  R12_SP,
  D0_D1_D2,
  D1_D2_D3,
  D2_D3_D4,
  D3_D4_D5,
  D4_D5_D6,
  D5_D6_D7,
  D6_D7_D8,
  D7_D8_D9,
  D8_D9_D10,
  D9_D10_D11,
  D10_D11_D12,
  D11_D12_D13,
  D12_D13_D14,
  D13_D14_D15,
  D14_D15_D16,
  D15_D16_D17,
  D16_D17_D18,
  D17_D18_D19,
  D18_D19_D20,
  D19_D20_D21,
  D20_D21_D22,
  D21_D22_D23,
  D22_D23_D24,
  D23_D24_D25,
  D24_D25_D26,
  D25_D26_D27,
  D26_D27_D28,
  D27_D28_D29,
  D28_D29_D30,
  D29_D30_D31,
  D0_D2_D4,
  D1_D3_D5,
  D2_D4_D6,
  D3_D5_D7,
  D4_D6_D8,
  D5_D7_D9,
  D6_D8_D10,
  D7_D9_D11,
  D8_D10_D12,
  D9_D11_D13,
  D10_D12_D14,
  D11_D13_D15,
  D12_D14_D16,
  D13_D15_D17,
  D14_D16_D18,
  D15_D17_D19,
  D16_D18_D20,
  D17_D19_D21,
  D18_D20_D22,
  D19_D21_D23,
  D20_D22_D24,
  D21_D23_D25,
  D22_D24_D26,
  D23_D25_D27,
  D24_D26_D28,
  D25_D27_D29,
  D26_D28_D30,
  D27_D29_D31,
  D0_D2_D4_D6,
  D1_D3_D5_D7,
  D2_D4_D6_D8,
  D3_D5_D7_D9,
  D4_D6_D8_D10,
  D5_D7_D9_D11,
  D6_D8_D10_D12,
  D7_D9_D11_D13,
  D8_D10_D12_D14,
  D9_D11_D13_D15,
  D10_D12_D14_D16,
  D11_D13_D15_D17,
  D12_D14_D16_D18,
  D13_D15_D17_D19,
  D14_D16_D18_D20,
  D15_D17_D19_D21,
  D16_D18_D20_D22,
  D17_D19_D21_D23,
  D18_D20_D22_D24,
  D19_D21_D23_D25,
  D20_D22_D24_D26,
  D21_D23_D25_D27,
  D22_D24_D26_D28,
  D23_D25_D27_D29,
  D24_D26_D28_D30,
  D25_D27_D29_D31,
  D1_D2,
  D3_D4,
  D5_D6,
  D7_D8,
  D9_D10,
  D11_D12,
  D13_D14,
  D15_D16,
  D17_D18,
  D19_D20,
  D21_D22,
  D23_D24,
  D25_D26,
  D27_D28,
  D29_D30,
  D1_D2_D3_D4,
  D3_D4_D5_D6,
  D5_D6_D7_D8,
  D7_D8_D9_D10,
  D9_D10_D11_D12,
  D11_D12_D13_D14,
  D13_D14_D15_D16,
  D15_D16_D17_D18,
  D17_D18_D19_D20,
  D19_D20_D21_D22,
  D21_D22_D23_D24,
  D23_D24_D25_D26,
  D25_D26_D27_D28,
  D27_D28_D29_D30,
  NUM_TARGET_REGS,
  UNKNOWN(u64),
}

impl From<u64> for Reg {
    fn from(value: u64) -> Self {
        match value {
          0 => Reg::NoRegister,
          1 => Reg::APSR,
          2 => Reg::APSR_NZCV,
          3 => Reg::CPSR,
          4 => Reg::FPCXTNS,
          5 => Reg::FPCXTS,
          6 => Reg::FPEXC,
          7 => Reg::FPINST,
          8 => Reg::FPSCR,
          9 => Reg::FPSCR_NZCV,
          10 => Reg::FPSCR_NZCVQC,
          11 => Reg::FPSID,
          12 => Reg::ITSTATE,
          13 => Reg::LR,
          14 => Reg::PC,
          15 => Reg::RA_AUTH_CODE,
          16 => Reg::SP,
          17 => Reg::SPSR,
          18 => Reg::VPR,
          19 => Reg::ZR,
          20 => Reg::D0,
          21 => Reg::D1,
          22 => Reg::D2,
          23 => Reg::D3,
          24 => Reg::D4,
          25 => Reg::D5,
          26 => Reg::D6,
          27 => Reg::D7,
          28 => Reg::D8,
          29 => Reg::D9,
          30 => Reg::D10,
          31 => Reg::D11,
          32 => Reg::D12,
          33 => Reg::D13,
          34 => Reg::D14,
          35 => Reg::D15,
          36 => Reg::D16,
          37 => Reg::D17,
          38 => Reg::D18,
          39 => Reg::D19,
          40 => Reg::D20,
          41 => Reg::D21,
          42 => Reg::D22,
          43 => Reg::D23,
          44 => Reg::D24,
          45 => Reg::D25,
          46 => Reg::D26,
          47 => Reg::D27,
          48 => Reg::D28,
          49 => Reg::D29,
          50 => Reg::D30,
          51 => Reg::D31,
          52 => Reg::FPINST2,
          53 => Reg::MVFR0,
          54 => Reg::MVFR1,
          55 => Reg::MVFR2,
          56 => Reg::P0,
          57 => Reg::Q0,
          58 => Reg::Q1,
          59 => Reg::Q2,
          60 => Reg::Q3,
          61 => Reg::Q4,
          62 => Reg::Q5,
          63 => Reg::Q6,
          64 => Reg::Q7,
          65 => Reg::Q8,
          66 => Reg::Q9,
          67 => Reg::Q10,
          68 => Reg::Q11,
          69 => Reg::Q12,
          70 => Reg::Q13,
          71 => Reg::Q14,
          72 => Reg::Q15,
          73 => Reg::R0,
          74 => Reg::R1,
          75 => Reg::R2,
          76 => Reg::R3,
          77 => Reg::R4,
          78 => Reg::R5,
          79 => Reg::R6,
          80 => Reg::R7,
          81 => Reg::R8,
          82 => Reg::R9,
          83 => Reg::R10,
          84 => Reg::R11,
          85 => Reg::R12,
          86 => Reg::S0,
          87 => Reg::S1,
          88 => Reg::S2,
          89 => Reg::S3,
          90 => Reg::S4,
          91 => Reg::S5,
          92 => Reg::S6,
          93 => Reg::S7,
          94 => Reg::S8,
          95 => Reg::S9,
          96 => Reg::S10,
          97 => Reg::S11,
          98 => Reg::S12,
          99 => Reg::S13,
          100 => Reg::S14,
          101 => Reg::S15,
          102 => Reg::S16,
          103 => Reg::S17,
          104 => Reg::S18,
          105 => Reg::S19,
          106 => Reg::S20,
          107 => Reg::S21,
          108 => Reg::S22,
          109 => Reg::S23,
          110 => Reg::S24,
          111 => Reg::S25,
          112 => Reg::S26,
          113 => Reg::S27,
          114 => Reg::S28,
          115 => Reg::S29,
          116 => Reg::S30,
          117 => Reg::S31,
          118 => Reg::D0_D2,
          119 => Reg::D1_D3,
          120 => Reg::D2_D4,
          121 => Reg::D3_D5,
          122 => Reg::D4_D6,
          123 => Reg::D5_D7,
          124 => Reg::D6_D8,
          125 => Reg::D7_D9,
          126 => Reg::D8_D10,
          127 => Reg::D9_D11,
          128 => Reg::D10_D12,
          129 => Reg::D11_D13,
          130 => Reg::D12_D14,
          131 => Reg::D13_D15,
          132 => Reg::D14_D16,
          133 => Reg::D15_D17,
          134 => Reg::D16_D18,
          135 => Reg::D17_D19,
          136 => Reg::D18_D20,
          137 => Reg::D19_D21,
          138 => Reg::D20_D22,
          139 => Reg::D21_D23,
          140 => Reg::D22_D24,
          141 => Reg::D23_D25,
          142 => Reg::D24_D26,
          143 => Reg::D25_D27,
          144 => Reg::D26_D28,
          145 => Reg::D27_D29,
          146 => Reg::D28_D30,
          147 => Reg::D29_D31,
          148 => Reg::Q0_Q1,
          149 => Reg::Q1_Q2,
          150 => Reg::Q2_Q3,
          151 => Reg::Q3_Q4,
          152 => Reg::Q4_Q5,
          153 => Reg::Q5_Q6,
          154 => Reg::Q6_Q7,
          155 => Reg::Q7_Q8,
          156 => Reg::Q8_Q9,
          157 => Reg::Q9_Q10,
          158 => Reg::Q10_Q11,
          159 => Reg::Q11_Q12,
          160 => Reg::Q12_Q13,
          161 => Reg::Q13_Q14,
          162 => Reg::Q14_Q15,
          163 => Reg::Q0_Q1_Q2_Q3,
          164 => Reg::Q1_Q2_Q3_Q4,
          165 => Reg::Q2_Q3_Q4_Q5,
          166 => Reg::Q3_Q4_Q5_Q6,
          167 => Reg::Q4_Q5_Q6_Q7,
          168 => Reg::Q5_Q6_Q7_Q8,
          169 => Reg::Q6_Q7_Q8_Q9,
          170 => Reg::Q7_Q8_Q9_Q10,
          171 => Reg::Q8_Q9_Q10_Q11,
          172 => Reg::Q9_Q10_Q11_Q12,
          173 => Reg::Q10_Q11_Q12_Q13,
          174 => Reg::Q11_Q12_Q13_Q14,
          175 => Reg::Q12_Q13_Q14_Q15,
          176 => Reg::R0_R1,
          177 => Reg::R2_R3,
          178 => Reg::R4_R5,
          179 => Reg::R6_R7,
          180 => Reg::R8_R9,
          181 => Reg::R10_R11,
          182 => Reg::R12_SP,
          183 => Reg::D0_D1_D2,
          184 => Reg::D1_D2_D3,
          185 => Reg::D2_D3_D4,
          186 => Reg::D3_D4_D5,
          187 => Reg::D4_D5_D6,
          188 => Reg::D5_D6_D7,
          189 => Reg::D6_D7_D8,
          190 => Reg::D7_D8_D9,
          191 => Reg::D8_D9_D10,
          192 => Reg::D9_D10_D11,
          193 => Reg::D10_D11_D12,
          194 => Reg::D11_D12_D13,
          195 => Reg::D12_D13_D14,
          196 => Reg::D13_D14_D15,
          197 => Reg::D14_D15_D16,
          198 => Reg::D15_D16_D17,
          199 => Reg::D16_D17_D18,
          200 => Reg::D17_D18_D19,
          201 => Reg::D18_D19_D20,
          202 => Reg::D19_D20_D21,
          203 => Reg::D20_D21_D22,
          204 => Reg::D21_D22_D23,
          205 => Reg::D22_D23_D24,
          206 => Reg::D23_D24_D25,
          207 => Reg::D24_D25_D26,
          208 => Reg::D25_D26_D27,
          209 => Reg::D26_D27_D28,
          210 => Reg::D27_D28_D29,
          211 => Reg::D28_D29_D30,
          212 => Reg::D29_D30_D31,
          213 => Reg::D0_D2_D4,
          214 => Reg::D1_D3_D5,
          215 => Reg::D2_D4_D6,
          216 => Reg::D3_D5_D7,
          217 => Reg::D4_D6_D8,
          218 => Reg::D5_D7_D9,
          219 => Reg::D6_D8_D10,
          220 => Reg::D7_D9_D11,
          221 => Reg::D8_D10_D12,
          222 => Reg::D9_D11_D13,
          223 => Reg::D10_D12_D14,
          224 => Reg::D11_D13_D15,
          225 => Reg::D12_D14_D16,
          226 => Reg::D13_D15_D17,
          227 => Reg::D14_D16_D18,
          228 => Reg::D15_D17_D19,
          229 => Reg::D16_D18_D20,
          230 => Reg::D17_D19_D21,
          231 => Reg::D18_D20_D22,
          232 => Reg::D19_D21_D23,
          233 => Reg::D20_D22_D24,
          234 => Reg::D21_D23_D25,
          235 => Reg::D22_D24_D26,
          236 => Reg::D23_D25_D27,
          237 => Reg::D24_D26_D28,
          238 => Reg::D25_D27_D29,
          239 => Reg::D26_D28_D30,
          240 => Reg::D27_D29_D31,
          241 => Reg::D0_D2_D4_D6,
          242 => Reg::D1_D3_D5_D7,
          243 => Reg::D2_D4_D6_D8,
          244 => Reg::D3_D5_D7_D9,
          245 => Reg::D4_D6_D8_D10,
          246 => Reg::D5_D7_D9_D11,
          247 => Reg::D6_D8_D10_D12,
          248 => Reg::D7_D9_D11_D13,
          249 => Reg::D8_D10_D12_D14,
          250 => Reg::D9_D11_D13_D15,
          251 => Reg::D10_D12_D14_D16,
          252 => Reg::D11_D13_D15_D17,
          253 => Reg::D12_D14_D16_D18,
          254 => Reg::D13_D15_D17_D19,
          255 => Reg::D14_D16_D18_D20,
          256 => Reg::D15_D17_D19_D21,
          257 => Reg::D16_D18_D20_D22,
          258 => Reg::D17_D19_D21_D23,
          259 => Reg::D18_D20_D22_D24,
          260 => Reg::D19_D21_D23_D25,
          261 => Reg::D20_D22_D24_D26,
          262 => Reg::D21_D23_D25_D27,
          263 => Reg::D22_D24_D26_D28,
          264 => Reg::D23_D25_D27_D29,
          265 => Reg::D24_D26_D28_D30,
          266 => Reg::D25_D27_D29_D31,
          267 => Reg::D1_D2,
          268 => Reg::D3_D4,
          269 => Reg::D5_D6,
          270 => Reg::D7_D8,
          271 => Reg::D9_D10,
          272 => Reg::D11_D12,
          273 => Reg::D13_D14,
          274 => Reg::D15_D16,
          275 => Reg::D17_D18,
          276 => Reg::D19_D20,
          277 => Reg::D21_D22,
          278 => Reg::D23_D24,
          279 => Reg::D25_D26,
          280 => Reg::D27_D28,
          281 => Reg::D29_D30,
          282 => Reg::D1_D2_D3_D4,
          283 => Reg::D3_D4_D5_D6,
          284 => Reg::D5_D6_D7_D8,
          285 => Reg::D7_D8_D9_D10,
          286 => Reg::D9_D10_D11_D12,
          287 => Reg::D11_D12_D13_D14,
          288 => Reg::D13_D14_D15_D16,
          289 => Reg::D15_D16_D17_D18,
          290 => Reg::D17_D18_D19_D20,
          291 => Reg::D19_D20_D21_D22,
          292 => Reg::D21_D22_D23_D24,
          293 => Reg::D23_D24_D25_D26,
          294 => Reg::D25_D26_D27_D28,
          295 => Reg::D27_D28_D29_D30,
          296 => Reg::NUM_TARGET_REGS,
          _ => Reg::UNKNOWN(value),
        }
    }
}
