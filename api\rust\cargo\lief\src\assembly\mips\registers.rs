#[allow(non_camel_case_types)]
#[derive(Debug, <PERSON>lone, Copy, <PERSON><PERSON><PERSON>q, Eq, PartialOrd, Ord, Hash)]
pub enum Reg {
  NoRegister,
  AT,
  DS<PERSON><PERSON><PERSON>,
  DSPCarry,
  DSP<PERSON><PERSON>,
  DSPOutFlag,
  DSPPos,
  DSPS<PERSON>ount,
  FP,
  GP,
  MSAAccess,
  MSACSR,
  MSAIR,
  MSAMap,
  MSAModify,
  MSARequest,
  MSASave,
  MSAUnmap,
  PC,
  RA,
  SP,
  ZERO,
  A0,
  A1,
  A2,
  A3,
  AC0,
  AC1,
  AC2,
  AC3,
  AT_64,
  COP00,
  COP01,
  COP02,
  COP03,
  COP04,
  COP05,
  COP06,
  COP07,
  COP08,
  COP09,
  COP20,
  COP21,
  COP22,
  COP23,
  COP24,
  COP25,
  COP26,
  COP27,
  COP28,
  COP29,
  COP30,
  COP31,
  COP32,
  COP33,
  COP34,
  COP35,
  COP36,
  COP37,
  COP38,
  COP39,
  COP010,
  COP011,
  COP012,
  COP013,
  COP014,
  COP015,
  COP016,
  COP017,
  COP018,
  COP019,
  COP020,
  COP021,
  COP022,
  COP023,
  COP024,
  COP025,
  COP026,
  COP027,
  COP028,
  COP029,
  COP030,
  COP031,
  COP210,
  COP211,
  COP212,
  COP213,
  COP214,
  COP215,
  COP216,
  COP217,
  COP218,
  COP219,
  COP220,
  COP221,
  COP222,
  COP223,
  COP224,
  COP225,
  COP226,
  COP227,
  COP228,
  COP229,
  COP230,
  COP231,
  COP310,
  COP311,
  COP312,
  COP313,
  COP314,
  COP315,
  COP316,
  COP317,
  COP318,
  COP319,
  COP320,
  COP321,
  COP322,
  COP323,
  COP324,
  COP325,
  COP326,
  COP327,
  COP328,
  COP329,
  COP330,
  COP331,
  D0,
  D1,
  D2,
  D3,
  D4,
  D5,
  D6,
  D7,
  D8,
  D9,
  D10,
  D11,
  D12,
  D13,
  D14,
  D15,
  DSPOutFlag20,
  DSPOutFlag21,
  DSPOutFlag22,
  DSPOutFlag23,
  F0,
  F1,
  F2,
  F3,
  F4,
  F5,
  F6,
  F7,
  F8,
  F9,
  F10,
  F11,
  F12,
  F13,
  F14,
  F15,
  F16,
  F17,
  F18,
  F19,
  F20,
  F21,
  F22,
  F23,
  F24,
  F25,
  F26,
  F27,
  F28,
  F29,
  F30,
  F31,
  FCC0,
  FCC1,
  FCC2,
  FCC3,
  FCC4,
  FCC5,
  FCC6,
  FCC7,
  FCR0,
  FCR1,
  FCR2,
  FCR3,
  FCR4,
  FCR5,
  FCR6,
  FCR7,
  FCR8,
  FCR9,
  FCR10,
  FCR11,
  FCR12,
  FCR13,
  FCR14,
  FCR15,
  FCR16,
  FCR17,
  FCR18,
  FCR19,
  FCR20,
  FCR21,
  FCR22,
  FCR23,
  FCR24,
  FCR25,
  FCR26,
  FCR27,
  FCR28,
  FCR29,
  FCR30,
  FCR31,
  FP_64,
  F_HI0,
  F_HI1,
  F_HI2,
  F_HI3,
  F_HI4,
  F_HI5,
  F_HI6,
  F_HI7,
  F_HI8,
  F_HI9,
  F_HI10,
  F_HI11,
  F_HI12,
  F_HI13,
  F_HI14,
  F_HI15,
  F_HI16,
  F_HI17,
  F_HI18,
  F_HI19,
  F_HI20,
  F_HI21,
  F_HI22,
  F_HI23,
  F_HI24,
  F_HI25,
  F_HI26,
  F_HI27,
  F_HI28,
  F_HI29,
  F_HI30,
  F_HI31,
  GP_64,
  HI0,
  HI1,
  HI2,
  HI3,
  HWR0,
  HWR1,
  HWR2,
  HWR3,
  HWR4,
  HWR5,
  HWR6,
  HWR7,
  HWR8,
  HWR9,
  HWR10,
  HWR11,
  HWR12,
  HWR13,
  HWR14,
  HWR15,
  HWR16,
  HWR17,
  HWR18,
  HWR19,
  HWR20,
  HWR21,
  HWR22,
  HWR23,
  HWR24,
  HWR25,
  HWR26,
  HWR27,
  HWR28,
  HWR29,
  HWR30,
  HWR31,
  K0,
  K1,
  LO0,
  LO1,
  LO2,
  LO3,
  MPL0,
  MPL1,
  MPL2,
  MSA8,
  MSA9,
  MSA10,
  MSA11,
  MSA12,
  MSA13,
  MSA14,
  MSA15,
  MSA16,
  MSA17,
  MSA18,
  MSA19,
  MSA20,
  MSA21,
  MSA22,
  MSA23,
  MSA24,
  MSA25,
  MSA26,
  MSA27,
  MSA28,
  MSA29,
  MSA30,
  MSA31,
  P0,
  P1,
  P2,
  RA_64,
  S0,
  S1,
  S2,
  S3,
  S4,
  S5,
  S6,
  S7,
  SP_64,
  T0,
  T1,
  T2,
  T3,
  T4,
  T5,
  T6,
  T7,
  T8,
  T9,
  V0,
  V1,
  W0,
  W1,
  W2,
  W3,
  W4,
  W5,
  W6,
  W7,
  W8,
  W9,
  W10,
  W11,
  W12,
  W13,
  W14,
  W15,
  W16,
  W17,
  W18,
  W19,
  W20,
  W21,
  W22,
  W23,
  W24,
  W25,
  W26,
  W27,
  W28,
  W29,
  W30,
  W31,
  ZERO_64,
  A0_64,
  A1_64,
  A2_64,
  A3_64,
  AC0_64,
  D0_64,
  D1_64,
  D2_64,
  D3_64,
  D4_64,
  D5_64,
  D6_64,
  D7_64,
  D8_64,
  D9_64,
  D10_64,
  D11_64,
  D12_64,
  D13_64,
  D14_64,
  D15_64,
  D16_64,
  D17_64,
  D18_64,
  D19_64,
  D20_64,
  D21_64,
  D22_64,
  D23_64,
  D24_64,
  D25_64,
  D26_64,
  D27_64,
  D28_64,
  D29_64,
  D30_64,
  D31_64,
  DSPOutFlag16_19,
  HI0_64,
  K0_64,
  K1_64,
  LO0_64,
  S0_64,
  S1_64,
  S2_64,
  S3_64,
  S4_64,
  S5_64,
  S6_64,
  S7_64,
  T0_64,
  T1_64,
  T2_64,
  T3_64,
  T4_64,
  T5_64,
  T6_64,
  T7_64,
  T8_64,
  T9_64,
  V0_64,
  V1_64,
  NUM_TARGET_REGS,
  UNKNOWN(u64),
}

impl From<u64> for Reg {
    fn from(value: u64) -> Self {
        match value {
          0 => Reg::NoRegister,
          1 => Reg::AT,
          2 => Reg::DSPCCond,
          3 => Reg::DSPCarry,
          4 => Reg::DSPEFI,
          5 => Reg::DSPOutFlag,
          6 => Reg::DSPPos,
          7 => Reg::DSPSCount,
          8 => Reg::FP,
          9 => Reg::GP,
          10 => Reg::MSAAccess,
          11 => Reg::MSACSR,
          12 => Reg::MSAIR,
          13 => Reg::MSAMap,
          14 => Reg::MSAModify,
          15 => Reg::MSARequest,
          16 => Reg::MSASave,
          17 => Reg::MSAUnmap,
          18 => Reg::PC,
          19 => Reg::RA,
          20 => Reg::SP,
          21 => Reg::ZERO,
          22 => Reg::A0,
          23 => Reg::A1,
          24 => Reg::A2,
          25 => Reg::A3,
          26 => Reg::AC0,
          27 => Reg::AC1,
          28 => Reg::AC2,
          29 => Reg::AC3,
          30 => Reg::AT_64,
          31 => Reg::COP00,
          32 => Reg::COP01,
          33 => Reg::COP02,
          34 => Reg::COP03,
          35 => Reg::COP04,
          36 => Reg::COP05,
          37 => Reg::COP06,
          38 => Reg::COP07,
          39 => Reg::COP08,
          40 => Reg::COP09,
          41 => Reg::COP20,
          42 => Reg::COP21,
          43 => Reg::COP22,
          44 => Reg::COP23,
          45 => Reg::COP24,
          46 => Reg::COP25,
          47 => Reg::COP26,
          48 => Reg::COP27,
          49 => Reg::COP28,
          50 => Reg::COP29,
          51 => Reg::COP30,
          52 => Reg::COP31,
          53 => Reg::COP32,
          54 => Reg::COP33,
          55 => Reg::COP34,
          56 => Reg::COP35,
          57 => Reg::COP36,
          58 => Reg::COP37,
          59 => Reg::COP38,
          60 => Reg::COP39,
          61 => Reg::COP010,
          62 => Reg::COP011,
          63 => Reg::COP012,
          64 => Reg::COP013,
          65 => Reg::COP014,
          66 => Reg::COP015,
          67 => Reg::COP016,
          68 => Reg::COP017,
          69 => Reg::COP018,
          70 => Reg::COP019,
          71 => Reg::COP020,
          72 => Reg::COP021,
          73 => Reg::COP022,
          74 => Reg::COP023,
          75 => Reg::COP024,
          76 => Reg::COP025,
          77 => Reg::COP026,
          78 => Reg::COP027,
          79 => Reg::COP028,
          80 => Reg::COP029,
          81 => Reg::COP030,
          82 => Reg::COP031,
          83 => Reg::COP210,
          84 => Reg::COP211,
          85 => Reg::COP212,
          86 => Reg::COP213,
          87 => Reg::COP214,
          88 => Reg::COP215,
          89 => Reg::COP216,
          90 => Reg::COP217,
          91 => Reg::COP218,
          92 => Reg::COP219,
          93 => Reg::COP220,
          94 => Reg::COP221,
          95 => Reg::COP222,
          96 => Reg::COP223,
          97 => Reg::COP224,
          98 => Reg::COP225,
          99 => Reg::COP226,
          100 => Reg::COP227,
          101 => Reg::COP228,
          102 => Reg::COP229,
          103 => Reg::COP230,
          104 => Reg::COP231,
          105 => Reg::COP310,
          106 => Reg::COP311,
          107 => Reg::COP312,
          108 => Reg::COP313,
          109 => Reg::COP314,
          110 => Reg::COP315,
          111 => Reg::COP316,
          112 => Reg::COP317,
          113 => Reg::COP318,
          114 => Reg::COP319,
          115 => Reg::COP320,
          116 => Reg::COP321,
          117 => Reg::COP322,
          118 => Reg::COP323,
          119 => Reg::COP324,
          120 => Reg::COP325,
          121 => Reg::COP326,
          122 => Reg::COP327,
          123 => Reg::COP328,
          124 => Reg::COP329,
          125 => Reg::COP330,
          126 => Reg::COP331,
          127 => Reg::D0,
          128 => Reg::D1,
          129 => Reg::D2,
          130 => Reg::D3,
          131 => Reg::D4,
          132 => Reg::D5,
          133 => Reg::D6,
          134 => Reg::D7,
          135 => Reg::D8,
          136 => Reg::D9,
          137 => Reg::D10,
          138 => Reg::D11,
          139 => Reg::D12,
          140 => Reg::D13,
          141 => Reg::D14,
          142 => Reg::D15,
          143 => Reg::DSPOutFlag20,
          144 => Reg::DSPOutFlag21,
          145 => Reg::DSPOutFlag22,
          146 => Reg::DSPOutFlag23,
          147 => Reg::F0,
          148 => Reg::F1,
          149 => Reg::F2,
          150 => Reg::F3,
          151 => Reg::F4,
          152 => Reg::F5,
          153 => Reg::F6,
          154 => Reg::F7,
          155 => Reg::F8,
          156 => Reg::F9,
          157 => Reg::F10,
          158 => Reg::F11,
          159 => Reg::F12,
          160 => Reg::F13,
          161 => Reg::F14,
          162 => Reg::F15,
          163 => Reg::F16,
          164 => Reg::F17,
          165 => Reg::F18,
          166 => Reg::F19,
          167 => Reg::F20,
          168 => Reg::F21,
          169 => Reg::F22,
          170 => Reg::F23,
          171 => Reg::F24,
          172 => Reg::F25,
          173 => Reg::F26,
          174 => Reg::F27,
          175 => Reg::F28,
          176 => Reg::F29,
          177 => Reg::F30,
          178 => Reg::F31,
          179 => Reg::FCC0,
          180 => Reg::FCC1,
          181 => Reg::FCC2,
          182 => Reg::FCC3,
          183 => Reg::FCC4,
          184 => Reg::FCC5,
          185 => Reg::FCC6,
          186 => Reg::FCC7,
          187 => Reg::FCR0,
          188 => Reg::FCR1,
          189 => Reg::FCR2,
          190 => Reg::FCR3,
          191 => Reg::FCR4,
          192 => Reg::FCR5,
          193 => Reg::FCR6,
          194 => Reg::FCR7,
          195 => Reg::FCR8,
          196 => Reg::FCR9,
          197 => Reg::FCR10,
          198 => Reg::FCR11,
          199 => Reg::FCR12,
          200 => Reg::FCR13,
          201 => Reg::FCR14,
          202 => Reg::FCR15,
          203 => Reg::FCR16,
          204 => Reg::FCR17,
          205 => Reg::FCR18,
          206 => Reg::FCR19,
          207 => Reg::FCR20,
          208 => Reg::FCR21,
          209 => Reg::FCR22,
          210 => Reg::FCR23,
          211 => Reg::FCR24,
          212 => Reg::FCR25,
          213 => Reg::FCR26,
          214 => Reg::FCR27,
          215 => Reg::FCR28,
          216 => Reg::FCR29,
          217 => Reg::FCR30,
          218 => Reg::FCR31,
          219 => Reg::FP_64,
          220 => Reg::F_HI0,
          221 => Reg::F_HI1,
          222 => Reg::F_HI2,
          223 => Reg::F_HI3,
          224 => Reg::F_HI4,
          225 => Reg::F_HI5,
          226 => Reg::F_HI6,
          227 => Reg::F_HI7,
          228 => Reg::F_HI8,
          229 => Reg::F_HI9,
          230 => Reg::F_HI10,
          231 => Reg::F_HI11,
          232 => Reg::F_HI12,
          233 => Reg::F_HI13,
          234 => Reg::F_HI14,
          235 => Reg::F_HI15,
          236 => Reg::F_HI16,
          237 => Reg::F_HI17,
          238 => Reg::F_HI18,
          239 => Reg::F_HI19,
          240 => Reg::F_HI20,
          241 => Reg::F_HI21,
          242 => Reg::F_HI22,
          243 => Reg::F_HI23,
          244 => Reg::F_HI24,
          245 => Reg::F_HI25,
          246 => Reg::F_HI26,
          247 => Reg::F_HI27,
          248 => Reg::F_HI28,
          249 => Reg::F_HI29,
          250 => Reg::F_HI30,
          251 => Reg::F_HI31,
          252 => Reg::GP_64,
          253 => Reg::HI0,
          254 => Reg::HI1,
          255 => Reg::HI2,
          256 => Reg::HI3,
          257 => Reg::HWR0,
          258 => Reg::HWR1,
          259 => Reg::HWR2,
          260 => Reg::HWR3,
          261 => Reg::HWR4,
          262 => Reg::HWR5,
          263 => Reg::HWR6,
          264 => Reg::HWR7,
          265 => Reg::HWR8,
          266 => Reg::HWR9,
          267 => Reg::HWR10,
          268 => Reg::HWR11,
          269 => Reg::HWR12,
          270 => Reg::HWR13,
          271 => Reg::HWR14,
          272 => Reg::HWR15,
          273 => Reg::HWR16,
          274 => Reg::HWR17,
          275 => Reg::HWR18,
          276 => Reg::HWR19,
          277 => Reg::HWR20,
          278 => Reg::HWR21,
          279 => Reg::HWR22,
          280 => Reg::HWR23,
          281 => Reg::HWR24,
          282 => Reg::HWR25,
          283 => Reg::HWR26,
          284 => Reg::HWR27,
          285 => Reg::HWR28,
          286 => Reg::HWR29,
          287 => Reg::HWR30,
          288 => Reg::HWR31,
          289 => Reg::K0,
          290 => Reg::K1,
          291 => Reg::LO0,
          292 => Reg::LO1,
          293 => Reg::LO2,
          294 => Reg::LO3,
          295 => Reg::MPL0,
          296 => Reg::MPL1,
          297 => Reg::MPL2,
          298 => Reg::MSA8,
          299 => Reg::MSA9,
          300 => Reg::MSA10,
          301 => Reg::MSA11,
          302 => Reg::MSA12,
          303 => Reg::MSA13,
          304 => Reg::MSA14,
          305 => Reg::MSA15,
          306 => Reg::MSA16,
          307 => Reg::MSA17,
          308 => Reg::MSA18,
          309 => Reg::MSA19,
          310 => Reg::MSA20,
          311 => Reg::MSA21,
          312 => Reg::MSA22,
          313 => Reg::MSA23,
          314 => Reg::MSA24,
          315 => Reg::MSA25,
          316 => Reg::MSA26,
          317 => Reg::MSA27,
          318 => Reg::MSA28,
          319 => Reg::MSA29,
          320 => Reg::MSA30,
          321 => Reg::MSA31,
          322 => Reg::P0,
          323 => Reg::P1,
          324 => Reg::P2,
          325 => Reg::RA_64,
          326 => Reg::S0,
          327 => Reg::S1,
          328 => Reg::S2,
          329 => Reg::S3,
          330 => Reg::S4,
          331 => Reg::S5,
          332 => Reg::S6,
          333 => Reg::S7,
          334 => Reg::SP_64,
          335 => Reg::T0,
          336 => Reg::T1,
          337 => Reg::T2,
          338 => Reg::T3,
          339 => Reg::T4,
          340 => Reg::T5,
          341 => Reg::T6,
          342 => Reg::T7,
          343 => Reg::T8,
          344 => Reg::T9,
          345 => Reg::V0,
          346 => Reg::V1,
          347 => Reg::W0,
          348 => Reg::W1,
          349 => Reg::W2,
          350 => Reg::W3,
          351 => Reg::W4,
          352 => Reg::W5,
          353 => Reg::W6,
          354 => Reg::W7,
          355 => Reg::W8,
          356 => Reg::W9,
          357 => Reg::W10,
          358 => Reg::W11,
          359 => Reg::W12,
          360 => Reg::W13,
          361 => Reg::W14,
          362 => Reg::W15,
          363 => Reg::W16,
          364 => Reg::W17,
          365 => Reg::W18,
          366 => Reg::W19,
          367 => Reg::W20,
          368 => Reg::W21,
          369 => Reg::W22,
          370 => Reg::W23,
          371 => Reg::W24,
          372 => Reg::W25,
          373 => Reg::W26,
          374 => Reg::W27,
          375 => Reg::W28,
          376 => Reg::W29,
          377 => Reg::W30,
          378 => Reg::W31,
          379 => Reg::ZERO_64,
          380 => Reg::A0_64,
          381 => Reg::A1_64,
          382 => Reg::A2_64,
          383 => Reg::A3_64,
          384 => Reg::AC0_64,
          385 => Reg::D0_64,
          386 => Reg::D1_64,
          387 => Reg::D2_64,
          388 => Reg::D3_64,
          389 => Reg::D4_64,
          390 => Reg::D5_64,
          391 => Reg::D6_64,
          392 => Reg::D7_64,
          393 => Reg::D8_64,
          394 => Reg::D9_64,
          395 => Reg::D10_64,
          396 => Reg::D11_64,
          397 => Reg::D12_64,
          398 => Reg::D13_64,
          399 => Reg::D14_64,
          400 => Reg::D15_64,
          401 => Reg::D16_64,
          402 => Reg::D17_64,
          403 => Reg::D18_64,
          404 => Reg::D19_64,
          405 => Reg::D20_64,
          406 => Reg::D21_64,
          407 => Reg::D22_64,
          408 => Reg::D23_64,
          409 => Reg::D24_64,
          410 => Reg::D25_64,
          411 => Reg::D26_64,
          412 => Reg::D27_64,
          413 => Reg::D28_64,
          414 => Reg::D29_64,
          415 => Reg::D30_64,
          416 => Reg::D31_64,
          417 => Reg::DSPOutFlag16_19,
          418 => Reg::HI0_64,
          419 => Reg::K0_64,
          420 => Reg::K1_64,
          421 => Reg::LO0_64,
          422 => Reg::S0_64,
          423 => Reg::S1_64,
          424 => Reg::S2_64,
          425 => Reg::S3_64,
          426 => Reg::S4_64,
          427 => Reg::S5_64,
          428 => Reg::S6_64,
          429 => Reg::S7_64,
          430 => Reg::T0_64,
          431 => Reg::T1_64,
          432 => Reg::T2_64,
          433 => Reg::T3_64,
          434 => Reg::T4_64,
          435 => Reg::T5_64,
          436 => Reg::T6_64,
          437 => Reg::T7_64,
          438 => Reg::T8_64,
          439 => Reg::T9_64,
          440 => Reg::V0_64,
          441 => Reg::V1_64,
          442 => Reg::NUM_TARGET_REGS,
          _ => Reg::UNKNOWN(value),
        }
    }
}
