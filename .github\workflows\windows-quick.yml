name: LIEF Windows Quick
on:
  push:
    branches:
      - '**'
      - '!master'
      - '!main'
      - '!release-**'
      - '!release/**'
    tags-ignore:
      - "**"

env:
  python-version: '3.12'

jobs:
  quick-test:
    runs-on: windows-2022
    strategy:
      matrix:
        arch: [x64, x86]
      fail-fast: false
    env:
      SCCACHE_CACHE_SIZE: 2G
      SCCACHE_DIR: ${{ vars.SCCACHE_DIR }}
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Python ${{ env.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.python-version }}
        architecture: ${{ matrix.python-version }}
    - name: Install system dependencies
      run: |
        choco install ninja
        choco install sccache
        python -m pip install --upgrade pip
    - uses: ilammy/msvc-dev-cmd@v1
      with:
        vsversion: "2022"
    - name: Get Date
      id: get-date
      run: |
        echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
      shell: bash
    - name: Save sccache
      uses: actions/cache@v4
      continue-on-error: false
      with:
        path: ${{ vars.SCCACHE_DIR }}
        key: ${{ runner.os }}-${{ matrix.arch }}-quick-${{ steps.get-date.outputs.date }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.arch }}-quick
    - name: Start sccache server
      run: sccache --start-server
    - name: Set env variables
      run: |
        chcp 65001 #set code page to utf-8
        echo ("LIEF_SAMPLES_DIR=" + $env:RUNNER_TEMP + "/samples") >> $env:GITHUB_ENV
    - name: Build Python ${{ env.python-version }}
      shell: cmd
      run: |
        python -m pip install -r tests/requirements.txt
        python -m pip -vvv install api/python
    - name: Download samples
      shell: cmd
      run: |
        python tests/dl_samples.py %LIEF_SAMPLES_DIR%
    - name: Run tests
      shell: cmd
      timeout-minutes: 20
      run: |
        python ./tests/run_pytest.py
    - name: sccache stats
      run: sccache --show-stats
    - name: sccache server
      run: sccache --stop-server || true
