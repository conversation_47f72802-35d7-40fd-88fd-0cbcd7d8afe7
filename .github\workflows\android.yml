name: 'Android'
on: push

jobs:
  build-sdk:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        arch: ['arm', 'arm64']
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: android-${{ runner.os }}-${{ matrix.arch }}-${{ steps.get-date.outputs.date }}
          restore-keys: |
              android-${{ runner.os }}-${{ matrix.arch }}
      - name: Build SDK [${{ matrix.arch }}]
        shell: bash
        env:
          ARCH: ${{ matrix.arch }}
        run: |
          docker run                  \
           -e CCACHE_COMPRESS=1       \
           -e CCACHE_MAXSIZE=10.0G    \
           -e CCACHE_CPP2=1           \
           -e CCACHE_DIR=/ccache      \
           -v $GITHUB_WORKSPACE:/work \
           -v $HOME/.ccache:/ccache   \
          --rm liefproject/android-${ARCH} bash /work/scripts/docker/android-sdk-${ARCH}
      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: android-sdk-${{ matrix.arch }}
          path: build/*.tar.gz
          retention-days: 3
  deploy:
    if: ${{ always() }}
    runs-on: ubuntu-latest
    needs: build-sdk
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/artifacts
      - name: Display artifacts
        run: ls -R
        working-directory: ${{ runner.temp }}/artifacts
      - name: Deploy
        env:
          LIEF_S3_KEY: ${{ secrets.LIEF_S3_KEY }}
          LIEF_S3_SECRET: ${{ secrets.LIEF_S3_SECRET }}
        shell: bash
        run: |
          docker run --rm                           \
            -v $GITHUB_WORKSPACE:/src               \
            -v $RUNNER_TEMP:/runner_tmp             \
            -e GITHUB_ACTIONS="true"                \
            -e GITHUB_WORKSPACE=$GITHUB_WORKSPACE   \
            -e GITHUB_REF=$GITHUB_REF               \
            -e GITHUB_REPOSITORY=$GITHUB_REPOSITORY \
            -e LIEF_S3_KEY=$LIEF_S3_KEY             \
            -e LIEF_S3_SECRET=$LIEF_S3_SECRET       \
            liefproject/deploy python3 /src/.github/deploy.py /runner_tmp/artifacts



