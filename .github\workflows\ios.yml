name: 'iOS'
on: push

jobs:
  ios-sdk:
    runs-on: macos-13
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install system dependencies
        run: |
          brew install cmake ninja ccache
      - name: Get Date
        id: get-date
        run: |
          echo "date=$(/bin/date -u "+%Y-%m-%d-%H;%M;%S")" >> $GITHUB_OUTPUT
        shell: bash
      - name: Setup cache dir
        shell: bash
        run: |
          mkdir -p ~/.ccache
      - name: ccache cache files
        uses: actions/cache@v4
        with:
          path: ${{ runner.temp }}/.ccache
          key: ios-${{ runner.os }}-${{ steps.get-date.outputs.date }}
          restore-keys: |
              ios-${{ runner.os }}
      - name: Build iOS SDK
        shell: bash
        env:
          CCACHE_DIR: ${{ runner.temp }}/.ccache
          CCACHE_MAXSIZE: 10.0G
          CCACHE_CPP2: 1
          CCACHE_COMPRESS: 1
        run: |
          bash scripts/osx/package_ios.sh
      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: ios-sdk
          path: build/*.tar.gz
          retention-days: 3
  deploy:
    runs-on: ubuntu-latest
    if: ${{ always() }}
    needs: ios-sdk
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/artifacts
      - name: Display artifacts
        run: ls -R
        working-directory: ${{ runner.temp }}/artifacts
      - name: Deploy
        env:
          LIEF_S3_KEY: ${{ secrets.LIEF_S3_KEY }}
          LIEF_S3_SECRET: ${{ secrets.LIEF_S3_SECRET }}
        shell: bash
        run: |
          docker run --rm                                     \
            -v $GITHUB_WORKSPACE:/src                         \
            -v $RUNNER_TEMP:/runner_tmp                       \
            -e GITHUB_ACTIONS="true"                          \
            -e GITHUB_WORKSPACE=$GITHUB_WORKSPACE             \
            -e GITHUB_REF=$GITHUB_REF                         \
            -e GITHUB_REPOSITORY=$GITHUB_REPOSITORY           \
            -e LIEF_S3_KEY=$LIEF_S3_KEY                       \
            -e LIEF_S3_SECRET=$LIEF_S3_SECRET                 \
            liefproject/deploy                                \
            python3 /src/.github/deploy.py /runner_tmp/artifacts



