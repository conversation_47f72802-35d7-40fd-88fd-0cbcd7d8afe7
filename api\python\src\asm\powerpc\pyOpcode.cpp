#include "asm/powerpc/init.hpp"

#include "LIEF/asm/powerpc/opcodes.hpp"

namespace LIEF::assembly::powerpc::py {
template<>
void create<LIEF::assembly::powerpc::OPCODE>(nb::module_& m) {
  nb::enum_<LIEF::assembly::powerpc::OPCODE> opcodes(m, "OPCODE");
  opcodes.value("PHI", LIEF::assembly::powerpc::OPCODE::PHI)
  .value("INLINEASM", LIEF::assembly::powerpc::OPCODE::INLINEASM)
  .value("INLINEASM_BR", LIEF::assembly::powerpc::OPCODE::INLINEASM_BR)
  .value("CFI_INSTRUCTION", LIEF::assembly::powerpc::OPCODE::CFI_INSTRUCTION)
  .value("EH_LABEL", LIEF::assembly::powerpc::OPCODE::EH_LABEL)
  .value("GC_LABEL", LIEF::assembly::powerpc::OPCODE::GC_LABEL)
  .value("ANNOTATION_LABEL", LIEF::assembly::powerpc::OPCODE::ANNOTATION_LABEL)
  .value("KILL", LIEF::assembly::powerpc::OPCODE::KILL)
  .value("EXTRACT_SUBREG", LIEF::assembly::powerpc::OPCODE::EXTRACT_SUBREG)
  .value("INSERT_SUBREG", LIEF::assembly::powerpc::OPCODE::INSERT_SUBREG)
  .value("IMPLICIT_DEF", LIEF::assembly::powerpc::OPCODE::IMPLICIT_DEF)
  .value("INIT_UNDEF", LIEF::assembly::powerpc::OPCODE::INIT_UNDEF)
  .value("SUBREG_TO_REG", LIEF::assembly::powerpc::OPCODE::SUBREG_TO_REG)
  .value("COPY_TO_REGCLASS", LIEF::assembly::powerpc::OPCODE::COPY_TO_REGCLASS)
  .value("DBG_VALUE", LIEF::assembly::powerpc::OPCODE::DBG_VALUE)
  .value("DBG_VALUE_LIST", LIEF::assembly::powerpc::OPCODE::DBG_VALUE_LIST)
  .value("DBG_INSTR_REF", LIEF::assembly::powerpc::OPCODE::DBG_INSTR_REF)
  .value("DBG_PHI", LIEF::assembly::powerpc::OPCODE::DBG_PHI)
  .value("DBG_LABEL", LIEF::assembly::powerpc::OPCODE::DBG_LABEL)
  .value("REG_SEQUENCE", LIEF::assembly::powerpc::OPCODE::REG_SEQUENCE)
  .value("COPY", LIEF::assembly::powerpc::OPCODE::COPY)
  .value("BUNDLE", LIEF::assembly::powerpc::OPCODE::BUNDLE)
  .value("LIFETIME_START", LIEF::assembly::powerpc::OPCODE::LIFETIME_START)
  .value("LIFETIME_END", LIEF::assembly::powerpc::OPCODE::LIFETIME_END)
  .value("PSEUDO_PROBE", LIEF::assembly::powerpc::OPCODE::PSEUDO_PROBE)
  .value("ARITH_FENCE", LIEF::assembly::powerpc::OPCODE::ARITH_FENCE)
  .value("STACKMAP", LIEF::assembly::powerpc::OPCODE::STACKMAP)
  .value("FENTRY_CALL", LIEF::assembly::powerpc::OPCODE::FENTRY_CALL)
  .value("PATCHPOINT", LIEF::assembly::powerpc::OPCODE::PATCHPOINT)
  .value("LOAD_STACK_GUARD", LIEF::assembly::powerpc::OPCODE::LOAD_STACK_GUARD)
  .value("PREALLOCATED_SETUP", LIEF::assembly::powerpc::OPCODE::PREALLOCATED_SETUP)
  .value("PREALLOCATED_ARG", LIEF::assembly::powerpc::OPCODE::PREALLOCATED_ARG)
  .value("STATEPOINT", LIEF::assembly::powerpc::OPCODE::STATEPOINT)
  .value("LOCAL_ESCAPE", LIEF::assembly::powerpc::OPCODE::LOCAL_ESCAPE)
  .value("FAULTING_OP", LIEF::assembly::powerpc::OPCODE::FAULTING_OP)
  .value("PATCHABLE_OP", LIEF::assembly::powerpc::OPCODE::PATCHABLE_OP)
  .value("PATCHABLE_FUNCTION_ENTER", LIEF::assembly::powerpc::OPCODE::PATCHABLE_FUNCTION_ENTER)
  .value("PATCHABLE_RET", LIEF::assembly::powerpc::OPCODE::PATCHABLE_RET)
  .value("PATCHABLE_FUNCTION_EXIT", LIEF::assembly::powerpc::OPCODE::PATCHABLE_FUNCTION_EXIT)
  .value("PATCHABLE_TAIL_CALL", LIEF::assembly::powerpc::OPCODE::PATCHABLE_TAIL_CALL)
  .value("PATCHABLE_EVENT_CALL", LIEF::assembly::powerpc::OPCODE::PATCHABLE_EVENT_CALL)
  .value("PATCHABLE_TYPED_EVENT_CALL", LIEF::assembly::powerpc::OPCODE::PATCHABLE_TYPED_EVENT_CALL)
  .value("ICALL_BRANCH_FUNNEL", LIEF::assembly::powerpc::OPCODE::ICALL_BRANCH_FUNNEL)
  .value("FAKE_USE", LIEF::assembly::powerpc::OPCODE::FAKE_USE)
  .value("MEMBARRIER", LIEF::assembly::powerpc::OPCODE::MEMBARRIER)
  .value("JUMP_TABLE_DEBUG_INFO", LIEF::assembly::powerpc::OPCODE::JUMP_TABLE_DEBUG_INFO)
  .value("CONVERGENCECTRL_ENTRY", LIEF::assembly::powerpc::OPCODE::CONVERGENCECTRL_ENTRY)
  .value("CONVERGENCECTRL_ANCHOR", LIEF::assembly::powerpc::OPCODE::CONVERGENCECTRL_ANCHOR)
  .value("CONVERGENCECTRL_LOOP", LIEF::assembly::powerpc::OPCODE::CONVERGENCECTRL_LOOP)
  .value("CONVERGENCECTRL_GLUE", LIEF::assembly::powerpc::OPCODE::CONVERGENCECTRL_GLUE)
  .value("G_ASSERT_SEXT", LIEF::assembly::powerpc::OPCODE::G_ASSERT_SEXT)
  .value("G_ASSERT_ZEXT", LIEF::assembly::powerpc::OPCODE::G_ASSERT_ZEXT)
  .value("G_ASSERT_ALIGN", LIEF::assembly::powerpc::OPCODE::G_ASSERT_ALIGN)
  .value("G_ADD", LIEF::assembly::powerpc::OPCODE::G_ADD)
  .value("G_SUB", LIEF::assembly::powerpc::OPCODE::G_SUB)
  .value("G_MUL", LIEF::assembly::powerpc::OPCODE::G_MUL)
  .value("G_SDIV", LIEF::assembly::powerpc::OPCODE::G_SDIV)
  .value("G_UDIV", LIEF::assembly::powerpc::OPCODE::G_UDIV)
  .value("G_SREM", LIEF::assembly::powerpc::OPCODE::G_SREM)
  .value("G_UREM", LIEF::assembly::powerpc::OPCODE::G_UREM)
  .value("G_SDIVREM", LIEF::assembly::powerpc::OPCODE::G_SDIVREM)
  .value("G_UDIVREM", LIEF::assembly::powerpc::OPCODE::G_UDIVREM)
  .value("G_AND", LIEF::assembly::powerpc::OPCODE::G_AND)
  .value("G_OR", LIEF::assembly::powerpc::OPCODE::G_OR)
  .value("G_XOR", LIEF::assembly::powerpc::OPCODE::G_XOR)
  .value("G_ABDS", LIEF::assembly::powerpc::OPCODE::G_ABDS)
  .value("G_ABDU", LIEF::assembly::powerpc::OPCODE::G_ABDU)
  .value("G_IMPLICIT_DEF", LIEF::assembly::powerpc::OPCODE::G_IMPLICIT_DEF)
  .value("G_PHI", LIEF::assembly::powerpc::OPCODE::G_PHI)
  .value("G_FRAME_INDEX", LIEF::assembly::powerpc::OPCODE::G_FRAME_INDEX)
  .value("G_GLOBAL_VALUE", LIEF::assembly::powerpc::OPCODE::G_GLOBAL_VALUE)
  .value("G_PTRAUTH_GLOBAL_VALUE", LIEF::assembly::powerpc::OPCODE::G_PTRAUTH_GLOBAL_VALUE)
  .value("G_CONSTANT_POOL", LIEF::assembly::powerpc::OPCODE::G_CONSTANT_POOL)
  .value("G_EXTRACT", LIEF::assembly::powerpc::OPCODE::G_EXTRACT)
  .value("G_UNMERGE_VALUES", LIEF::assembly::powerpc::OPCODE::G_UNMERGE_VALUES)
  .value("G_INSERT", LIEF::assembly::powerpc::OPCODE::G_INSERT)
  .value("G_MERGE_VALUES", LIEF::assembly::powerpc::OPCODE::G_MERGE_VALUES)
  .value("G_BUILD_VECTOR", LIEF::assembly::powerpc::OPCODE::G_BUILD_VECTOR)
  .value("G_BUILD_VECTOR_TRUNC", LIEF::assembly::powerpc::OPCODE::G_BUILD_VECTOR_TRUNC)
  .value("G_CONCAT_VECTORS", LIEF::assembly::powerpc::OPCODE::G_CONCAT_VECTORS)
  .value("G_PTRTOINT", LIEF::assembly::powerpc::OPCODE::G_PTRTOINT)
  .value("G_INTTOPTR", LIEF::assembly::powerpc::OPCODE::G_INTTOPTR)
  .value("G_BITCAST", LIEF::assembly::powerpc::OPCODE::G_BITCAST)
  .value("G_FREEZE", LIEF::assembly::powerpc::OPCODE::G_FREEZE)
  .value("G_CONSTANT_FOLD_BARRIER", LIEF::assembly::powerpc::OPCODE::G_CONSTANT_FOLD_BARRIER)
  .value("G_INTRINSIC_FPTRUNC_ROUND", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_FPTRUNC_ROUND)
  .value("G_INTRINSIC_TRUNC", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_TRUNC)
  .value("G_INTRINSIC_ROUND", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_ROUND)
  .value("G_INTRINSIC_LRINT", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_LRINT)
  .value("G_INTRINSIC_LLRINT", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_LLRINT)
  .value("G_INTRINSIC_ROUNDEVEN", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_ROUNDEVEN)
  .value("G_READCYCLECOUNTER", LIEF::assembly::powerpc::OPCODE::G_READCYCLECOUNTER)
  .value("G_READSTEADYCOUNTER", LIEF::assembly::powerpc::OPCODE::G_READSTEADYCOUNTER)
  .value("G_LOAD", LIEF::assembly::powerpc::OPCODE::G_LOAD)
  .value("G_SEXTLOAD", LIEF::assembly::powerpc::OPCODE::G_SEXTLOAD)
  .value("G_ZEXTLOAD", LIEF::assembly::powerpc::OPCODE::G_ZEXTLOAD)
  .value("G_INDEXED_LOAD", LIEF::assembly::powerpc::OPCODE::G_INDEXED_LOAD)
  .value("G_INDEXED_SEXTLOAD", LIEF::assembly::powerpc::OPCODE::G_INDEXED_SEXTLOAD)
  .value("G_INDEXED_ZEXTLOAD", LIEF::assembly::powerpc::OPCODE::G_INDEXED_ZEXTLOAD)
  .value("G_STORE", LIEF::assembly::powerpc::OPCODE::G_STORE)
  .value("G_INDEXED_STORE", LIEF::assembly::powerpc::OPCODE::G_INDEXED_STORE)
  .value("G_ATOMIC_CMPXCHG_WITH_SUCCESS", LIEF::assembly::powerpc::OPCODE::G_ATOMIC_CMPXCHG_WITH_SUCCESS)
  .value("G_ATOMIC_CMPXCHG", LIEF::assembly::powerpc::OPCODE::G_ATOMIC_CMPXCHG)
  .value("G_ATOMICRMW_XCHG", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_XCHG)
  .value("G_ATOMICRMW_ADD", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_ADD)
  .value("G_ATOMICRMW_SUB", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_SUB)
  .value("G_ATOMICRMW_AND", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_AND)
  .value("G_ATOMICRMW_NAND", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_NAND)
  .value("G_ATOMICRMW_OR", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_OR)
  .value("G_ATOMICRMW_XOR", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_XOR)
  .value("G_ATOMICRMW_MAX", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_MAX)
  .value("G_ATOMICRMW_MIN", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_MIN)
  .value("G_ATOMICRMW_UMAX", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_UMAX)
  .value("G_ATOMICRMW_UMIN", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_UMIN)
  .value("G_ATOMICRMW_FADD", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_FADD)
  .value("G_ATOMICRMW_FSUB", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_FSUB)
  .value("G_ATOMICRMW_FMAX", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_FMAX)
  .value("G_ATOMICRMW_FMIN", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_FMIN)
  .value("G_ATOMICRMW_UINC_WRAP", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_UINC_WRAP)
  .value("G_ATOMICRMW_UDEC_WRAP", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_UDEC_WRAP)
  .value("G_ATOMICRMW_USUB_COND", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_USUB_COND)
  .value("G_ATOMICRMW_USUB_SAT", LIEF::assembly::powerpc::OPCODE::G_ATOMICRMW_USUB_SAT)
  .value("G_FENCE", LIEF::assembly::powerpc::OPCODE::G_FENCE)
  .value("G_PREFETCH", LIEF::assembly::powerpc::OPCODE::G_PREFETCH)
  .value("G_BRCOND", LIEF::assembly::powerpc::OPCODE::G_BRCOND)
  .value("G_BRINDIRECT", LIEF::assembly::powerpc::OPCODE::G_BRINDIRECT)
  .value("G_INVOKE_REGION_START", LIEF::assembly::powerpc::OPCODE::G_INVOKE_REGION_START)
  .value("G_INTRINSIC", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC)
  .value("G_INTRINSIC_W_SIDE_EFFECTS", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_W_SIDE_EFFECTS)
  .value("G_INTRINSIC_CONVERGENT", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_CONVERGENT)
  .value("G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS", LIEF::assembly::powerpc::OPCODE::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS)
  .value("G_ANYEXT", LIEF::assembly::powerpc::OPCODE::G_ANYEXT)
  .value("G_TRUNC", LIEF::assembly::powerpc::OPCODE::G_TRUNC)
  .value("G_CONSTANT", LIEF::assembly::powerpc::OPCODE::G_CONSTANT)
  .value("G_FCONSTANT", LIEF::assembly::powerpc::OPCODE::G_FCONSTANT)
  .value("G_VASTART", LIEF::assembly::powerpc::OPCODE::G_VASTART)
  .value("G_VAARG", LIEF::assembly::powerpc::OPCODE::G_VAARG)
  .value("G_SEXT", LIEF::assembly::powerpc::OPCODE::G_SEXT)
  .value("G_SEXT_INREG", LIEF::assembly::powerpc::OPCODE::G_SEXT_INREG)
  .value("G_ZEXT", LIEF::assembly::powerpc::OPCODE::G_ZEXT)
  .value("G_SHL", LIEF::assembly::powerpc::OPCODE::G_SHL)
  .value("G_LSHR", LIEF::assembly::powerpc::OPCODE::G_LSHR)
  .value("G_ASHR", LIEF::assembly::powerpc::OPCODE::G_ASHR)
  .value("G_FSHL", LIEF::assembly::powerpc::OPCODE::G_FSHL)
  .value("G_FSHR", LIEF::assembly::powerpc::OPCODE::G_FSHR)
  .value("G_ROTR", LIEF::assembly::powerpc::OPCODE::G_ROTR)
  .value("G_ROTL", LIEF::assembly::powerpc::OPCODE::G_ROTL)
  .value("G_ICMP", LIEF::assembly::powerpc::OPCODE::G_ICMP)
  .value("G_FCMP", LIEF::assembly::powerpc::OPCODE::G_FCMP)
  .value("G_SCMP", LIEF::assembly::powerpc::OPCODE::G_SCMP)
  .value("G_UCMP", LIEF::assembly::powerpc::OPCODE::G_UCMP)
  .value("G_SELECT", LIEF::assembly::powerpc::OPCODE::G_SELECT)
  .value("G_UADDO", LIEF::assembly::powerpc::OPCODE::G_UADDO)
  .value("G_UADDE", LIEF::assembly::powerpc::OPCODE::G_UADDE)
  .value("G_USUBO", LIEF::assembly::powerpc::OPCODE::G_USUBO)
  .value("G_USUBE", LIEF::assembly::powerpc::OPCODE::G_USUBE)
  .value("G_SADDO", LIEF::assembly::powerpc::OPCODE::G_SADDO)
  .value("G_SADDE", LIEF::assembly::powerpc::OPCODE::G_SADDE)
  .value("G_SSUBO", LIEF::assembly::powerpc::OPCODE::G_SSUBO)
  .value("G_SSUBE", LIEF::assembly::powerpc::OPCODE::G_SSUBE)
  .value("G_UMULO", LIEF::assembly::powerpc::OPCODE::G_UMULO)
  .value("G_SMULO", LIEF::assembly::powerpc::OPCODE::G_SMULO)
  .value("G_UMULH", LIEF::assembly::powerpc::OPCODE::G_UMULH)
  .value("G_SMULH", LIEF::assembly::powerpc::OPCODE::G_SMULH)
  .value("G_UADDSAT", LIEF::assembly::powerpc::OPCODE::G_UADDSAT)
  .value("G_SADDSAT", LIEF::assembly::powerpc::OPCODE::G_SADDSAT)
  .value("G_USUBSAT", LIEF::assembly::powerpc::OPCODE::G_USUBSAT)
  .value("G_SSUBSAT", LIEF::assembly::powerpc::OPCODE::G_SSUBSAT)
  .value("G_USHLSAT", LIEF::assembly::powerpc::OPCODE::G_USHLSAT)
  .value("G_SSHLSAT", LIEF::assembly::powerpc::OPCODE::G_SSHLSAT)
  .value("G_SMULFIX", LIEF::assembly::powerpc::OPCODE::G_SMULFIX)
  .value("G_UMULFIX", LIEF::assembly::powerpc::OPCODE::G_UMULFIX)
  .value("G_SMULFIXSAT", LIEF::assembly::powerpc::OPCODE::G_SMULFIXSAT)
  .value("G_UMULFIXSAT", LIEF::assembly::powerpc::OPCODE::G_UMULFIXSAT)
  .value("G_SDIVFIX", LIEF::assembly::powerpc::OPCODE::G_SDIVFIX)
  .value("G_UDIVFIX", LIEF::assembly::powerpc::OPCODE::G_UDIVFIX)
  .value("G_SDIVFIXSAT", LIEF::assembly::powerpc::OPCODE::G_SDIVFIXSAT)
  .value("G_UDIVFIXSAT", LIEF::assembly::powerpc::OPCODE::G_UDIVFIXSAT)
  .value("G_FADD", LIEF::assembly::powerpc::OPCODE::G_FADD)
  .value("G_FSUB", LIEF::assembly::powerpc::OPCODE::G_FSUB)
  .value("G_FMUL", LIEF::assembly::powerpc::OPCODE::G_FMUL)
  .value("G_FMA", LIEF::assembly::powerpc::OPCODE::G_FMA)
  .value("G_FMAD", LIEF::assembly::powerpc::OPCODE::G_FMAD)
  .value("G_FDIV", LIEF::assembly::powerpc::OPCODE::G_FDIV)
  .value("G_FREM", LIEF::assembly::powerpc::OPCODE::G_FREM)
  .value("G_FPOW", LIEF::assembly::powerpc::OPCODE::G_FPOW)
  .value("G_FPOWI", LIEF::assembly::powerpc::OPCODE::G_FPOWI)
  .value("G_FEXP", LIEF::assembly::powerpc::OPCODE::G_FEXP)
  .value("G_FEXP2", LIEF::assembly::powerpc::OPCODE::G_FEXP2)
  .value("G_FEXP10", LIEF::assembly::powerpc::OPCODE::G_FEXP10)
  .value("G_FLOG", LIEF::assembly::powerpc::OPCODE::G_FLOG)
  .value("G_FLOG2", LIEF::assembly::powerpc::OPCODE::G_FLOG2)
  .value("G_FLOG10", LIEF::assembly::powerpc::OPCODE::G_FLOG10)
  .value("G_FLDEXP", LIEF::assembly::powerpc::OPCODE::G_FLDEXP)
  .value("G_FFREXP", LIEF::assembly::powerpc::OPCODE::G_FFREXP)
  .value("G_FNEG", LIEF::assembly::powerpc::OPCODE::G_FNEG)
  .value("G_FPEXT", LIEF::assembly::powerpc::OPCODE::G_FPEXT)
  .value("G_FPTRUNC", LIEF::assembly::powerpc::OPCODE::G_FPTRUNC)
  .value("G_FPTOSI", LIEF::assembly::powerpc::OPCODE::G_FPTOSI)
  .value("G_FPTOUI", LIEF::assembly::powerpc::OPCODE::G_FPTOUI)
  .value("G_SITOFP", LIEF::assembly::powerpc::OPCODE::G_SITOFP)
  .value("G_UITOFP", LIEF::assembly::powerpc::OPCODE::G_UITOFP)
  .value("G_FPTOSI_SAT", LIEF::assembly::powerpc::OPCODE::G_FPTOSI_SAT)
  .value("G_FPTOUI_SAT", LIEF::assembly::powerpc::OPCODE::G_FPTOUI_SAT)
  .value("G_FABS", LIEF::assembly::powerpc::OPCODE::G_FABS)
  .value("G_FCOPYSIGN", LIEF::assembly::powerpc::OPCODE::G_FCOPYSIGN)
  .value("G_IS_FPCLASS", LIEF::assembly::powerpc::OPCODE::G_IS_FPCLASS)
  .value("G_FCANONICALIZE", LIEF::assembly::powerpc::OPCODE::G_FCANONICALIZE)
  .value("G_FMINNUM", LIEF::assembly::powerpc::OPCODE::G_FMINNUM)
  .value("G_FMAXNUM", LIEF::assembly::powerpc::OPCODE::G_FMAXNUM)
  .value("G_FMINNUM_IEEE", LIEF::assembly::powerpc::OPCODE::G_FMINNUM_IEEE)
  .value("G_FMAXNUM_IEEE", LIEF::assembly::powerpc::OPCODE::G_FMAXNUM_IEEE)
  .value("G_FMINIMUM", LIEF::assembly::powerpc::OPCODE::G_FMINIMUM)
  .value("G_FMAXIMUM", LIEF::assembly::powerpc::OPCODE::G_FMAXIMUM)
  .value("G_GET_FPENV", LIEF::assembly::powerpc::OPCODE::G_GET_FPENV)
  .value("G_SET_FPENV", LIEF::assembly::powerpc::OPCODE::G_SET_FPENV)
  .value("G_RESET_FPENV", LIEF::assembly::powerpc::OPCODE::G_RESET_FPENV)
  .value("G_GET_FPMODE", LIEF::assembly::powerpc::OPCODE::G_GET_FPMODE)
  .value("G_SET_FPMODE", LIEF::assembly::powerpc::OPCODE::G_SET_FPMODE)
  .value("G_RESET_FPMODE", LIEF::assembly::powerpc::OPCODE::G_RESET_FPMODE)
  .value("G_PTR_ADD", LIEF::assembly::powerpc::OPCODE::G_PTR_ADD)
  .value("G_PTRMASK", LIEF::assembly::powerpc::OPCODE::G_PTRMASK)
  .value("G_SMIN", LIEF::assembly::powerpc::OPCODE::G_SMIN)
  .value("G_SMAX", LIEF::assembly::powerpc::OPCODE::G_SMAX)
  .value("G_UMIN", LIEF::assembly::powerpc::OPCODE::G_UMIN)
  .value("G_UMAX", LIEF::assembly::powerpc::OPCODE::G_UMAX)
  .value("G_ABS", LIEF::assembly::powerpc::OPCODE::G_ABS)
  .value("G_LROUND", LIEF::assembly::powerpc::OPCODE::G_LROUND)
  .value("G_LLROUND", LIEF::assembly::powerpc::OPCODE::G_LLROUND)
  .value("G_BR", LIEF::assembly::powerpc::OPCODE::G_BR)
  .value("G_BRJT", LIEF::assembly::powerpc::OPCODE::G_BRJT)
  .value("G_VSCALE", LIEF::assembly::powerpc::OPCODE::G_VSCALE)
  .value("G_INSERT_SUBVECTOR", LIEF::assembly::powerpc::OPCODE::G_INSERT_SUBVECTOR)
  .value("G_EXTRACT_SUBVECTOR", LIEF::assembly::powerpc::OPCODE::G_EXTRACT_SUBVECTOR)
  .value("G_INSERT_VECTOR_ELT", LIEF::assembly::powerpc::OPCODE::G_INSERT_VECTOR_ELT)
  .value("G_EXTRACT_VECTOR_ELT", LIEF::assembly::powerpc::OPCODE::G_EXTRACT_VECTOR_ELT)
  .value("G_SHUFFLE_VECTOR", LIEF::assembly::powerpc::OPCODE::G_SHUFFLE_VECTOR)
  .value("G_SPLAT_VECTOR", LIEF::assembly::powerpc::OPCODE::G_SPLAT_VECTOR)
  .value("G_STEP_VECTOR", LIEF::assembly::powerpc::OPCODE::G_STEP_VECTOR)
  .value("G_VECTOR_COMPRESS", LIEF::assembly::powerpc::OPCODE::G_VECTOR_COMPRESS)
  .value("G_CTTZ", LIEF::assembly::powerpc::OPCODE::G_CTTZ)
  .value("G_CTTZ_ZERO_UNDEF", LIEF::assembly::powerpc::OPCODE::G_CTTZ_ZERO_UNDEF)
  .value("G_CTLZ", LIEF::assembly::powerpc::OPCODE::G_CTLZ)
  .value("G_CTLZ_ZERO_UNDEF", LIEF::assembly::powerpc::OPCODE::G_CTLZ_ZERO_UNDEF)
  .value("G_CTPOP", LIEF::assembly::powerpc::OPCODE::G_CTPOP)
  .value("G_BSWAP", LIEF::assembly::powerpc::OPCODE::G_BSWAP)
  .value("G_BITREVERSE", LIEF::assembly::powerpc::OPCODE::G_BITREVERSE)
  .value("G_FCEIL", LIEF::assembly::powerpc::OPCODE::G_FCEIL)
  .value("G_FCOS", LIEF::assembly::powerpc::OPCODE::G_FCOS)
  .value("G_FSIN", LIEF::assembly::powerpc::OPCODE::G_FSIN)
  .value("G_FSINCOS", LIEF::assembly::powerpc::OPCODE::G_FSINCOS)
  .value("G_FTAN", LIEF::assembly::powerpc::OPCODE::G_FTAN)
  .value("G_FACOS", LIEF::assembly::powerpc::OPCODE::G_FACOS)
  .value("G_FASIN", LIEF::assembly::powerpc::OPCODE::G_FASIN)
  .value("G_FATAN", LIEF::assembly::powerpc::OPCODE::G_FATAN)
  .value("G_FATAN2", LIEF::assembly::powerpc::OPCODE::G_FATAN2)
  .value("G_FCOSH", LIEF::assembly::powerpc::OPCODE::G_FCOSH)
  .value("G_FSINH", LIEF::assembly::powerpc::OPCODE::G_FSINH)
  .value("G_FTANH", LIEF::assembly::powerpc::OPCODE::G_FTANH)
  .value("G_FSQRT", LIEF::assembly::powerpc::OPCODE::G_FSQRT)
  .value("G_FFLOOR", LIEF::assembly::powerpc::OPCODE::G_FFLOOR)
  .value("G_FRINT", LIEF::assembly::powerpc::OPCODE::G_FRINT)
  .value("G_FNEARBYINT", LIEF::assembly::powerpc::OPCODE::G_FNEARBYINT)
  .value("G_ADDRSPACE_CAST", LIEF::assembly::powerpc::OPCODE::G_ADDRSPACE_CAST)
  .value("G_BLOCK_ADDR", LIEF::assembly::powerpc::OPCODE::G_BLOCK_ADDR)
  .value("G_JUMP_TABLE", LIEF::assembly::powerpc::OPCODE::G_JUMP_TABLE)
  .value("G_DYN_STACKALLOC", LIEF::assembly::powerpc::OPCODE::G_DYN_STACKALLOC)
  .value("G_STACKSAVE", LIEF::assembly::powerpc::OPCODE::G_STACKSAVE)
  .value("G_STACKRESTORE", LIEF::assembly::powerpc::OPCODE::G_STACKRESTORE)
  .value("G_STRICT_FADD", LIEF::assembly::powerpc::OPCODE::G_STRICT_FADD)
  .value("G_STRICT_FSUB", LIEF::assembly::powerpc::OPCODE::G_STRICT_FSUB)
  .value("G_STRICT_FMUL", LIEF::assembly::powerpc::OPCODE::G_STRICT_FMUL)
  .value("G_STRICT_FDIV", LIEF::assembly::powerpc::OPCODE::G_STRICT_FDIV)
  .value("G_STRICT_FREM", LIEF::assembly::powerpc::OPCODE::G_STRICT_FREM)
  .value("G_STRICT_FMA", LIEF::assembly::powerpc::OPCODE::G_STRICT_FMA)
  .value("G_STRICT_FSQRT", LIEF::assembly::powerpc::OPCODE::G_STRICT_FSQRT)
  .value("G_STRICT_FLDEXP", LIEF::assembly::powerpc::OPCODE::G_STRICT_FLDEXP)
  .value("G_READ_REGISTER", LIEF::assembly::powerpc::OPCODE::G_READ_REGISTER)
  .value("G_WRITE_REGISTER", LIEF::assembly::powerpc::OPCODE::G_WRITE_REGISTER)
  .value("G_MEMCPY", LIEF::assembly::powerpc::OPCODE::G_MEMCPY)
  .value("G_MEMCPY_INLINE", LIEF::assembly::powerpc::OPCODE::G_MEMCPY_INLINE)
  .value("G_MEMMOVE", LIEF::assembly::powerpc::OPCODE::G_MEMMOVE)
  .value("G_MEMSET", LIEF::assembly::powerpc::OPCODE::G_MEMSET)
  .value("G_BZERO", LIEF::assembly::powerpc::OPCODE::G_BZERO)
  .value("G_TRAP", LIEF::assembly::powerpc::OPCODE::G_TRAP)
  .value("G_DEBUGTRAP", LIEF::assembly::powerpc::OPCODE::G_DEBUGTRAP)
  .value("G_UBSANTRAP", LIEF::assembly::powerpc::OPCODE::G_UBSANTRAP)
  .value("G_VECREDUCE_SEQ_FADD", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_SEQ_FADD)
  .value("G_VECREDUCE_SEQ_FMUL", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_SEQ_FMUL)
  .value("G_VECREDUCE_FADD", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FADD)
  .value("G_VECREDUCE_FMUL", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FMUL)
  .value("G_VECREDUCE_FMAX", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FMAX)
  .value("G_VECREDUCE_FMIN", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FMIN)
  .value("G_VECREDUCE_FMAXIMUM", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FMAXIMUM)
  .value("G_VECREDUCE_FMINIMUM", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_FMINIMUM)
  .value("G_VECREDUCE_ADD", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_ADD)
  .value("G_VECREDUCE_MUL", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_MUL)
  .value("G_VECREDUCE_AND", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_AND)
  .value("G_VECREDUCE_OR", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_OR)
  .value("G_VECREDUCE_XOR", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_XOR);
  opcodes.value("G_VECREDUCE_SMAX", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_SMAX)
  .value("G_VECREDUCE_SMIN", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_SMIN)
  .value("G_VECREDUCE_UMAX", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_UMAX)
  .value("G_VECREDUCE_UMIN", LIEF::assembly::powerpc::OPCODE::G_VECREDUCE_UMIN)
  .value("G_SBFX", LIEF::assembly::powerpc::OPCODE::G_SBFX)
  .value("G_UBFX", LIEF::assembly::powerpc::OPCODE::G_UBFX)
  .value("ATOMIC_CMP_SWAP_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_CMP_SWAP_I128)
  .value("ATOMIC_LOAD_ADD_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_ADD_I128)
  .value("ATOMIC_LOAD_AND_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_AND_I128)
  .value("ATOMIC_LOAD_NAND_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_NAND_I128)
  .value("ATOMIC_LOAD_OR_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_OR_I128)
  .value("ATOMIC_LOAD_SUB_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_SUB_I128)
  .value("ATOMIC_LOAD_XOR_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_XOR_I128)
  .value("ATOMIC_SWAP_I128", LIEF::assembly::powerpc::OPCODE::ATOMIC_SWAP_I128)
  .value("BUILD_QUADWORD", LIEF::assembly::powerpc::OPCODE::BUILD_QUADWORD)
  .value("BUILD_UACC", LIEF::assembly::powerpc::OPCODE::BUILD_UACC)
  .value("CFENCE", LIEF::assembly::powerpc::OPCODE::CFENCE)
  .value("CFENCE8", LIEF::assembly::powerpc::OPCODE::CFENCE8)
  .value("CLRLSLDI", LIEF::assembly::powerpc::OPCODE::CLRLSLDI)
  .value("CLRLSLDI_rec", LIEF::assembly::powerpc::OPCODE::CLRLSLDI_rec)
  .value("CLRLSLWI", LIEF::assembly::powerpc::OPCODE::CLRLSLWI)
  .value("CLRLSLWI_rec", LIEF::assembly::powerpc::OPCODE::CLRLSLWI_rec)
  .value("CLRRDI", LIEF::assembly::powerpc::OPCODE::CLRRDI)
  .value("CLRRDI_rec", LIEF::assembly::powerpc::OPCODE::CLRRDI_rec)
  .value("CLRRWI", LIEF::assembly::powerpc::OPCODE::CLRRWI)
  .value("CLRRWI_rec", LIEF::assembly::powerpc::OPCODE::CLRRWI_rec)
  .value("DCBFL", LIEF::assembly::powerpc::OPCODE::DCBFL)
  .value("DCBFLP", LIEF::assembly::powerpc::OPCODE::DCBFLP)
  .value("DCBFPS", LIEF::assembly::powerpc::OPCODE::DCBFPS)
  .value("DCBFx", LIEF::assembly::powerpc::OPCODE::DCBFx)
  .value("DCBSTPS", LIEF::assembly::powerpc::OPCODE::DCBSTPS)
  .value("DCBTCT", LIEF::assembly::powerpc::OPCODE::DCBTCT)
  .value("DCBTDS", LIEF::assembly::powerpc::OPCODE::DCBTDS)
  .value("DCBTSTCT", LIEF::assembly::powerpc::OPCODE::DCBTSTCT)
  .value("DCBTSTDS", LIEF::assembly::powerpc::OPCODE::DCBTSTDS)
  .value("DCBTSTT", LIEF::assembly::powerpc::OPCODE::DCBTSTT)
  .value("DCBTSTx", LIEF::assembly::powerpc::OPCODE::DCBTSTx)
  .value("DCBTT", LIEF::assembly::powerpc::OPCODE::DCBTT)
  .value("DCBTx", LIEF::assembly::powerpc::OPCODE::DCBTx)
  .value("DFLOADf32", LIEF::assembly::powerpc::OPCODE::DFLOADf32)
  .value("DFLOADf64", LIEF::assembly::powerpc::OPCODE::DFLOADf64)
  .value("DFSTOREf32", LIEF::assembly::powerpc::OPCODE::DFSTOREf32)
  .value("DFSTOREf64", LIEF::assembly::powerpc::OPCODE::DFSTOREf64)
  .value("EXTLDI", LIEF::assembly::powerpc::OPCODE::EXTLDI)
  .value("EXTLDI_rec", LIEF::assembly::powerpc::OPCODE::EXTLDI_rec)
  .value("EXTLWI", LIEF::assembly::powerpc::OPCODE::EXTLWI)
  .value("EXTLWI_rec", LIEF::assembly::powerpc::OPCODE::EXTLWI_rec)
  .value("EXTRDI", LIEF::assembly::powerpc::OPCODE::EXTRDI)
  .value("EXTRDI_rec", LIEF::assembly::powerpc::OPCODE::EXTRDI_rec)
  .value("EXTRWI", LIEF::assembly::powerpc::OPCODE::EXTRWI)
  .value("EXTRWI_rec", LIEF::assembly::powerpc::OPCODE::EXTRWI_rec)
  .value("INSLWI", LIEF::assembly::powerpc::OPCODE::INSLWI)
  .value("INSLWI_rec", LIEF::assembly::powerpc::OPCODE::INSLWI_rec)
  .value("INSRDI", LIEF::assembly::powerpc::OPCODE::INSRDI)
  .value("INSRDI_rec", LIEF::assembly::powerpc::OPCODE::INSRDI_rec)
  .value("INSRWI", LIEF::assembly::powerpc::OPCODE::INSRWI)
  .value("INSRWI_rec", LIEF::assembly::powerpc::OPCODE::INSRWI_rec)
  .value("KILL_PAIR", LIEF::assembly::powerpc::OPCODE::KILL_PAIR)
  .value("LAx", LIEF::assembly::powerpc::OPCODE::LAx)
  .value("LIWAX", LIEF::assembly::powerpc::OPCODE::LIWAX)
  .value("LIWZX", LIEF::assembly::powerpc::OPCODE::LIWZX)
  .value("PPCLdFixedAddr", LIEF::assembly::powerpc::OPCODE::PPCLdFixedAddr)
  .value("PSUBI", LIEF::assembly::powerpc::OPCODE::PSUBI)
  .value("RLWIMIbm", LIEF::assembly::powerpc::OPCODE::RLWIMIbm)
  .value("RLWIMIbm_rec", LIEF::assembly::powerpc::OPCODE::RLWIMIbm_rec)
  .value("RLWINMbm", LIEF::assembly::powerpc::OPCODE::RLWINMbm)
  .value("RLWINMbm_rec", LIEF::assembly::powerpc::OPCODE::RLWINMbm_rec)
  .value("RLWNMbm", LIEF::assembly::powerpc::OPCODE::RLWNMbm)
  .value("RLWNMbm_rec", LIEF::assembly::powerpc::OPCODE::RLWNMbm_rec)
  .value("ROTRDI", LIEF::assembly::powerpc::OPCODE::ROTRDI)
  .value("ROTRDI_rec", LIEF::assembly::powerpc::OPCODE::ROTRDI_rec)
  .value("ROTRWI", LIEF::assembly::powerpc::OPCODE::ROTRWI)
  .value("ROTRWI_rec", LIEF::assembly::powerpc::OPCODE::ROTRWI_rec)
  .value("SLDI", LIEF::assembly::powerpc::OPCODE::SLDI)
  .value("SLDI_rec", LIEF::assembly::powerpc::OPCODE::SLDI_rec)
  .value("SLWI", LIEF::assembly::powerpc::OPCODE::SLWI)
  .value("SLWI_rec", LIEF::assembly::powerpc::OPCODE::SLWI_rec)
  .value("SPILLTOVSR_LD", LIEF::assembly::powerpc::OPCODE::SPILLTOVSR_LD)
  .value("SPILLTOVSR_LDX", LIEF::assembly::powerpc::OPCODE::SPILLTOVSR_LDX)
  .value("SPILLTOVSR_ST", LIEF::assembly::powerpc::OPCODE::SPILLTOVSR_ST)
  .value("SPILLTOVSR_STX", LIEF::assembly::powerpc::OPCODE::SPILLTOVSR_STX)
  .value("SRDI", LIEF::assembly::powerpc::OPCODE::SRDI)
  .value("SRDI_rec", LIEF::assembly::powerpc::OPCODE::SRDI_rec)
  .value("SRWI", LIEF::assembly::powerpc::OPCODE::SRWI)
  .value("SRWI_rec", LIEF::assembly::powerpc::OPCODE::SRWI_rec)
  .value("STIWX", LIEF::assembly::powerpc::OPCODE::STIWX)
  .value("SUBI", LIEF::assembly::powerpc::OPCODE::SUBI)
  .value("SUBIC", LIEF::assembly::powerpc::OPCODE::SUBIC)
  .value("SUBIC_rec", LIEF::assembly::powerpc::OPCODE::SUBIC_rec)
  .value("SUBIS", LIEF::assembly::powerpc::OPCODE::SUBIS)
  .value("SUBPCIS", LIEF::assembly::powerpc::OPCODE::SUBPCIS)
  .value("XFLOADf32", LIEF::assembly::powerpc::OPCODE::XFLOADf32)
  .value("XFLOADf64", LIEF::assembly::powerpc::OPCODE::XFLOADf64)
  .value("XFSTOREf32", LIEF::assembly::powerpc::OPCODE::XFSTOREf32)
  .value("XFSTOREf64", LIEF::assembly::powerpc::OPCODE::XFSTOREf64)
  .value("ADD4", LIEF::assembly::powerpc::OPCODE::ADD4)
  .value("ADD4O", LIEF::assembly::powerpc::OPCODE::ADD4O)
  .value("ADD4O_rec", LIEF::assembly::powerpc::OPCODE::ADD4O_rec)
  .value("ADD4TLS", LIEF::assembly::powerpc::OPCODE::ADD4TLS)
  .value("ADD4_rec", LIEF::assembly::powerpc::OPCODE::ADD4_rec)
  .value("ADD8", LIEF::assembly::powerpc::OPCODE::ADD8)
  .value("ADD8O", LIEF::assembly::powerpc::OPCODE::ADD8O)
  .value("ADD8O_rec", LIEF::assembly::powerpc::OPCODE::ADD8O_rec)
  .value("ADD8TLS", LIEF::assembly::powerpc::OPCODE::ADD8TLS)
  .value("ADD8TLS_", LIEF::assembly::powerpc::OPCODE::ADD8TLS_)
  .value("ADD8_rec", LIEF::assembly::powerpc::OPCODE::ADD8_rec)
  .value("ADDC", LIEF::assembly::powerpc::OPCODE::ADDC)
  .value("ADDC8", LIEF::assembly::powerpc::OPCODE::ADDC8)
  .value("ADDC8O", LIEF::assembly::powerpc::OPCODE::ADDC8O)
  .value("ADDC8O_rec", LIEF::assembly::powerpc::OPCODE::ADDC8O_rec)
  .value("ADDC8_rec", LIEF::assembly::powerpc::OPCODE::ADDC8_rec)
  .value("ADDCO", LIEF::assembly::powerpc::OPCODE::ADDCO)
  .value("ADDCO_rec", LIEF::assembly::powerpc::OPCODE::ADDCO_rec)
  .value("ADDC_rec", LIEF::assembly::powerpc::OPCODE::ADDC_rec)
  .value("ADDE", LIEF::assembly::powerpc::OPCODE::ADDE)
  .value("ADDE8", LIEF::assembly::powerpc::OPCODE::ADDE8)
  .value("ADDE8O", LIEF::assembly::powerpc::OPCODE::ADDE8O)
  .value("ADDE8O_rec", LIEF::assembly::powerpc::OPCODE::ADDE8O_rec)
  .value("ADDE8_rec", LIEF::assembly::powerpc::OPCODE::ADDE8_rec)
  .value("ADDEO", LIEF::assembly::powerpc::OPCODE::ADDEO)
  .value("ADDEO_rec", LIEF::assembly::powerpc::OPCODE::ADDEO_rec)
  .value("ADDEX", LIEF::assembly::powerpc::OPCODE::ADDEX)
  .value("ADDEX8", LIEF::assembly::powerpc::OPCODE::ADDEX8)
  .value("ADDE_rec", LIEF::assembly::powerpc::OPCODE::ADDE_rec)
  .value("ADDG6S", LIEF::assembly::powerpc::OPCODE::ADDG6S)
  .value("ADDG6S8", LIEF::assembly::powerpc::OPCODE::ADDG6S8)
  .value("ADDI", LIEF::assembly::powerpc::OPCODE::ADDI)
  .value("ADDI8", LIEF::assembly::powerpc::OPCODE::ADDI8)
  .value("ADDIC", LIEF::assembly::powerpc::OPCODE::ADDIC)
  .value("ADDIC8", LIEF::assembly::powerpc::OPCODE::ADDIC8)
  .value("ADDIC_rec", LIEF::assembly::powerpc::OPCODE::ADDIC_rec)
  .value("ADDIS", LIEF::assembly::powerpc::OPCODE::ADDIS)
  .value("ADDIS8", LIEF::assembly::powerpc::OPCODE::ADDIS8)
  .value("ADDISdtprelHA", LIEF::assembly::powerpc::OPCODE::ADDISdtprelHA)
  .value("ADDISdtprelHA32", LIEF::assembly::powerpc::OPCODE::ADDISdtprelHA32)
  .value("ADDISgotTprelHA", LIEF::assembly::powerpc::OPCODE::ADDISgotTprelHA)
  .value("ADDIStlsgdHA", LIEF::assembly::powerpc::OPCODE::ADDIStlsgdHA)
  .value("ADDIStlsldHA", LIEF::assembly::powerpc::OPCODE::ADDIStlsldHA)
  .value("ADDIStocHA", LIEF::assembly::powerpc::OPCODE::ADDIStocHA)
  .value("ADDIStocHA8", LIEF::assembly::powerpc::OPCODE::ADDIStocHA8)
  .value("ADDIdtprelL", LIEF::assembly::powerpc::OPCODE::ADDIdtprelL)
  .value("ADDIdtprelL32", LIEF::assembly::powerpc::OPCODE::ADDIdtprelL32)
  .value("ADDItlsgdL", LIEF::assembly::powerpc::OPCODE::ADDItlsgdL)
  .value("ADDItlsgdL32", LIEF::assembly::powerpc::OPCODE::ADDItlsgdL32)
  .value("ADDItlsgdLADDR", LIEF::assembly::powerpc::OPCODE::ADDItlsgdLADDR)
  .value("ADDItlsgdLADDR32", LIEF::assembly::powerpc::OPCODE::ADDItlsgdLADDR32)
  .value("ADDItlsldL", LIEF::assembly::powerpc::OPCODE::ADDItlsldL)
  .value("ADDItlsldL32", LIEF::assembly::powerpc::OPCODE::ADDItlsldL32)
  .value("ADDItlsldLADDR", LIEF::assembly::powerpc::OPCODE::ADDItlsldLADDR)
  .value("ADDItlsldLADDR32", LIEF::assembly::powerpc::OPCODE::ADDItlsldLADDR32)
  .value("ADDItoc", LIEF::assembly::powerpc::OPCODE::ADDItoc)
  .value("ADDItoc8", LIEF::assembly::powerpc::OPCODE::ADDItoc8)
  .value("ADDItocL", LIEF::assembly::powerpc::OPCODE::ADDItocL)
  .value("ADDItocL8", LIEF::assembly::powerpc::OPCODE::ADDItocL8)
  .value("ADDME", LIEF::assembly::powerpc::OPCODE::ADDME)
  .value("ADDME8", LIEF::assembly::powerpc::OPCODE::ADDME8)
  .value("ADDME8O", LIEF::assembly::powerpc::OPCODE::ADDME8O)
  .value("ADDME8O_rec", LIEF::assembly::powerpc::OPCODE::ADDME8O_rec)
  .value("ADDME8_rec", LIEF::assembly::powerpc::OPCODE::ADDME8_rec)
  .value("ADDMEO", LIEF::assembly::powerpc::OPCODE::ADDMEO)
  .value("ADDMEO_rec", LIEF::assembly::powerpc::OPCODE::ADDMEO_rec)
  .value("ADDME_rec", LIEF::assembly::powerpc::OPCODE::ADDME_rec)
  .value("ADDPCIS", LIEF::assembly::powerpc::OPCODE::ADDPCIS)
  .value("ADDZE", LIEF::assembly::powerpc::OPCODE::ADDZE)
  .value("ADDZE8", LIEF::assembly::powerpc::OPCODE::ADDZE8)
  .value("ADDZE8O", LIEF::assembly::powerpc::OPCODE::ADDZE8O)
  .value("ADDZE8O_rec", LIEF::assembly::powerpc::OPCODE::ADDZE8O_rec)
  .value("ADDZE8_rec", LIEF::assembly::powerpc::OPCODE::ADDZE8_rec)
  .value("ADDZEO", LIEF::assembly::powerpc::OPCODE::ADDZEO)
  .value("ADDZEO_rec", LIEF::assembly::powerpc::OPCODE::ADDZEO_rec)
  .value("ADDZE_rec", LIEF::assembly::powerpc::OPCODE::ADDZE_rec)
  .value("ADJCALLSTACKDOWN", LIEF::assembly::powerpc::OPCODE::ADJCALLSTACKDOWN)
  .value("ADJCALLSTACKUP", LIEF::assembly::powerpc::OPCODE::ADJCALLSTACKUP)
  .value("AND", LIEF::assembly::powerpc::OPCODE::AND)
  .value("AND8", LIEF::assembly::powerpc::OPCODE::AND8)
  .value("AND8_rec", LIEF::assembly::powerpc::OPCODE::AND8_rec)
  .value("ANDC", LIEF::assembly::powerpc::OPCODE::ANDC)
  .value("ANDC8", LIEF::assembly::powerpc::OPCODE::ANDC8)
  .value("ANDC8_rec", LIEF::assembly::powerpc::OPCODE::ANDC8_rec)
  .value("ANDC_rec", LIEF::assembly::powerpc::OPCODE::ANDC_rec)
  .value("ANDI8_rec", LIEF::assembly::powerpc::OPCODE::ANDI8_rec)
  .value("ANDIS8_rec", LIEF::assembly::powerpc::OPCODE::ANDIS8_rec)
  .value("ANDIS_rec", LIEF::assembly::powerpc::OPCODE::ANDIS_rec)
  .value("ANDI_rec", LIEF::assembly::powerpc::OPCODE::ANDI_rec)
  .value("ANDI_rec_1_EQ_BIT", LIEF::assembly::powerpc::OPCODE::ANDI_rec_1_EQ_BIT)
  .value("ANDI_rec_1_EQ_BIT8", LIEF::assembly::powerpc::OPCODE::ANDI_rec_1_EQ_BIT8)
  .value("ANDI_rec_1_GT_BIT", LIEF::assembly::powerpc::OPCODE::ANDI_rec_1_GT_BIT)
  .value("ANDI_rec_1_GT_BIT8", LIEF::assembly::powerpc::OPCODE::ANDI_rec_1_GT_BIT8)
  .value("AND_rec", LIEF::assembly::powerpc::OPCODE::AND_rec)
  .value("ATOMIC_CMP_SWAP_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_CMP_SWAP_I16)
  .value("ATOMIC_CMP_SWAP_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_CMP_SWAP_I32)
  .value("ATOMIC_CMP_SWAP_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_CMP_SWAP_I64)
  .value("ATOMIC_CMP_SWAP_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_CMP_SWAP_I8)
  .value("ATOMIC_LOAD_ADD_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_ADD_I16)
  .value("ATOMIC_LOAD_ADD_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_ADD_I32)
  .value("ATOMIC_LOAD_ADD_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_ADD_I64)
  .value("ATOMIC_LOAD_ADD_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_ADD_I8)
  .value("ATOMIC_LOAD_AND_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_AND_I16)
  .value("ATOMIC_LOAD_AND_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_AND_I32)
  .value("ATOMIC_LOAD_AND_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_AND_I64)
  .value("ATOMIC_LOAD_AND_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_AND_I8)
  .value("ATOMIC_LOAD_MAX_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MAX_I16)
  .value("ATOMIC_LOAD_MAX_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MAX_I32)
  .value("ATOMIC_LOAD_MAX_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MAX_I64)
  .value("ATOMIC_LOAD_MAX_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MAX_I8)
  .value("ATOMIC_LOAD_MIN_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MIN_I16)
  .value("ATOMIC_LOAD_MIN_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MIN_I32)
  .value("ATOMIC_LOAD_MIN_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MIN_I64)
  .value("ATOMIC_LOAD_MIN_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_MIN_I8)
  .value("ATOMIC_LOAD_NAND_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_NAND_I16)
  .value("ATOMIC_LOAD_NAND_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_NAND_I32)
  .value("ATOMIC_LOAD_NAND_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_NAND_I64)
  .value("ATOMIC_LOAD_NAND_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_NAND_I8)
  .value("ATOMIC_LOAD_OR_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_OR_I16)
  .value("ATOMIC_LOAD_OR_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_OR_I32)
  .value("ATOMIC_LOAD_OR_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_OR_I64)
  .value("ATOMIC_LOAD_OR_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_OR_I8)
  .value("ATOMIC_LOAD_SUB_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_SUB_I16)
  .value("ATOMIC_LOAD_SUB_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_SUB_I32)
  .value("ATOMIC_LOAD_SUB_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_SUB_I64)
  .value("ATOMIC_LOAD_SUB_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_SUB_I8)
  .value("ATOMIC_LOAD_UMAX_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMAX_I16)
  .value("ATOMIC_LOAD_UMAX_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMAX_I32)
  .value("ATOMIC_LOAD_UMAX_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMAX_I64)
  .value("ATOMIC_LOAD_UMAX_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMAX_I8)
  .value("ATOMIC_LOAD_UMIN_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMIN_I16)
  .value("ATOMIC_LOAD_UMIN_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMIN_I32)
  .value("ATOMIC_LOAD_UMIN_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMIN_I64)
  .value("ATOMIC_LOAD_UMIN_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_UMIN_I8)
  .value("ATOMIC_LOAD_XOR_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_XOR_I16)
  .value("ATOMIC_LOAD_XOR_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_XOR_I32)
  .value("ATOMIC_LOAD_XOR_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_XOR_I64)
  .value("ATOMIC_LOAD_XOR_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_LOAD_XOR_I8)
  .value("ATOMIC_SWAP_I16", LIEF::assembly::powerpc::OPCODE::ATOMIC_SWAP_I16)
  .value("ATOMIC_SWAP_I32", LIEF::assembly::powerpc::OPCODE::ATOMIC_SWAP_I32)
  .value("ATOMIC_SWAP_I64", LIEF::assembly::powerpc::OPCODE::ATOMIC_SWAP_I64)
  .value("ATOMIC_SWAP_I8", LIEF::assembly::powerpc::OPCODE::ATOMIC_SWAP_I8)
  .value("ATTN", LIEF::assembly::powerpc::OPCODE::ATTN)
  .value("B", LIEF::assembly::powerpc::OPCODE::B)
  .value("BA", LIEF::assembly::powerpc::OPCODE::BA)
  .value("BC", LIEF::assembly::powerpc::OPCODE::BC)
  .value("BCC", LIEF::assembly::powerpc::OPCODE::BCC)
  .value("BCCA", LIEF::assembly::powerpc::OPCODE::BCCA)
  .value("BCCCTR", LIEF::assembly::powerpc::OPCODE::BCCCTR)
  .value("BCCCTR8", LIEF::assembly::powerpc::OPCODE::BCCCTR8)
  .value("BCCCTRL", LIEF::assembly::powerpc::OPCODE::BCCCTRL)
  .value("BCCCTRL8", LIEF::assembly::powerpc::OPCODE::BCCCTRL8)
  .value("BCCL", LIEF::assembly::powerpc::OPCODE::BCCL)
  .value("BCCLA", LIEF::assembly::powerpc::OPCODE::BCCLA)
  .value("BCCLR", LIEF::assembly::powerpc::OPCODE::BCCLR)
  .value("BCCLRL", LIEF::assembly::powerpc::OPCODE::BCCLRL)
  .value("BCCTR", LIEF::assembly::powerpc::OPCODE::BCCTR)
  .value("BCCTR8", LIEF::assembly::powerpc::OPCODE::BCCTR8)
  .value("BCCTR8n", LIEF::assembly::powerpc::OPCODE::BCCTR8n)
  .value("BCCTRL", LIEF::assembly::powerpc::OPCODE::BCCTRL)
  .value("BCCTRL8", LIEF::assembly::powerpc::OPCODE::BCCTRL8)
  .value("BCCTRL8n", LIEF::assembly::powerpc::OPCODE::BCCTRL8n)
  .value("BCCTRLn", LIEF::assembly::powerpc::OPCODE::BCCTRLn)
  .value("BCCTRn", LIEF::assembly::powerpc::OPCODE::BCCTRn)
  .value("BCDADD_rec", LIEF::assembly::powerpc::OPCODE::BCDADD_rec)
  .value("BCDCFN_rec", LIEF::assembly::powerpc::OPCODE::BCDCFN_rec)
  .value("BCDCFSQ_rec", LIEF::assembly::powerpc::OPCODE::BCDCFSQ_rec)
  .value("BCDCFZ_rec", LIEF::assembly::powerpc::OPCODE::BCDCFZ_rec)
  .value("BCDCPSGN_rec", LIEF::assembly::powerpc::OPCODE::BCDCPSGN_rec)
  .value("BCDCTN_rec", LIEF::assembly::powerpc::OPCODE::BCDCTN_rec)
  .value("BCDCTSQ_rec", LIEF::assembly::powerpc::OPCODE::BCDCTSQ_rec)
  .value("BCDCTZ_rec", LIEF::assembly::powerpc::OPCODE::BCDCTZ_rec)
  .value("BCDSETSGN_rec", LIEF::assembly::powerpc::OPCODE::BCDSETSGN_rec)
  .value("BCDSR_rec", LIEF::assembly::powerpc::OPCODE::BCDSR_rec)
  .value("BCDSUB_rec", LIEF::assembly::powerpc::OPCODE::BCDSUB_rec)
  .value("BCDS_rec", LIEF::assembly::powerpc::OPCODE::BCDS_rec)
  .value("BCDTRUNC_rec", LIEF::assembly::powerpc::OPCODE::BCDTRUNC_rec)
  .value("BCDUS_rec", LIEF::assembly::powerpc::OPCODE::BCDUS_rec)
  .value("BCDUTRUNC_rec", LIEF::assembly::powerpc::OPCODE::BCDUTRUNC_rec)
  .value("BCL", LIEF::assembly::powerpc::OPCODE::BCL)
  .value("BCLR", LIEF::assembly::powerpc::OPCODE::BCLR)
  .value("BCLRL", LIEF::assembly::powerpc::OPCODE::BCLRL)
  .value("BCLRLn", LIEF::assembly::powerpc::OPCODE::BCLRLn)
  .value("BCLRn", LIEF::assembly::powerpc::OPCODE::BCLRn)
  .value("BCLalways", LIEF::assembly::powerpc::OPCODE::BCLalways)
  .value("BCLn", LIEF::assembly::powerpc::OPCODE::BCLn)
  .value("BCTR", LIEF::assembly::powerpc::OPCODE::BCTR)
  .value("BCTR8", LIEF::assembly::powerpc::OPCODE::BCTR8)
  .value("BCTRL", LIEF::assembly::powerpc::OPCODE::BCTRL)
  .value("BCTRL8", LIEF::assembly::powerpc::OPCODE::BCTRL8)
  .value("BCTRL8_LDinto_toc", LIEF::assembly::powerpc::OPCODE::BCTRL8_LDinto_toc)
  .value("BCTRL8_LDinto_toc_RM", LIEF::assembly::powerpc::OPCODE::BCTRL8_LDinto_toc_RM)
  .value("BCTRL8_RM", LIEF::assembly::powerpc::OPCODE::BCTRL8_RM)
  .value("BCTRL_LWZinto_toc", LIEF::assembly::powerpc::OPCODE::BCTRL_LWZinto_toc)
  .value("BCTRL_LWZinto_toc_RM", LIEF::assembly::powerpc::OPCODE::BCTRL_LWZinto_toc_RM)
  .value("BCTRL_RM", LIEF::assembly::powerpc::OPCODE::BCTRL_RM)
  .value("BCn", LIEF::assembly::powerpc::OPCODE::BCn)
  .value("BDNZ", LIEF::assembly::powerpc::OPCODE::BDNZ)
  .value("BDNZ8", LIEF::assembly::powerpc::OPCODE::BDNZ8)
  .value("BDNZA", LIEF::assembly::powerpc::OPCODE::BDNZA)
  .value("BDNZAm", LIEF::assembly::powerpc::OPCODE::BDNZAm)
  .value("BDNZAp", LIEF::assembly::powerpc::OPCODE::BDNZAp)
  .value("BDNZL", LIEF::assembly::powerpc::OPCODE::BDNZL)
  .value("BDNZLA", LIEF::assembly::powerpc::OPCODE::BDNZLA)
  .value("BDNZLAm", LIEF::assembly::powerpc::OPCODE::BDNZLAm);
  opcodes.value("BDNZLAp", LIEF::assembly::powerpc::OPCODE::BDNZLAp)
  .value("BDNZLR", LIEF::assembly::powerpc::OPCODE::BDNZLR)
  .value("BDNZLR8", LIEF::assembly::powerpc::OPCODE::BDNZLR8)
  .value("BDNZLRL", LIEF::assembly::powerpc::OPCODE::BDNZLRL)
  .value("BDNZLRLm", LIEF::assembly::powerpc::OPCODE::BDNZLRLm)
  .value("BDNZLRLp", LIEF::assembly::powerpc::OPCODE::BDNZLRLp)
  .value("BDNZLRm", LIEF::assembly::powerpc::OPCODE::BDNZLRm)
  .value("BDNZLRp", LIEF::assembly::powerpc::OPCODE::BDNZLRp)
  .value("BDNZLm", LIEF::assembly::powerpc::OPCODE::BDNZLm)
  .value("BDNZLp", LIEF::assembly::powerpc::OPCODE::BDNZLp)
  .value("BDNZm", LIEF::assembly::powerpc::OPCODE::BDNZm)
  .value("BDNZp", LIEF::assembly::powerpc::OPCODE::BDNZp)
  .value("BDZ", LIEF::assembly::powerpc::OPCODE::BDZ)
  .value("BDZ8", LIEF::assembly::powerpc::OPCODE::BDZ8)
  .value("BDZA", LIEF::assembly::powerpc::OPCODE::BDZA)
  .value("BDZAm", LIEF::assembly::powerpc::OPCODE::BDZAm)
  .value("BDZAp", LIEF::assembly::powerpc::OPCODE::BDZAp)
  .value("BDZL", LIEF::assembly::powerpc::OPCODE::BDZL)
  .value("BDZLA", LIEF::assembly::powerpc::OPCODE::BDZLA)
  .value("BDZLAm", LIEF::assembly::powerpc::OPCODE::BDZLAm)
  .value("BDZLAp", LIEF::assembly::powerpc::OPCODE::BDZLAp)
  .value("BDZLR", LIEF::assembly::powerpc::OPCODE::BDZLR)
  .value("BDZLR8", LIEF::assembly::powerpc::OPCODE::BDZLR8)
  .value("BDZLRL", LIEF::assembly::powerpc::OPCODE::BDZLRL)
  .value("BDZLRLm", LIEF::assembly::powerpc::OPCODE::BDZLRLm)
  .value("BDZLRLp", LIEF::assembly::powerpc::OPCODE::BDZLRLp)
  .value("BDZLRm", LIEF::assembly::powerpc::OPCODE::BDZLRm)
  .value("BDZLRp", LIEF::assembly::powerpc::OPCODE::BDZLRp)
  .value("BDZLm", LIEF::assembly::powerpc::OPCODE::BDZLm)
  .value("BDZLp", LIEF::assembly::powerpc::OPCODE::BDZLp)
  .value("BDZm", LIEF::assembly::powerpc::OPCODE::BDZm)
  .value("BDZp", LIEF::assembly::powerpc::OPCODE::BDZp)
  .value("BL", LIEF::assembly::powerpc::OPCODE::BL)
  .value("BL8", LIEF::assembly::powerpc::OPCODE::BL8)
  .value("BL8_NOP", LIEF::assembly::powerpc::OPCODE::BL8_NOP)
  .value("BL8_NOP_RM", LIEF::assembly::powerpc::OPCODE::BL8_NOP_RM)
  .value("BL8_NOP_TLS", LIEF::assembly::powerpc::OPCODE::BL8_NOP_TLS)
  .value("BL8_NOTOC", LIEF::assembly::powerpc::OPCODE::BL8_NOTOC)
  .value("BL8_NOTOC_RM", LIEF::assembly::powerpc::OPCODE::BL8_NOTOC_RM)
  .value("BL8_NOTOC_TLS", LIEF::assembly::powerpc::OPCODE::BL8_NOTOC_TLS)
  .value("BL8_RM", LIEF::assembly::powerpc::OPCODE::BL8_RM)
  .value("BL8_TLS", LIEF::assembly::powerpc::OPCODE::BL8_TLS)
  .value("BL8_TLS_", LIEF::assembly::powerpc::OPCODE::BL8_TLS_)
  .value("BLA", LIEF::assembly::powerpc::OPCODE::BLA)
  .value("BLA8", LIEF::assembly::powerpc::OPCODE::BLA8)
  .value("BLA8_NOP", LIEF::assembly::powerpc::OPCODE::BLA8_NOP)
  .value("BLA8_NOP_RM", LIEF::assembly::powerpc::OPCODE::BLA8_NOP_RM)
  .value("BLA8_RM", LIEF::assembly::powerpc::OPCODE::BLA8_RM)
  .value("BLA_RM", LIEF::assembly::powerpc::OPCODE::BLA_RM)
  .value("BLR", LIEF::assembly::powerpc::OPCODE::BLR)
  .value("BLR8", LIEF::assembly::powerpc::OPCODE::BLR8)
  .value("BLRL", LIEF::assembly::powerpc::OPCODE::BLRL)
  .value("BL_NOP", LIEF::assembly::powerpc::OPCODE::BL_NOP)
  .value("BL_NOP_RM", LIEF::assembly::powerpc::OPCODE::BL_NOP_RM)
  .value("BL_RM", LIEF::assembly::powerpc::OPCODE::BL_RM)
  .value("BL_TLS", LIEF::assembly::powerpc::OPCODE::BL_TLS)
  .value("BPERMD", LIEF::assembly::powerpc::OPCODE::BPERMD)
  .value("BRD", LIEF::assembly::powerpc::OPCODE::BRD)
  .value("BRH", LIEF::assembly::powerpc::OPCODE::BRH)
  .value("BRH8", LIEF::assembly::powerpc::OPCODE::BRH8)
  .value("BRINC", LIEF::assembly::powerpc::OPCODE::BRINC)
  .value("BRW", LIEF::assembly::powerpc::OPCODE::BRW)
  .value("BRW8", LIEF::assembly::powerpc::OPCODE::BRW8)
  .value("CBCDTD", LIEF::assembly::powerpc::OPCODE::CBCDTD)
  .value("CBCDTD8", LIEF::assembly::powerpc::OPCODE::CBCDTD8)
  .value("CDTBCD", LIEF::assembly::powerpc::OPCODE::CDTBCD)
  .value("CDTBCD8", LIEF::assembly::powerpc::OPCODE::CDTBCD8)
  .value("CFUGED", LIEF::assembly::powerpc::OPCODE::CFUGED)
  .value("CLRBHRB", LIEF::assembly::powerpc::OPCODE::CLRBHRB)
  .value("CMPB", LIEF::assembly::powerpc::OPCODE::CMPB)
  .value("CMPB8", LIEF::assembly::powerpc::OPCODE::CMPB8)
  .value("CMPD", LIEF::assembly::powerpc::OPCODE::CMPD)
  .value("CMPDI", LIEF::assembly::powerpc::OPCODE::CMPDI)
  .value("CMPEQB", LIEF::assembly::powerpc::OPCODE::CMPEQB)
  .value("CMPLD", LIEF::assembly::powerpc::OPCODE::CMPLD)
  .value("CMPLDI", LIEF::assembly::powerpc::OPCODE::CMPLDI)
  .value("CMPLW", LIEF::assembly::powerpc::OPCODE::CMPLW)
  .value("CMPLWI", LIEF::assembly::powerpc::OPCODE::CMPLWI)
  .value("CMPRB", LIEF::assembly::powerpc::OPCODE::CMPRB)
  .value("CMPRB8", LIEF::assembly::powerpc::OPCODE::CMPRB8)
  .value("CMPW", LIEF::assembly::powerpc::OPCODE::CMPW)
  .value("CMPWI", LIEF::assembly::powerpc::OPCODE::CMPWI)
  .value("CNTLZD", LIEF::assembly::powerpc::OPCODE::CNTLZD)
  .value("CNTLZDM", LIEF::assembly::powerpc::OPCODE::CNTLZDM)
  .value("CNTLZD_rec", LIEF::assembly::powerpc::OPCODE::CNTLZD_rec)
  .value("CNTLZW", LIEF::assembly::powerpc::OPCODE::CNTLZW)
  .value("CNTLZW8", LIEF::assembly::powerpc::OPCODE::CNTLZW8)
  .value("CNTLZW8_rec", LIEF::assembly::powerpc::OPCODE::CNTLZW8_rec)
  .value("CNTLZW_rec", LIEF::assembly::powerpc::OPCODE::CNTLZW_rec)
  .value("CNTTZD", LIEF::assembly::powerpc::OPCODE::CNTTZD)
  .value("CNTTZDM", LIEF::assembly::powerpc::OPCODE::CNTTZDM)
  .value("CNTTZD_rec", LIEF::assembly::powerpc::OPCODE::CNTTZD_rec)
  .value("CNTTZW", LIEF::assembly::powerpc::OPCODE::CNTTZW)
  .value("CNTTZW8", LIEF::assembly::powerpc::OPCODE::CNTTZW8)
  .value("CNTTZW8_rec", LIEF::assembly::powerpc::OPCODE::CNTTZW8_rec)
  .value("CNTTZW_rec", LIEF::assembly::powerpc::OPCODE::CNTTZW_rec)
  .value("CP_ABORT", LIEF::assembly::powerpc::OPCODE::CP_ABORT)
  .value("CP_COPY", LIEF::assembly::powerpc::OPCODE::CP_COPY)
  .value("CP_COPY8", LIEF::assembly::powerpc::OPCODE::CP_COPY8)
  .value("CP_PASTE8_rec", LIEF::assembly::powerpc::OPCODE::CP_PASTE8_rec)
  .value("CP_PASTE_rec", LIEF::assembly::powerpc::OPCODE::CP_PASTE_rec)
  .value("CR6SET", LIEF::assembly::powerpc::OPCODE::CR6SET)
  .value("CR6UNSET", LIEF::assembly::powerpc::OPCODE::CR6UNSET)
  .value("CRAND", LIEF::assembly::powerpc::OPCODE::CRAND)
  .value("CRANDC", LIEF::assembly::powerpc::OPCODE::CRANDC)
  .value("CREQV", LIEF::assembly::powerpc::OPCODE::CREQV)
  .value("CRNAND", LIEF::assembly::powerpc::OPCODE::CRNAND)
  .value("CRNOR", LIEF::assembly::powerpc::OPCODE::CRNOR)
  .value("CRNOT", LIEF::assembly::powerpc::OPCODE::CRNOT)
  .value("CROR", LIEF::assembly::powerpc::OPCODE::CROR)
  .value("CRORC", LIEF::assembly::powerpc::OPCODE::CRORC)
  .value("CRSET", LIEF::assembly::powerpc::OPCODE::CRSET)
  .value("CRUNSET", LIEF::assembly::powerpc::OPCODE::CRUNSET)
  .value("CRXOR", LIEF::assembly::powerpc::OPCODE::CRXOR)
  .value("CTRL_DEP", LIEF::assembly::powerpc::OPCODE::CTRL_DEP)
  .value("DADD", LIEF::assembly::powerpc::OPCODE::DADD)
  .value("DADDQ", LIEF::assembly::powerpc::OPCODE::DADDQ)
  .value("DADDQ_rec", LIEF::assembly::powerpc::OPCODE::DADDQ_rec)
  .value("DADD_rec", LIEF::assembly::powerpc::OPCODE::DADD_rec)
  .value("DARN", LIEF::assembly::powerpc::OPCODE::DARN)
  .value("DCBA", LIEF::assembly::powerpc::OPCODE::DCBA)
  .value("DCBF", LIEF::assembly::powerpc::OPCODE::DCBF)
  .value("DCBFEP", LIEF::assembly::powerpc::OPCODE::DCBFEP)
  .value("DCBI", LIEF::assembly::powerpc::OPCODE::DCBI)
  .value("DCBST", LIEF::assembly::powerpc::OPCODE::DCBST)
  .value("DCBSTEP", LIEF::assembly::powerpc::OPCODE::DCBSTEP)
  .value("DCBT", LIEF::assembly::powerpc::OPCODE::DCBT)
  .value("DCBTEP", LIEF::assembly::powerpc::OPCODE::DCBTEP)
  .value("DCBTST", LIEF::assembly::powerpc::OPCODE::DCBTST)
  .value("DCBTSTEP", LIEF::assembly::powerpc::OPCODE::DCBTSTEP)
  .value("DCBZ", LIEF::assembly::powerpc::OPCODE::DCBZ)
  .value("DCBZEP", LIEF::assembly::powerpc::OPCODE::DCBZEP)
  .value("DCBZL", LIEF::assembly::powerpc::OPCODE::DCBZL)
  .value("DCBZLEP", LIEF::assembly::powerpc::OPCODE::DCBZLEP)
  .value("DCCCI", LIEF::assembly::powerpc::OPCODE::DCCCI)
  .value("DCFFIX", LIEF::assembly::powerpc::OPCODE::DCFFIX)
  .value("DCFFIXQ", LIEF::assembly::powerpc::OPCODE::DCFFIXQ)
  .value("DCFFIXQQ", LIEF::assembly::powerpc::OPCODE::DCFFIXQQ)
  .value("DCFFIXQ_rec", LIEF::assembly::powerpc::OPCODE::DCFFIXQ_rec)
  .value("DCFFIX_rec", LIEF::assembly::powerpc::OPCODE::DCFFIX_rec)
  .value("DCMPO", LIEF::assembly::powerpc::OPCODE::DCMPO)
  .value("DCMPOQ", LIEF::assembly::powerpc::OPCODE::DCMPOQ)
  .value("DCMPU", LIEF::assembly::powerpc::OPCODE::DCMPU)
  .value("DCMPUQ", LIEF::assembly::powerpc::OPCODE::DCMPUQ)
  .value("DCTDP", LIEF::assembly::powerpc::OPCODE::DCTDP)
  .value("DCTDP_rec", LIEF::assembly::powerpc::OPCODE::DCTDP_rec)
  .value("DCTFIX", LIEF::assembly::powerpc::OPCODE::DCTFIX)
  .value("DCTFIXQ", LIEF::assembly::powerpc::OPCODE::DCTFIXQ)
  .value("DCTFIXQQ", LIEF::assembly::powerpc::OPCODE::DCTFIXQQ)
  .value("DCTFIXQ_rec", LIEF::assembly::powerpc::OPCODE::DCTFIXQ_rec)
  .value("DCTFIX_rec", LIEF::assembly::powerpc::OPCODE::DCTFIX_rec)
  .value("DCTQPQ", LIEF::assembly::powerpc::OPCODE::DCTQPQ)
  .value("DCTQPQ_rec", LIEF::assembly::powerpc::OPCODE::DCTQPQ_rec)
  .value("DDEDPD", LIEF::assembly::powerpc::OPCODE::DDEDPD)
  .value("DDEDPDQ", LIEF::assembly::powerpc::OPCODE::DDEDPDQ)
  .value("DDEDPDQ_rec", LIEF::assembly::powerpc::OPCODE::DDEDPDQ_rec)
  .value("DDEDPD_rec", LIEF::assembly::powerpc::OPCODE::DDEDPD_rec)
  .value("DDIV", LIEF::assembly::powerpc::OPCODE::DDIV)
  .value("DDIVQ", LIEF::assembly::powerpc::OPCODE::DDIVQ)
  .value("DDIVQ_rec", LIEF::assembly::powerpc::OPCODE::DDIVQ_rec)
  .value("DDIV_rec", LIEF::assembly::powerpc::OPCODE::DDIV_rec)
  .value("DENBCD", LIEF::assembly::powerpc::OPCODE::DENBCD)
  .value("DENBCDQ", LIEF::assembly::powerpc::OPCODE::DENBCDQ)
  .value("DENBCDQ_rec", LIEF::assembly::powerpc::OPCODE::DENBCDQ_rec)
  .value("DENBCD_rec", LIEF::assembly::powerpc::OPCODE::DENBCD_rec)
  .value("DIEX", LIEF::assembly::powerpc::OPCODE::DIEX)
  .value("DIEXQ", LIEF::assembly::powerpc::OPCODE::DIEXQ)
  .value("DIEXQ_rec", LIEF::assembly::powerpc::OPCODE::DIEXQ_rec)
  .value("DIEX_rec", LIEF::assembly::powerpc::OPCODE::DIEX_rec)
  .value("DIVD", LIEF::assembly::powerpc::OPCODE::DIVD)
  .value("DIVDE", LIEF::assembly::powerpc::OPCODE::DIVDE)
  .value("DIVDEO", LIEF::assembly::powerpc::OPCODE::DIVDEO)
  .value("DIVDEO_rec", LIEF::assembly::powerpc::OPCODE::DIVDEO_rec)
  .value("DIVDEU", LIEF::assembly::powerpc::OPCODE::DIVDEU)
  .value("DIVDEUO", LIEF::assembly::powerpc::OPCODE::DIVDEUO)
  .value("DIVDEUO_rec", LIEF::assembly::powerpc::OPCODE::DIVDEUO_rec)
  .value("DIVDEU_rec", LIEF::assembly::powerpc::OPCODE::DIVDEU_rec)
  .value("DIVDE_rec", LIEF::assembly::powerpc::OPCODE::DIVDE_rec)
  .value("DIVDO", LIEF::assembly::powerpc::OPCODE::DIVDO)
  .value("DIVDO_rec", LIEF::assembly::powerpc::OPCODE::DIVDO_rec)
  .value("DIVDU", LIEF::assembly::powerpc::OPCODE::DIVDU)
  .value("DIVDUO", LIEF::assembly::powerpc::OPCODE::DIVDUO)
  .value("DIVDUO_rec", LIEF::assembly::powerpc::OPCODE::DIVDUO_rec)
  .value("DIVDU_rec", LIEF::assembly::powerpc::OPCODE::DIVDU_rec)
  .value("DIVD_rec", LIEF::assembly::powerpc::OPCODE::DIVD_rec)
  .value("DIVW", LIEF::assembly::powerpc::OPCODE::DIVW)
  .value("DIVWE", LIEF::assembly::powerpc::OPCODE::DIVWE)
  .value("DIVWEO", LIEF::assembly::powerpc::OPCODE::DIVWEO)
  .value("DIVWEO_rec", LIEF::assembly::powerpc::OPCODE::DIVWEO_rec)
  .value("DIVWEU", LIEF::assembly::powerpc::OPCODE::DIVWEU)
  .value("DIVWEUO", LIEF::assembly::powerpc::OPCODE::DIVWEUO)
  .value("DIVWEUO_rec", LIEF::assembly::powerpc::OPCODE::DIVWEUO_rec)
  .value("DIVWEU_rec", LIEF::assembly::powerpc::OPCODE::DIVWEU_rec)
  .value("DIVWE_rec", LIEF::assembly::powerpc::OPCODE::DIVWE_rec)
  .value("DIVWO", LIEF::assembly::powerpc::OPCODE::DIVWO)
  .value("DIVWO_rec", LIEF::assembly::powerpc::OPCODE::DIVWO_rec)
  .value("DIVWU", LIEF::assembly::powerpc::OPCODE::DIVWU)
  .value("DIVWUO", LIEF::assembly::powerpc::OPCODE::DIVWUO)
  .value("DIVWUO_rec", LIEF::assembly::powerpc::OPCODE::DIVWUO_rec)
  .value("DIVWU_rec", LIEF::assembly::powerpc::OPCODE::DIVWU_rec)
  .value("DIVW_rec", LIEF::assembly::powerpc::OPCODE::DIVW_rec)
  .value("DMMR", LIEF::assembly::powerpc::OPCODE::DMMR)
  .value("DMSETDMRZ", LIEF::assembly::powerpc::OPCODE::DMSETDMRZ)
  .value("DMUL", LIEF::assembly::powerpc::OPCODE::DMUL)
  .value("DMULQ", LIEF::assembly::powerpc::OPCODE::DMULQ)
  .value("DMULQ_rec", LIEF::assembly::powerpc::OPCODE::DMULQ_rec)
  .value("DMUL_rec", LIEF::assembly::powerpc::OPCODE::DMUL_rec)
  .value("DMXOR", LIEF::assembly::powerpc::OPCODE::DMXOR)
  .value("DMXXEXTFDMR256", LIEF::assembly::powerpc::OPCODE::DMXXEXTFDMR256)
  .value("DMXXEXTFDMR512", LIEF::assembly::powerpc::OPCODE::DMXXEXTFDMR512)
  .value("DMXXEXTFDMR512_HI", LIEF::assembly::powerpc::OPCODE::DMXXEXTFDMR512_HI)
  .value("DMXXINSTFDMR256", LIEF::assembly::powerpc::OPCODE::DMXXINSTFDMR256)
  .value("DMXXINSTFDMR512", LIEF::assembly::powerpc::OPCODE::DMXXINSTFDMR512)
  .value("DMXXINSTFDMR512_HI", LIEF::assembly::powerpc::OPCODE::DMXXINSTFDMR512_HI)
  .value("DQUA", LIEF::assembly::powerpc::OPCODE::DQUA)
  .value("DQUAI", LIEF::assembly::powerpc::OPCODE::DQUAI)
  .value("DQUAIQ", LIEF::assembly::powerpc::OPCODE::DQUAIQ)
  .value("DQUAIQ_rec", LIEF::assembly::powerpc::OPCODE::DQUAIQ_rec)
  .value("DQUAI_rec", LIEF::assembly::powerpc::OPCODE::DQUAI_rec)
  .value("DQUAQ", LIEF::assembly::powerpc::OPCODE::DQUAQ)
  .value("DQUAQ_rec", LIEF::assembly::powerpc::OPCODE::DQUAQ_rec)
  .value("DQUA_rec", LIEF::assembly::powerpc::OPCODE::DQUA_rec)
  .value("DRDPQ", LIEF::assembly::powerpc::OPCODE::DRDPQ)
  .value("DRDPQ_rec", LIEF::assembly::powerpc::OPCODE::DRDPQ_rec)
  .value("DRINTN", LIEF::assembly::powerpc::OPCODE::DRINTN)
  .value("DRINTNQ", LIEF::assembly::powerpc::OPCODE::DRINTNQ)
  .value("DRINTNQ_rec", LIEF::assembly::powerpc::OPCODE::DRINTNQ_rec)
  .value("DRINTN_rec", LIEF::assembly::powerpc::OPCODE::DRINTN_rec)
  .value("DRINTX", LIEF::assembly::powerpc::OPCODE::DRINTX)
  .value("DRINTXQ", LIEF::assembly::powerpc::OPCODE::DRINTXQ)
  .value("DRINTXQ_rec", LIEF::assembly::powerpc::OPCODE::DRINTXQ_rec)
  .value("DRINTX_rec", LIEF::assembly::powerpc::OPCODE::DRINTX_rec)
  .value("DRRND", LIEF::assembly::powerpc::OPCODE::DRRND)
  .value("DRRNDQ", LIEF::assembly::powerpc::OPCODE::DRRNDQ)
  .value("DRRNDQ_rec", LIEF::assembly::powerpc::OPCODE::DRRNDQ_rec)
  .value("DRRND_rec", LIEF::assembly::powerpc::OPCODE::DRRND_rec)
  .value("DRSP", LIEF::assembly::powerpc::OPCODE::DRSP)
  .value("DRSP_rec", LIEF::assembly::powerpc::OPCODE::DRSP_rec)
  .value("DSCLI", LIEF::assembly::powerpc::OPCODE::DSCLI)
  .value("DSCLIQ", LIEF::assembly::powerpc::OPCODE::DSCLIQ)
  .value("DSCLIQ_rec", LIEF::assembly::powerpc::OPCODE::DSCLIQ_rec)
  .value("DSCLI_rec", LIEF::assembly::powerpc::OPCODE::DSCLI_rec)
  .value("DSCRI", LIEF::assembly::powerpc::OPCODE::DSCRI)
  .value("DSCRIQ", LIEF::assembly::powerpc::OPCODE::DSCRIQ)
  .value("DSCRIQ_rec", LIEF::assembly::powerpc::OPCODE::DSCRIQ_rec)
  .value("DSCRI_rec", LIEF::assembly::powerpc::OPCODE::DSCRI_rec)
  .value("DSS", LIEF::assembly::powerpc::OPCODE::DSS)
  .value("DSSALL", LIEF::assembly::powerpc::OPCODE::DSSALL)
  .value("DST", LIEF::assembly::powerpc::OPCODE::DST)
  .value("DST64", LIEF::assembly::powerpc::OPCODE::DST64)
  .value("DSTST", LIEF::assembly::powerpc::OPCODE::DSTST)
  .value("DSTST64", LIEF::assembly::powerpc::OPCODE::DSTST64)
  .value("DSTSTT", LIEF::assembly::powerpc::OPCODE::DSTSTT)
  .value("DSTSTT64", LIEF::assembly::powerpc::OPCODE::DSTSTT64)
  .value("DSTT", LIEF::assembly::powerpc::OPCODE::DSTT)
  .value("DSTT64", LIEF::assembly::powerpc::OPCODE::DSTT64)
  .value("DSUB", LIEF::assembly::powerpc::OPCODE::DSUB)
  .value("DSUBQ", LIEF::assembly::powerpc::OPCODE::DSUBQ)
  .value("DSUBQ_rec", LIEF::assembly::powerpc::OPCODE::DSUBQ_rec)
  .value("DSUB_rec", LIEF::assembly::powerpc::OPCODE::DSUB_rec)
  .value("DTSTDC", LIEF::assembly::powerpc::OPCODE::DTSTDC)
  .value("DTSTDCQ", LIEF::assembly::powerpc::OPCODE::DTSTDCQ)
  .value("DTSTDG", LIEF::assembly::powerpc::OPCODE::DTSTDG)
  .value("DTSTDGQ", LIEF::assembly::powerpc::OPCODE::DTSTDGQ)
  .value("DTSTEX", LIEF::assembly::powerpc::OPCODE::DTSTEX)
  .value("DTSTEXQ", LIEF::assembly::powerpc::OPCODE::DTSTEXQ)
  .value("DTSTSF", LIEF::assembly::powerpc::OPCODE::DTSTSF)
  .value("DTSTSFI", LIEF::assembly::powerpc::OPCODE::DTSTSFI)
  .value("DTSTSFIQ", LIEF::assembly::powerpc::OPCODE::DTSTSFIQ)
  .value("DTSTSFQ", LIEF::assembly::powerpc::OPCODE::DTSTSFQ)
  .value("DXEX", LIEF::assembly::powerpc::OPCODE::DXEX)
  .value("DXEXQ", LIEF::assembly::powerpc::OPCODE::DXEXQ)
  .value("DXEXQ_rec", LIEF::assembly::powerpc::OPCODE::DXEXQ_rec)
  .value("DXEX_rec", LIEF::assembly::powerpc::OPCODE::DXEX_rec)
  .value("DYNALLOC", LIEF::assembly::powerpc::OPCODE::DYNALLOC)
  .value("DYNALLOC8", LIEF::assembly::powerpc::OPCODE::DYNALLOC8)
  .value("DYNAREAOFFSET", LIEF::assembly::powerpc::OPCODE::DYNAREAOFFSET)
  .value("DYNAREAOFFSET8", LIEF::assembly::powerpc::OPCODE::DYNAREAOFFSET8)
  .value("DecreaseCTR8loop", LIEF::assembly::powerpc::OPCODE::DecreaseCTR8loop)
  .value("DecreaseCTRloop", LIEF::assembly::powerpc::OPCODE::DecreaseCTRloop)
  .value("EFDABS", LIEF::assembly::powerpc::OPCODE::EFDABS)
  .value("EFDADD", LIEF::assembly::powerpc::OPCODE::EFDADD)
  .value("EFDCFS", LIEF::assembly::powerpc::OPCODE::EFDCFS)
  .value("EFDCFSF", LIEF::assembly::powerpc::OPCODE::EFDCFSF)
  .value("EFDCFSI", LIEF::assembly::powerpc::OPCODE::EFDCFSI)
  .value("EFDCFSID", LIEF::assembly::powerpc::OPCODE::EFDCFSID)
  .value("EFDCFUF", LIEF::assembly::powerpc::OPCODE::EFDCFUF)
  .value("EFDCFUI", LIEF::assembly::powerpc::OPCODE::EFDCFUI)
  .value("EFDCFUID", LIEF::assembly::powerpc::OPCODE::EFDCFUID)
  .value("EFDCMPEQ", LIEF::assembly::powerpc::OPCODE::EFDCMPEQ)
  .value("EFDCMPGT", LIEF::assembly::powerpc::OPCODE::EFDCMPGT)
  .value("EFDCMPLT", LIEF::assembly::powerpc::OPCODE::EFDCMPLT)
  .value("EFDCTSF", LIEF::assembly::powerpc::OPCODE::EFDCTSF)
  .value("EFDCTSI", LIEF::assembly::powerpc::OPCODE::EFDCTSI)
  .value("EFDCTSIDZ", LIEF::assembly::powerpc::OPCODE::EFDCTSIDZ)
  .value("EFDCTSIZ", LIEF::assembly::powerpc::OPCODE::EFDCTSIZ)
  .value("EFDCTUF", LIEF::assembly::powerpc::OPCODE::EFDCTUF)
  .value("EFDCTUI", LIEF::assembly::powerpc::OPCODE::EFDCTUI)
  .value("EFDCTUIDZ", LIEF::assembly::powerpc::OPCODE::EFDCTUIDZ)
  .value("EFDCTUIZ", LIEF::assembly::powerpc::OPCODE::EFDCTUIZ);
  opcodes.value("EFDDIV", LIEF::assembly::powerpc::OPCODE::EFDDIV)
  .value("EFDMUL", LIEF::assembly::powerpc::OPCODE::EFDMUL)
  .value("EFDNABS", LIEF::assembly::powerpc::OPCODE::EFDNABS)
  .value("EFDNEG", LIEF::assembly::powerpc::OPCODE::EFDNEG)
  .value("EFDSUB", LIEF::assembly::powerpc::OPCODE::EFDSUB)
  .value("EFDTSTEQ", LIEF::assembly::powerpc::OPCODE::EFDTSTEQ)
  .value("EFDTSTGT", LIEF::assembly::powerpc::OPCODE::EFDTSTGT)
  .value("EFDTSTLT", LIEF::assembly::powerpc::OPCODE::EFDTSTLT)
  .value("EFSABS", LIEF::assembly::powerpc::OPCODE::EFSABS)
  .value("EFSADD", LIEF::assembly::powerpc::OPCODE::EFSADD)
  .value("EFSCFD", LIEF::assembly::powerpc::OPCODE::EFSCFD)
  .value("EFSCFSF", LIEF::assembly::powerpc::OPCODE::EFSCFSF)
  .value("EFSCFSI", LIEF::assembly::powerpc::OPCODE::EFSCFSI)
  .value("EFSCFUF", LIEF::assembly::powerpc::OPCODE::EFSCFUF)
  .value("EFSCFUI", LIEF::assembly::powerpc::OPCODE::EFSCFUI)
  .value("EFSCMPEQ", LIEF::assembly::powerpc::OPCODE::EFSCMPEQ)
  .value("EFSCMPGT", LIEF::assembly::powerpc::OPCODE::EFSCMPGT)
  .value("EFSCMPLT", LIEF::assembly::powerpc::OPCODE::EFSCMPLT)
  .value("EFSCTSF", LIEF::assembly::powerpc::OPCODE::EFSCTSF)
  .value("EFSCTSI", LIEF::assembly::powerpc::OPCODE::EFSCTSI)
  .value("EFSCTSIZ", LIEF::assembly::powerpc::OPCODE::EFSCTSIZ)
  .value("EFSCTUF", LIEF::assembly::powerpc::OPCODE::EFSCTUF)
  .value("EFSCTUI", LIEF::assembly::powerpc::OPCODE::EFSCTUI)
  .value("EFSCTUIZ", LIEF::assembly::powerpc::OPCODE::EFSCTUIZ)
  .value("EFSDIV", LIEF::assembly::powerpc::OPCODE::EFSDIV)
  .value("EFSMUL", LIEF::assembly::powerpc::OPCODE::EFSMUL)
  .value("EFSNABS", LIEF::assembly::powerpc::OPCODE::EFSNABS)
  .value("EFSNEG", LIEF::assembly::powerpc::OPCODE::EFSNEG)
  .value("EFSSUB", LIEF::assembly::powerpc::OPCODE::EFSSUB)
  .value("EFSTSTEQ", LIEF::assembly::powerpc::OPCODE::EFSTSTEQ)
  .value("EFSTSTGT", LIEF::assembly::powerpc::OPCODE::EFSTSTGT)
  .value("EFSTSTLT", LIEF::assembly::powerpc::OPCODE::EFSTSTLT)
  .value("EH_SjLj_LongJmp32", LIEF::assembly::powerpc::OPCODE::EH_SjLj_LongJmp32)
  .value("EH_SjLj_LongJmp64", LIEF::assembly::powerpc::OPCODE::EH_SjLj_LongJmp64)
  .value("EH_SjLj_SetJmp32", LIEF::assembly::powerpc::OPCODE::EH_SjLj_SetJmp32)
  .value("EH_SjLj_SetJmp64", LIEF::assembly::powerpc::OPCODE::EH_SjLj_SetJmp64)
  .value("EH_SjLj_Setup", LIEF::assembly::powerpc::OPCODE::EH_SjLj_Setup)
  .value("EQV", LIEF::assembly::powerpc::OPCODE::EQV)
  .value("EQV8", LIEF::assembly::powerpc::OPCODE::EQV8)
  .value("EQV8_rec", LIEF::assembly::powerpc::OPCODE::EQV8_rec)
  .value("EQV_rec", LIEF::assembly::powerpc::OPCODE::EQV_rec)
  .value("EVABS", LIEF::assembly::powerpc::OPCODE::EVABS)
  .value("EVADDIW", LIEF::assembly::powerpc::OPCODE::EVADDIW)
  .value("EVADDSMIAAW", LIEF::assembly::powerpc::OPCODE::EVADDSMIAAW)
  .value("EVADDSSIAAW", LIEF::assembly::powerpc::OPCODE::EVADDSSIAAW)
  .value("EVADDUMIAAW", LIEF::assembly::powerpc::OPCODE::EVADDUMIAAW)
  .value("EVADDUSIAAW", LIEF::assembly::powerpc::OPCODE::EVADDUSIAAW)
  .value("EVADDW", LIEF::assembly::powerpc::OPCODE::EVADDW)
  .value("EVAND", LIEF::assembly::powerpc::OPCODE::EVAND)
  .value("EVANDC", LIEF::assembly::powerpc::OPCODE::EVANDC)
  .value("EVCMPEQ", LIEF::assembly::powerpc::OPCODE::EVCMPEQ)
  .value("EVCMPGTS", LIEF::assembly::powerpc::OPCODE::EVCMPGTS)
  .value("EVCMPGTU", LIEF::assembly::powerpc::OPCODE::EVCMPGTU)
  .value("EVCMPLTS", LIEF::assembly::powerpc::OPCODE::EVCMPLTS)
  .value("EVCMPLTU", LIEF::assembly::powerpc::OPCODE::EVCMPLTU)
  .value("EVCNTLSW", LIEF::assembly::powerpc::OPCODE::EVCNTLSW)
  .value("EVCNTLZW", LIEF::assembly::powerpc::OPCODE::EVCNTLZW)
  .value("EVDIVWS", LIEF::assembly::powerpc::OPCODE::EVDIVWS)
  .value("EVDIVWU", LIEF::assembly::powerpc::OPCODE::EVDIVWU)
  .value("EVEQV", LIEF::assembly::powerpc::OPCODE::EVEQV)
  .value("EVEXTSB", LIEF::assembly::powerpc::OPCODE::EVEXTSB)
  .value("EVEXTSH", LIEF::assembly::powerpc::OPCODE::EVEXTSH)
  .value("EVFSABS", LIEF::assembly::powerpc::OPCODE::EVFSABS)
  .value("EVFSADD", LIEF::assembly::powerpc::OPCODE::EVFSADD)
  .value("EVFSCFSF", LIEF::assembly::powerpc::OPCODE::EVFSCFSF)
  .value("EVFSCFSI", LIEF::assembly::powerpc::OPCODE::EVFSCFSI)
  .value("EVFSCFUF", LIEF::assembly::powerpc::OPCODE::EVFSCFUF)
  .value("EVFSCFUI", LIEF::assembly::powerpc::OPCODE::EVFSCFUI)
  .value("EVFSCMPEQ", LIEF::assembly::powerpc::OPCODE::EVFSCMPEQ)
  .value("EVFSCMPGT", LIEF::assembly::powerpc::OPCODE::EVFSCMPGT)
  .value("EVFSCMPLT", LIEF::assembly::powerpc::OPCODE::EVFSCMPLT)
  .value("EVFSCTSF", LIEF::assembly::powerpc::OPCODE::EVFSCTSF)
  .value("EVFSCTSI", LIEF::assembly::powerpc::OPCODE::EVFSCTSI)
  .value("EVFSCTSIZ", LIEF::assembly::powerpc::OPCODE::EVFSCTSIZ)
  .value("EVFSCTUF", LIEF::assembly::powerpc::OPCODE::EVFSCTUF)
  .value("EVFSCTUI", LIEF::assembly::powerpc::OPCODE::EVFSCTUI)
  .value("EVFSCTUIZ", LIEF::assembly::powerpc::OPCODE::EVFSCTUIZ)
  .value("EVFSDIV", LIEF::assembly::powerpc::OPCODE::EVFSDIV)
  .value("EVFSMUL", LIEF::assembly::powerpc::OPCODE::EVFSMUL)
  .value("EVFSNABS", LIEF::assembly::powerpc::OPCODE::EVFSNABS)
  .value("EVFSNEG", LIEF::assembly::powerpc::OPCODE::EVFSNEG)
  .value("EVFSSUB", LIEF::assembly::powerpc::OPCODE::EVFSSUB)
  .value("EVFSTSTEQ", LIEF::assembly::powerpc::OPCODE::EVFSTSTEQ)
  .value("EVFSTSTGT", LIEF::assembly::powerpc::OPCODE::EVFSTSTGT)
  .value("EVFSTSTLT", LIEF::assembly::powerpc::OPCODE::EVFSTSTLT)
  .value("EVLDD", LIEF::assembly::powerpc::OPCODE::EVLDD)
  .value("EVLDDX", LIEF::assembly::powerpc::OPCODE::EVLDDX)
  .value("EVLDH", LIEF::assembly::powerpc::OPCODE::EVLDH)
  .value("EVLDHX", LIEF::assembly::powerpc::OPCODE::EVLDHX)
  .value("EVLDW", LIEF::assembly::powerpc::OPCODE::EVLDW)
  .value("EVLDWX", LIEF::assembly::powerpc::OPCODE::EVLDWX)
  .value("EVLHHESPLAT", LIEF::assembly::powerpc::OPCODE::EVLHHESPLAT)
  .value("EVLHHESPLATX", LIEF::assembly::powerpc::OPCODE::EVLHHESPLATX)
  .value("EVLHHOSSPLAT", LIEF::assembly::powerpc::OPCODE::EVLHHOSSPLAT)
  .value("EVLHHOSSPLATX", LIEF::assembly::powerpc::OPCODE::EVLHHOSSPLATX)
  .value("EVLHHOUSPLAT", LIEF::assembly::powerpc::OPCODE::EVLHHOUSPLAT)
  .value("EVLHHOUSPLATX", LIEF::assembly::powerpc::OPCODE::EVLHHOUSPLATX)
  .value("EVLWHE", LIEF::assembly::powerpc::OPCODE::EVLWHE)
  .value("EVLWHEX", LIEF::assembly::powerpc::OPCODE::EVLWHEX)
  .value("EVLWHOS", LIEF::assembly::powerpc::OPCODE::EVLWHOS)
  .value("EVLWHOSX", LIEF::assembly::powerpc::OPCODE::EVLWHOSX)
  .value("EVLWHOU", LIEF::assembly::powerpc::OPCODE::EVLWHOU)
  .value("EVLWHOUX", LIEF::assembly::powerpc::OPCODE::EVLWHOUX)
  .value("EVLWHSPLAT", LIEF::assembly::powerpc::OPCODE::EVLWHSPLAT)
  .value("EVLWHSPLATX", LIEF::assembly::powerpc::OPCODE::EVLWHSPLATX)
  .value("EVLWWSPLAT", LIEF::assembly::powerpc::OPCODE::EVLWWSPLAT)
  .value("EVLWWSPLATX", LIEF::assembly::powerpc::OPCODE::EVLWWSPLATX)
  .value("EVMERGEHI", LIEF::assembly::powerpc::OPCODE::EVMERGEHI)
  .value("EVMERGEHILO", LIEF::assembly::powerpc::OPCODE::EVMERGEHILO)
  .value("EVMERGELO", LIEF::assembly::powerpc::OPCODE::EVMERGELO)
  .value("EVMERGELOHI", LIEF::assembly::powerpc::OPCODE::EVMERGELOHI)
  .value("EVMHEGSMFAA", LIEF::assembly::powerpc::OPCODE::EVMHEGSMFAA)
  .value("EVMHEGSMFAN", LIEF::assembly::powerpc::OPCODE::EVMHEGSMFAN)
  .value("EVMHEGSMIAA", LIEF::assembly::powerpc::OPCODE::EVMHEGSMIAA)
  .value("EVMHEGSMIAN", LIEF::assembly::powerpc::OPCODE::EVMHEGSMIAN)
  .value("EVMHEGUMIAA", LIEF::assembly::powerpc::OPCODE::EVMHEGUMIAA)
  .value("EVMHEGUMIAN", LIEF::assembly::powerpc::OPCODE::EVMHEGUMIAN)
  .value("EVMHESMF", LIEF::assembly::powerpc::OPCODE::EVMHESMF)
  .value("EVMHESMFA", LIEF::assembly::powerpc::OPCODE::EVMHESMFA)
  .value("EVMHESMFAAW", LIEF::assembly::powerpc::OPCODE::EVMHESMFAAW)
  .value("EVMHESMFANW", LIEF::assembly::powerpc::OPCODE::EVMHESMFANW)
  .value("EVMHESMI", LIEF::assembly::powerpc::OPCODE::EVMHESMI)
  .value("EVMHESMIA", LIEF::assembly::powerpc::OPCODE::EVMHESMIA)
  .value("EVMHESMIAAW", LIEF::assembly::powerpc::OPCODE::EVMHESMIAAW)
  .value("EVMHESMIANW", LIEF::assembly::powerpc::OPCODE::EVMHESMIANW)
  .value("EVMHESSF", LIEF::assembly::powerpc::OPCODE::EVMHESSF)
  .value("EVMHESSFA", LIEF::assembly::powerpc::OPCODE::EVMHESSFA)
  .value("EVMHESSFAAW", LIEF::assembly::powerpc::OPCODE::EVMHESSFAAW)
  .value("EVMHESSFANW", LIEF::assembly::powerpc::OPCODE::EVMHESSFANW)
  .value("EVMHESSIAAW", LIEF::assembly::powerpc::OPCODE::EVMHESSIAAW)
  .value("EVMHESSIANW", LIEF::assembly::powerpc::OPCODE::EVMHESSIANW)
  .value("EVMHEUMI", LIEF::assembly::powerpc::OPCODE::EVMHEUMI)
  .value("EVMHEUMIA", LIEF::assembly::powerpc::OPCODE::EVMHEUMIA)
  .value("EVMHEUMIAAW", LIEF::assembly::powerpc::OPCODE::EVMHEUMIAAW)
  .value("EVMHEUMIANW", LIEF::assembly::powerpc::OPCODE::EVMHEUMIANW)
  .value("EVMHEUSIAAW", LIEF::assembly::powerpc::OPCODE::EVMHEUSIAAW)
  .value("EVMHEUSIANW", LIEF::assembly::powerpc::OPCODE::EVMHEUSIANW)
  .value("EVMHOGSMFAA", LIEF::assembly::powerpc::OPCODE::EVMHOGSMFAA)
  .value("EVMHOGSMFAN", LIEF::assembly::powerpc::OPCODE::EVMHOGSMFAN)
  .value("EVMHOGSMIAA", LIEF::assembly::powerpc::OPCODE::EVMHOGSMIAA)
  .value("EVMHOGSMIAN", LIEF::assembly::powerpc::OPCODE::EVMHOGSMIAN)
  .value("EVMHOGUMIAA", LIEF::assembly::powerpc::OPCODE::EVMHOGUMIAA)
  .value("EVMHOGUMIAN", LIEF::assembly::powerpc::OPCODE::EVMHOGUMIAN)
  .value("EVMHOSMF", LIEF::assembly::powerpc::OPCODE::EVMHOSMF)
  .value("EVMHOSMFA", LIEF::assembly::powerpc::OPCODE::EVMHOSMFA)
  .value("EVMHOSMFAAW", LIEF::assembly::powerpc::OPCODE::EVMHOSMFAAW)
  .value("EVMHOSMFANW", LIEF::assembly::powerpc::OPCODE::EVMHOSMFANW)
  .value("EVMHOSMI", LIEF::assembly::powerpc::OPCODE::EVMHOSMI)
  .value("EVMHOSMIA", LIEF::assembly::powerpc::OPCODE::EVMHOSMIA)
  .value("EVMHOSMIAAW", LIEF::assembly::powerpc::OPCODE::EVMHOSMIAAW)
  .value("EVMHOSMIANW", LIEF::assembly::powerpc::OPCODE::EVMHOSMIANW)
  .value("EVMHOSSF", LIEF::assembly::powerpc::OPCODE::EVMHOSSF)
  .value("EVMHOSSFA", LIEF::assembly::powerpc::OPCODE::EVMHOSSFA)
  .value("EVMHOSSFAAW", LIEF::assembly::powerpc::OPCODE::EVMHOSSFAAW)
  .value("EVMHOSSFANW", LIEF::assembly::powerpc::OPCODE::EVMHOSSFANW)
  .value("EVMHOSSIAAW", LIEF::assembly::powerpc::OPCODE::EVMHOSSIAAW)
  .value("EVMHOSSIANW", LIEF::assembly::powerpc::OPCODE::EVMHOSSIANW)
  .value("EVMHOUMI", LIEF::assembly::powerpc::OPCODE::EVMHOUMI)
  .value("EVMHOUMIA", LIEF::assembly::powerpc::OPCODE::EVMHOUMIA)
  .value("EVMHOUMIAAW", LIEF::assembly::powerpc::OPCODE::EVMHOUMIAAW)
  .value("EVMHOUMIANW", LIEF::assembly::powerpc::OPCODE::EVMHOUMIANW)
  .value("EVMHOUSIAAW", LIEF::assembly::powerpc::OPCODE::EVMHOUSIAAW)
  .value("EVMHOUSIANW", LIEF::assembly::powerpc::OPCODE::EVMHOUSIANW)
  .value("EVMRA", LIEF::assembly::powerpc::OPCODE::EVMRA)
  .value("EVMWHSMF", LIEF::assembly::powerpc::OPCODE::EVMWHSMF)
  .value("EVMWHSMFA", LIEF::assembly::powerpc::OPCODE::EVMWHSMFA)
  .value("EVMWHSMI", LIEF::assembly::powerpc::OPCODE::EVMWHSMI)
  .value("EVMWHSMIA", LIEF::assembly::powerpc::OPCODE::EVMWHSMIA)
  .value("EVMWHSSF", LIEF::assembly::powerpc::OPCODE::EVMWHSSF)
  .value("EVMWHSSFA", LIEF::assembly::powerpc::OPCODE::EVMWHSSFA)
  .value("EVMWHUMI", LIEF::assembly::powerpc::OPCODE::EVMWHUMI)
  .value("EVMWHUMIA", LIEF::assembly::powerpc::OPCODE::EVMWHUMIA)
  .value("EVMWLSMIAAW", LIEF::assembly::powerpc::OPCODE::EVMWLSMIAAW)
  .value("EVMWLSMIANW", LIEF::assembly::powerpc::OPCODE::EVMWLSMIANW)
  .value("EVMWLSSIAAW", LIEF::assembly::powerpc::OPCODE::EVMWLSSIAAW)
  .value("EVMWLSSIANW", LIEF::assembly::powerpc::OPCODE::EVMWLSSIANW)
  .value("EVMWLUMI", LIEF::assembly::powerpc::OPCODE::EVMWLUMI)
  .value("EVMWLUMIA", LIEF::assembly::powerpc::OPCODE::EVMWLUMIA)
  .value("EVMWLUMIAAW", LIEF::assembly::powerpc::OPCODE::EVMWLUMIAAW)
  .value("EVMWLUMIANW", LIEF::assembly::powerpc::OPCODE::EVMWLUMIANW)
  .value("EVMWLUSIAAW", LIEF::assembly::powerpc::OPCODE::EVMWLUSIAAW)
  .value("EVMWLUSIANW", LIEF::assembly::powerpc::OPCODE::EVMWLUSIANW)
  .value("EVMWSMF", LIEF::assembly::powerpc::OPCODE::EVMWSMF)
  .value("EVMWSMFA", LIEF::assembly::powerpc::OPCODE::EVMWSMFA)
  .value("EVMWSMFAA", LIEF::assembly::powerpc::OPCODE::EVMWSMFAA)
  .value("EVMWSMFAN", LIEF::assembly::powerpc::OPCODE::EVMWSMFAN)
  .value("EVMWSMI", LIEF::assembly::powerpc::OPCODE::EVMWSMI)
  .value("EVMWSMIA", LIEF::assembly::powerpc::OPCODE::EVMWSMIA)
  .value("EVMWSMIAA", LIEF::assembly::powerpc::OPCODE::EVMWSMIAA)
  .value("EVMWSMIAN", LIEF::assembly::powerpc::OPCODE::EVMWSMIAN)
  .value("EVMWSSF", LIEF::assembly::powerpc::OPCODE::EVMWSSF)
  .value("EVMWSSFA", LIEF::assembly::powerpc::OPCODE::EVMWSSFA)
  .value("EVMWSSFAA", LIEF::assembly::powerpc::OPCODE::EVMWSSFAA)
  .value("EVMWSSFAN", LIEF::assembly::powerpc::OPCODE::EVMWSSFAN)
  .value("EVMWUMI", LIEF::assembly::powerpc::OPCODE::EVMWUMI)
  .value("EVMWUMIA", LIEF::assembly::powerpc::OPCODE::EVMWUMIA)
  .value("EVMWUMIAA", LIEF::assembly::powerpc::OPCODE::EVMWUMIAA)
  .value("EVMWUMIAN", LIEF::assembly::powerpc::OPCODE::EVMWUMIAN)
  .value("EVNAND", LIEF::assembly::powerpc::OPCODE::EVNAND)
  .value("EVNEG", LIEF::assembly::powerpc::OPCODE::EVNEG)
  .value("EVNOR", LIEF::assembly::powerpc::OPCODE::EVNOR)
  .value("EVOR", LIEF::assembly::powerpc::OPCODE::EVOR)
  .value("EVORC", LIEF::assembly::powerpc::OPCODE::EVORC)
  .value("EVRLW", LIEF::assembly::powerpc::OPCODE::EVRLW)
  .value("EVRLWI", LIEF::assembly::powerpc::OPCODE::EVRLWI)
  .value("EVRNDW", LIEF::assembly::powerpc::OPCODE::EVRNDW)
  .value("EVSEL", LIEF::assembly::powerpc::OPCODE::EVSEL)
  .value("EVSLW", LIEF::assembly::powerpc::OPCODE::EVSLW)
  .value("EVSLWI", LIEF::assembly::powerpc::OPCODE::EVSLWI)
  .value("EVSPLATFI", LIEF::assembly::powerpc::OPCODE::EVSPLATFI)
  .value("EVSPLATI", LIEF::assembly::powerpc::OPCODE::EVSPLATI)
  .value("EVSRWIS", LIEF::assembly::powerpc::OPCODE::EVSRWIS)
  .value("EVSRWIU", LIEF::assembly::powerpc::OPCODE::EVSRWIU)
  .value("EVSRWS", LIEF::assembly::powerpc::OPCODE::EVSRWS)
  .value("EVSRWU", LIEF::assembly::powerpc::OPCODE::EVSRWU)
  .value("EVSTDD", LIEF::assembly::powerpc::OPCODE::EVSTDD)
  .value("EVSTDDX", LIEF::assembly::powerpc::OPCODE::EVSTDDX)
  .value("EVSTDH", LIEF::assembly::powerpc::OPCODE::EVSTDH)
  .value("EVSTDHX", LIEF::assembly::powerpc::OPCODE::EVSTDHX)
  .value("EVSTDW", LIEF::assembly::powerpc::OPCODE::EVSTDW)
  .value("EVSTDWX", LIEF::assembly::powerpc::OPCODE::EVSTDWX)
  .value("EVSTWHE", LIEF::assembly::powerpc::OPCODE::EVSTWHE)
  .value("EVSTWHEX", LIEF::assembly::powerpc::OPCODE::EVSTWHEX)
  .value("EVSTWHO", LIEF::assembly::powerpc::OPCODE::EVSTWHO)
  .value("EVSTWHOX", LIEF::assembly::powerpc::OPCODE::EVSTWHOX)
  .value("EVSTWWE", LIEF::assembly::powerpc::OPCODE::EVSTWWE)
  .value("EVSTWWEX", LIEF::assembly::powerpc::OPCODE::EVSTWWEX)
  .value("EVSTWWO", LIEF::assembly::powerpc::OPCODE::EVSTWWO)
  .value("EVSTWWOX", LIEF::assembly::powerpc::OPCODE::EVSTWWOX)
  .value("EVSUBFSMIAAW", LIEF::assembly::powerpc::OPCODE::EVSUBFSMIAAW)
  .value("EVSUBFSSIAAW", LIEF::assembly::powerpc::OPCODE::EVSUBFSSIAAW)
  .value("EVSUBFUMIAAW", LIEF::assembly::powerpc::OPCODE::EVSUBFUMIAAW)
  .value("EVSUBFUSIAAW", LIEF::assembly::powerpc::OPCODE::EVSUBFUSIAAW)
  .value("EVSUBFW", LIEF::assembly::powerpc::OPCODE::EVSUBFW)
  .value("EVSUBIFW", LIEF::assembly::powerpc::OPCODE::EVSUBIFW)
  .value("EVXOR", LIEF::assembly::powerpc::OPCODE::EVXOR)
  .value("EXTSB", LIEF::assembly::powerpc::OPCODE::EXTSB)
  .value("EXTSB8", LIEF::assembly::powerpc::OPCODE::EXTSB8)
  .value("EXTSB8_32_64", LIEF::assembly::powerpc::OPCODE::EXTSB8_32_64)
  .value("EXTSB8_rec", LIEF::assembly::powerpc::OPCODE::EXTSB8_rec)
  .value("EXTSB_rec", LIEF::assembly::powerpc::OPCODE::EXTSB_rec)
  .value("EXTSH", LIEF::assembly::powerpc::OPCODE::EXTSH)
  .value("EXTSH8", LIEF::assembly::powerpc::OPCODE::EXTSH8)
  .value("EXTSH8_32_64", LIEF::assembly::powerpc::OPCODE::EXTSH8_32_64)
  .value("EXTSH8_rec", LIEF::assembly::powerpc::OPCODE::EXTSH8_rec)
  .value("EXTSH_rec", LIEF::assembly::powerpc::OPCODE::EXTSH_rec)
  .value("EXTSW", LIEF::assembly::powerpc::OPCODE::EXTSW)
  .value("EXTSWSLI", LIEF::assembly::powerpc::OPCODE::EXTSWSLI)
  .value("EXTSWSLI_32_64", LIEF::assembly::powerpc::OPCODE::EXTSWSLI_32_64)
  .value("EXTSWSLI_32_64_rec", LIEF::assembly::powerpc::OPCODE::EXTSWSLI_32_64_rec)
  .value("EXTSWSLI_rec", LIEF::assembly::powerpc::OPCODE::EXTSWSLI_rec)
  .value("EXTSW_32", LIEF::assembly::powerpc::OPCODE::EXTSW_32)
  .value("EXTSW_32_64", LIEF::assembly::powerpc::OPCODE::EXTSW_32_64)
  .value("EXTSW_32_64_rec", LIEF::assembly::powerpc::OPCODE::EXTSW_32_64_rec)
  .value("EXTSW_rec", LIEF::assembly::powerpc::OPCODE::EXTSW_rec)
  .value("EnforceIEIO", LIEF::assembly::powerpc::OPCODE::EnforceIEIO)
  .value("FABSD", LIEF::assembly::powerpc::OPCODE::FABSD)
  .value("FABSD_rec", LIEF::assembly::powerpc::OPCODE::FABSD_rec)
  .value("FABSS", LIEF::assembly::powerpc::OPCODE::FABSS)
  .value("FABSS_rec", LIEF::assembly::powerpc::OPCODE::FABSS_rec)
  .value("FADD", LIEF::assembly::powerpc::OPCODE::FADD)
  .value("FADDS", LIEF::assembly::powerpc::OPCODE::FADDS)
  .value("FADDS_rec", LIEF::assembly::powerpc::OPCODE::FADDS_rec)
  .value("FADD_rec", LIEF::assembly::powerpc::OPCODE::FADD_rec)
  .value("FADDrtz", LIEF::assembly::powerpc::OPCODE::FADDrtz)
  .value("FCFID", LIEF::assembly::powerpc::OPCODE::FCFID)
  .value("FCFIDS", LIEF::assembly::powerpc::OPCODE::FCFIDS)
  .value("FCFIDS_rec", LIEF::assembly::powerpc::OPCODE::FCFIDS_rec)
  .value("FCFIDU", LIEF::assembly::powerpc::OPCODE::FCFIDU)
  .value("FCFIDUS", LIEF::assembly::powerpc::OPCODE::FCFIDUS)
  .value("FCFIDUS_rec", LIEF::assembly::powerpc::OPCODE::FCFIDUS_rec)
  .value("FCFIDU_rec", LIEF::assembly::powerpc::OPCODE::FCFIDU_rec)
  .value("FCFID_rec", LIEF::assembly::powerpc::OPCODE::FCFID_rec)
  .value("FCMPOD", LIEF::assembly::powerpc::OPCODE::FCMPOD)
  .value("FCMPOS", LIEF::assembly::powerpc::OPCODE::FCMPOS)
  .value("FCMPUD", LIEF::assembly::powerpc::OPCODE::FCMPUD)
  .value("FCMPUS", LIEF::assembly::powerpc::OPCODE::FCMPUS)
  .value("FCPSGND", LIEF::assembly::powerpc::OPCODE::FCPSGND)
  .value("FCPSGND_rec", LIEF::assembly::powerpc::OPCODE::FCPSGND_rec)
  .value("FCPSGNS", LIEF::assembly::powerpc::OPCODE::FCPSGNS)
  .value("FCPSGNS_rec", LIEF::assembly::powerpc::OPCODE::FCPSGNS_rec)
  .value("FCTID", LIEF::assembly::powerpc::OPCODE::FCTID)
  .value("FCTIDU", LIEF::assembly::powerpc::OPCODE::FCTIDU)
  .value("FCTIDUZ", LIEF::assembly::powerpc::OPCODE::FCTIDUZ)
  .value("FCTIDUZ_rec", LIEF::assembly::powerpc::OPCODE::FCTIDUZ_rec)
  .value("FCTIDU_rec", LIEF::assembly::powerpc::OPCODE::FCTIDU_rec)
  .value("FCTIDZ", LIEF::assembly::powerpc::OPCODE::FCTIDZ)
  .value("FCTIDZ_rec", LIEF::assembly::powerpc::OPCODE::FCTIDZ_rec)
  .value("FCTID_rec", LIEF::assembly::powerpc::OPCODE::FCTID_rec)
  .value("FCTIW", LIEF::assembly::powerpc::OPCODE::FCTIW)
  .value("FCTIWU", LIEF::assembly::powerpc::OPCODE::FCTIWU)
  .value("FCTIWUZ", LIEF::assembly::powerpc::OPCODE::FCTIWUZ)
  .value("FCTIWUZ_rec", LIEF::assembly::powerpc::OPCODE::FCTIWUZ_rec)
  .value("FCTIWU_rec", LIEF::assembly::powerpc::OPCODE::FCTIWU_rec)
  .value("FCTIWZ", LIEF::assembly::powerpc::OPCODE::FCTIWZ)
  .value("FCTIWZ_rec", LIEF::assembly::powerpc::OPCODE::FCTIWZ_rec)
  .value("FCTIW_rec", LIEF::assembly::powerpc::OPCODE::FCTIW_rec)
  .value("FDIV", LIEF::assembly::powerpc::OPCODE::FDIV)
  .value("FDIVS", LIEF::assembly::powerpc::OPCODE::FDIVS)
  .value("FDIVS_rec", LIEF::assembly::powerpc::OPCODE::FDIVS_rec);
  opcodes.value("FDIV_rec", LIEF::assembly::powerpc::OPCODE::FDIV_rec)
  .value("FENCE", LIEF::assembly::powerpc::OPCODE::FENCE)
  .value("FMADD", LIEF::assembly::powerpc::OPCODE::FMADD)
  .value("FMADDS", LIEF::assembly::powerpc::OPCODE::FMADDS)
  .value("FMADDS_rec", LIEF::assembly::powerpc::OPCODE::FMADDS_rec)
  .value("FMADD_rec", LIEF::assembly::powerpc::OPCODE::FMADD_rec)
  .value("FMR", LIEF::assembly::powerpc::OPCODE::FMR)
  .value("FMR_rec", LIEF::assembly::powerpc::OPCODE::FMR_rec)
  .value("FMSUB", LIEF::assembly::powerpc::OPCODE::FMSUB)
  .value("FMSUBS", LIEF::assembly::powerpc::OPCODE::FMSUBS)
  .value("FMSUBS_rec", LIEF::assembly::powerpc::OPCODE::FMSUBS_rec)
  .value("FMSUB_rec", LIEF::assembly::powerpc::OPCODE::FMSUB_rec)
  .value("FMUL", LIEF::assembly::powerpc::OPCODE::FMUL)
  .value("FMULS", LIEF::assembly::powerpc::OPCODE::FMULS)
  .value("FMULS_rec", LIEF::assembly::powerpc::OPCODE::FMULS_rec)
  .value("FMUL_rec", LIEF::assembly::powerpc::OPCODE::FMUL_rec)
  .value("FNABSD", LIEF::assembly::powerpc::OPCODE::FNABSD)
  .value("FNABSD_rec", LIEF::assembly::powerpc::OPCODE::FNABSD_rec)
  .value("FNABSS", LIEF::assembly::powerpc::OPCODE::FNABSS)
  .value("FNABSS_rec", LIEF::assembly::powerpc::OPCODE::FNABSS_rec)
  .value("FNEGD", LIEF::assembly::powerpc::OPCODE::FNEGD)
  .value("FNEGD_rec", LIEF::assembly::powerpc::OPCODE::FNEGD_rec)
  .value("FNEGS", LIEF::assembly::powerpc::OPCODE::FNEGS)
  .value("FNEGS_rec", LIEF::assembly::powerpc::OPCODE::FNEGS_rec)
  .value("FNMADD", LIEF::assembly::powerpc::OPCODE::FNMADD)
  .value("FNMADDS", LIEF::assembly::powerpc::OPCODE::FNMADDS)
  .value("FNMADDS_rec", LIEF::assembly::powerpc::OPCODE::FNMADDS_rec)
  .value("FNMADD_rec", LIEF::assembly::powerpc::OPCODE::FNMADD_rec)
  .value("FNMSUB", LIEF::assembly::powerpc::OPCODE::FNMSUB)
  .value("FNMSUBS", LIEF::assembly::powerpc::OPCODE::FNMSUBS)
  .value("FNMSUBS_rec", LIEF::assembly::powerpc::OPCODE::FNMSUBS_rec)
  .value("FNMSUB_rec", LIEF::assembly::powerpc::OPCODE::FNMSUB_rec)
  .value("FRE", LIEF::assembly::powerpc::OPCODE::FRE)
  .value("FRES", LIEF::assembly::powerpc::OPCODE::FRES)
  .value("FRES_rec", LIEF::assembly::powerpc::OPCODE::FRES_rec)
  .value("FRE_rec", LIEF::assembly::powerpc::OPCODE::FRE_rec)
  .value("FRIMD", LIEF::assembly::powerpc::OPCODE::FRIMD)
  .value("FRIMD_rec", LIEF::assembly::powerpc::OPCODE::FRIMD_rec)
  .value("FRIMS", LIEF::assembly::powerpc::OPCODE::FRIMS)
  .value("FRIMS_rec", LIEF::assembly::powerpc::OPCODE::FRIMS_rec)
  .value("FRIND", LIEF::assembly::powerpc::OPCODE::FRIND)
  .value("FRIND_rec", LIEF::assembly::powerpc::OPCODE::FRIND_rec)
  .value("FRINS", LIEF::assembly::powerpc::OPCODE::FRINS)
  .value("FRINS_rec", LIEF::assembly::powerpc::OPCODE::FRINS_rec)
  .value("FRIPD", LIEF::assembly::powerpc::OPCODE::FRIPD)
  .value("FRIPD_rec", LIEF::assembly::powerpc::OPCODE::FRIPD_rec)
  .value("FRIPS", LIEF::assembly::powerpc::OPCODE::FRIPS)
  .value("FRIPS_rec", LIEF::assembly::powerpc::OPCODE::FRIPS_rec)
  .value("FRIZD", LIEF::assembly::powerpc::OPCODE::FRIZD)
  .value("FRIZD_rec", LIEF::assembly::powerpc::OPCODE::FRIZD_rec)
  .value("FRIZS", LIEF::assembly::powerpc::OPCODE::FRIZS)
  .value("FRIZS_rec", LIEF::assembly::powerpc::OPCODE::FRIZS_rec)
  .value("FRSP", LIEF::assembly::powerpc::OPCODE::FRSP)
  .value("FRSP_rec", LIEF::assembly::powerpc::OPCODE::FRSP_rec)
  .value("FRSQRTE", LIEF::assembly::powerpc::OPCODE::FRSQRTE)
  .value("FRSQRTES", LIEF::assembly::powerpc::OPCODE::FRSQRTES)
  .value("FRSQRTES_rec", LIEF::assembly::powerpc::OPCODE::FRSQRTES_rec)
  .value("FRSQRTE_rec", LIEF::assembly::powerpc::OPCODE::FRSQRTE_rec)
  .value("FSELD", LIEF::assembly::powerpc::OPCODE::FSELD)
  .value("FSELD_rec", LIEF::assembly::powerpc::OPCODE::FSELD_rec)
  .value("FSELS", LIEF::assembly::powerpc::OPCODE::FSELS)
  .value("FSELS_rec", LIEF::assembly::powerpc::OPCODE::FSELS_rec)
  .value("FSQRT", LIEF::assembly::powerpc::OPCODE::FSQRT)
  .value("FSQRTS", LIEF::assembly::powerpc::OPCODE::FSQRTS)
  .value("FSQRTS_rec", LIEF::assembly::powerpc::OPCODE::FSQRTS_rec)
  .value("FSQRT_rec", LIEF::assembly::powerpc::OPCODE::FSQRT_rec)
  .value("FSUB", LIEF::assembly::powerpc::OPCODE::FSUB)
  .value("FSUBS", LIEF::assembly::powerpc::OPCODE::FSUBS)
  .value("FSUBS_rec", LIEF::assembly::powerpc::OPCODE::FSUBS_rec)
  .value("FSUB_rec", LIEF::assembly::powerpc::OPCODE::FSUB_rec)
  .value("FTDIV", LIEF::assembly::powerpc::OPCODE::FTDIV)
  .value("FTSQRT", LIEF::assembly::powerpc::OPCODE::FTSQRT)
  .value("GETtlsADDR", LIEF::assembly::powerpc::OPCODE::GETtlsADDR)
  .value("GETtlsADDR32", LIEF::assembly::powerpc::OPCODE::GETtlsADDR32)
  .value("GETtlsADDR32AIX", LIEF::assembly::powerpc::OPCODE::GETtlsADDR32AIX)
  .value("GETtlsADDR64AIX", LIEF::assembly::powerpc::OPCODE::GETtlsADDR64AIX)
  .value("GETtlsADDRPCREL", LIEF::assembly::powerpc::OPCODE::GETtlsADDRPCREL)
  .value("GETtlsMOD32AIX", LIEF::assembly::powerpc::OPCODE::GETtlsMOD32AIX)
  .value("GETtlsMOD64AIX", LIEF::assembly::powerpc::OPCODE::GETtlsMOD64AIX)
  .value("GETtlsTpointer32AIX", LIEF::assembly::powerpc::OPCODE::GETtlsTpointer32AIX)
  .value("GETtlsldADDR", LIEF::assembly::powerpc::OPCODE::GETtlsldADDR)
  .value("GETtlsldADDR32", LIEF::assembly::powerpc::OPCODE::GETtlsldADDR32)
  .value("GETtlsldADDRPCREL", LIEF::assembly::powerpc::OPCODE::GETtlsldADDRPCREL)
  .value("HASHCHK", LIEF::assembly::powerpc::OPCODE::HASHCHK)
  .value("HASHCHK8", LIEF::assembly::powerpc::OPCODE::HASHCHK8)
  .value("HASHCHKP", LIEF::assembly::powerpc::OPCODE::HASHCHKP)
  .value("HASHCHKP8", LIEF::assembly::powerpc::OPCODE::HASHCHKP8)
  .value("HASHST", LIEF::assembly::powerpc::OPCODE::HASHST)
  .value("HASHST8", LIEF::assembly::powerpc::OPCODE::HASHST8)
  .value("HASHSTP", LIEF::assembly::powerpc::OPCODE::HASHSTP)
  .value("HASHSTP8", LIEF::assembly::powerpc::OPCODE::HASHSTP8)
  .value("HRFID", LIEF::assembly::powerpc::OPCODE::HRFID)
  .value("ICBI", LIEF::assembly::powerpc::OPCODE::ICBI)
  .value("ICBIEP", LIEF::assembly::powerpc::OPCODE::ICBIEP)
  .value("ICBLC", LIEF::assembly::powerpc::OPCODE::ICBLC)
  .value("ICBLQ", LIEF::assembly::powerpc::OPCODE::ICBLQ)
  .value("ICBT", LIEF::assembly::powerpc::OPCODE::ICBT)
  .value("ICBTLS", LIEF::assembly::powerpc::OPCODE::ICBTLS)
  .value("ICCCI", LIEF::assembly::powerpc::OPCODE::ICCCI)
  .value("ISEL", LIEF::assembly::powerpc::OPCODE::ISEL)
  .value("ISEL8", LIEF::assembly::powerpc::OPCODE::ISEL8)
  .value("ISYNC", LIEF::assembly::powerpc::OPCODE::ISYNC)
  .value("LA", LIEF::assembly::powerpc::OPCODE::LA)
  .value("LA8", LIEF::assembly::powerpc::OPCODE::LA8)
  .value("LBARX", LIEF::assembly::powerpc::OPCODE::LBARX)
  .value("LBARXL", LIEF::assembly::powerpc::OPCODE::LBARXL)
  .value("LBEPX", LIEF::assembly::powerpc::OPCODE::LBEPX)
  .value("LBZ", LIEF::assembly::powerpc::OPCODE::LBZ)
  .value("LBZ8", LIEF::assembly::powerpc::OPCODE::LBZ8)
  .value("LBZCIX", LIEF::assembly::powerpc::OPCODE::LBZCIX)
  .value("LBZU", LIEF::assembly::powerpc::OPCODE::LBZU)
  .value("LBZU8", LIEF::assembly::powerpc::OPCODE::LBZU8)
  .value("LBZUX", LIEF::assembly::powerpc::OPCODE::LBZUX)
  .value("LBZUX8", LIEF::assembly::powerpc::OPCODE::LBZUX8)
  .value("LBZX", LIEF::assembly::powerpc::OPCODE::LBZX)
  .value("LBZX8", LIEF::assembly::powerpc::OPCODE::LBZX8)
  .value("LBZXTLS", LIEF::assembly::powerpc::OPCODE::LBZXTLS)
  .value("LBZXTLS_", LIEF::assembly::powerpc::OPCODE::LBZXTLS_)
  .value("LBZXTLS_32", LIEF::assembly::powerpc::OPCODE::LBZXTLS_32)
  .value("LD", LIEF::assembly::powerpc::OPCODE::LD)
  .value("LDARX", LIEF::assembly::powerpc::OPCODE::LDARX)
  .value("LDARXL", LIEF::assembly::powerpc::OPCODE::LDARXL)
  .value("LDAT", LIEF::assembly::powerpc::OPCODE::LDAT)
  .value("LDBRX", LIEF::assembly::powerpc::OPCODE::LDBRX)
  .value("LDCIX", LIEF::assembly::powerpc::OPCODE::LDCIX)
  .value("LDU", LIEF::assembly::powerpc::OPCODE::LDU)
  .value("LDUX", LIEF::assembly::powerpc::OPCODE::LDUX)
  .value("LDX", LIEF::assembly::powerpc::OPCODE::LDX)
  .value("LDXTLS", LIEF::assembly::powerpc::OPCODE::LDXTLS)
  .value("LDXTLS_", LIEF::assembly::powerpc::OPCODE::LDXTLS_)
  .value("LDgotTprelL", LIEF::assembly::powerpc::OPCODE::LDgotTprelL)
  .value("LDgotTprelL32", LIEF::assembly::powerpc::OPCODE::LDgotTprelL32)
  .value("LDtoc", LIEF::assembly::powerpc::OPCODE::LDtoc)
  .value("LDtocBA", LIEF::assembly::powerpc::OPCODE::LDtocBA)
  .value("LDtocCPT", LIEF::assembly::powerpc::OPCODE::LDtocCPT)
  .value("LDtocJTI", LIEF::assembly::powerpc::OPCODE::LDtocJTI)
  .value("LDtocL", LIEF::assembly::powerpc::OPCODE::LDtocL)
  .value("LFD", LIEF::assembly::powerpc::OPCODE::LFD)
  .value("LFDEPX", LIEF::assembly::powerpc::OPCODE::LFDEPX)
  .value("LFDU", LIEF::assembly::powerpc::OPCODE::LFDU)
  .value("LFDUX", LIEF::assembly::powerpc::OPCODE::LFDUX)
  .value("LFDX", LIEF::assembly::powerpc::OPCODE::LFDX)
  .value("LFDXTLS", LIEF::assembly::powerpc::OPCODE::LFDXTLS)
  .value("LFDXTLS_", LIEF::assembly::powerpc::OPCODE::LFDXTLS_)
  .value("LFIWAX", LIEF::assembly::powerpc::OPCODE::LFIWAX)
  .value("LFIWZX", LIEF::assembly::powerpc::OPCODE::LFIWZX)
  .value("LFS", LIEF::assembly::powerpc::OPCODE::LFS)
  .value("LFSU", LIEF::assembly::powerpc::OPCODE::LFSU)
  .value("LFSUX", LIEF::assembly::powerpc::OPCODE::LFSUX)
  .value("LFSX", LIEF::assembly::powerpc::OPCODE::LFSX)
  .value("LFSXTLS", LIEF::assembly::powerpc::OPCODE::LFSXTLS)
  .value("LFSXTLS_", LIEF::assembly::powerpc::OPCODE::LFSXTLS_)
  .value("LHA", LIEF::assembly::powerpc::OPCODE::LHA)
  .value("LHA8", LIEF::assembly::powerpc::OPCODE::LHA8)
  .value("LHARX", LIEF::assembly::powerpc::OPCODE::LHARX)
  .value("LHARXL", LIEF::assembly::powerpc::OPCODE::LHARXL)
  .value("LHAU", LIEF::assembly::powerpc::OPCODE::LHAU)
  .value("LHAU8", LIEF::assembly::powerpc::OPCODE::LHAU8)
  .value("LHAUX", LIEF::assembly::powerpc::OPCODE::LHAUX)
  .value("LHAUX8", LIEF::assembly::powerpc::OPCODE::LHAUX8)
  .value("LHAX", LIEF::assembly::powerpc::OPCODE::LHAX)
  .value("LHAX8", LIEF::assembly::powerpc::OPCODE::LHAX8)
  .value("LHAXTLS", LIEF::assembly::powerpc::OPCODE::LHAXTLS)
  .value("LHAXTLS_", LIEF::assembly::powerpc::OPCODE::LHAXTLS_)
  .value("LHAXTLS_32", LIEF::assembly::powerpc::OPCODE::LHAXTLS_32)
  .value("LHBRX", LIEF::assembly::powerpc::OPCODE::LHBRX)
  .value("LHBRX8", LIEF::assembly::powerpc::OPCODE::LHBRX8)
  .value("LHEPX", LIEF::assembly::powerpc::OPCODE::LHEPX)
  .value("LHZ", LIEF::assembly::powerpc::OPCODE::LHZ)
  .value("LHZ8", LIEF::assembly::powerpc::OPCODE::LHZ8)
  .value("LHZCIX", LIEF::assembly::powerpc::OPCODE::LHZCIX)
  .value("LHZU", LIEF::assembly::powerpc::OPCODE::LHZU)
  .value("LHZU8", LIEF::assembly::powerpc::OPCODE::LHZU8)
  .value("LHZUX", LIEF::assembly::powerpc::OPCODE::LHZUX)
  .value("LHZUX8", LIEF::assembly::powerpc::OPCODE::LHZUX8)
  .value("LHZX", LIEF::assembly::powerpc::OPCODE::LHZX)
  .value("LHZX8", LIEF::assembly::powerpc::OPCODE::LHZX8)
  .value("LHZXTLS", LIEF::assembly::powerpc::OPCODE::LHZXTLS)
  .value("LHZXTLS_", LIEF::assembly::powerpc::OPCODE::LHZXTLS_)
  .value("LHZXTLS_32", LIEF::assembly::powerpc::OPCODE::LHZXTLS_32)
  .value("LI", LIEF::assembly::powerpc::OPCODE::LI)
  .value("LI8", LIEF::assembly::powerpc::OPCODE::LI8)
  .value("LIS", LIEF::assembly::powerpc::OPCODE::LIS)
  .value("LIS8", LIEF::assembly::powerpc::OPCODE::LIS8)
  .value("LMW", LIEF::assembly::powerpc::OPCODE::LMW)
  .value("LQ", LIEF::assembly::powerpc::OPCODE::LQ)
  .value("LQARX", LIEF::assembly::powerpc::OPCODE::LQARX)
  .value("LQARXL", LIEF::assembly::powerpc::OPCODE::LQARXL)
  .value("LQX_PSEUDO", LIEF::assembly::powerpc::OPCODE::LQX_PSEUDO)
  .value("LSWI", LIEF::assembly::powerpc::OPCODE::LSWI)
  .value("LVEBX", LIEF::assembly::powerpc::OPCODE::LVEBX)
  .value("LVEHX", LIEF::assembly::powerpc::OPCODE::LVEHX)
  .value("LVEWX", LIEF::assembly::powerpc::OPCODE::LVEWX)
  .value("LVSL", LIEF::assembly::powerpc::OPCODE::LVSL)
  .value("LVSR", LIEF::assembly::powerpc::OPCODE::LVSR)
  .value("LVX", LIEF::assembly::powerpc::OPCODE::LVX)
  .value("LVXL", LIEF::assembly::powerpc::OPCODE::LVXL)
  .value("LWA", LIEF::assembly::powerpc::OPCODE::LWA)
  .value("LWARX", LIEF::assembly::powerpc::OPCODE::LWARX)
  .value("LWARXL", LIEF::assembly::powerpc::OPCODE::LWARXL)
  .value("LWAT", LIEF::assembly::powerpc::OPCODE::LWAT)
  .value("LWAUX", LIEF::assembly::powerpc::OPCODE::LWAUX)
  .value("LWAX", LIEF::assembly::powerpc::OPCODE::LWAX)
  .value("LWAXTLS", LIEF::assembly::powerpc::OPCODE::LWAXTLS)
  .value("LWAXTLS_", LIEF::assembly::powerpc::OPCODE::LWAXTLS_)
  .value("LWAXTLS_32", LIEF::assembly::powerpc::OPCODE::LWAXTLS_32)
  .value("LWAX_32", LIEF::assembly::powerpc::OPCODE::LWAX_32)
  .value("LWA_32", LIEF::assembly::powerpc::OPCODE::LWA_32)
  .value("LWBRX", LIEF::assembly::powerpc::OPCODE::LWBRX)
  .value("LWBRX8", LIEF::assembly::powerpc::OPCODE::LWBRX8)
  .value("LWEPX", LIEF::assembly::powerpc::OPCODE::LWEPX)
  .value("LWZ", LIEF::assembly::powerpc::OPCODE::LWZ)
  .value("LWZ8", LIEF::assembly::powerpc::OPCODE::LWZ8)
  .value("LWZCIX", LIEF::assembly::powerpc::OPCODE::LWZCIX)
  .value("LWZU", LIEF::assembly::powerpc::OPCODE::LWZU)
  .value("LWZU8", LIEF::assembly::powerpc::OPCODE::LWZU8)
  .value("LWZUX", LIEF::assembly::powerpc::OPCODE::LWZUX)
  .value("LWZUX8", LIEF::assembly::powerpc::OPCODE::LWZUX8)
  .value("LWZX", LIEF::assembly::powerpc::OPCODE::LWZX)
  .value("LWZX8", LIEF::assembly::powerpc::OPCODE::LWZX8)
  .value("LWZXTLS", LIEF::assembly::powerpc::OPCODE::LWZXTLS)
  .value("LWZXTLS_", LIEF::assembly::powerpc::OPCODE::LWZXTLS_)
  .value("LWZXTLS_32", LIEF::assembly::powerpc::OPCODE::LWZXTLS_32)
  .value("LWZtoc", LIEF::assembly::powerpc::OPCODE::LWZtoc)
  .value("LWZtocL", LIEF::assembly::powerpc::OPCODE::LWZtocL)
  .value("LXSD", LIEF::assembly::powerpc::OPCODE::LXSD)
  .value("LXSDX", LIEF::assembly::powerpc::OPCODE::LXSDX)
  .value("LXSIBZX", LIEF::assembly::powerpc::OPCODE::LXSIBZX)
  .value("LXSIHZX", LIEF::assembly::powerpc::OPCODE::LXSIHZX)
  .value("LXSIWAX", LIEF::assembly::powerpc::OPCODE::LXSIWAX)
  .value("LXSIWZX", LIEF::assembly::powerpc::OPCODE::LXSIWZX)
  .value("LXSSP", LIEF::assembly::powerpc::OPCODE::LXSSP)
  .value("LXSSPX", LIEF::assembly::powerpc::OPCODE::LXSSPX)
  .value("LXV", LIEF::assembly::powerpc::OPCODE::LXV)
  .value("LXVB16X", LIEF::assembly::powerpc::OPCODE::LXVB16X)
  .value("LXVD2X", LIEF::assembly::powerpc::OPCODE::LXVD2X)
  .value("LXVDSX", LIEF::assembly::powerpc::OPCODE::LXVDSX)
  .value("LXVH8X", LIEF::assembly::powerpc::OPCODE::LXVH8X)
  .value("LXVKQ", LIEF::assembly::powerpc::OPCODE::LXVKQ)
  .value("LXVL", LIEF::assembly::powerpc::OPCODE::LXVL)
  .value("LXVLL", LIEF::assembly::powerpc::OPCODE::LXVLL)
  .value("LXVP", LIEF::assembly::powerpc::OPCODE::LXVP)
  .value("LXVPRL", LIEF::assembly::powerpc::OPCODE::LXVPRL)
  .value("LXVPRLL", LIEF::assembly::powerpc::OPCODE::LXVPRLL)
  .value("LXVPX", LIEF::assembly::powerpc::OPCODE::LXVPX)
  .value("LXVRBX", LIEF::assembly::powerpc::OPCODE::LXVRBX)
  .value("LXVRDX", LIEF::assembly::powerpc::OPCODE::LXVRDX)
  .value("LXVRHX", LIEF::assembly::powerpc::OPCODE::LXVRHX)
  .value("LXVRL", LIEF::assembly::powerpc::OPCODE::LXVRL)
  .value("LXVRLL", LIEF::assembly::powerpc::OPCODE::LXVRLL)
  .value("LXVRWX", LIEF::assembly::powerpc::OPCODE::LXVRWX)
  .value("LXVW4X", LIEF::assembly::powerpc::OPCODE::LXVW4X)
  .value("LXVWSX", LIEF::assembly::powerpc::OPCODE::LXVWSX)
  .value("LXVX", LIEF::assembly::powerpc::OPCODE::LXVX)
  .value("MADDHD", LIEF::assembly::powerpc::OPCODE::MADDHD)
  .value("MADDHDU", LIEF::assembly::powerpc::OPCODE::MADDHDU)
  .value("MADDLD", LIEF::assembly::powerpc::OPCODE::MADDLD)
  .value("MADDLD8", LIEF::assembly::powerpc::OPCODE::MADDLD8)
  .value("MBAR", LIEF::assembly::powerpc::OPCODE::MBAR)
  .value("MCRF", LIEF::assembly::powerpc::OPCODE::MCRF)
  .value("MCRFS", LIEF::assembly::powerpc::OPCODE::MCRFS)
  .value("MCRXRX", LIEF::assembly::powerpc::OPCODE::MCRXRX)
  .value("MFBHRBE", LIEF::assembly::powerpc::OPCODE::MFBHRBE)
  .value("MFCR", LIEF::assembly::powerpc::OPCODE::MFCR)
  .value("MFCR8", LIEF::assembly::powerpc::OPCODE::MFCR8)
  .value("MFCTR", LIEF::assembly::powerpc::OPCODE::MFCTR)
  .value("MFCTR8", LIEF::assembly::powerpc::OPCODE::MFCTR8)
  .value("MFDCR", LIEF::assembly::powerpc::OPCODE::MFDCR)
  .value("MFFS", LIEF::assembly::powerpc::OPCODE::MFFS)
  .value("MFFSCDRN", LIEF::assembly::powerpc::OPCODE::MFFSCDRN)
  .value("MFFSCDRNI", LIEF::assembly::powerpc::OPCODE::MFFSCDRNI)
  .value("MFFSCE", LIEF::assembly::powerpc::OPCODE::MFFSCE)
  .value("MFFSCRN", LIEF::assembly::powerpc::OPCODE::MFFSCRN)
  .value("MFFSCRNI", LIEF::assembly::powerpc::OPCODE::MFFSCRNI)
  .value("MFFSL", LIEF::assembly::powerpc::OPCODE::MFFSL)
  .value("MFFS_rec", LIEF::assembly::powerpc::OPCODE::MFFS_rec)
  .value("MFLR", LIEF::assembly::powerpc::OPCODE::MFLR)
  .value("MFLR8", LIEF::assembly::powerpc::OPCODE::MFLR8)
  .value("MFMSR", LIEF::assembly::powerpc::OPCODE::MFMSR)
  .value("MFOCRF", LIEF::assembly::powerpc::OPCODE::MFOCRF)
  .value("MFOCRF8", LIEF::assembly::powerpc::OPCODE::MFOCRF8)
  .value("MFPMR", LIEF::assembly::powerpc::OPCODE::MFPMR)
  .value("MFSPR", LIEF::assembly::powerpc::OPCODE::MFSPR)
  .value("MFSPR8", LIEF::assembly::powerpc::OPCODE::MFSPR8)
  .value("MFSR", LIEF::assembly::powerpc::OPCODE::MFSR)
  .value("MFSRIN", LIEF::assembly::powerpc::OPCODE::MFSRIN)
  .value("MFTB", LIEF::assembly::powerpc::OPCODE::MFTB)
  .value("MFTB8", LIEF::assembly::powerpc::OPCODE::MFTB8)
  .value("MFUDSCR", LIEF::assembly::powerpc::OPCODE::MFUDSCR)
  .value("MFVRD", LIEF::assembly::powerpc::OPCODE::MFVRD)
  .value("MFVRSAVE", LIEF::assembly::powerpc::OPCODE::MFVRSAVE)
  .value("MFVRSAVEv", LIEF::assembly::powerpc::OPCODE::MFVRSAVEv)
  .value("MFVRWZ", LIEF::assembly::powerpc::OPCODE::MFVRWZ)
  .value("MFVSCR", LIEF::assembly::powerpc::OPCODE::MFVSCR)
  .value("MFVSRD", LIEF::assembly::powerpc::OPCODE::MFVSRD)
  .value("MFVSRLD", LIEF::assembly::powerpc::OPCODE::MFVSRLD)
  .value("MFVSRWZ", LIEF::assembly::powerpc::OPCODE::MFVSRWZ)
  .value("MODSD", LIEF::assembly::powerpc::OPCODE::MODSD)
  .value("MODSW", LIEF::assembly::powerpc::OPCODE::MODSW)
  .value("MODUD", LIEF::assembly::powerpc::OPCODE::MODUD);
  opcodes.value("MODUW", LIEF::assembly::powerpc::OPCODE::MODUW)
  .value("MSGSYNC", LIEF::assembly::powerpc::OPCODE::MSGSYNC)
  .value("MSYNC", LIEF::assembly::powerpc::OPCODE::MSYNC)
  .value("MTCRF", LIEF::assembly::powerpc::OPCODE::MTCRF)
  .value("MTCRF8", LIEF::assembly::powerpc::OPCODE::MTCRF8)
  .value("MTCTR", LIEF::assembly::powerpc::OPCODE::MTCTR)
  .value("MTCTR8", LIEF::assembly::powerpc::OPCODE::MTCTR8)
  .value("MTCTR8loop", LIEF::assembly::powerpc::OPCODE::MTCTR8loop)
  .value("MTCTRloop", LIEF::assembly::powerpc::OPCODE::MTCTRloop)
  .value("MTDCR", LIEF::assembly::powerpc::OPCODE::MTDCR)
  .value("MTFSB0", LIEF::assembly::powerpc::OPCODE::MTFSB0)
  .value("MTFSB1", LIEF::assembly::powerpc::OPCODE::MTFSB1)
  .value("MTFSF", LIEF::assembly::powerpc::OPCODE::MTFSF)
  .value("MTFSFI", LIEF::assembly::powerpc::OPCODE::MTFSFI)
  .value("MTFSFI_rec", LIEF::assembly::powerpc::OPCODE::MTFSFI_rec)
  .value("MTFSFIb", LIEF::assembly::powerpc::OPCODE::MTFSFIb)
  .value("MTFSF_rec", LIEF::assembly::powerpc::OPCODE::MTFSF_rec)
  .value("MTFSFb", LIEF::assembly::powerpc::OPCODE::MTFSFb)
  .value("MTLR", LIEF::assembly::powerpc::OPCODE::MTLR)
  .value("MTLR8", LIEF::assembly::powerpc::OPCODE::MTLR8)
  .value("MTMSR", LIEF::assembly::powerpc::OPCODE::MTMSR)
  .value("MTMSRD", LIEF::assembly::powerpc::OPCODE::MTMSRD)
  .value("MTOCRF", LIEF::assembly::powerpc::OPCODE::MTOCRF)
  .value("MTOCRF8", LIEF::assembly::powerpc::OPCODE::MTOCRF8)
  .value("MTPMR", LIEF::assembly::powerpc::OPCODE::MTPMR)
  .value("MTSPR", LIEF::assembly::powerpc::OPCODE::MTSPR)
  .value("MTSPR8", LIEF::assembly::powerpc::OPCODE::MTSPR8)
  .value("MTSR", LIEF::assembly::powerpc::OPCODE::MTSR)
  .value("MTSRIN", LIEF::assembly::powerpc::OPCODE::MTSRIN)
  .value("MTUDSCR", LIEF::assembly::powerpc::OPCODE::MTUDSCR)
  .value("MTVRD", LIEF::assembly::powerpc::OPCODE::MTVRD)
  .value("MTVRSAVE", LIEF::assembly::powerpc::OPCODE::MTVRSAVE)
  .value("MTVRSAVEv", LIEF::assembly::powerpc::OPCODE::MTVRSAVEv)
  .value("MTVRWA", LIEF::assembly::powerpc::OPCODE::MTVRWA)
  .value("MTVRWZ", LIEF::assembly::powerpc::OPCODE::MTVRWZ)
  .value("MTVSCR", LIEF::assembly::powerpc::OPCODE::MTVSCR)
  .value("MTVSRBM", LIEF::assembly::powerpc::OPCODE::MTVSRBM)
  .value("MTVSRBMI", LIEF::assembly::powerpc::OPCODE::MTVSRBMI)
  .value("MTVSRD", LIEF::assembly::powerpc::OPCODE::MTVSRD)
  .value("MTVSRDD", LIEF::assembly::powerpc::OPCODE::MTVSRDD)
  .value("MTVSRDM", LIEF::assembly::powerpc::OPCODE::MTVSRDM)
  .value("MTVSRHM", LIEF::assembly::powerpc::OPCODE::MTVSRHM)
  .value("MTVSRQM", LIEF::assembly::powerpc::OPCODE::MTVSRQM)
  .value("MTVSRWA", LIEF::assembly::powerpc::OPCODE::MTVSRWA)
  .value("MTVSRWM", LIEF::assembly::powerpc::OPCODE::MTVSRWM)
  .value("MTVSRWS", LIEF::assembly::powerpc::OPCODE::MTVSRWS)
  .value("MTVSRWZ", LIEF::assembly::powerpc::OPCODE::MTVSRWZ)
  .value("MULHD", LIEF::assembly::powerpc::OPCODE::MULHD)
  .value("MULHDU", LIEF::assembly::powerpc::OPCODE::MULHDU)
  .value("MULHDU_rec", LIEF::assembly::powerpc::OPCODE::MULHDU_rec)
  .value("MULHD_rec", LIEF::assembly::powerpc::OPCODE::MULHD_rec)
  .value("MULHW", LIEF::assembly::powerpc::OPCODE::MULHW)
  .value("MULHWU", LIEF::assembly::powerpc::OPCODE::MULHWU)
  .value("MULHWU_rec", LIEF::assembly::powerpc::OPCODE::MULHWU_rec)
  .value("MULHW_rec", LIEF::assembly::powerpc::OPCODE::MULHW_rec)
  .value("MULLD", LIEF::assembly::powerpc::OPCODE::MULLD)
  .value("MULLDO", LIEF::assembly::powerpc::OPCODE::MULLDO)
  .value("MULLDO_rec", LIEF::assembly::powerpc::OPCODE::MULLDO_rec)
  .value("MULLD_rec", LIEF::assembly::powerpc::OPCODE::MULLD_rec)
  .value("MULLI", LIEF::assembly::powerpc::OPCODE::MULLI)
  .value("MULLI8", LIEF::assembly::powerpc::OPCODE::MULLI8)
  .value("MULLW", LIEF::assembly::powerpc::OPCODE::MULLW)
  .value("MULLWO", LIEF::assembly::powerpc::OPCODE::MULLWO)
  .value("MULLWO_rec", LIEF::assembly::powerpc::OPCODE::MULLWO_rec)
  .value("MULLW_rec", LIEF::assembly::powerpc::OPCODE::MULLW_rec)
  .value("MoveGOTtoLR", LIEF::assembly::powerpc::OPCODE::MoveGOTtoLR)
  .value("MovePCtoLR", LIEF::assembly::powerpc::OPCODE::MovePCtoLR)
  .value("MovePCtoLR8", LIEF::assembly::powerpc::OPCODE::MovePCtoLR8)
  .value("NAND", LIEF::assembly::powerpc::OPCODE::NAND)
  .value("NAND8", LIEF::assembly::powerpc::OPCODE::NAND8)
  .value("NAND8_rec", LIEF::assembly::powerpc::OPCODE::NAND8_rec)
  .value("NAND_rec", LIEF::assembly::powerpc::OPCODE::NAND_rec)
  .value("NAP", LIEF::assembly::powerpc::OPCODE::NAP)
  .value("NEG", LIEF::assembly::powerpc::OPCODE::NEG)
  .value("NEG8", LIEF::assembly::powerpc::OPCODE::NEG8)
  .value("NEG8O", LIEF::assembly::powerpc::OPCODE::NEG8O)
  .value("NEG8O_rec", LIEF::assembly::powerpc::OPCODE::NEG8O_rec)
  .value("NEG8_rec", LIEF::assembly::powerpc::OPCODE::NEG8_rec)
  .value("NEGO", LIEF::assembly::powerpc::OPCODE::NEGO)
  .value("NEGO_rec", LIEF::assembly::powerpc::OPCODE::NEGO_rec)
  .value("NEG_rec", LIEF::assembly::powerpc::OPCODE::NEG_rec)
  .value("NOP", LIEF::assembly::powerpc::OPCODE::NOP)
  .value("NOP_GT_PWR6", LIEF::assembly::powerpc::OPCODE::NOP_GT_PWR6)
  .value("NOP_GT_PWR7", LIEF::assembly::powerpc::OPCODE::NOP_GT_PWR7)
  .value("NOR", LIEF::assembly::powerpc::OPCODE::NOR)
  .value("NOR8", LIEF::assembly::powerpc::OPCODE::NOR8)
  .value("NOR8_rec", LIEF::assembly::powerpc::OPCODE::NOR8_rec)
  .value("NOR_rec", LIEF::assembly::powerpc::OPCODE::NOR_rec)
  .value("OR", LIEF::assembly::powerpc::OPCODE::OR)
  .value("OR8", LIEF::assembly::powerpc::OPCODE::OR8)
  .value("OR8_rec", LIEF::assembly::powerpc::OPCODE::OR8_rec)
  .value("ORC", LIEF::assembly::powerpc::OPCODE::ORC)
  .value("ORC8", LIEF::assembly::powerpc::OPCODE::ORC8)
  .value("ORC8_rec", LIEF::assembly::powerpc::OPCODE::ORC8_rec)
  .value("ORC_rec", LIEF::assembly::powerpc::OPCODE::ORC_rec)
  .value("ORI", LIEF::assembly::powerpc::OPCODE::ORI)
  .value("ORI8", LIEF::assembly::powerpc::OPCODE::ORI8)
  .value("ORIS", LIEF::assembly::powerpc::OPCODE::ORIS)
  .value("ORIS8", LIEF::assembly::powerpc::OPCODE::ORIS8)
  .value("OR_rec", LIEF::assembly::powerpc::OPCODE::OR_rec)
  .value("PADDI", LIEF::assembly::powerpc::OPCODE::PADDI)
  .value("PADDI8", LIEF::assembly::powerpc::OPCODE::PADDI8)
  .value("PADDI8pc", LIEF::assembly::powerpc::OPCODE::PADDI8pc)
  .value("PADDIdtprel", LIEF::assembly::powerpc::OPCODE::PADDIdtprel)
  .value("PADDIpc", LIEF::assembly::powerpc::OPCODE::PADDIpc)
  .value("PDEPD", LIEF::assembly::powerpc::OPCODE::PDEPD)
  .value("PEXTD", LIEF::assembly::powerpc::OPCODE::PEXTD)
  .value("PLA", LIEF::assembly::powerpc::OPCODE::PLA)
  .value("PLA8", LIEF::assembly::powerpc::OPCODE::PLA8)
  .value("PLA8pc", LIEF::assembly::powerpc::OPCODE::PLA8pc)
  .value("PLApc", LIEF::assembly::powerpc::OPCODE::PLApc)
  .value("PLBZ", LIEF::assembly::powerpc::OPCODE::PLBZ)
  .value("PLBZ8", LIEF::assembly::powerpc::OPCODE::PLBZ8)
  .value("PLBZ8nopc", LIEF::assembly::powerpc::OPCODE::PLBZ8nopc)
  .value("PLBZ8onlypc", LIEF::assembly::powerpc::OPCODE::PLBZ8onlypc)
  .value("PLBZ8pc", LIEF::assembly::powerpc::OPCODE::PLBZ8pc)
  .value("PLBZnopc", LIEF::assembly::powerpc::OPCODE::PLBZnopc)
  .value("PLBZonlypc", LIEF::assembly::powerpc::OPCODE::PLBZonlypc)
  .value("PLBZpc", LIEF::assembly::powerpc::OPCODE::PLBZpc)
  .value("PLD", LIEF::assembly::powerpc::OPCODE::PLD)
  .value("PLDnopc", LIEF::assembly::powerpc::OPCODE::PLDnopc)
  .value("PLDonlypc", LIEF::assembly::powerpc::OPCODE::PLDonlypc)
  .value("PLDpc", LIEF::assembly::powerpc::OPCODE::PLDpc)
  .value("PLFD", LIEF::assembly::powerpc::OPCODE::PLFD)
  .value("PLFDnopc", LIEF::assembly::powerpc::OPCODE::PLFDnopc)
  .value("PLFDonlypc", LIEF::assembly::powerpc::OPCODE::PLFDonlypc)
  .value("PLFDpc", LIEF::assembly::powerpc::OPCODE::PLFDpc)
  .value("PLFS", LIEF::assembly::powerpc::OPCODE::PLFS)
  .value("PLFSnopc", LIEF::assembly::powerpc::OPCODE::PLFSnopc)
  .value("PLFSonlypc", LIEF::assembly::powerpc::OPCODE::PLFSonlypc)
  .value("PLFSpc", LIEF::assembly::powerpc::OPCODE::PLFSpc)
  .value("PLHA", LIEF::assembly::powerpc::OPCODE::PLHA)
  .value("PLHA8", LIEF::assembly::powerpc::OPCODE::PLHA8)
  .value("PLHA8nopc", LIEF::assembly::powerpc::OPCODE::PLHA8nopc)
  .value("PLHA8onlypc", LIEF::assembly::powerpc::OPCODE::PLHA8onlypc)
  .value("PLHA8pc", LIEF::assembly::powerpc::OPCODE::PLHA8pc)
  .value("PLHAnopc", LIEF::assembly::powerpc::OPCODE::PLHAnopc)
  .value("PLHAonlypc", LIEF::assembly::powerpc::OPCODE::PLHAonlypc)
  .value("PLHApc", LIEF::assembly::powerpc::OPCODE::PLHApc)
  .value("PLHZ", LIEF::assembly::powerpc::OPCODE::PLHZ)
  .value("PLHZ8", LIEF::assembly::powerpc::OPCODE::PLHZ8)
  .value("PLHZ8nopc", LIEF::assembly::powerpc::OPCODE::PLHZ8nopc)
  .value("PLHZ8onlypc", LIEF::assembly::powerpc::OPCODE::PLHZ8onlypc)
  .value("PLHZ8pc", LIEF::assembly::powerpc::OPCODE::PLHZ8pc)
  .value("PLHZnopc", LIEF::assembly::powerpc::OPCODE::PLHZnopc)
  .value("PLHZonlypc", LIEF::assembly::powerpc::OPCODE::PLHZonlypc)
  .value("PLHZpc", LIEF::assembly::powerpc::OPCODE::PLHZpc)
  .value("PLI", LIEF::assembly::powerpc::OPCODE::PLI)
  .value("PLI8", LIEF::assembly::powerpc::OPCODE::PLI8)
  .value("PLWA", LIEF::assembly::powerpc::OPCODE::PLWA)
  .value("PLWA8", LIEF::assembly::powerpc::OPCODE::PLWA8)
  .value("PLWA8nopc", LIEF::assembly::powerpc::OPCODE::PLWA8nopc)
  .value("PLWA8onlypc", LIEF::assembly::powerpc::OPCODE::PLWA8onlypc)
  .value("PLWA8pc", LIEF::assembly::powerpc::OPCODE::PLWA8pc)
  .value("PLWAnopc", LIEF::assembly::powerpc::OPCODE::PLWAnopc)
  .value("PLWAonlypc", LIEF::assembly::powerpc::OPCODE::PLWAonlypc)
  .value("PLWApc", LIEF::assembly::powerpc::OPCODE::PLWApc)
  .value("PLWZ", LIEF::assembly::powerpc::OPCODE::PLWZ)
  .value("PLWZ8", LIEF::assembly::powerpc::OPCODE::PLWZ8)
  .value("PLWZ8nopc", LIEF::assembly::powerpc::OPCODE::PLWZ8nopc)
  .value("PLWZ8onlypc", LIEF::assembly::powerpc::OPCODE::PLWZ8onlypc)
  .value("PLWZ8pc", LIEF::assembly::powerpc::OPCODE::PLWZ8pc)
  .value("PLWZnopc", LIEF::assembly::powerpc::OPCODE::PLWZnopc)
  .value("PLWZonlypc", LIEF::assembly::powerpc::OPCODE::PLWZonlypc)
  .value("PLWZpc", LIEF::assembly::powerpc::OPCODE::PLWZpc)
  .value("PLXSD", LIEF::assembly::powerpc::OPCODE::PLXSD)
  .value("PLXSDnopc", LIEF::assembly::powerpc::OPCODE::PLXSDnopc)
  .value("PLXSDonlypc", LIEF::assembly::powerpc::OPCODE::PLXSDonlypc)
  .value("PLXSDpc", LIEF::assembly::powerpc::OPCODE::PLXSDpc)
  .value("PLXSSP", LIEF::assembly::powerpc::OPCODE::PLXSSP)
  .value("PLXSSPnopc", LIEF::assembly::powerpc::OPCODE::PLXSSPnopc)
  .value("PLXSSPonlypc", LIEF::assembly::powerpc::OPCODE::PLXSSPonlypc)
  .value("PLXSSPpc", LIEF::assembly::powerpc::OPCODE::PLXSSPpc)
  .value("PLXV", LIEF::assembly::powerpc::OPCODE::PLXV)
  .value("PLXVP", LIEF::assembly::powerpc::OPCODE::PLXVP)
  .value("PLXVPnopc", LIEF::assembly::powerpc::OPCODE::PLXVPnopc)
  .value("PLXVPonlypc", LIEF::assembly::powerpc::OPCODE::PLXVPonlypc)
  .value("PLXVPpc", LIEF::assembly::powerpc::OPCODE::PLXVPpc)
  .value("PLXVnopc", LIEF::assembly::powerpc::OPCODE::PLXVnopc)
  .value("PLXVonlypc", LIEF::assembly::powerpc::OPCODE::PLXVonlypc)
  .value("PLXVpc", LIEF::assembly::powerpc::OPCODE::PLXVpc)
  .value("PMXVBF16GER2", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2)
  .value("PMXVBF16GER2NN", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2NN)
  .value("PMXVBF16GER2NP", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2NP)
  .value("PMXVBF16GER2PN", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2PN)
  .value("PMXVBF16GER2PP", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2PP)
  .value("PMXVBF16GER2W", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2W)
  .value("PMXVBF16GER2WNN", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2WNN)
  .value("PMXVBF16GER2WNP", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2WNP)
  .value("PMXVBF16GER2WPN", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2WPN)
  .value("PMXVBF16GER2WPP", LIEF::assembly::powerpc::OPCODE::PMXVBF16GER2WPP)
  .value("PMXVF16GER2", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2)
  .value("PMXVF16GER2NN", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2NN)
  .value("PMXVF16GER2NP", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2NP)
  .value("PMXVF16GER2PN", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2PN)
  .value("PMXVF16GER2PP", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2PP)
  .value("PMXVF16GER2W", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2W)
  .value("PMXVF16GER2WNN", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2WNN)
  .value("PMXVF16GER2WNP", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2WNP)
  .value("PMXVF16GER2WPN", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2WPN)
  .value("PMXVF16GER2WPP", LIEF::assembly::powerpc::OPCODE::PMXVF16GER2WPP)
  .value("PMXVF32GER", LIEF::assembly::powerpc::OPCODE::PMXVF32GER)
  .value("PMXVF32GERNN", LIEF::assembly::powerpc::OPCODE::PMXVF32GERNN)
  .value("PMXVF32GERNP", LIEF::assembly::powerpc::OPCODE::PMXVF32GERNP)
  .value("PMXVF32GERPN", LIEF::assembly::powerpc::OPCODE::PMXVF32GERPN)
  .value("PMXVF32GERPP", LIEF::assembly::powerpc::OPCODE::PMXVF32GERPP)
  .value("PMXVF32GERW", LIEF::assembly::powerpc::OPCODE::PMXVF32GERW)
  .value("PMXVF32GERWNN", LIEF::assembly::powerpc::OPCODE::PMXVF32GERWNN)
  .value("PMXVF32GERWNP", LIEF::assembly::powerpc::OPCODE::PMXVF32GERWNP)
  .value("PMXVF32GERWPN", LIEF::assembly::powerpc::OPCODE::PMXVF32GERWPN)
  .value("PMXVF32GERWPP", LIEF::assembly::powerpc::OPCODE::PMXVF32GERWPP)
  .value("PMXVF64GER", LIEF::assembly::powerpc::OPCODE::PMXVF64GER)
  .value("PMXVF64GERNN", LIEF::assembly::powerpc::OPCODE::PMXVF64GERNN)
  .value("PMXVF64GERNP", LIEF::assembly::powerpc::OPCODE::PMXVF64GERNP)
  .value("PMXVF64GERPN", LIEF::assembly::powerpc::OPCODE::PMXVF64GERPN)
  .value("PMXVF64GERPP", LIEF::assembly::powerpc::OPCODE::PMXVF64GERPP)
  .value("PMXVF64GERW", LIEF::assembly::powerpc::OPCODE::PMXVF64GERW)
  .value("PMXVF64GERWNN", LIEF::assembly::powerpc::OPCODE::PMXVF64GERWNN)
  .value("PMXVF64GERWNP", LIEF::assembly::powerpc::OPCODE::PMXVF64GERWNP)
  .value("PMXVF64GERWPN", LIEF::assembly::powerpc::OPCODE::PMXVF64GERWPN)
  .value("PMXVF64GERWPP", LIEF::assembly::powerpc::OPCODE::PMXVF64GERWPP)
  .value("PMXVI16GER2", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2)
  .value("PMXVI16GER2PP", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2PP)
  .value("PMXVI16GER2S", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2S)
  .value("PMXVI16GER2SPP", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2SPP)
  .value("PMXVI16GER2SW", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2SW)
  .value("PMXVI16GER2SWPP", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2SWPP)
  .value("PMXVI16GER2W", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2W)
  .value("PMXVI16GER2WPP", LIEF::assembly::powerpc::OPCODE::PMXVI16GER2WPP)
  .value("PMXVI4GER8", LIEF::assembly::powerpc::OPCODE::PMXVI4GER8)
  .value("PMXVI4GER8PP", LIEF::assembly::powerpc::OPCODE::PMXVI4GER8PP)
  .value("PMXVI4GER8W", LIEF::assembly::powerpc::OPCODE::PMXVI4GER8W)
  .value("PMXVI4GER8WPP", LIEF::assembly::powerpc::OPCODE::PMXVI4GER8WPP)
  .value("PMXVI8GER4", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4)
  .value("PMXVI8GER4PP", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4PP)
  .value("PMXVI8GER4SPP", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4SPP)
  .value("PMXVI8GER4W", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4W)
  .value("PMXVI8GER4WPP", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4WPP)
  .value("PMXVI8GER4WSPP", LIEF::assembly::powerpc::OPCODE::PMXVI8GER4WSPP)
  .value("POPCNTB", LIEF::assembly::powerpc::OPCODE::POPCNTB)
  .value("POPCNTB8", LIEF::assembly::powerpc::OPCODE::POPCNTB8)
  .value("POPCNTD", LIEF::assembly::powerpc::OPCODE::POPCNTD)
  .value("POPCNTW", LIEF::assembly::powerpc::OPCODE::POPCNTW)
  .value("PPC32GOT", LIEF::assembly::powerpc::OPCODE::PPC32GOT)
  .value("PPC32PICGOT", LIEF::assembly::powerpc::OPCODE::PPC32PICGOT)
  .value("PREPARE_PROBED_ALLOCA_32", LIEF::assembly::powerpc::OPCODE::PREPARE_PROBED_ALLOCA_32)
  .value("PREPARE_PROBED_ALLOCA_64", LIEF::assembly::powerpc::OPCODE::PREPARE_PROBED_ALLOCA_64)
  .value("PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_32", LIEF::assembly::powerpc::OPCODE::PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_32)
  .value("PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_64", LIEF::assembly::powerpc::OPCODE::PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_64)
  .value("PROBED_ALLOCA_32", LIEF::assembly::powerpc::OPCODE::PROBED_ALLOCA_32)
  .value("PROBED_ALLOCA_64", LIEF::assembly::powerpc::OPCODE::PROBED_ALLOCA_64)
  .value("PROBED_STACKALLOC_32", LIEF::assembly::powerpc::OPCODE::PROBED_STACKALLOC_32)
  .value("PROBED_STACKALLOC_64", LIEF::assembly::powerpc::OPCODE::PROBED_STACKALLOC_64)
  .value("PSTB", LIEF::assembly::powerpc::OPCODE::PSTB)
  .value("PSTB8", LIEF::assembly::powerpc::OPCODE::PSTB8)
  .value("PSTB8nopc", LIEF::assembly::powerpc::OPCODE::PSTB8nopc)
  .value("PSTB8onlypc", LIEF::assembly::powerpc::OPCODE::PSTB8onlypc)
  .value("PSTB8pc", LIEF::assembly::powerpc::OPCODE::PSTB8pc)
  .value("PSTBnopc", LIEF::assembly::powerpc::OPCODE::PSTBnopc)
  .value("PSTBonlypc", LIEF::assembly::powerpc::OPCODE::PSTBonlypc)
  .value("PSTBpc", LIEF::assembly::powerpc::OPCODE::PSTBpc)
  .value("PSTD", LIEF::assembly::powerpc::OPCODE::PSTD)
  .value("PSTDnopc", LIEF::assembly::powerpc::OPCODE::PSTDnopc)
  .value("PSTDonlypc", LIEF::assembly::powerpc::OPCODE::PSTDonlypc)
  .value("PSTDpc", LIEF::assembly::powerpc::OPCODE::PSTDpc)
  .value("PSTFD", LIEF::assembly::powerpc::OPCODE::PSTFD)
  .value("PSTFDnopc", LIEF::assembly::powerpc::OPCODE::PSTFDnopc)
  .value("PSTFDonlypc", LIEF::assembly::powerpc::OPCODE::PSTFDonlypc)
  .value("PSTFDpc", LIEF::assembly::powerpc::OPCODE::PSTFDpc)
  .value("PSTFS", LIEF::assembly::powerpc::OPCODE::PSTFS)
  .value("PSTFSnopc", LIEF::assembly::powerpc::OPCODE::PSTFSnopc)
  .value("PSTFSonlypc", LIEF::assembly::powerpc::OPCODE::PSTFSonlypc)
  .value("PSTFSpc", LIEF::assembly::powerpc::OPCODE::PSTFSpc)
  .value("PSTH", LIEF::assembly::powerpc::OPCODE::PSTH)
  .value("PSTH8", LIEF::assembly::powerpc::OPCODE::PSTH8)
  .value("PSTH8nopc", LIEF::assembly::powerpc::OPCODE::PSTH8nopc)
  .value("PSTH8onlypc", LIEF::assembly::powerpc::OPCODE::PSTH8onlypc)
  .value("PSTH8pc", LIEF::assembly::powerpc::OPCODE::PSTH8pc)
  .value("PSTHnopc", LIEF::assembly::powerpc::OPCODE::PSTHnopc)
  .value("PSTHonlypc", LIEF::assembly::powerpc::OPCODE::PSTHonlypc)
  .value("PSTHpc", LIEF::assembly::powerpc::OPCODE::PSTHpc)
  .value("PSTW", LIEF::assembly::powerpc::OPCODE::PSTW)
  .value("PSTW8", LIEF::assembly::powerpc::OPCODE::PSTW8)
  .value("PSTW8nopc", LIEF::assembly::powerpc::OPCODE::PSTW8nopc)
  .value("PSTW8onlypc", LIEF::assembly::powerpc::OPCODE::PSTW8onlypc)
  .value("PSTW8pc", LIEF::assembly::powerpc::OPCODE::PSTW8pc)
  .value("PSTWnopc", LIEF::assembly::powerpc::OPCODE::PSTWnopc)
  .value("PSTWonlypc", LIEF::assembly::powerpc::OPCODE::PSTWonlypc)
  .value("PSTWpc", LIEF::assembly::powerpc::OPCODE::PSTWpc)
  .value("PSTXSD", LIEF::assembly::powerpc::OPCODE::PSTXSD)
  .value("PSTXSDnopc", LIEF::assembly::powerpc::OPCODE::PSTXSDnopc)
  .value("PSTXSDonlypc", LIEF::assembly::powerpc::OPCODE::PSTXSDonlypc)
  .value("PSTXSDpc", LIEF::assembly::powerpc::OPCODE::PSTXSDpc)
  .value("PSTXSSP", LIEF::assembly::powerpc::OPCODE::PSTXSSP)
  .value("PSTXSSPnopc", LIEF::assembly::powerpc::OPCODE::PSTXSSPnopc)
  .value("PSTXSSPonlypc", LIEF::assembly::powerpc::OPCODE::PSTXSSPonlypc)
  .value("PSTXSSPpc", LIEF::assembly::powerpc::OPCODE::PSTXSSPpc)
  .value("PSTXV", LIEF::assembly::powerpc::OPCODE::PSTXV)
  .value("PSTXVP", LIEF::assembly::powerpc::OPCODE::PSTXVP)
  .value("PSTXVPnopc", LIEF::assembly::powerpc::OPCODE::PSTXVPnopc);
  opcodes.value("PSTXVPonlypc", LIEF::assembly::powerpc::OPCODE::PSTXVPonlypc)
  .value("PSTXVPpc", LIEF::assembly::powerpc::OPCODE::PSTXVPpc)
  .value("PSTXVnopc", LIEF::assembly::powerpc::OPCODE::PSTXVnopc)
  .value("PSTXVonlypc", LIEF::assembly::powerpc::OPCODE::PSTXVonlypc)
  .value("PSTXVpc", LIEF::assembly::powerpc::OPCODE::PSTXVpc)
  .value("PseudoEIEIO", LIEF::assembly::powerpc::OPCODE::PseudoEIEIO)
  .value("RESTORE_ACC", LIEF::assembly::powerpc::OPCODE::RESTORE_ACC)
  .value("RESTORE_CR", LIEF::assembly::powerpc::OPCODE::RESTORE_CR)
  .value("RESTORE_CRBIT", LIEF::assembly::powerpc::OPCODE::RESTORE_CRBIT)
  .value("RESTORE_QUADWORD", LIEF::assembly::powerpc::OPCODE::RESTORE_QUADWORD)
  .value("RESTORE_UACC", LIEF::assembly::powerpc::OPCODE::RESTORE_UACC)
  .value("RESTORE_WACC", LIEF::assembly::powerpc::OPCODE::RESTORE_WACC)
  .value("RFCI", LIEF::assembly::powerpc::OPCODE::RFCI)
  .value("RFDI", LIEF::assembly::powerpc::OPCODE::RFDI)
  .value("RFEBB", LIEF::assembly::powerpc::OPCODE::RFEBB)
  .value("RFI", LIEF::assembly::powerpc::OPCODE::RFI)
  .value("RFID", LIEF::assembly::powerpc::OPCODE::RFID)
  .value("RFMCI", LIEF::assembly::powerpc::OPCODE::RFMCI)
  .value("RLDCL", LIEF::assembly::powerpc::OPCODE::RLDCL)
  .value("RLDCL_rec", LIEF::assembly::powerpc::OPCODE::RLDCL_rec)
  .value("RLDCR", LIEF::assembly::powerpc::OPCODE::RLDCR)
  .value("RLDCR_rec", LIEF::assembly::powerpc::OPCODE::RLDCR_rec)
  .value("RLDIC", LIEF::assembly::powerpc::OPCODE::RLDIC)
  .value("RLDICL", LIEF::assembly::powerpc::OPCODE::RLDICL)
  .value("RLDICL_32", LIEF::assembly::powerpc::OPCODE::RLDICL_32)
  .value("RLDICL_32_64", LIEF::assembly::powerpc::OPCODE::RLDICL_32_64)
  .value("RLDICL_32_rec", LIEF::assembly::powerpc::OPCODE::RLDICL_32_rec)
  .value("RLDICL_rec", LIEF::assembly::powerpc::OPCODE::RLDICL_rec)
  .value("RLDICR", LIEF::assembly::powerpc::OPCODE::RLDICR)
  .value("RLDICR_32", LIEF::assembly::powerpc::OPCODE::RLDICR_32)
  .value("RLDICR_rec", LIEF::assembly::powerpc::OPCODE::RLDICR_rec)
  .value("RLDIC_rec", LIEF::assembly::powerpc::OPCODE::RLDIC_rec)
  .value("RLDIMI", LIEF::assembly::powerpc::OPCODE::RLDIMI)
  .value("RLDIMI_rec", LIEF::assembly::powerpc::OPCODE::RLDIMI_rec)
  .value("RLWIMI", LIEF::assembly::powerpc::OPCODE::RLWIMI)
  .value("RLWIMI8", LIEF::assembly::powerpc::OPCODE::RLWIMI8)
  .value("RLWIMI8_rec", LIEF::assembly::powerpc::OPCODE::RLWIMI8_rec)
  .value("RLWIMI_rec", LIEF::assembly::powerpc::OPCODE::RLWIMI_rec)
  .value("RLWINM", LIEF::assembly::powerpc::OPCODE::RLWINM)
  .value("RLWINM8", LIEF::assembly::powerpc::OPCODE::RLWINM8)
  .value("RLWINM8_rec", LIEF::assembly::powerpc::OPCODE::RLWINM8_rec)
  .value("RLWINM_rec", LIEF::assembly::powerpc::OPCODE::RLWINM_rec)
  .value("RLWNM", LIEF::assembly::powerpc::OPCODE::RLWNM)
  .value("RLWNM8", LIEF::assembly::powerpc::OPCODE::RLWNM8)
  .value("RLWNM8_rec", LIEF::assembly::powerpc::OPCODE::RLWNM8_rec)
  .value("RLWNM_rec", LIEF::assembly::powerpc::OPCODE::RLWNM_rec)
  .value("ReadTB", LIEF::assembly::powerpc::OPCODE::ReadTB)
  .value("SC", LIEF::assembly::powerpc::OPCODE::SC)
  .value("SCV", LIEF::assembly::powerpc::OPCODE::SCV)
  .value("SELECT_CC_F16", LIEF::assembly::powerpc::OPCODE::SELECT_CC_F16)
  .value("SELECT_CC_F4", LIEF::assembly::powerpc::OPCODE::SELECT_CC_F4)
  .value("SELECT_CC_F8", LIEF::assembly::powerpc::OPCODE::SELECT_CC_F8)
  .value("SELECT_CC_I4", LIEF::assembly::powerpc::OPCODE::SELECT_CC_I4)
  .value("SELECT_CC_I8", LIEF::assembly::powerpc::OPCODE::SELECT_CC_I8)
  .value("SELECT_CC_SPE", LIEF::assembly::powerpc::OPCODE::SELECT_CC_SPE)
  .value("SELECT_CC_SPE4", LIEF::assembly::powerpc::OPCODE::SELECT_CC_SPE4)
  .value("SELECT_CC_VRRC", LIEF::assembly::powerpc::OPCODE::SELECT_CC_VRRC)
  .value("SELECT_CC_VSFRC", LIEF::assembly::powerpc::OPCODE::SELECT_CC_VSFRC)
  .value("SELECT_CC_VSRC", LIEF::assembly::powerpc::OPCODE::SELECT_CC_VSRC)
  .value("SELECT_CC_VSSRC", LIEF::assembly::powerpc::OPCODE::SELECT_CC_VSSRC)
  .value("SELECT_F16", LIEF::assembly::powerpc::OPCODE::SELECT_F16)
  .value("SELECT_F4", LIEF::assembly::powerpc::OPCODE::SELECT_F4)
  .value("SELECT_F8", LIEF::assembly::powerpc::OPCODE::SELECT_F8)
  .value("SELECT_I4", LIEF::assembly::powerpc::OPCODE::SELECT_I4)
  .value("SELECT_I8", LIEF::assembly::powerpc::OPCODE::SELECT_I8)
  .value("SELECT_SPE", LIEF::assembly::powerpc::OPCODE::SELECT_SPE)
  .value("SELECT_SPE4", LIEF::assembly::powerpc::OPCODE::SELECT_SPE4)
  .value("SELECT_VRRC", LIEF::assembly::powerpc::OPCODE::SELECT_VRRC)
  .value("SELECT_VSFRC", LIEF::assembly::powerpc::OPCODE::SELECT_VSFRC)
  .value("SELECT_VSRC", LIEF::assembly::powerpc::OPCODE::SELECT_VSRC)
  .value("SELECT_VSSRC", LIEF::assembly::powerpc::OPCODE::SELECT_VSSRC)
  .value("SETB", LIEF::assembly::powerpc::OPCODE::SETB)
  .value("SETB8", LIEF::assembly::powerpc::OPCODE::SETB8)
  .value("SETBC", LIEF::assembly::powerpc::OPCODE::SETBC)
  .value("SETBC8", LIEF::assembly::powerpc::OPCODE::SETBC8)
  .value("SETBCR", LIEF::assembly::powerpc::OPCODE::SETBCR)
  .value("SETBCR8", LIEF::assembly::powerpc::OPCODE::SETBCR8)
  .value("SETFLM", LIEF::assembly::powerpc::OPCODE::SETFLM)
  .value("SETNBC", LIEF::assembly::powerpc::OPCODE::SETNBC)
  .value("SETNBC8", LIEF::assembly::powerpc::OPCODE::SETNBC8)
  .value("SETNBCR", LIEF::assembly::powerpc::OPCODE::SETNBCR)
  .value("SETNBCR8", LIEF::assembly::powerpc::OPCODE::SETNBCR8)
  .value("SETRND", LIEF::assembly::powerpc::OPCODE::SETRND)
  .value("SETRNDi", LIEF::assembly::powerpc::OPCODE::SETRNDi)
  .value("SLBFEE_rec", LIEF::assembly::powerpc::OPCODE::SLBFEE_rec)
  .value("SLBIA", LIEF::assembly::powerpc::OPCODE::SLBIA)
  .value("SLBIE", LIEF::assembly::powerpc::OPCODE::SLBIE)
  .value("SLBIEG", LIEF::assembly::powerpc::OPCODE::SLBIEG)
  .value("SLBMFEE", LIEF::assembly::powerpc::OPCODE::SLBMFEE)
  .value("SLBMFEV", LIEF::assembly::powerpc::OPCODE::SLBMFEV)
  .value("SLBMTE", LIEF::assembly::powerpc::OPCODE::SLBMTE)
  .value("SLBSYNC", LIEF::assembly::powerpc::OPCODE::SLBSYNC)
  .value("SLD", LIEF::assembly::powerpc::OPCODE::SLD)
  .value("SLD_rec", LIEF::assembly::powerpc::OPCODE::SLD_rec)
  .value("SLW", LIEF::assembly::powerpc::OPCODE::SLW)
  .value("SLW8", LIEF::assembly::powerpc::OPCODE::SLW8)
  .value("SLW8_rec", LIEF::assembly::powerpc::OPCODE::SLW8_rec)
  .value("SLW_rec", LIEF::assembly::powerpc::OPCODE::SLW_rec)
  .value("SPELWZ", LIEF::assembly::powerpc::OPCODE::SPELWZ)
  .value("SPELWZX", LIEF::assembly::powerpc::OPCODE::SPELWZX)
  .value("SPESTW", LIEF::assembly::powerpc::OPCODE::SPESTW)
  .value("SPESTWX", LIEF::assembly::powerpc::OPCODE::SPESTWX)
  .value("SPILL_ACC", LIEF::assembly::powerpc::OPCODE::SPILL_ACC)
  .value("SPILL_CR", LIEF::assembly::powerpc::OPCODE::SPILL_CR)
  .value("SPILL_CRBIT", LIEF::assembly::powerpc::OPCODE::SPILL_CRBIT)
  .value("SPILL_QUADWORD", LIEF::assembly::powerpc::OPCODE::SPILL_QUADWORD)
  .value("SPILL_UACC", LIEF::assembly::powerpc::OPCODE::SPILL_UACC)
  .value("SPILL_WACC", LIEF::assembly::powerpc::OPCODE::SPILL_WACC)
  .value("SPLIT_QUADWORD", LIEF::assembly::powerpc::OPCODE::SPLIT_QUADWORD)
  .value("SRAD", LIEF::assembly::powerpc::OPCODE::SRAD)
  .value("SRADI", LIEF::assembly::powerpc::OPCODE::SRADI)
  .value("SRADI_32", LIEF::assembly::powerpc::OPCODE::SRADI_32)
  .value("SRADI_rec", LIEF::assembly::powerpc::OPCODE::SRADI_rec)
  .value("SRAD_rec", LIEF::assembly::powerpc::OPCODE::SRAD_rec)
  .value("SRAW", LIEF::assembly::powerpc::OPCODE::SRAW)
  .value("SRAW8", LIEF::assembly::powerpc::OPCODE::SRAW8)
  .value("SRAW8_rec", LIEF::assembly::powerpc::OPCODE::SRAW8_rec)
  .value("SRAWI", LIEF::assembly::powerpc::OPCODE::SRAWI)
  .value("SRAWI8", LIEF::assembly::powerpc::OPCODE::SRAWI8)
  .value("SRAWI8_rec", LIEF::assembly::powerpc::OPCODE::SRAWI8_rec)
  .value("SRAWI_rec", LIEF::assembly::powerpc::OPCODE::SRAWI_rec)
  .value("SRAW_rec", LIEF::assembly::powerpc::OPCODE::SRAW_rec)
  .value("SRD", LIEF::assembly::powerpc::OPCODE::SRD)
  .value("SRD_rec", LIEF::assembly::powerpc::OPCODE::SRD_rec)
  .value("SRW", LIEF::assembly::powerpc::OPCODE::SRW)
  .value("SRW8", LIEF::assembly::powerpc::OPCODE::SRW8)
  .value("SRW8_rec", LIEF::assembly::powerpc::OPCODE::SRW8_rec)
  .value("SRW_rec", LIEF::assembly::powerpc::OPCODE::SRW_rec)
  .value("STB", LIEF::assembly::powerpc::OPCODE::STB)
  .value("STB8", LIEF::assembly::powerpc::OPCODE::STB8)
  .value("STBCIX", LIEF::assembly::powerpc::OPCODE::STBCIX)
  .value("STBCX", LIEF::assembly::powerpc::OPCODE::STBCX)
  .value("STBEPX", LIEF::assembly::powerpc::OPCODE::STBEPX)
  .value("STBU", LIEF::assembly::powerpc::OPCODE::STBU)
  .value("STBU8", LIEF::assembly::powerpc::OPCODE::STBU8)
  .value("STBUX", LIEF::assembly::powerpc::OPCODE::STBUX)
  .value("STBUX8", LIEF::assembly::powerpc::OPCODE::STBUX8)
  .value("STBX", LIEF::assembly::powerpc::OPCODE::STBX)
  .value("STBX8", LIEF::assembly::powerpc::OPCODE::STBX8)
  .value("STBXTLS", LIEF::assembly::powerpc::OPCODE::STBXTLS)
  .value("STBXTLS_", LIEF::assembly::powerpc::OPCODE::STBXTLS_)
  .value("STBXTLS_32", LIEF::assembly::powerpc::OPCODE::STBXTLS_32)
  .value("STD", LIEF::assembly::powerpc::OPCODE::STD)
  .value("STDAT", LIEF::assembly::powerpc::OPCODE::STDAT)
  .value("STDBRX", LIEF::assembly::powerpc::OPCODE::STDBRX)
  .value("STDCIX", LIEF::assembly::powerpc::OPCODE::STDCIX)
  .value("STDCX", LIEF::assembly::powerpc::OPCODE::STDCX)
  .value("STDU", LIEF::assembly::powerpc::OPCODE::STDU)
  .value("STDUX", LIEF::assembly::powerpc::OPCODE::STDUX)
  .value("STDX", LIEF::assembly::powerpc::OPCODE::STDX)
  .value("STDXTLS", LIEF::assembly::powerpc::OPCODE::STDXTLS)
  .value("STDXTLS_", LIEF::assembly::powerpc::OPCODE::STDXTLS_)
  .value("STFD", LIEF::assembly::powerpc::OPCODE::STFD)
  .value("STFDEPX", LIEF::assembly::powerpc::OPCODE::STFDEPX)
  .value("STFDU", LIEF::assembly::powerpc::OPCODE::STFDU)
  .value("STFDUX", LIEF::assembly::powerpc::OPCODE::STFDUX)
  .value("STFDX", LIEF::assembly::powerpc::OPCODE::STFDX)
  .value("STFDXTLS", LIEF::assembly::powerpc::OPCODE::STFDXTLS)
  .value("STFDXTLS_", LIEF::assembly::powerpc::OPCODE::STFDXTLS_)
  .value("STFIWX", LIEF::assembly::powerpc::OPCODE::STFIWX)
  .value("STFS", LIEF::assembly::powerpc::OPCODE::STFS)
  .value("STFSU", LIEF::assembly::powerpc::OPCODE::STFSU)
  .value("STFSUX", LIEF::assembly::powerpc::OPCODE::STFSUX)
  .value("STFSX", LIEF::assembly::powerpc::OPCODE::STFSX)
  .value("STFSXTLS", LIEF::assembly::powerpc::OPCODE::STFSXTLS)
  .value("STFSXTLS_", LIEF::assembly::powerpc::OPCODE::STFSXTLS_)
  .value("STH", LIEF::assembly::powerpc::OPCODE::STH)
  .value("STH8", LIEF::assembly::powerpc::OPCODE::STH8)
  .value("STHBRX", LIEF::assembly::powerpc::OPCODE::STHBRX)
  .value("STHCIX", LIEF::assembly::powerpc::OPCODE::STHCIX)
  .value("STHCX", LIEF::assembly::powerpc::OPCODE::STHCX)
  .value("STHEPX", LIEF::assembly::powerpc::OPCODE::STHEPX)
  .value("STHU", LIEF::assembly::powerpc::OPCODE::STHU)
  .value("STHU8", LIEF::assembly::powerpc::OPCODE::STHU8)
  .value("STHUX", LIEF::assembly::powerpc::OPCODE::STHUX)
  .value("STHUX8", LIEF::assembly::powerpc::OPCODE::STHUX8)
  .value("STHX", LIEF::assembly::powerpc::OPCODE::STHX)
  .value("STHX8", LIEF::assembly::powerpc::OPCODE::STHX8)
  .value("STHXTLS", LIEF::assembly::powerpc::OPCODE::STHXTLS)
  .value("STHXTLS_", LIEF::assembly::powerpc::OPCODE::STHXTLS_)
  .value("STHXTLS_32", LIEF::assembly::powerpc::OPCODE::STHXTLS_32)
  .value("STMW", LIEF::assembly::powerpc::OPCODE::STMW)
  .value("STOP", LIEF::assembly::powerpc::OPCODE::STOP)
  .value("STQ", LIEF::assembly::powerpc::OPCODE::STQ)
  .value("STQCX", LIEF::assembly::powerpc::OPCODE::STQCX)
  .value("STQX_PSEUDO", LIEF::assembly::powerpc::OPCODE::STQX_PSEUDO)
  .value("STSWI", LIEF::assembly::powerpc::OPCODE::STSWI)
  .value("STVEBX", LIEF::assembly::powerpc::OPCODE::STVEBX)
  .value("STVEHX", LIEF::assembly::powerpc::OPCODE::STVEHX)
  .value("STVEWX", LIEF::assembly::powerpc::OPCODE::STVEWX)
  .value("STVX", LIEF::assembly::powerpc::OPCODE::STVX)
  .value("STVXL", LIEF::assembly::powerpc::OPCODE::STVXL)
  .value("STW", LIEF::assembly::powerpc::OPCODE::STW)
  .value("STW8", LIEF::assembly::powerpc::OPCODE::STW8)
  .value("STWAT", LIEF::assembly::powerpc::OPCODE::STWAT)
  .value("STWBRX", LIEF::assembly::powerpc::OPCODE::STWBRX)
  .value("STWCIX", LIEF::assembly::powerpc::OPCODE::STWCIX)
  .value("STWCX", LIEF::assembly::powerpc::OPCODE::STWCX)
  .value("STWEPX", LIEF::assembly::powerpc::OPCODE::STWEPX)
  .value("STWU", LIEF::assembly::powerpc::OPCODE::STWU)
  .value("STWU8", LIEF::assembly::powerpc::OPCODE::STWU8)
  .value("STWUX", LIEF::assembly::powerpc::OPCODE::STWUX)
  .value("STWUX8", LIEF::assembly::powerpc::OPCODE::STWUX8)
  .value("STWX", LIEF::assembly::powerpc::OPCODE::STWX)
  .value("STWX8", LIEF::assembly::powerpc::OPCODE::STWX8)
  .value("STWXTLS", LIEF::assembly::powerpc::OPCODE::STWXTLS)
  .value("STWXTLS_", LIEF::assembly::powerpc::OPCODE::STWXTLS_)
  .value("STWXTLS_32", LIEF::assembly::powerpc::OPCODE::STWXTLS_32)
  .value("STXSD", LIEF::assembly::powerpc::OPCODE::STXSD)
  .value("STXSDX", LIEF::assembly::powerpc::OPCODE::STXSDX)
  .value("STXSIBX", LIEF::assembly::powerpc::OPCODE::STXSIBX)
  .value("STXSIBXv", LIEF::assembly::powerpc::OPCODE::STXSIBXv)
  .value("STXSIHX", LIEF::assembly::powerpc::OPCODE::STXSIHX)
  .value("STXSIHXv", LIEF::assembly::powerpc::OPCODE::STXSIHXv)
  .value("STXSIWX", LIEF::assembly::powerpc::OPCODE::STXSIWX)
  .value("STXSSP", LIEF::assembly::powerpc::OPCODE::STXSSP)
  .value("STXSSPX", LIEF::assembly::powerpc::OPCODE::STXSSPX)
  .value("STXV", LIEF::assembly::powerpc::OPCODE::STXV)
  .value("STXVB16X", LIEF::assembly::powerpc::OPCODE::STXVB16X)
  .value("STXVD2X", LIEF::assembly::powerpc::OPCODE::STXVD2X)
  .value("STXVH8X", LIEF::assembly::powerpc::OPCODE::STXVH8X)
  .value("STXVL", LIEF::assembly::powerpc::OPCODE::STXVL)
  .value("STXVLL", LIEF::assembly::powerpc::OPCODE::STXVLL)
  .value("STXVP", LIEF::assembly::powerpc::OPCODE::STXVP)
  .value("STXVPRL", LIEF::assembly::powerpc::OPCODE::STXVPRL)
  .value("STXVPRLL", LIEF::assembly::powerpc::OPCODE::STXVPRLL)
  .value("STXVPX", LIEF::assembly::powerpc::OPCODE::STXVPX)
  .value("STXVRBX", LIEF::assembly::powerpc::OPCODE::STXVRBX)
  .value("STXVRDX", LIEF::assembly::powerpc::OPCODE::STXVRDX)
  .value("STXVRHX", LIEF::assembly::powerpc::OPCODE::STXVRHX)
  .value("STXVRL", LIEF::assembly::powerpc::OPCODE::STXVRL)
  .value("STXVRLL", LIEF::assembly::powerpc::OPCODE::STXVRLL)
  .value("STXVRWX", LIEF::assembly::powerpc::OPCODE::STXVRWX)
  .value("STXVW4X", LIEF::assembly::powerpc::OPCODE::STXVW4X)
  .value("STXVX", LIEF::assembly::powerpc::OPCODE::STXVX)
  .value("SUBF", LIEF::assembly::powerpc::OPCODE::SUBF)
  .value("SUBF8", LIEF::assembly::powerpc::OPCODE::SUBF8)
  .value("SUBF8O", LIEF::assembly::powerpc::OPCODE::SUBF8O)
  .value("SUBF8O_rec", LIEF::assembly::powerpc::OPCODE::SUBF8O_rec)
  .value("SUBF8_rec", LIEF::assembly::powerpc::OPCODE::SUBF8_rec)
  .value("SUBFC", LIEF::assembly::powerpc::OPCODE::SUBFC)
  .value("SUBFC8", LIEF::assembly::powerpc::OPCODE::SUBFC8)
  .value("SUBFC8O", LIEF::assembly::powerpc::OPCODE::SUBFC8O)
  .value("SUBFC8O_rec", LIEF::assembly::powerpc::OPCODE::SUBFC8O_rec)
  .value("SUBFC8_rec", LIEF::assembly::powerpc::OPCODE::SUBFC8_rec)
  .value("SUBFCO", LIEF::assembly::powerpc::OPCODE::SUBFCO)
  .value("SUBFCO_rec", LIEF::assembly::powerpc::OPCODE::SUBFCO_rec)
  .value("SUBFC_rec", LIEF::assembly::powerpc::OPCODE::SUBFC_rec)
  .value("SUBFE", LIEF::assembly::powerpc::OPCODE::SUBFE)
  .value("SUBFE8", LIEF::assembly::powerpc::OPCODE::SUBFE8)
  .value("SUBFE8O", LIEF::assembly::powerpc::OPCODE::SUBFE8O)
  .value("SUBFE8O_rec", LIEF::assembly::powerpc::OPCODE::SUBFE8O_rec)
  .value("SUBFE8_rec", LIEF::assembly::powerpc::OPCODE::SUBFE8_rec)
  .value("SUBFEO", LIEF::assembly::powerpc::OPCODE::SUBFEO)
  .value("SUBFEO_rec", LIEF::assembly::powerpc::OPCODE::SUBFEO_rec)
  .value("SUBFE_rec", LIEF::assembly::powerpc::OPCODE::SUBFE_rec)
  .value("SUBFIC", LIEF::assembly::powerpc::OPCODE::SUBFIC)
  .value("SUBFIC8", LIEF::assembly::powerpc::OPCODE::SUBFIC8)
  .value("SUBFME", LIEF::assembly::powerpc::OPCODE::SUBFME)
  .value("SUBFME8", LIEF::assembly::powerpc::OPCODE::SUBFME8)
  .value("SUBFME8O", LIEF::assembly::powerpc::OPCODE::SUBFME8O)
  .value("SUBFME8O_rec", LIEF::assembly::powerpc::OPCODE::SUBFME8O_rec)
  .value("SUBFME8_rec", LIEF::assembly::powerpc::OPCODE::SUBFME8_rec)
  .value("SUBFMEO", LIEF::assembly::powerpc::OPCODE::SUBFMEO)
  .value("SUBFMEO_rec", LIEF::assembly::powerpc::OPCODE::SUBFMEO_rec)
  .value("SUBFME_rec", LIEF::assembly::powerpc::OPCODE::SUBFME_rec)
  .value("SUBFO", LIEF::assembly::powerpc::OPCODE::SUBFO)
  .value("SUBFO_rec", LIEF::assembly::powerpc::OPCODE::SUBFO_rec)
  .value("SUBFUS", LIEF::assembly::powerpc::OPCODE::SUBFUS)
  .value("SUBFUS_rec", LIEF::assembly::powerpc::OPCODE::SUBFUS_rec)
  .value("SUBFZE", LIEF::assembly::powerpc::OPCODE::SUBFZE)
  .value("SUBFZE8", LIEF::assembly::powerpc::OPCODE::SUBFZE8)
  .value("SUBFZE8O", LIEF::assembly::powerpc::OPCODE::SUBFZE8O)
  .value("SUBFZE8O_rec", LIEF::assembly::powerpc::OPCODE::SUBFZE8O_rec)
  .value("SUBFZE8_rec", LIEF::assembly::powerpc::OPCODE::SUBFZE8_rec)
  .value("SUBFZEO", LIEF::assembly::powerpc::OPCODE::SUBFZEO)
  .value("SUBFZEO_rec", LIEF::assembly::powerpc::OPCODE::SUBFZEO_rec)
  .value("SUBFZE_rec", LIEF::assembly::powerpc::OPCODE::SUBFZE_rec)
  .value("SUBF_rec", LIEF::assembly::powerpc::OPCODE::SUBF_rec)
  .value("SYNC", LIEF::assembly::powerpc::OPCODE::SYNC)
  .value("SYNCP10", LIEF::assembly::powerpc::OPCODE::SYNCP10)
  .value("TABORT", LIEF::assembly::powerpc::OPCODE::TABORT)
  .value("TABORTDC", LIEF::assembly::powerpc::OPCODE::TABORTDC)
  .value("TABORTDCI", LIEF::assembly::powerpc::OPCODE::TABORTDCI)
  .value("TABORTWC", LIEF::assembly::powerpc::OPCODE::TABORTWC)
  .value("TABORTWCI", LIEF::assembly::powerpc::OPCODE::TABORTWCI)
  .value("TAILB", LIEF::assembly::powerpc::OPCODE::TAILB)
  .value("TAILB8", LIEF::assembly::powerpc::OPCODE::TAILB8)
  .value("TAILBA", LIEF::assembly::powerpc::OPCODE::TAILBA)
  .value("TAILBA8", LIEF::assembly::powerpc::OPCODE::TAILBA8)
  .value("TAILBCTR", LIEF::assembly::powerpc::OPCODE::TAILBCTR)
  .value("TAILBCTR8", LIEF::assembly::powerpc::OPCODE::TAILBCTR8)
  .value("TBEGIN", LIEF::assembly::powerpc::OPCODE::TBEGIN)
  .value("TBEGIN_RET", LIEF::assembly::powerpc::OPCODE::TBEGIN_RET)
  .value("TCHECK", LIEF::assembly::powerpc::OPCODE::TCHECK)
  .value("TCHECK_RET", LIEF::assembly::powerpc::OPCODE::TCHECK_RET)
  .value("TCRETURNai", LIEF::assembly::powerpc::OPCODE::TCRETURNai)
  .value("TCRETURNai8", LIEF::assembly::powerpc::OPCODE::TCRETURNai8)
  .value("TCRETURNdi", LIEF::assembly::powerpc::OPCODE::TCRETURNdi)
  .value("TCRETURNdi8", LIEF::assembly::powerpc::OPCODE::TCRETURNdi8);
  opcodes.value("TCRETURNri", LIEF::assembly::powerpc::OPCODE::TCRETURNri)
  .value("TCRETURNri8", LIEF::assembly::powerpc::OPCODE::TCRETURNri8)
  .value("TD", LIEF::assembly::powerpc::OPCODE::TD)
  .value("TDI", LIEF::assembly::powerpc::OPCODE::TDI)
  .value("TEND", LIEF::assembly::powerpc::OPCODE::TEND)
  .value("TLBIA", LIEF::assembly::powerpc::OPCODE::TLBIA)
  .value("TLBIE", LIEF::assembly::powerpc::OPCODE::TLBIE)
  .value("TLBIEL", LIEF::assembly::powerpc::OPCODE::TLBIEL)
  .value("TLBILX", LIEF::assembly::powerpc::OPCODE::TLBILX)
  .value("TLBIVAX", LIEF::assembly::powerpc::OPCODE::TLBIVAX)
  .value("TLBLD", LIEF::assembly::powerpc::OPCODE::TLBLD)
  .value("TLBLI", LIEF::assembly::powerpc::OPCODE::TLBLI)
  .value("TLBRE", LIEF::assembly::powerpc::OPCODE::TLBRE)
  .value("TLBRE2", LIEF::assembly::powerpc::OPCODE::TLBRE2)
  .value("TLBSX", LIEF::assembly::powerpc::OPCODE::TLBSX)
  .value("TLBSX2", LIEF::assembly::powerpc::OPCODE::TLBSX2)
  .value("TLBSX2D", LIEF::assembly::powerpc::OPCODE::TLBSX2D)
  .value("TLBSYNC", LIEF::assembly::powerpc::OPCODE::TLBSYNC)
  .value("TLBWE", LIEF::assembly::powerpc::OPCODE::TLBWE)
  .value("TLBWE2", LIEF::assembly::powerpc::OPCODE::TLBWE2)
  .value("TLSGDAIX", LIEF::assembly::powerpc::OPCODE::TLSGDAIX)
  .value("TLSGDAIX8", LIEF::assembly::powerpc::OPCODE::TLSGDAIX8)
  .value("TLSLDAIX", LIEF::assembly::powerpc::OPCODE::TLSLDAIX)
  .value("TLSLDAIX8", LIEF::assembly::powerpc::OPCODE::TLSLDAIX8)
  .value("TRAP", LIEF::assembly::powerpc::OPCODE::TRAP)
  .value("TRECHKPT", LIEF::assembly::powerpc::OPCODE::TRECHKPT)
  .value("TRECLAIM", LIEF::assembly::powerpc::OPCODE::TRECLAIM)
  .value("TSR", LIEF::assembly::powerpc::OPCODE::TSR)
  .value("TW", LIEF::assembly::powerpc::OPCODE::TW)
  .value("TWI", LIEF::assembly::powerpc::OPCODE::TWI)
  .value("UNENCODED_NOP", LIEF::assembly::powerpc::OPCODE::UNENCODED_NOP)
  .value("UpdateGBR", LIEF::assembly::powerpc::OPCODE::UpdateGBR)
  .value("VABSDUB", LIEF::assembly::powerpc::OPCODE::VABSDUB)
  .value("VABSDUH", LIEF::assembly::powerpc::OPCODE::VABSDUH)
  .value("VABSDUW", LIEF::assembly::powerpc::OPCODE::VABSDUW)
  .value("VADDCUQ", LIEF::assembly::powerpc::OPCODE::VADDCUQ)
  .value("VADDCUW", LIEF::assembly::powerpc::OPCODE::VADDCUW)
  .value("VADDECUQ", LIEF::assembly::powerpc::OPCODE::VADDECUQ)
  .value("VADDEUQM", LIEF::assembly::powerpc::OPCODE::VADDEUQM)
  .value("VADDFP", LIEF::assembly::powerpc::OPCODE::VADDFP)
  .value("VADDSBS", LIEF::assembly::powerpc::OPCODE::VADDSBS)
  .value("VADDSHS", LIEF::assembly::powerpc::OPCODE::VADDSHS)
  .value("VADDSWS", LIEF::assembly::powerpc::OPCODE::VADDSWS)
  .value("VADDUBM", LIEF::assembly::powerpc::OPCODE::VADDUBM)
  .value("VADDUBS", LIEF::assembly::powerpc::OPCODE::VADDUBS)
  .value("VADDUDM", LIEF::assembly::powerpc::OPCODE::VADDUDM)
  .value("VADDUHM", LIEF::assembly::powerpc::OPCODE::VADDUHM)
  .value("VADDUHS", LIEF::assembly::powerpc::OPCODE::VADDUHS)
  .value("VADDUQM", LIEF::assembly::powerpc::OPCODE::VADDUQM)
  .value("VADDUWM", LIEF::assembly::powerpc::OPCODE::VADDUWM)
  .value("VADDUWS", LIEF::assembly::powerpc::OPCODE::VADDUWS)
  .value("VAND", LIEF::assembly::powerpc::OPCODE::VAND)
  .value("VANDC", LIEF::assembly::powerpc::OPCODE::VANDC)
  .value("VAVGSB", LIEF::assembly::powerpc::OPCODE::VAVGSB)
  .value("VAVGSH", LIEF::assembly::powerpc::OPCODE::VAVGSH)
  .value("VAVGSW", LIEF::assembly::powerpc::OPCODE::VAVGSW)
  .value("VAVGUB", LIEF::assembly::powerpc::OPCODE::VAVGUB)
  .value("VAVGUH", LIEF::assembly::powerpc::OPCODE::VAVGUH)
  .value("VAVGUW", LIEF::assembly::powerpc::OPCODE::VAVGUW)
  .value("VBPERMD", LIEF::assembly::powerpc::OPCODE::VBPERMD)
  .value("VBPERMQ", LIEF::assembly::powerpc::OPCODE::VBPERMQ)
  .value("VCFSX", LIEF::assembly::powerpc::OPCODE::VCFSX)
  .value("VCFSX_0", LIEF::assembly::powerpc::OPCODE::VCFSX_0)
  .value("VCFUGED", LIEF::assembly::powerpc::OPCODE::VCFUGED)
  .value("VCFUX", LIEF::assembly::powerpc::OPCODE::VCFUX)
  .value("VCFUX_0", LIEF::assembly::powerpc::OPCODE::VCFUX_0)
  .value("VCIPHER", LIEF::assembly::powerpc::OPCODE::VCIPHER)
  .value("VCIPHERLAST", LIEF::assembly::powerpc::OPCODE::VCIPHERLAST)
  .value("VCLRLB", LIEF::assembly::powerpc::OPCODE::VCLRLB)
  .value("VCLRRB", LIEF::assembly::powerpc::OPCODE::VCLRRB)
  .value("VCLZB", LIEF::assembly::powerpc::OPCODE::VCLZB)
  .value("VCLZD", LIEF::assembly::powerpc::OPCODE::VCLZD)
  .value("VCLZDM", LIEF::assembly::powerpc::OPCODE::VCLZDM)
  .value("VCLZH", LIEF::assembly::powerpc::OPCODE::VCLZH)
  .value("VCLZLSBB", LIEF::assembly::powerpc::OPCODE::VCLZLSBB)
  .value("VCLZW", LIEF::assembly::powerpc::OPCODE::VCLZW)
  .value("VCMPBFP", LIEF::assembly::powerpc::OPCODE::VCMPBFP)
  .value("VCMPBFP_rec", LIEF::assembly::powerpc::OPCODE::VCMPBFP_rec)
  .value("VCMPEQFP", LIEF::assembly::powerpc::OPCODE::VCMPEQFP)
  .value("VCMPEQFP_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQFP_rec)
  .value("VCMPEQUB", LIEF::assembly::powerpc::OPCODE::VCMPEQUB)
  .value("VCMPEQUB_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQUB_rec)
  .value("VCMPEQUD", LIEF::assembly::powerpc::OPCODE::VCMPEQUD)
  .value("VCMPEQUD_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQUD_rec)
  .value("VCMPEQUH", LIEF::assembly::powerpc::OPCODE::VCMPEQUH)
  .value("VCMPEQUH_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQUH_rec)
  .value("VCMPEQUQ", LIEF::assembly::powerpc::OPCODE::VCMPEQUQ)
  .value("VCMPEQUQ_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQUQ_rec)
  .value("VCMPEQUW", LIEF::assembly::powerpc::OPCODE::VCMPEQUW)
  .value("VCMPEQUW_rec", LIEF::assembly::powerpc::OPCODE::VCMPEQUW_rec)
  .value("VCMPGEFP", LIEF::assembly::powerpc::OPCODE::VCMPGEFP)
  .value("VCMPGEFP_rec", LIEF::assembly::powerpc::OPCODE::VCMPGEFP_rec)
  .value("VCMPGTFP", LIEF::assembly::powerpc::OPCODE::VCMPGTFP)
  .value("VCMPGTFP_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTFP_rec)
  .value("VCMPGTSB", LIEF::assembly::powerpc::OPCODE::VCMPGTSB)
  .value("VCMPGTSB_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTSB_rec)
  .value("VCMPGTSD", LIEF::assembly::powerpc::OPCODE::VCMPGTSD)
  .value("VCMPGTSD_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTSD_rec)
  .value("VCMPGTSH", LIEF::assembly::powerpc::OPCODE::VCMPGTSH)
  .value("VCMPGTSH_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTSH_rec)
  .value("VCMPGTSQ", LIEF::assembly::powerpc::OPCODE::VCMPGTSQ)
  .value("VCMPGTSQ_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTSQ_rec)
  .value("VCMPGTSW", LIEF::assembly::powerpc::OPCODE::VCMPGTSW)
  .value("VCMPGTSW_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTSW_rec)
  .value("VCMPGTUB", LIEF::assembly::powerpc::OPCODE::VCMPGTUB)
  .value("VCMPGTUB_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTUB_rec)
  .value("VCMPGTUD", LIEF::assembly::powerpc::OPCODE::VCMPGTUD)
  .value("VCMPGTUD_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTUD_rec)
  .value("VCMPGTUH", LIEF::assembly::powerpc::OPCODE::VCMPGTUH)
  .value("VCMPGTUH_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTUH_rec)
  .value("VCMPGTUQ", LIEF::assembly::powerpc::OPCODE::VCMPGTUQ)
  .value("VCMPGTUQ_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTUQ_rec)
  .value("VCMPGTUW", LIEF::assembly::powerpc::OPCODE::VCMPGTUW)
  .value("VCMPGTUW_rec", LIEF::assembly::powerpc::OPCODE::VCMPGTUW_rec)
  .value("VCMPNEB", LIEF::assembly::powerpc::OPCODE::VCMPNEB)
  .value("VCMPNEB_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEB_rec)
  .value("VCMPNEH", LIEF::assembly::powerpc::OPCODE::VCMPNEH)
  .value("VCMPNEH_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEH_rec)
  .value("VCMPNEW", LIEF::assembly::powerpc::OPCODE::VCMPNEW)
  .value("VCMPNEW_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEW_rec)
  .value("VCMPNEZB", LIEF::assembly::powerpc::OPCODE::VCMPNEZB)
  .value("VCMPNEZB_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEZB_rec)
  .value("VCMPNEZH", LIEF::assembly::powerpc::OPCODE::VCMPNEZH)
  .value("VCMPNEZH_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEZH_rec)
  .value("VCMPNEZW", LIEF::assembly::powerpc::OPCODE::VCMPNEZW)
  .value("VCMPNEZW_rec", LIEF::assembly::powerpc::OPCODE::VCMPNEZW_rec)
  .value("VCMPSQ", LIEF::assembly::powerpc::OPCODE::VCMPSQ)
  .value("VCMPUQ", LIEF::assembly::powerpc::OPCODE::VCMPUQ)
  .value("VCNTMBB", LIEF::assembly::powerpc::OPCODE::VCNTMBB)
  .value("VCNTMBD", LIEF::assembly::powerpc::OPCODE::VCNTMBD)
  .value("VCNTMBH", LIEF::assembly::powerpc::OPCODE::VCNTMBH)
  .value("VCNTMBW", LIEF::assembly::powerpc::OPCODE::VCNTMBW)
  .value("VCTSXS", LIEF::assembly::powerpc::OPCODE::VCTSXS)
  .value("VCTSXS_0", LIEF::assembly::powerpc::OPCODE::VCTSXS_0)
  .value("VCTUXS", LIEF::assembly::powerpc::OPCODE::VCTUXS)
  .value("VCTUXS_0", LIEF::assembly::powerpc::OPCODE::VCTUXS_0)
  .value("VCTZB", LIEF::assembly::powerpc::OPCODE::VCTZB)
  .value("VCTZD", LIEF::assembly::powerpc::OPCODE::VCTZD)
  .value("VCTZDM", LIEF::assembly::powerpc::OPCODE::VCTZDM)
  .value("VCTZH", LIEF::assembly::powerpc::OPCODE::VCTZH)
  .value("VCTZLSBB", LIEF::assembly::powerpc::OPCODE::VCTZLSBB)
  .value("VCTZW", LIEF::assembly::powerpc::OPCODE::VCTZW)
  .value("VDIVESD", LIEF::assembly::powerpc::OPCODE::VDIVESD)
  .value("VDIVESQ", LIEF::assembly::powerpc::OPCODE::VDIVESQ)
  .value("VDIVESW", LIEF::assembly::powerpc::OPCODE::VDIVESW)
  .value("VDIVEUD", LIEF::assembly::powerpc::OPCODE::VDIVEUD)
  .value("VDIVEUQ", LIEF::assembly::powerpc::OPCODE::VDIVEUQ)
  .value("VDIVEUW", LIEF::assembly::powerpc::OPCODE::VDIVEUW)
  .value("VDIVSD", LIEF::assembly::powerpc::OPCODE::VDIVSD)
  .value("VDIVSQ", LIEF::assembly::powerpc::OPCODE::VDIVSQ)
  .value("VDIVSW", LIEF::assembly::powerpc::OPCODE::VDIVSW)
  .value("VDIVUD", LIEF::assembly::powerpc::OPCODE::VDIVUD)
  .value("VDIVUQ", LIEF::assembly::powerpc::OPCODE::VDIVUQ)
  .value("VDIVUW", LIEF::assembly::powerpc::OPCODE::VDIVUW)
  .value("VEQV", LIEF::assembly::powerpc::OPCODE::VEQV)
  .value("VEXPANDBM", LIEF::assembly::powerpc::OPCODE::VEXPANDBM)
  .value("VEXPANDDM", LIEF::assembly::powerpc::OPCODE::VEXPANDDM)
  .value("VEXPANDHM", LIEF::assembly::powerpc::OPCODE::VEXPANDHM)
  .value("VEXPANDQM", LIEF::assembly::powerpc::OPCODE::VEXPANDQM)
  .value("VEXPANDWM", LIEF::assembly::powerpc::OPCODE::VEXPANDWM)
  .value("VEXPTEFP", LIEF::assembly::powerpc::OPCODE::VEXPTEFP)
  .value("VEXTDDVLX", LIEF::assembly::powerpc::OPCODE::VEXTDDVLX)
  .value("VEXTDDVRX", LIEF::assembly::powerpc::OPCODE::VEXTDDVRX)
  .value("VEXTDUBVLX", LIEF::assembly::powerpc::OPCODE::VEXTDUBVLX)
  .value("VEXTDUBVRX", LIEF::assembly::powerpc::OPCODE::VEXTDUBVRX)
  .value("VEXTDUHVLX", LIEF::assembly::powerpc::OPCODE::VEXTDUHVLX)
  .value("VEXTDUHVRX", LIEF::assembly::powerpc::OPCODE::VEXTDUHVRX)
  .value("VEXTDUWVLX", LIEF::assembly::powerpc::OPCODE::VEXTDUWVLX)
  .value("VEXTDUWVRX", LIEF::assembly::powerpc::OPCODE::VEXTDUWVRX)
  .value("VEXTRACTBM", LIEF::assembly::powerpc::OPCODE::VEXTRACTBM)
  .value("VEXTRACTD", LIEF::assembly::powerpc::OPCODE::VEXTRACTD)
  .value("VEXTRACTDM", LIEF::assembly::powerpc::OPCODE::VEXTRACTDM)
  .value("VEXTRACTHM", LIEF::assembly::powerpc::OPCODE::VEXTRACTHM)
  .value("VEXTRACTQM", LIEF::assembly::powerpc::OPCODE::VEXTRACTQM)
  .value("VEXTRACTUB", LIEF::assembly::powerpc::OPCODE::VEXTRACTUB)
  .value("VEXTRACTUH", LIEF::assembly::powerpc::OPCODE::VEXTRACTUH)
  .value("VEXTRACTUW", LIEF::assembly::powerpc::OPCODE::VEXTRACTUW)
  .value("VEXTRACTWM", LIEF::assembly::powerpc::OPCODE::VEXTRACTWM)
  .value("VEXTSB2D", LIEF::assembly::powerpc::OPCODE::VEXTSB2D)
  .value("VEXTSB2Ds", LIEF::assembly::powerpc::OPCODE::VEXTSB2Ds)
  .value("VEXTSB2W", LIEF::assembly::powerpc::OPCODE::VEXTSB2W)
  .value("VEXTSB2Ws", LIEF::assembly::powerpc::OPCODE::VEXTSB2Ws)
  .value("VEXTSD2Q", LIEF::assembly::powerpc::OPCODE::VEXTSD2Q)
  .value("VEXTSH2D", LIEF::assembly::powerpc::OPCODE::VEXTSH2D)
  .value("VEXTSH2Ds", LIEF::assembly::powerpc::OPCODE::VEXTSH2Ds)
  .value("VEXTSH2W", LIEF::assembly::powerpc::OPCODE::VEXTSH2W)
  .value("VEXTSH2Ws", LIEF::assembly::powerpc::OPCODE::VEXTSH2Ws)
  .value("VEXTSW2D", LIEF::assembly::powerpc::OPCODE::VEXTSW2D)
  .value("VEXTSW2Ds", LIEF::assembly::powerpc::OPCODE::VEXTSW2Ds)
  .value("VEXTUBLX", LIEF::assembly::powerpc::OPCODE::VEXTUBLX)
  .value("VEXTUBRX", LIEF::assembly::powerpc::OPCODE::VEXTUBRX)
  .value("VEXTUHLX", LIEF::assembly::powerpc::OPCODE::VEXTUHLX)
  .value("VEXTUHRX", LIEF::assembly::powerpc::OPCODE::VEXTUHRX)
  .value("VEXTUWLX", LIEF::assembly::powerpc::OPCODE::VEXTUWLX)
  .value("VEXTUWRX", LIEF::assembly::powerpc::OPCODE::VEXTUWRX)
  .value("VGBBD", LIEF::assembly::powerpc::OPCODE::VGBBD)
  .value("VGNB", LIEF::assembly::powerpc::OPCODE::VGNB)
  .value("VINSBLX", LIEF::assembly::powerpc::OPCODE::VINSBLX)
  .value("VINSBRX", LIEF::assembly::powerpc::OPCODE::VINSBRX)
  .value("VINSBVLX", LIEF::assembly::powerpc::OPCODE::VINSBVLX)
  .value("VINSBVRX", LIEF::assembly::powerpc::OPCODE::VINSBVRX)
  .value("VINSD", LIEF::assembly::powerpc::OPCODE::VINSD)
  .value("VINSDLX", LIEF::assembly::powerpc::OPCODE::VINSDLX)
  .value("VINSDRX", LIEF::assembly::powerpc::OPCODE::VINSDRX)
  .value("VINSERTB", LIEF::assembly::powerpc::OPCODE::VINSERTB)
  .value("VINSERTD", LIEF::assembly::powerpc::OPCODE::VINSERTD)
  .value("VINSERTH", LIEF::assembly::powerpc::OPCODE::VINSERTH)
  .value("VINSERTW", LIEF::assembly::powerpc::OPCODE::VINSERTW)
  .value("VINSHLX", LIEF::assembly::powerpc::OPCODE::VINSHLX)
  .value("VINSHRX", LIEF::assembly::powerpc::OPCODE::VINSHRX)
  .value("VINSHVLX", LIEF::assembly::powerpc::OPCODE::VINSHVLX)
  .value("VINSHVRX", LIEF::assembly::powerpc::OPCODE::VINSHVRX)
  .value("VINSW", LIEF::assembly::powerpc::OPCODE::VINSW)
  .value("VINSWLX", LIEF::assembly::powerpc::OPCODE::VINSWLX)
  .value("VINSWRX", LIEF::assembly::powerpc::OPCODE::VINSWRX)
  .value("VINSWVLX", LIEF::assembly::powerpc::OPCODE::VINSWVLX)
  .value("VINSWVRX", LIEF::assembly::powerpc::OPCODE::VINSWVRX)
  .value("VLOGEFP", LIEF::assembly::powerpc::OPCODE::VLOGEFP)
  .value("VMADDFP", LIEF::assembly::powerpc::OPCODE::VMADDFP)
  .value("VMAXFP", LIEF::assembly::powerpc::OPCODE::VMAXFP)
  .value("VMAXSB", LIEF::assembly::powerpc::OPCODE::VMAXSB)
  .value("VMAXSD", LIEF::assembly::powerpc::OPCODE::VMAXSD)
  .value("VMAXSH", LIEF::assembly::powerpc::OPCODE::VMAXSH)
  .value("VMAXSW", LIEF::assembly::powerpc::OPCODE::VMAXSW)
  .value("VMAXUB", LIEF::assembly::powerpc::OPCODE::VMAXUB)
  .value("VMAXUD", LIEF::assembly::powerpc::OPCODE::VMAXUD)
  .value("VMAXUH", LIEF::assembly::powerpc::OPCODE::VMAXUH)
  .value("VMAXUW", LIEF::assembly::powerpc::OPCODE::VMAXUW)
  .value("VMHADDSHS", LIEF::assembly::powerpc::OPCODE::VMHADDSHS)
  .value("VMHRADDSHS", LIEF::assembly::powerpc::OPCODE::VMHRADDSHS)
  .value("VMINFP", LIEF::assembly::powerpc::OPCODE::VMINFP)
  .value("VMINSB", LIEF::assembly::powerpc::OPCODE::VMINSB)
  .value("VMINSD", LIEF::assembly::powerpc::OPCODE::VMINSD)
  .value("VMINSH", LIEF::assembly::powerpc::OPCODE::VMINSH)
  .value("VMINSW", LIEF::assembly::powerpc::OPCODE::VMINSW)
  .value("VMINUB", LIEF::assembly::powerpc::OPCODE::VMINUB)
  .value("VMINUD", LIEF::assembly::powerpc::OPCODE::VMINUD)
  .value("VMINUH", LIEF::assembly::powerpc::OPCODE::VMINUH)
  .value("VMINUW", LIEF::assembly::powerpc::OPCODE::VMINUW)
  .value("VMLADDUHM", LIEF::assembly::powerpc::OPCODE::VMLADDUHM)
  .value("VMODSD", LIEF::assembly::powerpc::OPCODE::VMODSD)
  .value("VMODSQ", LIEF::assembly::powerpc::OPCODE::VMODSQ)
  .value("VMODSW", LIEF::assembly::powerpc::OPCODE::VMODSW)
  .value("VMODUD", LIEF::assembly::powerpc::OPCODE::VMODUD)
  .value("VMODUQ", LIEF::assembly::powerpc::OPCODE::VMODUQ)
  .value("VMODUW", LIEF::assembly::powerpc::OPCODE::VMODUW)
  .value("VMRGEW", LIEF::assembly::powerpc::OPCODE::VMRGEW)
  .value("VMRGHB", LIEF::assembly::powerpc::OPCODE::VMRGHB)
  .value("VMRGHH", LIEF::assembly::powerpc::OPCODE::VMRGHH)
  .value("VMRGHW", LIEF::assembly::powerpc::OPCODE::VMRGHW)
  .value("VMRGLB", LIEF::assembly::powerpc::OPCODE::VMRGLB)
  .value("VMRGLH", LIEF::assembly::powerpc::OPCODE::VMRGLH)
  .value("VMRGLW", LIEF::assembly::powerpc::OPCODE::VMRGLW)
  .value("VMRGOW", LIEF::assembly::powerpc::OPCODE::VMRGOW)
  .value("VMSUMCUD", LIEF::assembly::powerpc::OPCODE::VMSUMCUD)
  .value("VMSUMMBM", LIEF::assembly::powerpc::OPCODE::VMSUMMBM)
  .value("VMSUMSHM", LIEF::assembly::powerpc::OPCODE::VMSUMSHM)
  .value("VMSUMSHS", LIEF::assembly::powerpc::OPCODE::VMSUMSHS)
  .value("VMSUMUBM", LIEF::assembly::powerpc::OPCODE::VMSUMUBM)
  .value("VMSUMUDM", LIEF::assembly::powerpc::OPCODE::VMSUMUDM)
  .value("VMSUMUHM", LIEF::assembly::powerpc::OPCODE::VMSUMUHM)
  .value("VMSUMUHS", LIEF::assembly::powerpc::OPCODE::VMSUMUHS)
  .value("VMUL10CUQ", LIEF::assembly::powerpc::OPCODE::VMUL10CUQ)
  .value("VMUL10ECUQ", LIEF::assembly::powerpc::OPCODE::VMUL10ECUQ)
  .value("VMUL10EUQ", LIEF::assembly::powerpc::OPCODE::VMUL10EUQ)
  .value("VMUL10UQ", LIEF::assembly::powerpc::OPCODE::VMUL10UQ)
  .value("VMULESB", LIEF::assembly::powerpc::OPCODE::VMULESB)
  .value("VMULESD", LIEF::assembly::powerpc::OPCODE::VMULESD)
  .value("VMULESH", LIEF::assembly::powerpc::OPCODE::VMULESH)
  .value("VMULESW", LIEF::assembly::powerpc::OPCODE::VMULESW)
  .value("VMULEUB", LIEF::assembly::powerpc::OPCODE::VMULEUB)
  .value("VMULEUD", LIEF::assembly::powerpc::OPCODE::VMULEUD)
  .value("VMULEUH", LIEF::assembly::powerpc::OPCODE::VMULEUH)
  .value("VMULEUW", LIEF::assembly::powerpc::OPCODE::VMULEUW)
  .value("VMULHSD", LIEF::assembly::powerpc::OPCODE::VMULHSD)
  .value("VMULHSW", LIEF::assembly::powerpc::OPCODE::VMULHSW)
  .value("VMULHUD", LIEF::assembly::powerpc::OPCODE::VMULHUD)
  .value("VMULHUW", LIEF::assembly::powerpc::OPCODE::VMULHUW)
  .value("VMULLD", LIEF::assembly::powerpc::OPCODE::VMULLD)
  .value("VMULOSB", LIEF::assembly::powerpc::OPCODE::VMULOSB)
  .value("VMULOSD", LIEF::assembly::powerpc::OPCODE::VMULOSD)
  .value("VMULOSH", LIEF::assembly::powerpc::OPCODE::VMULOSH)
  .value("VMULOSW", LIEF::assembly::powerpc::OPCODE::VMULOSW)
  .value("VMULOUB", LIEF::assembly::powerpc::OPCODE::VMULOUB)
  .value("VMULOUD", LIEF::assembly::powerpc::OPCODE::VMULOUD)
  .value("VMULOUH", LIEF::assembly::powerpc::OPCODE::VMULOUH)
  .value("VMULOUW", LIEF::assembly::powerpc::OPCODE::VMULOUW)
  .value("VMULUWM", LIEF::assembly::powerpc::OPCODE::VMULUWM)
  .value("VNAND", LIEF::assembly::powerpc::OPCODE::VNAND)
  .value("VNCIPHER", LIEF::assembly::powerpc::OPCODE::VNCIPHER)
  .value("VNCIPHERLAST", LIEF::assembly::powerpc::OPCODE::VNCIPHERLAST)
  .value("VNEGD", LIEF::assembly::powerpc::OPCODE::VNEGD)
  .value("VNEGW", LIEF::assembly::powerpc::OPCODE::VNEGW)
  .value("VNMSUBFP", LIEF::assembly::powerpc::OPCODE::VNMSUBFP)
  .value("VNOR", LIEF::assembly::powerpc::OPCODE::VNOR)
  .value("VOR", LIEF::assembly::powerpc::OPCODE::VOR)
  .value("VORC", LIEF::assembly::powerpc::OPCODE::VORC)
  .value("VPDEPD", LIEF::assembly::powerpc::OPCODE::VPDEPD)
  .value("VPERM", LIEF::assembly::powerpc::OPCODE::VPERM)
  .value("VPERMR", LIEF::assembly::powerpc::OPCODE::VPERMR);
  opcodes.value("VPERMXOR", LIEF::assembly::powerpc::OPCODE::VPERMXOR)
  .value("VPEXTD", LIEF::assembly::powerpc::OPCODE::VPEXTD)
  .value("VPKPX", LIEF::assembly::powerpc::OPCODE::VPKPX)
  .value("VPKSDSS", LIEF::assembly::powerpc::OPCODE::VPKSDSS)
  .value("VPKSDUS", LIEF::assembly::powerpc::OPCODE::VPKSDUS)
  .value("VPKSHSS", LIEF::assembly::powerpc::OPCODE::VPKSHSS)
  .value("VPKSHUS", LIEF::assembly::powerpc::OPCODE::VPKSHUS)
  .value("VPKSWSS", LIEF::assembly::powerpc::OPCODE::VPKSWSS)
  .value("VPKSWUS", LIEF::assembly::powerpc::OPCODE::VPKSWUS)
  .value("VPKUDUM", LIEF::assembly::powerpc::OPCODE::VPKUDUM)
  .value("VPKUDUS", LIEF::assembly::powerpc::OPCODE::VPKUDUS)
  .value("VPKUHUM", LIEF::assembly::powerpc::OPCODE::VPKUHUM)
  .value("VPKUHUS", LIEF::assembly::powerpc::OPCODE::VPKUHUS)
  .value("VPKUWUM", LIEF::assembly::powerpc::OPCODE::VPKUWUM)
  .value("VPKUWUS", LIEF::assembly::powerpc::OPCODE::VPKUWUS)
  .value("VPMSUMB", LIEF::assembly::powerpc::OPCODE::VPMSUMB)
  .value("VPMSUMD", LIEF::assembly::powerpc::OPCODE::VPMSUMD)
  .value("VPMSUMH", LIEF::assembly::powerpc::OPCODE::VPMSUMH)
  .value("VPMSUMW", LIEF::assembly::powerpc::OPCODE::VPMSUMW)
  .value("VPOPCNTB", LIEF::assembly::powerpc::OPCODE::VPOPCNTB)
  .value("VPOPCNTD", LIEF::assembly::powerpc::OPCODE::VPOPCNTD)
  .value("VPOPCNTH", LIEF::assembly::powerpc::OPCODE::VPOPCNTH)
  .value("VPOPCNTW", LIEF::assembly::powerpc::OPCODE::VPOPCNTW)
  .value("VPRTYBD", LIEF::assembly::powerpc::OPCODE::VPRTYBD)
  .value("VPRTYBQ", LIEF::assembly::powerpc::OPCODE::VPRTYBQ)
  .value("VPRTYBW", LIEF::assembly::powerpc::OPCODE::VPRTYBW)
  .value("VREFP", LIEF::assembly::powerpc::OPCODE::VREFP)
  .value("VRFIM", LIEF::assembly::powerpc::OPCODE::VRFIM)
  .value("VRFIN", LIEF::assembly::powerpc::OPCODE::VRFIN)
  .value("VRFIP", LIEF::assembly::powerpc::OPCODE::VRFIP)
  .value("VRFIZ", LIEF::assembly::powerpc::OPCODE::VRFIZ)
  .value("VRLB", LIEF::assembly::powerpc::OPCODE::VRLB)
  .value("VRLD", LIEF::assembly::powerpc::OPCODE::VRLD)
  .value("VRLDMI", LIEF::assembly::powerpc::OPCODE::VRLDMI)
  .value("VRLDNM", LIEF::assembly::powerpc::OPCODE::VRLDNM)
  .value("VRLH", LIEF::assembly::powerpc::OPCODE::VRLH)
  .value("VRLQ", LIEF::assembly::powerpc::OPCODE::VRLQ)
  .value("VRLQMI", LIEF::assembly::powerpc::OPCODE::VRLQMI)
  .value("VRLQNM", LIEF::assembly::powerpc::OPCODE::VRLQNM)
  .value("VRLW", LIEF::assembly::powerpc::OPCODE::VRLW)
  .value("VRLWMI", LIEF::assembly::powerpc::OPCODE::VRLWMI)
  .value("VRLWNM", LIEF::assembly::powerpc::OPCODE::VRLWNM)
  .value("VRSQRTEFP", LIEF::assembly::powerpc::OPCODE::VRSQRTEFP)
  .value("VSBOX", LIEF::assembly::powerpc::OPCODE::VSBOX)
  .value("VSEL", LIEF::assembly::powerpc::OPCODE::VSEL)
  .value("VSHASIGMAD", LIEF::assembly::powerpc::OPCODE::VSHASIGMAD)
  .value("VSHASIGMAW", LIEF::assembly::powerpc::OPCODE::VSHASIGMAW)
  .value("VSL", LIEF::assembly::powerpc::OPCODE::VSL)
  .value("VSLB", LIEF::assembly::powerpc::OPCODE::VSLB)
  .value("VSLD", LIEF::assembly::powerpc::OPCODE::VSLD)
  .value("VSLDBI", LIEF::assembly::powerpc::OPCODE::VSLDBI)
  .value("VSLDOI", LIEF::assembly::powerpc::OPCODE::VSLDOI)
  .value("VSLH", LIEF::assembly::powerpc::OPCODE::VSLH)
  .value("VSLO", LIEF::assembly::powerpc::OPCODE::VSLO)
  .value("VSLQ", LIEF::assembly::powerpc::OPCODE::VSLQ)
  .value("VSLV", LIEF::assembly::powerpc::OPCODE::VSLV)
  .value("VSLW", LIEF::assembly::powerpc::OPCODE::VSLW)
  .value("VSPLTB", LIEF::assembly::powerpc::OPCODE::VSPLTB)
  .value("VSPLTBs", LIEF::assembly::powerpc::OPCODE::VSPLTBs)
  .value("VSPLTH", LIEF::assembly::powerpc::OPCODE::VSPLTH)
  .value("VSPLTHs", LIEF::assembly::powerpc::OPCODE::VSPLTHs)
  .value("VSPLTISB", LIEF::assembly::powerpc::OPCODE::VSPLTISB)
  .value("VSPLTISH", LIEF::assembly::powerpc::OPCODE::VSPLTISH)
  .value("VSPLTISW", LIEF::assembly::powerpc::OPCODE::VSPLTISW)
  .value("VSPLTW", LIEF::assembly::powerpc::OPCODE::VSPLTW)
  .value("VSR", LIEF::assembly::powerpc::OPCODE::VSR)
  .value("VSRAB", LIEF::assembly::powerpc::OPCODE::VSRAB)
  .value("VSRAD", LIEF::assembly::powerpc::OPCODE::VSRAD)
  .value("VSRAH", LIEF::assembly::powerpc::OPCODE::VSRAH)
  .value("VSRAQ", LIEF::assembly::powerpc::OPCODE::VSRAQ)
  .value("VSRAW", LIEF::assembly::powerpc::OPCODE::VSRAW)
  .value("VSRB", LIEF::assembly::powerpc::OPCODE::VSRB)
  .value("VSRD", LIEF::assembly::powerpc::OPCODE::VSRD)
  .value("VSRDBI", LIEF::assembly::powerpc::OPCODE::VSRDBI)
  .value("VSRH", LIEF::assembly::powerpc::OPCODE::VSRH)
  .value("VSRO", LIEF::assembly::powerpc::OPCODE::VSRO)
  .value("VSRQ", LIEF::assembly::powerpc::OPCODE::VSRQ)
  .value("VSRV", LIEF::assembly::powerpc::OPCODE::VSRV)
  .value("VSRW", LIEF::assembly::powerpc::OPCODE::VSRW)
  .value("VSTRIBL", LIEF::assembly::powerpc::OPCODE::VSTRIBL)
  .value("VSTRIBL_rec", LIEF::assembly::powerpc::OPCODE::VSTRIBL_rec)
  .value("VSTRIBR", LIEF::assembly::powerpc::OPCODE::VSTRIBR)
  .value("VSTRIBR_rec", LIEF::assembly::powerpc::OPCODE::VSTRIBR_rec)
  .value("VSTRIHL", LIEF::assembly::powerpc::OPCODE::VSTRIHL)
  .value("VSTRIHL_rec", LIEF::assembly::powerpc::OPCODE::VSTRIHL_rec)
  .value("VSTRIHR", LIEF::assembly::powerpc::OPCODE::VSTRIHR)
  .value("VSTRIHR_rec", LIEF::assembly::powerpc::OPCODE::VSTRIHR_rec)
  .value("VSUBCUQ", LIEF::assembly::powerpc::OPCODE::VSUBCUQ)
  .value("VSUBCUW", LIEF::assembly::powerpc::OPCODE::VSUBCUW)
  .value("VSUBECUQ", LIEF::assembly::powerpc::OPCODE::VSUBECUQ)
  .value("VSUBEUQM", LIEF::assembly::powerpc::OPCODE::VSUBEUQM)
  .value("VSUBFP", LIEF::assembly::powerpc::OPCODE::VSUBFP)
  .value("VSUBSBS", LIEF::assembly::powerpc::OPCODE::VSUBSBS)
  .value("VSUBSHS", LIEF::assembly::powerpc::OPCODE::VSUBSHS)
  .value("VSUBSWS", LIEF::assembly::powerpc::OPCODE::VSUBSWS)
  .value("VSUBUBM", LIEF::assembly::powerpc::OPCODE::VSUBUBM)
  .value("VSUBUBS", LIEF::assembly::powerpc::OPCODE::VSUBUBS)
  .value("VSUBUDM", LIEF::assembly::powerpc::OPCODE::VSUBUDM)
  .value("VSUBUHM", LIEF::assembly::powerpc::OPCODE::VSUBUHM)
  .value("VSUBUHS", LIEF::assembly::powerpc::OPCODE::VSUBUHS)
  .value("VSUBUQM", LIEF::assembly::powerpc::OPCODE::VSUBUQM)
  .value("VSUBUWM", LIEF::assembly::powerpc::OPCODE::VSUBUWM)
  .value("VSUBUWS", LIEF::assembly::powerpc::OPCODE::VSUBUWS)
  .value("VSUM2SWS", LIEF::assembly::powerpc::OPCODE::VSUM2SWS)
  .value("VSUM4SBS", LIEF::assembly::powerpc::OPCODE::VSUM4SBS)
  .value("VSUM4SHS", LIEF::assembly::powerpc::OPCODE::VSUM4SHS)
  .value("VSUM4UBS", LIEF::assembly::powerpc::OPCODE::VSUM4UBS)
  .value("VSUMSWS", LIEF::assembly::powerpc::OPCODE::VSUMSWS)
  .value("VUPKHPX", LIEF::assembly::powerpc::OPCODE::VUPKHPX)
  .value("VUPKHSB", LIEF::assembly::powerpc::OPCODE::VUPKHSB)
  .value("VUPKHSH", LIEF::assembly::powerpc::OPCODE::VUPKHSH)
  .value("VUPKHSW", LIEF::assembly::powerpc::OPCODE::VUPKHSW)
  .value("VUPKLPX", LIEF::assembly::powerpc::OPCODE::VUPKLPX)
  .value("VUPKLSB", LIEF::assembly::powerpc::OPCODE::VUPKLSB)
  .value("VUPKLSH", LIEF::assembly::powerpc::OPCODE::VUPKLSH)
  .value("VUPKLSW", LIEF::assembly::powerpc::OPCODE::VUPKLSW)
  .value("VXOR", LIEF::assembly::powerpc::OPCODE::VXOR)
  .value("V_SET0", LIEF::assembly::powerpc::OPCODE::V_SET0)
  .value("V_SET0B", LIEF::assembly::powerpc::OPCODE::V_SET0B)
  .value("V_SET0H", LIEF::assembly::powerpc::OPCODE::V_SET0H)
  .value("V_SETALLONES", LIEF::assembly::powerpc::OPCODE::V_SETALLONES)
  .value("V_SETALLONESB", LIEF::assembly::powerpc::OPCODE::V_SETALLONESB)
  .value("V_SETALLONESH", LIEF::assembly::powerpc::OPCODE::V_SETALLONESH)
  .value("WAIT", LIEF::assembly::powerpc::OPCODE::WAIT)
  .value("WAITP10", LIEF::assembly::powerpc::OPCODE::WAITP10)
  .value("WRTEE", LIEF::assembly::powerpc::OPCODE::WRTEE)
  .value("WRTEEI", LIEF::assembly::powerpc::OPCODE::WRTEEI)
  .value("XOR", LIEF::assembly::powerpc::OPCODE::XOR)
  .value("XOR8", LIEF::assembly::powerpc::OPCODE::XOR8)
  .value("XOR8_rec", LIEF::assembly::powerpc::OPCODE::XOR8_rec)
  .value("XORI", LIEF::assembly::powerpc::OPCODE::XORI)
  .value("XORI8", LIEF::assembly::powerpc::OPCODE::XORI8)
  .value("XORIS", LIEF::assembly::powerpc::OPCODE::XORIS)
  .value("XORIS8", LIEF::assembly::powerpc::OPCODE::XORIS8)
  .value("XOR_rec", LIEF::assembly::powerpc::OPCODE::XOR_rec)
  .value("XSABSDP", LIEF::assembly::powerpc::OPCODE::XSABSDP)
  .value("XSABSQP", LIEF::assembly::powerpc::OPCODE::XSABSQP)
  .value("XSADDDP", LIEF::assembly::powerpc::OPCODE::XSADDDP)
  .value("XSADDQP", LIEF::assembly::powerpc::OPCODE::XSADDQP)
  .value("XSADDQPO", LIEF::assembly::powerpc::OPCODE::XSADDQPO)
  .value("XSADDSP", LIEF::assembly::powerpc::OPCODE::XSADDSP)
  .value("XSCMPEQDP", LIEF::assembly::powerpc::OPCODE::XSCMPEQDP)
  .value("XSCMPEQQP", LIEF::assembly::powerpc::OPCODE::XSCMPEQQP)
  .value("XSCMPEXPDP", LIEF::assembly::powerpc::OPCODE::XSCMPEXPDP)
  .value("XSCMPEXPQP", LIEF::assembly::powerpc::OPCODE::XSCMPEXPQP)
  .value("XSCMPGEDP", LIEF::assembly::powerpc::OPCODE::XSCMPGEDP)
  .value("XSCMPGEQP", LIEF::assembly::powerpc::OPCODE::XSCMPGEQP)
  .value("XSCMPGTDP", LIEF::assembly::powerpc::OPCODE::XSCMPGTDP)
  .value("XSCMPGTQP", LIEF::assembly::powerpc::OPCODE::XSCMPGTQP)
  .value("XSCMPODP", LIEF::assembly::powerpc::OPCODE::XSCMPODP)
  .value("XSCMPOQP", LIEF::assembly::powerpc::OPCODE::XSCMPOQP)
  .value("XSCMPUDP", LIEF::assembly::powerpc::OPCODE::XSCMPUDP)
  .value("XSCMPUQP", LIEF::assembly::powerpc::OPCODE::XSCMPUQP)
  .value("XSCPSGNDP", LIEF::assembly::powerpc::OPCODE::XSCPSGNDP)
  .value("XSCPSGNQP", LIEF::assembly::powerpc::OPCODE::XSCPSGNQP)
  .value("XSCVDPHP", LIEF::assembly::powerpc::OPCODE::XSCVDPHP)
  .value("XSCVDPQP", LIEF::assembly::powerpc::OPCODE::XSCVDPQP)
  .value("XSCVDPSP", LIEF::assembly::powerpc::OPCODE::XSCVDPSP)
  .value("XSCVDPSPN", LIEF::assembly::powerpc::OPCODE::XSCVDPSPN)
  .value("XSCVDPSXDS", LIEF::assembly::powerpc::OPCODE::XSCVDPSXDS)
  .value("XSCVDPSXDSs", LIEF::assembly::powerpc::OPCODE::XSCVDPSXDSs)
  .value("XSCVDPSXWS", LIEF::assembly::powerpc::OPCODE::XSCVDPSXWS)
  .value("XSCVDPSXWSs", LIEF::assembly::powerpc::OPCODE::XSCVDPSXWSs)
  .value("XSCVDPUXDS", LIEF::assembly::powerpc::OPCODE::XSCVDPUXDS)
  .value("XSCVDPUXDSs", LIEF::assembly::powerpc::OPCODE::XSCVDPUXDSs)
  .value("XSCVDPUXWS", LIEF::assembly::powerpc::OPCODE::XSCVDPUXWS)
  .value("XSCVDPUXWSs", LIEF::assembly::powerpc::OPCODE::XSCVDPUXWSs)
  .value("XSCVHPDP", LIEF::assembly::powerpc::OPCODE::XSCVHPDP)
  .value("XSCVQPDP", LIEF::assembly::powerpc::OPCODE::XSCVQPDP)
  .value("XSCVQPDPO", LIEF::assembly::powerpc::OPCODE::XSCVQPDPO)
  .value("XSCVQPSDZ", LIEF::assembly::powerpc::OPCODE::XSCVQPSDZ)
  .value("XSCVQPSQZ", LIEF::assembly::powerpc::OPCODE::XSCVQPSQZ)
  .value("XSCVQPSWZ", LIEF::assembly::powerpc::OPCODE::XSCVQPSWZ)
  .value("XSCVQPUDZ", LIEF::assembly::powerpc::OPCODE::XSCVQPUDZ)
  .value("XSCVQPUQZ", LIEF::assembly::powerpc::OPCODE::XSCVQPUQZ)
  .value("XSCVQPUWZ", LIEF::assembly::powerpc::OPCODE::XSCVQPUWZ)
  .value("XSCVSDQP", LIEF::assembly::powerpc::OPCODE::XSCVSDQP)
  .value("XSCVSPDP", LIEF::assembly::powerpc::OPCODE::XSCVSPDP)
  .value("XSCVSPDPN", LIEF::assembly::powerpc::OPCODE::XSCVSPDPN)
  .value("XSCVSQQP", LIEF::assembly::powerpc::OPCODE::XSCVSQQP)
  .value("XSCVSXDDP", LIEF::assembly::powerpc::OPCODE::XSCVSXDDP)
  .value("XSCVSXDSP", LIEF::assembly::powerpc::OPCODE::XSCVSXDSP)
  .value("XSCVUDQP", LIEF::assembly::powerpc::OPCODE::XSCVUDQP)
  .value("XSCVUQQP", LIEF::assembly::powerpc::OPCODE::XSCVUQQP)
  .value("XSCVUXDDP", LIEF::assembly::powerpc::OPCODE::XSCVUXDDP)
  .value("XSCVUXDSP", LIEF::assembly::powerpc::OPCODE::XSCVUXDSP)
  .value("XSDIVDP", LIEF::assembly::powerpc::OPCODE::XSDIVDP)
  .value("XSDIVQP", LIEF::assembly::powerpc::OPCODE::XSDIVQP)
  .value("XSDIVQPO", LIEF::assembly::powerpc::OPCODE::XSDIVQPO)
  .value("XSDIVSP", LIEF::assembly::powerpc::OPCODE::XSDIVSP)
  .value("XSIEXPDP", LIEF::assembly::powerpc::OPCODE::XSIEXPDP)
  .value("XSIEXPQP", LIEF::assembly::powerpc::OPCODE::XSIEXPQP)
  .value("XSMADDADP", LIEF::assembly::powerpc::OPCODE::XSMADDADP)
  .value("XSMADDASP", LIEF::assembly::powerpc::OPCODE::XSMADDASP)
  .value("XSMADDMDP", LIEF::assembly::powerpc::OPCODE::XSMADDMDP)
  .value("XSMADDMSP", LIEF::assembly::powerpc::OPCODE::XSMADDMSP)
  .value("XSMADDQP", LIEF::assembly::powerpc::OPCODE::XSMADDQP)
  .value("XSMADDQPO", LIEF::assembly::powerpc::OPCODE::XSMADDQPO)
  .value("XSMAXCDP", LIEF::assembly::powerpc::OPCODE::XSMAXCDP)
  .value("XSMAXCQP", LIEF::assembly::powerpc::OPCODE::XSMAXCQP)
  .value("XSMAXDP", LIEF::assembly::powerpc::OPCODE::XSMAXDP)
  .value("XSMAXJDP", LIEF::assembly::powerpc::OPCODE::XSMAXJDP)
  .value("XSMINCDP", LIEF::assembly::powerpc::OPCODE::XSMINCDP)
  .value("XSMINCQP", LIEF::assembly::powerpc::OPCODE::XSMINCQP)
  .value("XSMINDP", LIEF::assembly::powerpc::OPCODE::XSMINDP)
  .value("XSMINJDP", LIEF::assembly::powerpc::OPCODE::XSMINJDP)
  .value("XSMSUBADP", LIEF::assembly::powerpc::OPCODE::XSMSUBADP)
  .value("XSMSUBASP", LIEF::assembly::powerpc::OPCODE::XSMSUBASP)
  .value("XSMSUBMDP", LIEF::assembly::powerpc::OPCODE::XSMSUBMDP)
  .value("XSMSUBMSP", LIEF::assembly::powerpc::OPCODE::XSMSUBMSP)
  .value("XSMSUBQP", LIEF::assembly::powerpc::OPCODE::XSMSUBQP)
  .value("XSMSUBQPO", LIEF::assembly::powerpc::OPCODE::XSMSUBQPO)
  .value("XSMULDP", LIEF::assembly::powerpc::OPCODE::XSMULDP)
  .value("XSMULQP", LIEF::assembly::powerpc::OPCODE::XSMULQP)
  .value("XSMULQPO", LIEF::assembly::powerpc::OPCODE::XSMULQPO)
  .value("XSMULSP", LIEF::assembly::powerpc::OPCODE::XSMULSP)
  .value("XSNABSDP", LIEF::assembly::powerpc::OPCODE::XSNABSDP)
  .value("XSNABSDPs", LIEF::assembly::powerpc::OPCODE::XSNABSDPs)
  .value("XSNABSQP", LIEF::assembly::powerpc::OPCODE::XSNABSQP)
  .value("XSNEGDP", LIEF::assembly::powerpc::OPCODE::XSNEGDP)
  .value("XSNEGQP", LIEF::assembly::powerpc::OPCODE::XSNEGQP)
  .value("XSNMADDADP", LIEF::assembly::powerpc::OPCODE::XSNMADDADP)
  .value("XSNMADDASP", LIEF::assembly::powerpc::OPCODE::XSNMADDASP)
  .value("XSNMADDMDP", LIEF::assembly::powerpc::OPCODE::XSNMADDMDP)
  .value("XSNMADDMSP", LIEF::assembly::powerpc::OPCODE::XSNMADDMSP)
  .value("XSNMADDQP", LIEF::assembly::powerpc::OPCODE::XSNMADDQP)
  .value("XSNMADDQPO", LIEF::assembly::powerpc::OPCODE::XSNMADDQPO)
  .value("XSNMSUBADP", LIEF::assembly::powerpc::OPCODE::XSNMSUBADP)
  .value("XSNMSUBASP", LIEF::assembly::powerpc::OPCODE::XSNMSUBASP)
  .value("XSNMSUBMDP", LIEF::assembly::powerpc::OPCODE::XSNMSUBMDP)
  .value("XSNMSUBMSP", LIEF::assembly::powerpc::OPCODE::XSNMSUBMSP)
  .value("XSNMSUBQP", LIEF::assembly::powerpc::OPCODE::XSNMSUBQP)
  .value("XSNMSUBQPO", LIEF::assembly::powerpc::OPCODE::XSNMSUBQPO)
  .value("XSRDPI", LIEF::assembly::powerpc::OPCODE::XSRDPI)
  .value("XSRDPIC", LIEF::assembly::powerpc::OPCODE::XSRDPIC)
  .value("XSRDPIM", LIEF::assembly::powerpc::OPCODE::XSRDPIM)
  .value("XSRDPIP", LIEF::assembly::powerpc::OPCODE::XSRDPIP)
  .value("XSRDPIZ", LIEF::assembly::powerpc::OPCODE::XSRDPIZ)
  .value("XSREDP", LIEF::assembly::powerpc::OPCODE::XSREDP)
  .value("XSRESP", LIEF::assembly::powerpc::OPCODE::XSRESP)
  .value("XSRQPI", LIEF::assembly::powerpc::OPCODE::XSRQPI)
  .value("XSRQPIX", LIEF::assembly::powerpc::OPCODE::XSRQPIX)
  .value("XSRQPXP", LIEF::assembly::powerpc::OPCODE::XSRQPXP)
  .value("XSRSP", LIEF::assembly::powerpc::OPCODE::XSRSP)
  .value("XSRSQRTEDP", LIEF::assembly::powerpc::OPCODE::XSRSQRTEDP)
  .value("XSRSQRTESP", LIEF::assembly::powerpc::OPCODE::XSRSQRTESP)
  .value("XSSQRTDP", LIEF::assembly::powerpc::OPCODE::XSSQRTDP)
  .value("XSSQRTQP", LIEF::assembly::powerpc::OPCODE::XSSQRTQP)
  .value("XSSQRTQPO", LIEF::assembly::powerpc::OPCODE::XSSQRTQPO)
  .value("XSSQRTSP", LIEF::assembly::powerpc::OPCODE::XSSQRTSP)
  .value("XSSUBDP", LIEF::assembly::powerpc::OPCODE::XSSUBDP)
  .value("XSSUBQP", LIEF::assembly::powerpc::OPCODE::XSSUBQP)
  .value("XSSUBQPO", LIEF::assembly::powerpc::OPCODE::XSSUBQPO)
  .value("XSSUBSP", LIEF::assembly::powerpc::OPCODE::XSSUBSP)
  .value("XSTDIVDP", LIEF::assembly::powerpc::OPCODE::XSTDIVDP)
  .value("XSTSQRTDP", LIEF::assembly::powerpc::OPCODE::XSTSQRTDP)
  .value("XSTSTDCDP", LIEF::assembly::powerpc::OPCODE::XSTSTDCDP)
  .value("XSTSTDCQP", LIEF::assembly::powerpc::OPCODE::XSTSTDCQP)
  .value("XSTSTDCSP", LIEF::assembly::powerpc::OPCODE::XSTSTDCSP)
  .value("XSXEXPDP", LIEF::assembly::powerpc::OPCODE::XSXEXPDP)
  .value("XSXEXPQP", LIEF::assembly::powerpc::OPCODE::XSXEXPQP)
  .value("XSXSIGDP", LIEF::assembly::powerpc::OPCODE::XSXSIGDP)
  .value("XSXSIGQP", LIEF::assembly::powerpc::OPCODE::XSXSIGQP)
  .value("XVABSDP", LIEF::assembly::powerpc::OPCODE::XVABSDP)
  .value("XVABSSP", LIEF::assembly::powerpc::OPCODE::XVABSSP)
  .value("XVADDDP", LIEF::assembly::powerpc::OPCODE::XVADDDP)
  .value("XVADDSP", LIEF::assembly::powerpc::OPCODE::XVADDSP)
  .value("XVBF16GER2", LIEF::assembly::powerpc::OPCODE::XVBF16GER2)
  .value("XVBF16GER2NN", LIEF::assembly::powerpc::OPCODE::XVBF16GER2NN)
  .value("XVBF16GER2NP", LIEF::assembly::powerpc::OPCODE::XVBF16GER2NP)
  .value("XVBF16GER2PN", LIEF::assembly::powerpc::OPCODE::XVBF16GER2PN)
  .value("XVBF16GER2PP", LIEF::assembly::powerpc::OPCODE::XVBF16GER2PP)
  .value("XVBF16GER2W", LIEF::assembly::powerpc::OPCODE::XVBF16GER2W)
  .value("XVBF16GER2WNN", LIEF::assembly::powerpc::OPCODE::XVBF16GER2WNN)
  .value("XVBF16GER2WNP", LIEF::assembly::powerpc::OPCODE::XVBF16GER2WNP)
  .value("XVBF16GER2WPN", LIEF::assembly::powerpc::OPCODE::XVBF16GER2WPN)
  .value("XVBF16GER2WPP", LIEF::assembly::powerpc::OPCODE::XVBF16GER2WPP)
  .value("XVCMPEQDP", LIEF::assembly::powerpc::OPCODE::XVCMPEQDP)
  .value("XVCMPEQDP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPEQDP_rec)
  .value("XVCMPEQSP", LIEF::assembly::powerpc::OPCODE::XVCMPEQSP)
  .value("XVCMPEQSP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPEQSP_rec)
  .value("XVCMPGEDP", LIEF::assembly::powerpc::OPCODE::XVCMPGEDP)
  .value("XVCMPGEDP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPGEDP_rec)
  .value("XVCMPGESP", LIEF::assembly::powerpc::OPCODE::XVCMPGESP)
  .value("XVCMPGESP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPGESP_rec)
  .value("XVCMPGTDP", LIEF::assembly::powerpc::OPCODE::XVCMPGTDP)
  .value("XVCMPGTDP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPGTDP_rec)
  .value("XVCMPGTSP", LIEF::assembly::powerpc::OPCODE::XVCMPGTSP)
  .value("XVCMPGTSP_rec", LIEF::assembly::powerpc::OPCODE::XVCMPGTSP_rec)
  .value("XVCPSGNDP", LIEF::assembly::powerpc::OPCODE::XVCPSGNDP)
  .value("XVCPSGNSP", LIEF::assembly::powerpc::OPCODE::XVCPSGNSP)
  .value("XVCVBF16SPN", LIEF::assembly::powerpc::OPCODE::XVCVBF16SPN)
  .value("XVCVDPSP", LIEF::assembly::powerpc::OPCODE::XVCVDPSP)
  .value("XVCVDPSXDS", LIEF::assembly::powerpc::OPCODE::XVCVDPSXDS)
  .value("XVCVDPSXWS", LIEF::assembly::powerpc::OPCODE::XVCVDPSXWS)
  .value("XVCVDPUXDS", LIEF::assembly::powerpc::OPCODE::XVCVDPUXDS)
  .value("XVCVDPUXWS", LIEF::assembly::powerpc::OPCODE::XVCVDPUXWS)
  .value("XVCVHPSP", LIEF::assembly::powerpc::OPCODE::XVCVHPSP)
  .value("XVCVSPBF16", LIEF::assembly::powerpc::OPCODE::XVCVSPBF16)
  .value("XVCVSPDP", LIEF::assembly::powerpc::OPCODE::XVCVSPDP);
  opcodes.value("XVCVSPHP", LIEF::assembly::powerpc::OPCODE::XVCVSPHP)
  .value("XVCVSPSXDS", LIEF::assembly::powerpc::OPCODE::XVCVSPSXDS)
  .value("XVCVSPSXWS", LIEF::assembly::powerpc::OPCODE::XVCVSPSXWS)
  .value("XVCVSPUXDS", LIEF::assembly::powerpc::OPCODE::XVCVSPUXDS)
  .value("XVCVSPUXWS", LIEF::assembly::powerpc::OPCODE::XVCVSPUXWS)
  .value("XVCVSXDDP", LIEF::assembly::powerpc::OPCODE::XVCVSXDDP)
  .value("XVCVSXDSP", LIEF::assembly::powerpc::OPCODE::XVCVSXDSP)
  .value("XVCVSXWDP", LIEF::assembly::powerpc::OPCODE::XVCVSXWDP)
  .value("XVCVSXWSP", LIEF::assembly::powerpc::OPCODE::XVCVSXWSP)
  .value("XVCVUXDDP", LIEF::assembly::powerpc::OPCODE::XVCVUXDDP)
  .value("XVCVUXDSP", LIEF::assembly::powerpc::OPCODE::XVCVUXDSP)
  .value("XVCVUXWDP", LIEF::assembly::powerpc::OPCODE::XVCVUXWDP)
  .value("XVCVUXWSP", LIEF::assembly::powerpc::OPCODE::XVCVUXWSP)
  .value("XVDIVDP", LIEF::assembly::powerpc::OPCODE::XVDIVDP)
  .value("XVDIVSP", LIEF::assembly::powerpc::OPCODE::XVDIVSP)
  .value("XVF16GER2", LIEF::assembly::powerpc::OPCODE::XVF16GER2)
  .value("XVF16GER2NN", LIEF::assembly::powerpc::OPCODE::XVF16GER2NN)
  .value("XVF16GER2NP", LIEF::assembly::powerpc::OPCODE::XVF16GER2NP)
  .value("XVF16GER2PN", LIEF::assembly::powerpc::OPCODE::XVF16GER2PN)
  .value("XVF16GER2PP", LIEF::assembly::powerpc::OPCODE::XVF16GER2PP)
  .value("XVF16GER2W", LIEF::assembly::powerpc::OPCODE::XVF16GER2W)
  .value("XVF16GER2WNN", LIEF::assembly::powerpc::OPCODE::XVF16GER2WNN)
  .value("XVF16GER2WNP", LIEF::assembly::powerpc::OPCODE::XVF16GER2WNP)
  .value("XVF16GER2WPN", LIEF::assembly::powerpc::OPCODE::XVF16GER2WPN)
  .value("XVF16GER2WPP", LIEF::assembly::powerpc::OPCODE::XVF16GER2WPP)
  .value("XVF32GER", LIEF::assembly::powerpc::OPCODE::XVF32GER)
  .value("XVF32GERNN", LIEF::assembly::powerpc::OPCODE::XVF32GERNN)
  .value("XVF32GERNP", LIEF::assembly::powerpc::OPCODE::XVF32GERNP)
  .value("XVF32GERPN", LIEF::assembly::powerpc::OPCODE::XVF32GERPN)
  .value("XVF32GERPP", LIEF::assembly::powerpc::OPCODE::XVF32GERPP)
  .value("XVF32GERW", LIEF::assembly::powerpc::OPCODE::XVF32GERW)
  .value("XVF32GERWNN", LIEF::assembly::powerpc::OPCODE::XVF32GERWNN)
  .value("XVF32GERWNP", LIEF::assembly::powerpc::OPCODE::XVF32GERWNP)
  .value("XVF32GERWPN", LIEF::assembly::powerpc::OPCODE::XVF32GERWPN)
  .value("XVF32GERWPP", LIEF::assembly::powerpc::OPCODE::XVF32GERWPP)
  .value("XVF64GER", LIEF::assembly::powerpc::OPCODE::XVF64GER)
  .value("XVF64GERNN", LIEF::assembly::powerpc::OPCODE::XVF64GERNN)
  .value("XVF64GERNP", LIEF::assembly::powerpc::OPCODE::XVF64GERNP)
  .value("XVF64GERPN", LIEF::assembly::powerpc::OPCODE::XVF64GERPN)
  .value("XVF64GERPP", LIEF::assembly::powerpc::OPCODE::XVF64GERPP)
  .value("XVF64GERW", LIEF::assembly::powerpc::OPCODE::XVF64GERW)
  .value("XVF64GERWNN", LIEF::assembly::powerpc::OPCODE::XVF64GERWNN)
  .value("XVF64GERWNP", LIEF::assembly::powerpc::OPCODE::XVF64GERWNP)
  .value("XVF64GERWPN", LIEF::assembly::powerpc::OPCODE::XVF64GERWPN)
  .value("XVF64GERWPP", LIEF::assembly::powerpc::OPCODE::XVF64GERWPP)
  .value("XVI16GER2", LIEF::assembly::powerpc::OPCODE::XVI16GER2)
  .value("XVI16GER2PP", LIEF::assembly::powerpc::OPCODE::XVI16GER2PP)
  .value("XVI16GER2S", LIEF::assembly::powerpc::OPCODE::XVI16GER2S)
  .value("XVI16GER2SPP", LIEF::assembly::powerpc::OPCODE::XVI16GER2SPP)
  .value("XVI16GER2SW", LIEF::assembly::powerpc::OPCODE::XVI16GER2SW)
  .value("XVI16GER2SWPP", LIEF::assembly::powerpc::OPCODE::XVI16GER2SWPP)
  .value("XVI16GER2W", LIEF::assembly::powerpc::OPCODE::XVI16GER2W)
  .value("XVI16GER2WPP", LIEF::assembly::powerpc::OPCODE::XVI16GER2WPP)
  .value("XVI4GER8", LIEF::assembly::powerpc::OPCODE::XVI4GER8)
  .value("XVI4GER8PP", LIEF::assembly::powerpc::OPCODE::XVI4GER8PP)
  .value("XVI4GER8W", LIEF::assembly::powerpc::OPCODE::XVI4GER8W)
  .value("XVI4GER8WPP", LIEF::assembly::powerpc::OPCODE::XVI4GER8WPP)
  .value("XVI8GER4", LIEF::assembly::powerpc::OPCODE::XVI8GER4)
  .value("XVI8GER4PP", LIEF::assembly::powerpc::OPCODE::XVI8GER4PP)
  .value("XVI8GER4SPP", LIEF::assembly::powerpc::OPCODE::XVI8GER4SPP)
  .value("XVI8GER4W", LIEF::assembly::powerpc::OPCODE::XVI8GER4W)
  .value("XVI8GER4WPP", LIEF::assembly::powerpc::OPCODE::XVI8GER4WPP)
  .value("XVI8GER4WSPP", LIEF::assembly::powerpc::OPCODE::XVI8GER4WSPP)
  .value("XVIEXPDP", LIEF::assembly::powerpc::OPCODE::XVIEXPDP)
  .value("XVIEXPSP", LIEF::assembly::powerpc::OPCODE::XVIEXPSP)
  .value("XVMADDADP", LIEF::assembly::powerpc::OPCODE::XVMADDADP)
  .value("XVMADDASP", LIEF::assembly::powerpc::OPCODE::XVMADDASP)
  .value("XVMADDMDP", LIEF::assembly::powerpc::OPCODE::XVMADDMDP)
  .value("XVMADDMSP", LIEF::assembly::powerpc::OPCODE::XVMADDMSP)
  .value("XVMAXDP", LIEF::assembly::powerpc::OPCODE::XVMAXDP)
  .value("XVMAXSP", LIEF::assembly::powerpc::OPCODE::XVMAXSP)
  .value("XVMINDP", LIEF::assembly::powerpc::OPCODE::XVMINDP)
  .value("XVMINSP", LIEF::assembly::powerpc::OPCODE::XVMINSP)
  .value("XVMSUBADP", LIEF::assembly::powerpc::OPCODE::XVMSUBADP)
  .value("XVMSUBASP", LIEF::assembly::powerpc::OPCODE::XVMSUBASP)
  .value("XVMSUBMDP", LIEF::assembly::powerpc::OPCODE::XVMSUBMDP)
  .value("XVMSUBMSP", LIEF::assembly::powerpc::OPCODE::XVMSUBMSP)
  .value("XVMULDP", LIEF::assembly::powerpc::OPCODE::XVMULDP)
  .value("XVMULSP", LIEF::assembly::powerpc::OPCODE::XVMULSP)
  .value("XVNABSDP", LIEF::assembly::powerpc::OPCODE::XVNABSDP)
  .value("XVNABSSP", LIEF::assembly::powerpc::OPCODE::XVNABSSP)
  .value("XVNEGDP", LIEF::assembly::powerpc::OPCODE::XVNEGDP)
  .value("XVNEGSP", LIEF::assembly::powerpc::OPCODE::XVNEGSP)
  .value("XVNMADDADP", LIEF::assembly::powerpc::OPCODE::XVNMADDADP)
  .value("XVNMADDASP", LIEF::assembly::powerpc::OPCODE::XVNMADDASP)
  .value("XVNMADDMDP", LIEF::assembly::powerpc::OPCODE::XVNMADDMDP)
  .value("XVNMADDMSP", LIEF::assembly::powerpc::OPCODE::XVNMADDMSP)
  .value("XVNMSUBADP", LIEF::assembly::powerpc::OPCODE::XVNMSUBADP)
  .value("XVNMSUBASP", LIEF::assembly::powerpc::OPCODE::XVNMSUBASP)
  .value("XVNMSUBMDP", LIEF::assembly::powerpc::OPCODE::XVNMSUBMDP)
  .value("XVNMSUBMSP", LIEF::assembly::powerpc::OPCODE::XVNMSUBMSP)
  .value("XVRDPI", LIEF::assembly::powerpc::OPCODE::XVRDPI)
  .value("XVRDPIC", LIEF::assembly::powerpc::OPCODE::XVRDPIC)
  .value("XVRDPIM", LIEF::assembly::powerpc::OPCODE::XVRDPIM)
  .value("XVRDPIP", LIEF::assembly::powerpc::OPCODE::XVRDPIP)
  .value("XVRDPIZ", LIEF::assembly::powerpc::OPCODE::XVRDPIZ)
  .value("XVREDP", LIEF::assembly::powerpc::OPCODE::XVREDP)
  .value("XVRESP", LIEF::assembly::powerpc::OPCODE::XVRESP)
  .value("XVRSPI", LIEF::assembly::powerpc::OPCODE::XVRSPI)
  .value("XVRSPIC", LIEF::assembly::powerpc::OPCODE::XVRSPIC)
  .value("XVRSPIM", LIEF::assembly::powerpc::OPCODE::XVRSPIM)
  .value("XVRSPIP", LIEF::assembly::powerpc::OPCODE::XVRSPIP)
  .value("XVRSPIZ", LIEF::assembly::powerpc::OPCODE::XVRSPIZ)
  .value("XVRSQRTEDP", LIEF::assembly::powerpc::OPCODE::XVRSQRTEDP)
  .value("XVRSQRTESP", LIEF::assembly::powerpc::OPCODE::XVRSQRTESP)
  .value("XVSQRTDP", LIEF::assembly::powerpc::OPCODE::XVSQRTDP)
  .value("XVSQRTSP", LIEF::assembly::powerpc::OPCODE::XVSQRTSP)
  .value("XVSUBDP", LIEF::assembly::powerpc::OPCODE::XVSUBDP)
  .value("XVSUBSP", LIEF::assembly::powerpc::OPCODE::XVSUBSP)
  .value("XVTDIVDP", LIEF::assembly::powerpc::OPCODE::XVTDIVDP)
  .value("XVTDIVSP", LIEF::assembly::powerpc::OPCODE::XVTDIVSP)
  .value("XVTLSBB", LIEF::assembly::powerpc::OPCODE::XVTLSBB)
  .value("XVTSQRTDP", LIEF::assembly::powerpc::OPCODE::XVTSQRTDP)
  .value("XVTSQRTSP", LIEF::assembly::powerpc::OPCODE::XVTSQRTSP)
  .value("XVTSTDCDP", LIEF::assembly::powerpc::OPCODE::XVTSTDCDP)
  .value("XVTSTDCSP", LIEF::assembly::powerpc::OPCODE::XVTSTDCSP)
  .value("XVXEXPDP", LIEF::assembly::powerpc::OPCODE::XVXEXPDP)
  .value("XVXEXPSP", LIEF::assembly::powerpc::OPCODE::XVXEXPSP)
  .value("XVXSIGDP", LIEF::assembly::powerpc::OPCODE::XVXSIGDP)
  .value("XVXSIGSP", LIEF::assembly::powerpc::OPCODE::XVXSIGSP)
  .value("XXBLENDVB", LIEF::assembly::powerpc::OPCODE::XXBLENDVB)
  .value("XXBLENDVD", LIEF::assembly::powerpc::OPCODE::XXBLENDVD)
  .value("XXBLENDVH", LIEF::assembly::powerpc::OPCODE::XXBLENDVH)
  .value("XXBLENDVW", LIEF::assembly::powerpc::OPCODE::XXBLENDVW)
  .value("XXBRD", LIEF::assembly::powerpc::OPCODE::XXBRD)
  .value("XXBRH", LIEF::assembly::powerpc::OPCODE::XXBRH)
  .value("XXBRQ", LIEF::assembly::powerpc::OPCODE::XXBRQ)
  .value("XXBRW", LIEF::assembly::powerpc::OPCODE::XXBRW)
  .value("XXEVAL", LIEF::assembly::powerpc::OPCODE::XXEVAL)
  .value("XXEXTRACTUW", LIEF::assembly::powerpc::OPCODE::XXEXTRACTUW)
  .value("XXGENPCVBM", LIEF::assembly::powerpc::OPCODE::XXGENPCVBM)
  .value("XXGENPCVDM", LIEF::assembly::powerpc::OPCODE::XXGENPCVDM)
  .value("XXGENPCVHM", LIEF::assembly::powerpc::OPCODE::XXGENPCVHM)
  .value("XXGENPCVWM", LIEF::assembly::powerpc::OPCODE::XXGENPCVWM)
  .value("XXINSERTW", LIEF::assembly::powerpc::OPCODE::XXINSERTW)
  .value("XXLAND", LIEF::assembly::powerpc::OPCODE::XXLAND)
  .value("XXLANDC", LIEF::assembly::powerpc::OPCODE::XXLANDC)
  .value("XXLEQV", LIEF::assembly::powerpc::OPCODE::XXLEQV)
  .value("XXLEQVOnes", LIEF::assembly::powerpc::OPCODE::XXLEQVOnes)
  .value("XXLNAND", LIEF::assembly::powerpc::OPCODE::XXLNAND)
  .value("XXLNOR", LIEF::assembly::powerpc::OPCODE::XXLNOR)
  .value("XXLOR", LIEF::assembly::powerpc::OPCODE::XXLOR)
  .value("XXLORC", LIEF::assembly::powerpc::OPCODE::XXLORC)
  .value("XXLORf", LIEF::assembly::powerpc::OPCODE::XXLORf)
  .value("XXLXOR", LIEF::assembly::powerpc::OPCODE::XXLXOR)
  .value("XXLXORdpz", LIEF::assembly::powerpc::OPCODE::XXLXORdpz)
  .value("XXLXORspz", LIEF::assembly::powerpc::OPCODE::XXLXORspz)
  .value("XXLXORz", LIEF::assembly::powerpc::OPCODE::XXLXORz)
  .value("XXMFACC", LIEF::assembly::powerpc::OPCODE::XXMFACC)
  .value("XXMFACCW", LIEF::assembly::powerpc::OPCODE::XXMFACCW)
  .value("XXMRGHW", LIEF::assembly::powerpc::OPCODE::XXMRGHW)
  .value("XXMRGLW", LIEF::assembly::powerpc::OPCODE::XXMRGLW)
  .value("XXMTACC", LIEF::assembly::powerpc::OPCODE::XXMTACC)
  .value("XXMTACCW", LIEF::assembly::powerpc::OPCODE::XXMTACCW)
  .value("XXPERM", LIEF::assembly::powerpc::OPCODE::XXPERM)
  .value("XXPERMDI", LIEF::assembly::powerpc::OPCODE::XXPERMDI)
  .value("XXPERMDIs", LIEF::assembly::powerpc::OPCODE::XXPERMDIs)
  .value("XXPERMR", LIEF::assembly::powerpc::OPCODE::XXPERMR)
  .value("XXPERMX", LIEF::assembly::powerpc::OPCODE::XXPERMX)
  .value("XXSEL", LIEF::assembly::powerpc::OPCODE::XXSEL)
  .value("XXSETACCZ", LIEF::assembly::powerpc::OPCODE::XXSETACCZ)
  .value("XXSETACCZW", LIEF::assembly::powerpc::OPCODE::XXSETACCZW)
  .value("XXSLDWI", LIEF::assembly::powerpc::OPCODE::XXSLDWI)
  .value("XXSLDWIs", LIEF::assembly::powerpc::OPCODE::XXSLDWIs)
  .value("XXSPLTI32DX", LIEF::assembly::powerpc::OPCODE::XXSPLTI32DX)
  .value("XXSPLTIB", LIEF::assembly::powerpc::OPCODE::XXSPLTIB)
  .value("XXSPLTIDP", LIEF::assembly::powerpc::OPCODE::XXSPLTIDP)
  .value("XXSPLTIW", LIEF::assembly::powerpc::OPCODE::XXSPLTIW)
  .value("XXSPLTW", LIEF::assembly::powerpc::OPCODE::XXSPLTW)
  .value("XXSPLTWs", LIEF::assembly::powerpc::OPCODE::XXSPLTWs)
  .value("gBC", LIEF::assembly::powerpc::OPCODE::gBC)
  .value("gBCA", LIEF::assembly::powerpc::OPCODE::gBCA)
  .value("gBCAat", LIEF::assembly::powerpc::OPCODE::gBCAat)
  .value("gBCCTR", LIEF::assembly::powerpc::OPCODE::gBCCTR)
  .value("gBCCTRL", LIEF::assembly::powerpc::OPCODE::gBCCTRL)
  .value("gBCL", LIEF::assembly::powerpc::OPCODE::gBCL)
  .value("gBCLA", LIEF::assembly::powerpc::OPCODE::gBCLA)
  .value("gBCLAat", LIEF::assembly::powerpc::OPCODE::gBCLAat)
  .value("gBCLR", LIEF::assembly::powerpc::OPCODE::gBCLR)
  .value("gBCLRL", LIEF::assembly::powerpc::OPCODE::gBCLRL)
  .value("gBCLat", LIEF::assembly::powerpc::OPCODE::gBCLat)
  .value("gBCat", LIEF::assembly::powerpc::OPCODE::gBCat)
  .value("INSTRUCTION_LIST_END", LIEF::assembly::powerpc::OPCODE::INSTRUCTION_LIST_END)
  ;
}
}
