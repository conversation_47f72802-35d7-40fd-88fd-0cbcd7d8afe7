/* Copyright 2017 - 2025 <PERSON><PERSON>
 * Copyright 2017 - 2025 Quarkslab
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef C_LIEF_MACHO_H_
#define C_LIEF_MACHO_H_

#include "LIEF/MachO/Binary.h"
#include "LIEF/MachO/Symbol.h"
#include "LIEF/MachO/Segment.h"
#include "LIEF/MachO/Section.h"
#include "LIEF/MachO/LoadCommand.h"
#include "LIEF/MachO/Header.h"
#endif
