#include "asm/arm/init.hpp"

#include "LIEF/asm/arm/opcodes.hpp"

namespace LIEF::assembly::arm::py {
template<>
void create<LIEF::assembly::arm::OPCODE>(nb::module_& m) {
  nb::enum_<LIEF::assembly::arm::OPCODE> opcodes(m, "OPCODE");
  opcodes.value("PHI", LIEF::assembly::arm::OPCODE::PHI)
  .value("INLINEASM", LIEF::assembly::arm::OPCODE::INLINEASM)
  .value("INLINEASM_BR", LIEF::assembly::arm::OPCODE::INLINEASM_BR)
  .value("CFI_INSTRUCTION", LIEF::assembly::arm::OPCODE::CFI_INSTRUCTION)
  .value("EH_LABEL", LIEF::assembly::arm::OPCODE::EH_LABEL)
  .value("GC_LABEL", LIEF::assembly::arm::OPCODE::GC_LABEL)
  .value("ANNOTATION_LABEL", LIEF::assembly::arm::OPCODE::ANNOTATION_LABEL)
  .value("KILL", LIEF::assembly::arm::OPCODE::KILL)
  .value("EXTRACT_SUBREG", LIEF::assembly::arm::OPCODE::EXTRACT_SUBREG)
  .value("INSERT_SUBREG", LIEF::assembly::arm::OPCODE::INSERT_SUBREG)
  .value("IMPLICIT_DEF", LIEF::assembly::arm::OPCODE::IMPLICIT_DEF)
  .value("INIT_UNDEF", LIEF::assembly::arm::OPCODE::INIT_UNDEF)
  .value("SUBREG_TO_REG", LIEF::assembly::arm::OPCODE::SUBREG_TO_REG)
  .value("COPY_TO_REGCLASS", LIEF::assembly::arm::OPCODE::COPY_TO_REGCLASS)
  .value("DBG_VALUE", LIEF::assembly::arm::OPCODE::DBG_VALUE)
  .value("DBG_VALUE_LIST", LIEF::assembly::arm::OPCODE::DBG_VALUE_LIST)
  .value("DBG_INSTR_REF", LIEF::assembly::arm::OPCODE::DBG_INSTR_REF)
  .value("DBG_PHI", LIEF::assembly::arm::OPCODE::DBG_PHI)
  .value("DBG_LABEL", LIEF::assembly::arm::OPCODE::DBG_LABEL)
  .value("REG_SEQUENCE", LIEF::assembly::arm::OPCODE::REG_SEQUENCE)
  .value("COPY", LIEF::assembly::arm::OPCODE::COPY)
  .value("BUNDLE", LIEF::assembly::arm::OPCODE::BUNDLE)
  .value("LIFETIME_START", LIEF::assembly::arm::OPCODE::LIFETIME_START)
  .value("LIFETIME_END", LIEF::assembly::arm::OPCODE::LIFETIME_END)
  .value("PSEUDO_PROBE", LIEF::assembly::arm::OPCODE::PSEUDO_PROBE)
  .value("ARITH_FENCE", LIEF::assembly::arm::OPCODE::ARITH_FENCE)
  .value("STACKMAP", LIEF::assembly::arm::OPCODE::STACKMAP)
  .value("FENTRY_CALL", LIEF::assembly::arm::OPCODE::FENTRY_CALL)
  .value("PATCHPOINT", LIEF::assembly::arm::OPCODE::PATCHPOINT)
  .value("LOAD_STACK_GUARD", LIEF::assembly::arm::OPCODE::LOAD_STACK_GUARD)
  .value("PREALLOCATED_SETUP", LIEF::assembly::arm::OPCODE::PREALLOCATED_SETUP)
  .value("PREALLOCATED_ARG", LIEF::assembly::arm::OPCODE::PREALLOCATED_ARG)
  .value("STATEPOINT", LIEF::assembly::arm::OPCODE::STATEPOINT)
  .value("LOCAL_ESCAPE", LIEF::assembly::arm::OPCODE::LOCAL_ESCAPE)
  .value("FAULTING_OP", LIEF::assembly::arm::OPCODE::FAULTING_OP)
  .value("PATCHABLE_OP", LIEF::assembly::arm::OPCODE::PATCHABLE_OP)
  .value("PATCHABLE_FUNCTION_ENTER", LIEF::assembly::arm::OPCODE::PATCHABLE_FUNCTION_ENTER)
  .value("PATCHABLE_RET", LIEF::assembly::arm::OPCODE::PATCHABLE_RET)
  .value("PATCHABLE_FUNCTION_EXIT", LIEF::assembly::arm::OPCODE::PATCHABLE_FUNCTION_EXIT)
  .value("PATCHABLE_TAIL_CALL", LIEF::assembly::arm::OPCODE::PATCHABLE_TAIL_CALL)
  .value("PATCHABLE_EVENT_CALL", LIEF::assembly::arm::OPCODE::PATCHABLE_EVENT_CALL)
  .value("PATCHABLE_TYPED_EVENT_CALL", LIEF::assembly::arm::OPCODE::PATCHABLE_TYPED_EVENT_CALL)
  .value("ICALL_BRANCH_FUNNEL", LIEF::assembly::arm::OPCODE::ICALL_BRANCH_FUNNEL)
  .value("FAKE_USE", LIEF::assembly::arm::OPCODE::FAKE_USE)
  .value("MEMBARRIER", LIEF::assembly::arm::OPCODE::MEMBARRIER)
  .value("JUMP_TABLE_DEBUG_INFO", LIEF::assembly::arm::OPCODE::JUMP_TABLE_DEBUG_INFO)
  .value("CONVERGENCECTRL_ENTRY", LIEF::assembly::arm::OPCODE::CONVERGENCECTRL_ENTRY)
  .value("CONVERGENCECTRL_ANCHOR", LIEF::assembly::arm::OPCODE::CONVERGENCECTRL_ANCHOR)
  .value("CONVERGENCECTRL_LOOP", LIEF::assembly::arm::OPCODE::CONVERGENCECTRL_LOOP)
  .value("CONVERGENCECTRL_GLUE", LIEF::assembly::arm::OPCODE::CONVERGENCECTRL_GLUE)
  .value("G_ASSERT_SEXT", LIEF::assembly::arm::OPCODE::G_ASSERT_SEXT)
  .value("G_ASSERT_ZEXT", LIEF::assembly::arm::OPCODE::G_ASSERT_ZEXT)
  .value("G_ASSERT_ALIGN", LIEF::assembly::arm::OPCODE::G_ASSERT_ALIGN)
  .value("G_ADD", LIEF::assembly::arm::OPCODE::G_ADD)
  .value("G_SUB", LIEF::assembly::arm::OPCODE::G_SUB)
  .value("G_MUL", LIEF::assembly::arm::OPCODE::G_MUL)
  .value("G_SDIV", LIEF::assembly::arm::OPCODE::G_SDIV)
  .value("G_UDIV", LIEF::assembly::arm::OPCODE::G_UDIV)
  .value("G_SREM", LIEF::assembly::arm::OPCODE::G_SREM)
  .value("G_UREM", LIEF::assembly::arm::OPCODE::G_UREM)
  .value("G_SDIVREM", LIEF::assembly::arm::OPCODE::G_SDIVREM)
  .value("G_UDIVREM", LIEF::assembly::arm::OPCODE::G_UDIVREM)
  .value("G_AND", LIEF::assembly::arm::OPCODE::G_AND)
  .value("G_OR", LIEF::assembly::arm::OPCODE::G_OR)
  .value("G_XOR", LIEF::assembly::arm::OPCODE::G_XOR)
  .value("G_ABDS", LIEF::assembly::arm::OPCODE::G_ABDS)
  .value("G_ABDU", LIEF::assembly::arm::OPCODE::G_ABDU)
  .value("G_IMPLICIT_DEF", LIEF::assembly::arm::OPCODE::G_IMPLICIT_DEF)
  .value("G_PHI", LIEF::assembly::arm::OPCODE::G_PHI)
  .value("G_FRAME_INDEX", LIEF::assembly::arm::OPCODE::G_FRAME_INDEX)
  .value("G_GLOBAL_VALUE", LIEF::assembly::arm::OPCODE::G_GLOBAL_VALUE)
  .value("G_PTRAUTH_GLOBAL_VALUE", LIEF::assembly::arm::OPCODE::G_PTRAUTH_GLOBAL_VALUE)
  .value("G_CONSTANT_POOL", LIEF::assembly::arm::OPCODE::G_CONSTANT_POOL)
  .value("G_EXTRACT", LIEF::assembly::arm::OPCODE::G_EXTRACT)
  .value("G_UNMERGE_VALUES", LIEF::assembly::arm::OPCODE::G_UNMERGE_VALUES)
  .value("G_INSERT", LIEF::assembly::arm::OPCODE::G_INSERT)
  .value("G_MERGE_VALUES", LIEF::assembly::arm::OPCODE::G_MERGE_VALUES)
  .value("G_BUILD_VECTOR", LIEF::assembly::arm::OPCODE::G_BUILD_VECTOR)
  .value("G_BUILD_VECTOR_TRUNC", LIEF::assembly::arm::OPCODE::G_BUILD_VECTOR_TRUNC)
  .value("G_CONCAT_VECTORS", LIEF::assembly::arm::OPCODE::G_CONCAT_VECTORS)
  .value("G_PTRTOINT", LIEF::assembly::arm::OPCODE::G_PTRTOINT)
  .value("G_INTTOPTR", LIEF::assembly::arm::OPCODE::G_INTTOPTR)
  .value("G_BITCAST", LIEF::assembly::arm::OPCODE::G_BITCAST)
  .value("G_FREEZE", LIEF::assembly::arm::OPCODE::G_FREEZE)
  .value("G_CONSTANT_FOLD_BARRIER", LIEF::assembly::arm::OPCODE::G_CONSTANT_FOLD_BARRIER)
  .value("G_INTRINSIC_FPTRUNC_ROUND", LIEF::assembly::arm::OPCODE::G_INTRINSIC_FPTRUNC_ROUND)
  .value("G_INTRINSIC_TRUNC", LIEF::assembly::arm::OPCODE::G_INTRINSIC_TRUNC)
  .value("G_INTRINSIC_ROUND", LIEF::assembly::arm::OPCODE::G_INTRINSIC_ROUND)
  .value("G_INTRINSIC_LRINT", LIEF::assembly::arm::OPCODE::G_INTRINSIC_LRINT)
  .value("G_INTRINSIC_LLRINT", LIEF::assembly::arm::OPCODE::G_INTRINSIC_LLRINT)
  .value("G_INTRINSIC_ROUNDEVEN", LIEF::assembly::arm::OPCODE::G_INTRINSIC_ROUNDEVEN)
  .value("G_READCYCLECOUNTER", LIEF::assembly::arm::OPCODE::G_READCYCLECOUNTER)
  .value("G_READSTEADYCOUNTER", LIEF::assembly::arm::OPCODE::G_READSTEADYCOUNTER)
  .value("G_LOAD", LIEF::assembly::arm::OPCODE::G_LOAD)
  .value("G_SEXTLOAD", LIEF::assembly::arm::OPCODE::G_SEXTLOAD)
  .value("G_ZEXTLOAD", LIEF::assembly::arm::OPCODE::G_ZEXTLOAD)
  .value("G_INDEXED_LOAD", LIEF::assembly::arm::OPCODE::G_INDEXED_LOAD)
  .value("G_INDEXED_SEXTLOAD", LIEF::assembly::arm::OPCODE::G_INDEXED_SEXTLOAD)
  .value("G_INDEXED_ZEXTLOAD", LIEF::assembly::arm::OPCODE::G_INDEXED_ZEXTLOAD)
  .value("G_STORE", LIEF::assembly::arm::OPCODE::G_STORE)
  .value("G_INDEXED_STORE", LIEF::assembly::arm::OPCODE::G_INDEXED_STORE)
  .value("G_ATOMIC_CMPXCHG_WITH_SUCCESS", LIEF::assembly::arm::OPCODE::G_ATOMIC_CMPXCHG_WITH_SUCCESS)
  .value("G_ATOMIC_CMPXCHG", LIEF::assembly::arm::OPCODE::G_ATOMIC_CMPXCHG)
  .value("G_ATOMICRMW_XCHG", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_XCHG)
  .value("G_ATOMICRMW_ADD", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_ADD)
  .value("G_ATOMICRMW_SUB", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_SUB)
  .value("G_ATOMICRMW_AND", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_AND)
  .value("G_ATOMICRMW_NAND", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_NAND)
  .value("G_ATOMICRMW_OR", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_OR)
  .value("G_ATOMICRMW_XOR", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_XOR)
  .value("G_ATOMICRMW_MAX", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_MAX)
  .value("G_ATOMICRMW_MIN", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_MIN)
  .value("G_ATOMICRMW_UMAX", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_UMAX)
  .value("G_ATOMICRMW_UMIN", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_UMIN)
  .value("G_ATOMICRMW_FADD", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_FADD)
  .value("G_ATOMICRMW_FSUB", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_FSUB)
  .value("G_ATOMICRMW_FMAX", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_FMAX)
  .value("G_ATOMICRMW_FMIN", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_FMIN)
  .value("G_ATOMICRMW_UINC_WRAP", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_UINC_WRAP)
  .value("G_ATOMICRMW_UDEC_WRAP", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_UDEC_WRAP)
  .value("G_ATOMICRMW_USUB_COND", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_USUB_COND)
  .value("G_ATOMICRMW_USUB_SAT", LIEF::assembly::arm::OPCODE::G_ATOMICRMW_USUB_SAT)
  .value("G_FENCE", LIEF::assembly::arm::OPCODE::G_FENCE)
  .value("G_PREFETCH", LIEF::assembly::arm::OPCODE::G_PREFETCH)
  .value("G_BRCOND", LIEF::assembly::arm::OPCODE::G_BRCOND)
  .value("G_BRINDIRECT", LIEF::assembly::arm::OPCODE::G_BRINDIRECT)
  .value("G_INVOKE_REGION_START", LIEF::assembly::arm::OPCODE::G_INVOKE_REGION_START)
  .value("G_INTRINSIC", LIEF::assembly::arm::OPCODE::G_INTRINSIC)
  .value("G_INTRINSIC_W_SIDE_EFFECTS", LIEF::assembly::arm::OPCODE::G_INTRINSIC_W_SIDE_EFFECTS)
  .value("G_INTRINSIC_CONVERGENT", LIEF::assembly::arm::OPCODE::G_INTRINSIC_CONVERGENT)
  .value("G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS", LIEF::assembly::arm::OPCODE::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS)
  .value("G_ANYEXT", LIEF::assembly::arm::OPCODE::G_ANYEXT)
  .value("G_TRUNC", LIEF::assembly::arm::OPCODE::G_TRUNC)
  .value("G_CONSTANT", LIEF::assembly::arm::OPCODE::G_CONSTANT)
  .value("G_FCONSTANT", LIEF::assembly::arm::OPCODE::G_FCONSTANT)
  .value("G_VASTART", LIEF::assembly::arm::OPCODE::G_VASTART)
  .value("G_VAARG", LIEF::assembly::arm::OPCODE::G_VAARG)
  .value("G_SEXT", LIEF::assembly::arm::OPCODE::G_SEXT)
  .value("G_SEXT_INREG", LIEF::assembly::arm::OPCODE::G_SEXT_INREG)
  .value("G_ZEXT", LIEF::assembly::arm::OPCODE::G_ZEXT)
  .value("G_SHL", LIEF::assembly::arm::OPCODE::G_SHL)
  .value("G_LSHR", LIEF::assembly::arm::OPCODE::G_LSHR)
  .value("G_ASHR", LIEF::assembly::arm::OPCODE::G_ASHR)
  .value("G_FSHL", LIEF::assembly::arm::OPCODE::G_FSHL)
  .value("G_FSHR", LIEF::assembly::arm::OPCODE::G_FSHR)
  .value("G_ROTR", LIEF::assembly::arm::OPCODE::G_ROTR)
  .value("G_ROTL", LIEF::assembly::arm::OPCODE::G_ROTL)
  .value("G_ICMP", LIEF::assembly::arm::OPCODE::G_ICMP)
  .value("G_FCMP", LIEF::assembly::arm::OPCODE::G_FCMP)
  .value("G_SCMP", LIEF::assembly::arm::OPCODE::G_SCMP)
  .value("G_UCMP", LIEF::assembly::arm::OPCODE::G_UCMP)
  .value("G_SELECT", LIEF::assembly::arm::OPCODE::G_SELECT)
  .value("G_UADDO", LIEF::assembly::arm::OPCODE::G_UADDO)
  .value("G_UADDE", LIEF::assembly::arm::OPCODE::G_UADDE)
  .value("G_USUBO", LIEF::assembly::arm::OPCODE::G_USUBO)
  .value("G_USUBE", LIEF::assembly::arm::OPCODE::G_USUBE)
  .value("G_SADDO", LIEF::assembly::arm::OPCODE::G_SADDO)
  .value("G_SADDE", LIEF::assembly::arm::OPCODE::G_SADDE)
  .value("G_SSUBO", LIEF::assembly::arm::OPCODE::G_SSUBO)
  .value("G_SSUBE", LIEF::assembly::arm::OPCODE::G_SSUBE)
  .value("G_UMULO", LIEF::assembly::arm::OPCODE::G_UMULO)
  .value("G_SMULO", LIEF::assembly::arm::OPCODE::G_SMULO)
  .value("G_UMULH", LIEF::assembly::arm::OPCODE::G_UMULH)
  .value("G_SMULH", LIEF::assembly::arm::OPCODE::G_SMULH)
  .value("G_UADDSAT", LIEF::assembly::arm::OPCODE::G_UADDSAT)
  .value("G_SADDSAT", LIEF::assembly::arm::OPCODE::G_SADDSAT)
  .value("G_USUBSAT", LIEF::assembly::arm::OPCODE::G_USUBSAT)
  .value("G_SSUBSAT", LIEF::assembly::arm::OPCODE::G_SSUBSAT)
  .value("G_USHLSAT", LIEF::assembly::arm::OPCODE::G_USHLSAT)
  .value("G_SSHLSAT", LIEF::assembly::arm::OPCODE::G_SSHLSAT)
  .value("G_SMULFIX", LIEF::assembly::arm::OPCODE::G_SMULFIX)
  .value("G_UMULFIX", LIEF::assembly::arm::OPCODE::G_UMULFIX)
  .value("G_SMULFIXSAT", LIEF::assembly::arm::OPCODE::G_SMULFIXSAT)
  .value("G_UMULFIXSAT", LIEF::assembly::arm::OPCODE::G_UMULFIXSAT)
  .value("G_SDIVFIX", LIEF::assembly::arm::OPCODE::G_SDIVFIX)
  .value("G_UDIVFIX", LIEF::assembly::arm::OPCODE::G_UDIVFIX)
  .value("G_SDIVFIXSAT", LIEF::assembly::arm::OPCODE::G_SDIVFIXSAT)
  .value("G_UDIVFIXSAT", LIEF::assembly::arm::OPCODE::G_UDIVFIXSAT)
  .value("G_FADD", LIEF::assembly::arm::OPCODE::G_FADD)
  .value("G_FSUB", LIEF::assembly::arm::OPCODE::G_FSUB)
  .value("G_FMUL", LIEF::assembly::arm::OPCODE::G_FMUL)
  .value("G_FMA", LIEF::assembly::arm::OPCODE::G_FMA)
  .value("G_FMAD", LIEF::assembly::arm::OPCODE::G_FMAD)
  .value("G_FDIV", LIEF::assembly::arm::OPCODE::G_FDIV)
  .value("G_FREM", LIEF::assembly::arm::OPCODE::G_FREM)
  .value("G_FPOW", LIEF::assembly::arm::OPCODE::G_FPOW)
  .value("G_FPOWI", LIEF::assembly::arm::OPCODE::G_FPOWI)
  .value("G_FEXP", LIEF::assembly::arm::OPCODE::G_FEXP)
  .value("G_FEXP2", LIEF::assembly::arm::OPCODE::G_FEXP2)
  .value("G_FEXP10", LIEF::assembly::arm::OPCODE::G_FEXP10)
  .value("G_FLOG", LIEF::assembly::arm::OPCODE::G_FLOG)
  .value("G_FLOG2", LIEF::assembly::arm::OPCODE::G_FLOG2)
  .value("G_FLOG10", LIEF::assembly::arm::OPCODE::G_FLOG10)
  .value("G_FLDEXP", LIEF::assembly::arm::OPCODE::G_FLDEXP)
  .value("G_FFREXP", LIEF::assembly::arm::OPCODE::G_FFREXP)
  .value("G_FNEG", LIEF::assembly::arm::OPCODE::G_FNEG)
  .value("G_FPEXT", LIEF::assembly::arm::OPCODE::G_FPEXT)
  .value("G_FPTRUNC", LIEF::assembly::arm::OPCODE::G_FPTRUNC)
  .value("G_FPTOSI", LIEF::assembly::arm::OPCODE::G_FPTOSI)
  .value("G_FPTOUI", LIEF::assembly::arm::OPCODE::G_FPTOUI)
  .value("G_SITOFP", LIEF::assembly::arm::OPCODE::G_SITOFP)
  .value("G_UITOFP", LIEF::assembly::arm::OPCODE::G_UITOFP)
  .value("G_FPTOSI_SAT", LIEF::assembly::arm::OPCODE::G_FPTOSI_SAT)
  .value("G_FPTOUI_SAT", LIEF::assembly::arm::OPCODE::G_FPTOUI_SAT)
  .value("G_FABS", LIEF::assembly::arm::OPCODE::G_FABS)
  .value("G_FCOPYSIGN", LIEF::assembly::arm::OPCODE::G_FCOPYSIGN)
  .value("G_IS_FPCLASS", LIEF::assembly::arm::OPCODE::G_IS_FPCLASS)
  .value("G_FCANONICALIZE", LIEF::assembly::arm::OPCODE::G_FCANONICALIZE)
  .value("G_FMINNUM", LIEF::assembly::arm::OPCODE::G_FMINNUM)
  .value("G_FMAXNUM", LIEF::assembly::arm::OPCODE::G_FMAXNUM)
  .value("G_FMINNUM_IEEE", LIEF::assembly::arm::OPCODE::G_FMINNUM_IEEE)
  .value("G_FMAXNUM_IEEE", LIEF::assembly::arm::OPCODE::G_FMAXNUM_IEEE)
  .value("G_FMINIMUM", LIEF::assembly::arm::OPCODE::G_FMINIMUM)
  .value("G_FMAXIMUM", LIEF::assembly::arm::OPCODE::G_FMAXIMUM)
  .value("G_GET_FPENV", LIEF::assembly::arm::OPCODE::G_GET_FPENV)
  .value("G_SET_FPENV", LIEF::assembly::arm::OPCODE::G_SET_FPENV)
  .value("G_RESET_FPENV", LIEF::assembly::arm::OPCODE::G_RESET_FPENV)
  .value("G_GET_FPMODE", LIEF::assembly::arm::OPCODE::G_GET_FPMODE)
  .value("G_SET_FPMODE", LIEF::assembly::arm::OPCODE::G_SET_FPMODE)
  .value("G_RESET_FPMODE", LIEF::assembly::arm::OPCODE::G_RESET_FPMODE)
  .value("G_PTR_ADD", LIEF::assembly::arm::OPCODE::G_PTR_ADD)
  .value("G_PTRMASK", LIEF::assembly::arm::OPCODE::G_PTRMASK)
  .value("G_SMIN", LIEF::assembly::arm::OPCODE::G_SMIN)
  .value("G_SMAX", LIEF::assembly::arm::OPCODE::G_SMAX)
  .value("G_UMIN", LIEF::assembly::arm::OPCODE::G_UMIN)
  .value("G_UMAX", LIEF::assembly::arm::OPCODE::G_UMAX)
  .value("G_ABS", LIEF::assembly::arm::OPCODE::G_ABS)
  .value("G_LROUND", LIEF::assembly::arm::OPCODE::G_LROUND)
  .value("G_LLROUND", LIEF::assembly::arm::OPCODE::G_LLROUND)
  .value("G_BR", LIEF::assembly::arm::OPCODE::G_BR)
  .value("G_BRJT", LIEF::assembly::arm::OPCODE::G_BRJT)
  .value("G_VSCALE", LIEF::assembly::arm::OPCODE::G_VSCALE)
  .value("G_INSERT_SUBVECTOR", LIEF::assembly::arm::OPCODE::G_INSERT_SUBVECTOR)
  .value("G_EXTRACT_SUBVECTOR", LIEF::assembly::arm::OPCODE::G_EXTRACT_SUBVECTOR)
  .value("G_INSERT_VECTOR_ELT", LIEF::assembly::arm::OPCODE::G_INSERT_VECTOR_ELT)
  .value("G_EXTRACT_VECTOR_ELT", LIEF::assembly::arm::OPCODE::G_EXTRACT_VECTOR_ELT)
  .value("G_SHUFFLE_VECTOR", LIEF::assembly::arm::OPCODE::G_SHUFFLE_VECTOR)
  .value("G_SPLAT_VECTOR", LIEF::assembly::arm::OPCODE::G_SPLAT_VECTOR)
  .value("G_STEP_VECTOR", LIEF::assembly::arm::OPCODE::G_STEP_VECTOR)
  .value("G_VECTOR_COMPRESS", LIEF::assembly::arm::OPCODE::G_VECTOR_COMPRESS)
  .value("G_CTTZ", LIEF::assembly::arm::OPCODE::G_CTTZ)
  .value("G_CTTZ_ZERO_UNDEF", LIEF::assembly::arm::OPCODE::G_CTTZ_ZERO_UNDEF)
  .value("G_CTLZ", LIEF::assembly::arm::OPCODE::G_CTLZ)
  .value("G_CTLZ_ZERO_UNDEF", LIEF::assembly::arm::OPCODE::G_CTLZ_ZERO_UNDEF)
  .value("G_CTPOP", LIEF::assembly::arm::OPCODE::G_CTPOP)
  .value("G_BSWAP", LIEF::assembly::arm::OPCODE::G_BSWAP)
  .value("G_BITREVERSE", LIEF::assembly::arm::OPCODE::G_BITREVERSE)
  .value("G_FCEIL", LIEF::assembly::arm::OPCODE::G_FCEIL)
  .value("G_FCOS", LIEF::assembly::arm::OPCODE::G_FCOS)
  .value("G_FSIN", LIEF::assembly::arm::OPCODE::G_FSIN)
  .value("G_FSINCOS", LIEF::assembly::arm::OPCODE::G_FSINCOS)
  .value("G_FTAN", LIEF::assembly::arm::OPCODE::G_FTAN)
  .value("G_FACOS", LIEF::assembly::arm::OPCODE::G_FACOS)
  .value("G_FASIN", LIEF::assembly::arm::OPCODE::G_FASIN)
  .value("G_FATAN", LIEF::assembly::arm::OPCODE::G_FATAN)
  .value("G_FATAN2", LIEF::assembly::arm::OPCODE::G_FATAN2)
  .value("G_FCOSH", LIEF::assembly::arm::OPCODE::G_FCOSH)
  .value("G_FSINH", LIEF::assembly::arm::OPCODE::G_FSINH)
  .value("G_FTANH", LIEF::assembly::arm::OPCODE::G_FTANH)
  .value("G_FSQRT", LIEF::assembly::arm::OPCODE::G_FSQRT)
  .value("G_FFLOOR", LIEF::assembly::arm::OPCODE::G_FFLOOR)
  .value("G_FRINT", LIEF::assembly::arm::OPCODE::G_FRINT)
  .value("G_FNEARBYINT", LIEF::assembly::arm::OPCODE::G_FNEARBYINT)
  .value("G_ADDRSPACE_CAST", LIEF::assembly::arm::OPCODE::G_ADDRSPACE_CAST)
  .value("G_BLOCK_ADDR", LIEF::assembly::arm::OPCODE::G_BLOCK_ADDR)
  .value("G_JUMP_TABLE", LIEF::assembly::arm::OPCODE::G_JUMP_TABLE)
  .value("G_DYN_STACKALLOC", LIEF::assembly::arm::OPCODE::G_DYN_STACKALLOC)
  .value("G_STACKSAVE", LIEF::assembly::arm::OPCODE::G_STACKSAVE)
  .value("G_STACKRESTORE", LIEF::assembly::arm::OPCODE::G_STACKRESTORE)
  .value("G_STRICT_FADD", LIEF::assembly::arm::OPCODE::G_STRICT_FADD)
  .value("G_STRICT_FSUB", LIEF::assembly::arm::OPCODE::G_STRICT_FSUB)
  .value("G_STRICT_FMUL", LIEF::assembly::arm::OPCODE::G_STRICT_FMUL)
  .value("G_STRICT_FDIV", LIEF::assembly::arm::OPCODE::G_STRICT_FDIV)
  .value("G_STRICT_FREM", LIEF::assembly::arm::OPCODE::G_STRICT_FREM)
  .value("G_STRICT_FMA", LIEF::assembly::arm::OPCODE::G_STRICT_FMA)
  .value("G_STRICT_FSQRT", LIEF::assembly::arm::OPCODE::G_STRICT_FSQRT)
  .value("G_STRICT_FLDEXP", LIEF::assembly::arm::OPCODE::G_STRICT_FLDEXP)
  .value("G_READ_REGISTER", LIEF::assembly::arm::OPCODE::G_READ_REGISTER)
  .value("G_WRITE_REGISTER", LIEF::assembly::arm::OPCODE::G_WRITE_REGISTER)
  .value("G_MEMCPY", LIEF::assembly::arm::OPCODE::G_MEMCPY)
  .value("G_MEMCPY_INLINE", LIEF::assembly::arm::OPCODE::G_MEMCPY_INLINE)
  .value("G_MEMMOVE", LIEF::assembly::arm::OPCODE::G_MEMMOVE)
  .value("G_MEMSET", LIEF::assembly::arm::OPCODE::G_MEMSET)
  .value("G_BZERO", LIEF::assembly::arm::OPCODE::G_BZERO)
  .value("G_TRAP", LIEF::assembly::arm::OPCODE::G_TRAP)
  .value("G_DEBUGTRAP", LIEF::assembly::arm::OPCODE::G_DEBUGTRAP)
  .value("G_UBSANTRAP", LIEF::assembly::arm::OPCODE::G_UBSANTRAP)
  .value("G_VECREDUCE_SEQ_FADD", LIEF::assembly::arm::OPCODE::G_VECREDUCE_SEQ_FADD)
  .value("G_VECREDUCE_SEQ_FMUL", LIEF::assembly::arm::OPCODE::G_VECREDUCE_SEQ_FMUL)
  .value("G_VECREDUCE_FADD", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FADD)
  .value("G_VECREDUCE_FMUL", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FMUL)
  .value("G_VECREDUCE_FMAX", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FMAX)
  .value("G_VECREDUCE_FMIN", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FMIN)
  .value("G_VECREDUCE_FMAXIMUM", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FMAXIMUM)
  .value("G_VECREDUCE_FMINIMUM", LIEF::assembly::arm::OPCODE::G_VECREDUCE_FMINIMUM)
  .value("G_VECREDUCE_ADD", LIEF::assembly::arm::OPCODE::G_VECREDUCE_ADD)
  .value("G_VECREDUCE_MUL", LIEF::assembly::arm::OPCODE::G_VECREDUCE_MUL)
  .value("G_VECREDUCE_AND", LIEF::assembly::arm::OPCODE::G_VECREDUCE_AND)
  .value("G_VECREDUCE_OR", LIEF::assembly::arm::OPCODE::G_VECREDUCE_OR)
  .value("G_VECREDUCE_XOR", LIEF::assembly::arm::OPCODE::G_VECREDUCE_XOR);
  opcodes.value("G_VECREDUCE_SMAX", LIEF::assembly::arm::OPCODE::G_VECREDUCE_SMAX)
  .value("G_VECREDUCE_SMIN", LIEF::assembly::arm::OPCODE::G_VECREDUCE_SMIN)
  .value("G_VECREDUCE_UMAX", LIEF::assembly::arm::OPCODE::G_VECREDUCE_UMAX)
  .value("G_VECREDUCE_UMIN", LIEF::assembly::arm::OPCODE::G_VECREDUCE_UMIN)
  .value("G_SBFX", LIEF::assembly::arm::OPCODE::G_SBFX)
  .value("G_UBFX", LIEF::assembly::arm::OPCODE::G_UBFX)
  .value("ABS", LIEF::assembly::arm::OPCODE::ABS)
  .value("ADDSri", LIEF::assembly::arm::OPCODE::ADDSri)
  .value("ADDSrr", LIEF::assembly::arm::OPCODE::ADDSrr)
  .value("ADDSrsi", LIEF::assembly::arm::OPCODE::ADDSrsi)
  .value("ADDSrsr", LIEF::assembly::arm::OPCODE::ADDSrsr)
  .value("ADJCALLSTACKDOWN", LIEF::assembly::arm::OPCODE::ADJCALLSTACKDOWN)
  .value("ADJCALLSTACKUP", LIEF::assembly::arm::OPCODE::ADJCALLSTACKUP)
  .value("ASRi", LIEF::assembly::arm::OPCODE::ASRi)
  .value("ASRr", LIEF::assembly::arm::OPCODE::ASRr)
  .value("ASRs1", LIEF::assembly::arm::OPCODE::ASRs1)
  .value("B", LIEF::assembly::arm::OPCODE::B)
  .value("BCCZi64", LIEF::assembly::arm::OPCODE::BCCZi64)
  .value("BCCi64", LIEF::assembly::arm::OPCODE::BCCi64)
  .value("BLX_noip", LIEF::assembly::arm::OPCODE::BLX_noip)
  .value("BLX_pred_noip", LIEF::assembly::arm::OPCODE::BLX_pred_noip)
  .value("BL_PUSHLR", LIEF::assembly::arm::OPCODE::BL_PUSHLR)
  .value("BMOVPCB_CALL", LIEF::assembly::arm::OPCODE::BMOVPCB_CALL)
  .value("BMOVPCRX_CALL", LIEF::assembly::arm::OPCODE::BMOVPCRX_CALL)
  .value("BR_JTadd", LIEF::assembly::arm::OPCODE::BR_JTadd)
  .value("BR_JTm_i12", LIEF::assembly::arm::OPCODE::BR_JTm_i12)
  .value("BR_JTm_rs", LIEF::assembly::arm::OPCODE::BR_JTm_rs)
  .value("BR_JTr", LIEF::assembly::arm::OPCODE::BR_JTr)
  .value("BX_CALL", LIEF::assembly::arm::OPCODE::BX_CALL)
  .value("CMP_SWAP_16", LIEF::assembly::arm::OPCODE::CMP_SWAP_16)
  .value("CMP_SWAP_32", LIEF::assembly::arm::OPCODE::CMP_SWAP_32)
  .value("CMP_SWAP_64", LIEF::assembly::arm::OPCODE::CMP_SWAP_64)
  .value("CMP_SWAP_8", LIEF::assembly::arm::OPCODE::CMP_SWAP_8)
  .value("CONSTPOOL_ENTRY", LIEF::assembly::arm::OPCODE::CONSTPOOL_ENTRY)
  .value("COPY_STRUCT_BYVAL_I32", LIEF::assembly::arm::OPCODE::COPY_STRUCT_BYVAL_I32)
  .value("ITasm", LIEF::assembly::arm::OPCODE::ITasm)
  .value("Int_eh_sjlj_dispatchsetup", LIEF::assembly::arm::OPCODE::Int_eh_sjlj_dispatchsetup)
  .value("Int_eh_sjlj_longjmp", LIEF::assembly::arm::OPCODE::Int_eh_sjlj_longjmp)
  .value("Int_eh_sjlj_setjmp", LIEF::assembly::arm::OPCODE::Int_eh_sjlj_setjmp)
  .value("Int_eh_sjlj_setjmp_nofp", LIEF::assembly::arm::OPCODE::Int_eh_sjlj_setjmp_nofp)
  .value("Int_eh_sjlj_setup_dispatch", LIEF::assembly::arm::OPCODE::Int_eh_sjlj_setup_dispatch)
  .value("JUMPTABLE_ADDRS", LIEF::assembly::arm::OPCODE::JUMPTABLE_ADDRS)
  .value("JUMPTABLE_INSTS", LIEF::assembly::arm::OPCODE::JUMPTABLE_INSTS)
  .value("JUMPTABLE_TBB", LIEF::assembly::arm::OPCODE::JUMPTABLE_TBB)
  .value("JUMPTABLE_TBH", LIEF::assembly::arm::OPCODE::JUMPTABLE_TBH)
  .value("LDMIA_RET", LIEF::assembly::arm::OPCODE::LDMIA_RET)
  .value("LDRBT_POST", LIEF::assembly::arm::OPCODE::LDRBT_POST)
  .value("LDRConstPool", LIEF::assembly::arm::OPCODE::LDRConstPool)
  .value("LDRHTii", LIEF::assembly::arm::OPCODE::LDRHTii)
  .value("LDRLIT_ga_abs", LIEF::assembly::arm::OPCODE::LDRLIT_ga_abs)
  .value("LDRLIT_ga_pcrel", LIEF::assembly::arm::OPCODE::LDRLIT_ga_pcrel)
  .value("LDRLIT_ga_pcrel_ldr", LIEF::assembly::arm::OPCODE::LDRLIT_ga_pcrel_ldr)
  .value("LDRSBTii", LIEF::assembly::arm::OPCODE::LDRSBTii)
  .value("LDRSHTii", LIEF::assembly::arm::OPCODE::LDRSHTii)
  .value("LDRT_POST", LIEF::assembly::arm::OPCODE::LDRT_POST)
  .value("LEApcrel", LIEF::assembly::arm::OPCODE::LEApcrel)
  .value("LEApcrelJT", LIEF::assembly::arm::OPCODE::LEApcrelJT)
  .value("LOADDUAL", LIEF::assembly::arm::OPCODE::LOADDUAL)
  .value("LSLi", LIEF::assembly::arm::OPCODE::LSLi)
  .value("LSLr", LIEF::assembly::arm::OPCODE::LSLr)
  .value("LSRi", LIEF::assembly::arm::OPCODE::LSRi)
  .value("LSRr", LIEF::assembly::arm::OPCODE::LSRr)
  .value("LSRs1", LIEF::assembly::arm::OPCODE::LSRs1)
  .value("MEMCPY", LIEF::assembly::arm::OPCODE::MEMCPY)
  .value("MLAv5", LIEF::assembly::arm::OPCODE::MLAv5)
  .value("MOVCCi", LIEF::assembly::arm::OPCODE::MOVCCi)
  .value("MOVCCi16", LIEF::assembly::arm::OPCODE::MOVCCi16)
  .value("MOVCCi32imm", LIEF::assembly::arm::OPCODE::MOVCCi32imm)
  .value("MOVCCr", LIEF::assembly::arm::OPCODE::MOVCCr)
  .value("MOVCCsi", LIEF::assembly::arm::OPCODE::MOVCCsi)
  .value("MOVCCsr", LIEF::assembly::arm::OPCODE::MOVCCsr)
  .value("MOVPCRX", LIEF::assembly::arm::OPCODE::MOVPCRX)
  .value("MOVTi16_ga_pcrel", LIEF::assembly::arm::OPCODE::MOVTi16_ga_pcrel)
  .value("MOV_ga_pcrel", LIEF::assembly::arm::OPCODE::MOV_ga_pcrel)
  .value("MOV_ga_pcrel_ldr", LIEF::assembly::arm::OPCODE::MOV_ga_pcrel_ldr)
  .value("MOVi16_ga_pcrel", LIEF::assembly::arm::OPCODE::MOVi16_ga_pcrel)
  .value("MOVi32imm", LIEF::assembly::arm::OPCODE::MOVi32imm)
  .value("MQPRCopy", LIEF::assembly::arm::OPCODE::MQPRCopy)
  .value("MQQPRLoad", LIEF::assembly::arm::OPCODE::MQQPRLoad)
  .value("MQQPRStore", LIEF::assembly::arm::OPCODE::MQQPRStore)
  .value("MQQQQPRLoad", LIEF::assembly::arm::OPCODE::MQQQQPRLoad)
  .value("MQQQQPRStore", LIEF::assembly::arm::OPCODE::MQQQQPRStore)
  .value("MULv5", LIEF::assembly::arm::OPCODE::MULv5)
  .value("MVE_MEMCPYLOOPINST", LIEF::assembly::arm::OPCODE::MVE_MEMCPYLOOPINST)
  .value("MVE_MEMSETLOOPINST", LIEF::assembly::arm::OPCODE::MVE_MEMSETLOOPINST)
  .value("MVNCCi", LIEF::assembly::arm::OPCODE::MVNCCi)
  .value("PICADD", LIEF::assembly::arm::OPCODE::PICADD)
  .value("PICLDR", LIEF::assembly::arm::OPCODE::PICLDR)
  .value("PICLDRB", LIEF::assembly::arm::OPCODE::PICLDRB)
  .value("PICLDRH", LIEF::assembly::arm::OPCODE::PICLDRH)
  .value("PICLDRSB", LIEF::assembly::arm::OPCODE::PICLDRSB)
  .value("PICLDRSH", LIEF::assembly::arm::OPCODE::PICLDRSH)
  .value("PICSTR", LIEF::assembly::arm::OPCODE::PICSTR)
  .value("PICSTRB", LIEF::assembly::arm::OPCODE::PICSTRB)
  .value("PICSTRH", LIEF::assembly::arm::OPCODE::PICSTRH)
  .value("RORi", LIEF::assembly::arm::OPCODE::RORi)
  .value("RORr", LIEF::assembly::arm::OPCODE::RORr)
  .value("RRX", LIEF::assembly::arm::OPCODE::RRX)
  .value("RRXi", LIEF::assembly::arm::OPCODE::RRXi)
  .value("RSBSri", LIEF::assembly::arm::OPCODE::RSBSri)
  .value("RSBSrsi", LIEF::assembly::arm::OPCODE::RSBSrsi)
  .value("RSBSrsr", LIEF::assembly::arm::OPCODE::RSBSrsr)
  .value("SEH_EpilogEnd", LIEF::assembly::arm::OPCODE::SEH_EpilogEnd)
  .value("SEH_EpilogStart", LIEF::assembly::arm::OPCODE::SEH_EpilogStart)
  .value("SEH_Nop", LIEF::assembly::arm::OPCODE::SEH_Nop)
  .value("SEH_Nop_Ret", LIEF::assembly::arm::OPCODE::SEH_Nop_Ret)
  .value("SEH_PrologEnd", LIEF::assembly::arm::OPCODE::SEH_PrologEnd)
  .value("SEH_SaveFRegs", LIEF::assembly::arm::OPCODE::SEH_SaveFRegs)
  .value("SEH_SaveLR", LIEF::assembly::arm::OPCODE::SEH_SaveLR)
  .value("SEH_SaveRegs", LIEF::assembly::arm::OPCODE::SEH_SaveRegs)
  .value("SEH_SaveRegs_Ret", LIEF::assembly::arm::OPCODE::SEH_SaveRegs_Ret)
  .value("SEH_SaveSP", LIEF::assembly::arm::OPCODE::SEH_SaveSP)
  .value("SEH_StackAlloc", LIEF::assembly::arm::OPCODE::SEH_StackAlloc)
  .value("SMLALv5", LIEF::assembly::arm::OPCODE::SMLALv5)
  .value("SMULLv5", LIEF::assembly::arm::OPCODE::SMULLv5)
  .value("SPACE", LIEF::assembly::arm::OPCODE::SPACE)
  .value("STOREDUAL", LIEF::assembly::arm::OPCODE::STOREDUAL)
  .value("STRBT_POST", LIEF::assembly::arm::OPCODE::STRBT_POST)
  .value("STRBi_preidx", LIEF::assembly::arm::OPCODE::STRBi_preidx)
  .value("STRBr_preidx", LIEF::assembly::arm::OPCODE::STRBr_preidx)
  .value("STRH_preidx", LIEF::assembly::arm::OPCODE::STRH_preidx)
  .value("STRT_POST", LIEF::assembly::arm::OPCODE::STRT_POST)
  .value("STRi_preidx", LIEF::assembly::arm::OPCODE::STRi_preidx)
  .value("STRr_preidx", LIEF::assembly::arm::OPCODE::STRr_preidx)
  .value("SUBS_PC_LR", LIEF::assembly::arm::OPCODE::SUBS_PC_LR)
  .value("SUBSri", LIEF::assembly::arm::OPCODE::SUBSri)
  .value("SUBSrr", LIEF::assembly::arm::OPCODE::SUBSrr)
  .value("SUBSrsi", LIEF::assembly::arm::OPCODE::SUBSrsi)
  .value("SUBSrsr", LIEF::assembly::arm::OPCODE::SUBSrsr)
  .value("SpeculationBarrierISBDSBEndBB", LIEF::assembly::arm::OPCODE::SpeculationBarrierISBDSBEndBB)
  .value("SpeculationBarrierSBEndBB", LIEF::assembly::arm::OPCODE::SpeculationBarrierSBEndBB)
  .value("TAILJMPd", LIEF::assembly::arm::OPCODE::TAILJMPd)
  .value("TAILJMPr", LIEF::assembly::arm::OPCODE::TAILJMPr)
  .value("TAILJMPr4", LIEF::assembly::arm::OPCODE::TAILJMPr4)
  .value("TCRETURNdi", LIEF::assembly::arm::OPCODE::TCRETURNdi)
  .value("TCRETURNri", LIEF::assembly::arm::OPCODE::TCRETURNri)
  .value("TCRETURNrinotr12", LIEF::assembly::arm::OPCODE::TCRETURNrinotr12)
  .value("TPsoft", LIEF::assembly::arm::OPCODE::TPsoft)
  .value("UMLALv5", LIEF::assembly::arm::OPCODE::UMLALv5)
  .value("UMULLv5", LIEF::assembly::arm::OPCODE::UMULLv5)
  .value("VLD1LNdAsm_16", LIEF::assembly::arm::OPCODE::VLD1LNdAsm_16)
  .value("VLD1LNdAsm_32", LIEF::assembly::arm::OPCODE::VLD1LNdAsm_32)
  .value("VLD1LNdAsm_8", LIEF::assembly::arm::OPCODE::VLD1LNdAsm_8)
  .value("VLD1LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD1LNdWB_fixed_Asm_16)
  .value("VLD1LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD1LNdWB_fixed_Asm_32)
  .value("VLD1LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD1LNdWB_fixed_Asm_8)
  .value("VLD1LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD1LNdWB_register_Asm_16)
  .value("VLD1LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD1LNdWB_register_Asm_32)
  .value("VLD1LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD1LNdWB_register_Asm_8)
  .value("VLD2LNdAsm_16", LIEF::assembly::arm::OPCODE::VLD2LNdAsm_16)
  .value("VLD2LNdAsm_32", LIEF::assembly::arm::OPCODE::VLD2LNdAsm_32)
  .value("VLD2LNdAsm_8", LIEF::assembly::arm::OPCODE::VLD2LNdAsm_8)
  .value("VLD2LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD2LNdWB_fixed_Asm_16)
  .value("VLD2LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD2LNdWB_fixed_Asm_32)
  .value("VLD2LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD2LNdWB_fixed_Asm_8)
  .value("VLD2LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD2LNdWB_register_Asm_16)
  .value("VLD2LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD2LNdWB_register_Asm_32)
  .value("VLD2LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD2LNdWB_register_Asm_8)
  .value("VLD2LNqAsm_16", LIEF::assembly::arm::OPCODE::VLD2LNqAsm_16)
  .value("VLD2LNqAsm_32", LIEF::assembly::arm::OPCODE::VLD2LNqAsm_32)
  .value("VLD2LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD2LNqWB_fixed_Asm_16)
  .value("VLD2LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD2LNqWB_fixed_Asm_32)
  .value("VLD2LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD2LNqWB_register_Asm_16)
  .value("VLD2LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD2LNqWB_register_Asm_32)
  .value("VLD3DUPdAsm_16", LIEF::assembly::arm::OPCODE::VLD3DUPdAsm_16)
  .value("VLD3DUPdAsm_32", LIEF::assembly::arm::OPCODE::VLD3DUPdAsm_32)
  .value("VLD3DUPdAsm_8", LIEF::assembly::arm::OPCODE::VLD3DUPdAsm_8)
  .value("VLD3DUPdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_fixed_Asm_16)
  .value("VLD3DUPdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_fixed_Asm_32)
  .value("VLD3DUPdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_fixed_Asm_8)
  .value("VLD3DUPdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_register_Asm_16)
  .value("VLD3DUPdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_register_Asm_32)
  .value("VLD3DUPdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD3DUPdWB_register_Asm_8)
  .value("VLD3DUPqAsm_16", LIEF::assembly::arm::OPCODE::VLD3DUPqAsm_16)
  .value("VLD3DUPqAsm_32", LIEF::assembly::arm::OPCODE::VLD3DUPqAsm_32)
  .value("VLD3DUPqAsm_8", LIEF::assembly::arm::OPCODE::VLD3DUPqAsm_8)
  .value("VLD3DUPqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_fixed_Asm_16)
  .value("VLD3DUPqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_fixed_Asm_32)
  .value("VLD3DUPqWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_fixed_Asm_8)
  .value("VLD3DUPqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_register_Asm_16)
  .value("VLD3DUPqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_register_Asm_32)
  .value("VLD3DUPqWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD3DUPqWB_register_Asm_8)
  .value("VLD3LNdAsm_16", LIEF::assembly::arm::OPCODE::VLD3LNdAsm_16)
  .value("VLD3LNdAsm_32", LIEF::assembly::arm::OPCODE::VLD3LNdAsm_32)
  .value("VLD3LNdAsm_8", LIEF::assembly::arm::OPCODE::VLD3LNdAsm_8)
  .value("VLD3LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3LNdWB_fixed_Asm_16)
  .value("VLD3LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3LNdWB_fixed_Asm_32)
  .value("VLD3LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD3LNdWB_fixed_Asm_8)
  .value("VLD3LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3LNdWB_register_Asm_16)
  .value("VLD3LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3LNdWB_register_Asm_32)
  .value("VLD3LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD3LNdWB_register_Asm_8)
  .value("VLD3LNqAsm_16", LIEF::assembly::arm::OPCODE::VLD3LNqAsm_16)
  .value("VLD3LNqAsm_32", LIEF::assembly::arm::OPCODE::VLD3LNqAsm_32)
  .value("VLD3LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3LNqWB_fixed_Asm_16)
  .value("VLD3LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3LNqWB_fixed_Asm_32)
  .value("VLD3LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3LNqWB_register_Asm_16)
  .value("VLD3LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3LNqWB_register_Asm_32)
  .value("VLD3dAsm_16", LIEF::assembly::arm::OPCODE::VLD3dAsm_16)
  .value("VLD3dAsm_32", LIEF::assembly::arm::OPCODE::VLD3dAsm_32)
  .value("VLD3dAsm_8", LIEF::assembly::arm::OPCODE::VLD3dAsm_8)
  .value("VLD3dWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3dWB_fixed_Asm_16)
  .value("VLD3dWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3dWB_fixed_Asm_32)
  .value("VLD3dWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD3dWB_fixed_Asm_8)
  .value("VLD3dWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3dWB_register_Asm_16)
  .value("VLD3dWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3dWB_register_Asm_32)
  .value("VLD3dWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD3dWB_register_Asm_8)
  .value("VLD3qAsm_16", LIEF::assembly::arm::OPCODE::VLD3qAsm_16)
  .value("VLD3qAsm_32", LIEF::assembly::arm::OPCODE::VLD3qAsm_32)
  .value("VLD3qAsm_8", LIEF::assembly::arm::OPCODE::VLD3qAsm_8)
  .value("VLD3qWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD3qWB_fixed_Asm_16)
  .value("VLD3qWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD3qWB_fixed_Asm_32)
  .value("VLD3qWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD3qWB_fixed_Asm_8)
  .value("VLD3qWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD3qWB_register_Asm_16)
  .value("VLD3qWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD3qWB_register_Asm_32)
  .value("VLD3qWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD3qWB_register_Asm_8)
  .value("VLD4DUPdAsm_16", LIEF::assembly::arm::OPCODE::VLD4DUPdAsm_16)
  .value("VLD4DUPdAsm_32", LIEF::assembly::arm::OPCODE::VLD4DUPdAsm_32)
  .value("VLD4DUPdAsm_8", LIEF::assembly::arm::OPCODE::VLD4DUPdAsm_8)
  .value("VLD4DUPdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_fixed_Asm_16)
  .value("VLD4DUPdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_fixed_Asm_32)
  .value("VLD4DUPdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_fixed_Asm_8)
  .value("VLD4DUPdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_register_Asm_16)
  .value("VLD4DUPdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_register_Asm_32)
  .value("VLD4DUPdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD4DUPdWB_register_Asm_8)
  .value("VLD4DUPqAsm_16", LIEF::assembly::arm::OPCODE::VLD4DUPqAsm_16)
  .value("VLD4DUPqAsm_32", LIEF::assembly::arm::OPCODE::VLD4DUPqAsm_32)
  .value("VLD4DUPqAsm_8", LIEF::assembly::arm::OPCODE::VLD4DUPqAsm_8)
  .value("VLD4DUPqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_fixed_Asm_16)
  .value("VLD4DUPqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_fixed_Asm_32)
  .value("VLD4DUPqWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_fixed_Asm_8)
  .value("VLD4DUPqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_register_Asm_16)
  .value("VLD4DUPqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_register_Asm_32)
  .value("VLD4DUPqWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD4DUPqWB_register_Asm_8)
  .value("VLD4LNdAsm_16", LIEF::assembly::arm::OPCODE::VLD4LNdAsm_16)
  .value("VLD4LNdAsm_32", LIEF::assembly::arm::OPCODE::VLD4LNdAsm_32)
  .value("VLD4LNdAsm_8", LIEF::assembly::arm::OPCODE::VLD4LNdAsm_8)
  .value("VLD4LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4LNdWB_fixed_Asm_16)
  .value("VLD4LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4LNdWB_fixed_Asm_32)
  .value("VLD4LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD4LNdWB_fixed_Asm_8)
  .value("VLD4LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4LNdWB_register_Asm_16)
  .value("VLD4LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4LNdWB_register_Asm_32)
  .value("VLD4LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD4LNdWB_register_Asm_8)
  .value("VLD4LNqAsm_16", LIEF::assembly::arm::OPCODE::VLD4LNqAsm_16)
  .value("VLD4LNqAsm_32", LIEF::assembly::arm::OPCODE::VLD4LNqAsm_32)
  .value("VLD4LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4LNqWB_fixed_Asm_16)
  .value("VLD4LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4LNqWB_fixed_Asm_32)
  .value("VLD4LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4LNqWB_register_Asm_16)
  .value("VLD4LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4LNqWB_register_Asm_32)
  .value("VLD4dAsm_16", LIEF::assembly::arm::OPCODE::VLD4dAsm_16)
  .value("VLD4dAsm_32", LIEF::assembly::arm::OPCODE::VLD4dAsm_32)
  .value("VLD4dAsm_8", LIEF::assembly::arm::OPCODE::VLD4dAsm_8)
  .value("VLD4dWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4dWB_fixed_Asm_16)
  .value("VLD4dWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4dWB_fixed_Asm_32)
  .value("VLD4dWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD4dWB_fixed_Asm_8)
  .value("VLD4dWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4dWB_register_Asm_16)
  .value("VLD4dWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4dWB_register_Asm_32)
  .value("VLD4dWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD4dWB_register_Asm_8)
  .value("VLD4qAsm_16", LIEF::assembly::arm::OPCODE::VLD4qAsm_16)
  .value("VLD4qAsm_32", LIEF::assembly::arm::OPCODE::VLD4qAsm_32)
  .value("VLD4qAsm_8", LIEF::assembly::arm::OPCODE::VLD4qAsm_8)
  .value("VLD4qWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VLD4qWB_fixed_Asm_16)
  .value("VLD4qWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VLD4qWB_fixed_Asm_32)
  .value("VLD4qWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VLD4qWB_fixed_Asm_8)
  .value("VLD4qWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VLD4qWB_register_Asm_16)
  .value("VLD4qWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VLD4qWB_register_Asm_32)
  .value("VLD4qWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VLD4qWB_register_Asm_8)
  .value("VMOVD0", LIEF::assembly::arm::OPCODE::VMOVD0)
  .value("VMOVDcc", LIEF::assembly::arm::OPCODE::VMOVDcc)
  .value("VMOVHcc", LIEF::assembly::arm::OPCODE::VMOVHcc)
  .value("VMOVQ0", LIEF::assembly::arm::OPCODE::VMOVQ0)
  .value("VMOVScc", LIEF::assembly::arm::OPCODE::VMOVScc)
  .value("VST1LNdAsm_16", LIEF::assembly::arm::OPCODE::VST1LNdAsm_16)
  .value("VST1LNdAsm_32", LIEF::assembly::arm::OPCODE::VST1LNdAsm_32)
  .value("VST1LNdAsm_8", LIEF::assembly::arm::OPCODE::VST1LNdAsm_8)
  .value("VST1LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST1LNdWB_fixed_Asm_16)
  .value("VST1LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST1LNdWB_fixed_Asm_32)
  .value("VST1LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST1LNdWB_fixed_Asm_8)
  .value("VST1LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST1LNdWB_register_Asm_16)
  .value("VST1LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST1LNdWB_register_Asm_32)
  .value("VST1LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST1LNdWB_register_Asm_8)
  .value("VST2LNdAsm_16", LIEF::assembly::arm::OPCODE::VST2LNdAsm_16)
  .value("VST2LNdAsm_32", LIEF::assembly::arm::OPCODE::VST2LNdAsm_32)
  .value("VST2LNdAsm_8", LIEF::assembly::arm::OPCODE::VST2LNdAsm_8)
  .value("VST2LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST2LNdWB_fixed_Asm_16)
  .value("VST2LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST2LNdWB_fixed_Asm_32)
  .value("VST2LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST2LNdWB_fixed_Asm_8)
  .value("VST2LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST2LNdWB_register_Asm_16)
  .value("VST2LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST2LNdWB_register_Asm_32)
  .value("VST2LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST2LNdWB_register_Asm_8)
  .value("VST2LNqAsm_16", LIEF::assembly::arm::OPCODE::VST2LNqAsm_16)
  .value("VST2LNqAsm_32", LIEF::assembly::arm::OPCODE::VST2LNqAsm_32)
  .value("VST2LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST2LNqWB_fixed_Asm_16)
  .value("VST2LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST2LNqWB_fixed_Asm_32)
  .value("VST2LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST2LNqWB_register_Asm_16)
  .value("VST2LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST2LNqWB_register_Asm_32)
  .value("VST3LNdAsm_16", LIEF::assembly::arm::OPCODE::VST3LNdAsm_16)
  .value("VST3LNdAsm_32", LIEF::assembly::arm::OPCODE::VST3LNdAsm_32)
  .value("VST3LNdAsm_8", LIEF::assembly::arm::OPCODE::VST3LNdAsm_8)
  .value("VST3LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST3LNdWB_fixed_Asm_16)
  .value("VST3LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST3LNdWB_fixed_Asm_32);
  opcodes.value("VST3LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST3LNdWB_fixed_Asm_8)
  .value("VST3LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST3LNdWB_register_Asm_16)
  .value("VST3LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST3LNdWB_register_Asm_32)
  .value("VST3LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST3LNdWB_register_Asm_8)
  .value("VST3LNqAsm_16", LIEF::assembly::arm::OPCODE::VST3LNqAsm_16)
  .value("VST3LNqAsm_32", LIEF::assembly::arm::OPCODE::VST3LNqAsm_32)
  .value("VST3LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST3LNqWB_fixed_Asm_16)
  .value("VST3LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST3LNqWB_fixed_Asm_32)
  .value("VST3LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST3LNqWB_register_Asm_16)
  .value("VST3LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST3LNqWB_register_Asm_32)
  .value("VST3dAsm_16", LIEF::assembly::arm::OPCODE::VST3dAsm_16)
  .value("VST3dAsm_32", LIEF::assembly::arm::OPCODE::VST3dAsm_32)
  .value("VST3dAsm_8", LIEF::assembly::arm::OPCODE::VST3dAsm_8)
  .value("VST3dWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST3dWB_fixed_Asm_16)
  .value("VST3dWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST3dWB_fixed_Asm_32)
  .value("VST3dWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST3dWB_fixed_Asm_8)
  .value("VST3dWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST3dWB_register_Asm_16)
  .value("VST3dWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST3dWB_register_Asm_32)
  .value("VST3dWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST3dWB_register_Asm_8)
  .value("VST3qAsm_16", LIEF::assembly::arm::OPCODE::VST3qAsm_16)
  .value("VST3qAsm_32", LIEF::assembly::arm::OPCODE::VST3qAsm_32)
  .value("VST3qAsm_8", LIEF::assembly::arm::OPCODE::VST3qAsm_8)
  .value("VST3qWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST3qWB_fixed_Asm_16)
  .value("VST3qWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST3qWB_fixed_Asm_32)
  .value("VST3qWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST3qWB_fixed_Asm_8)
  .value("VST3qWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST3qWB_register_Asm_16)
  .value("VST3qWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST3qWB_register_Asm_32)
  .value("VST3qWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST3qWB_register_Asm_8)
  .value("VST4LNdAsm_16", LIEF::assembly::arm::OPCODE::VST4LNdAsm_16)
  .value("VST4LNdAsm_32", LIEF::assembly::arm::OPCODE::VST4LNdAsm_32)
  .value("VST4LNdAsm_8", LIEF::assembly::arm::OPCODE::VST4LNdAsm_8)
  .value("VST4LNdWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST4LNdWB_fixed_Asm_16)
  .value("VST4LNdWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST4LNdWB_fixed_Asm_32)
  .value("VST4LNdWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST4LNdWB_fixed_Asm_8)
  .value("VST4LNdWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST4LNdWB_register_Asm_16)
  .value("VST4LNdWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST4LNdWB_register_Asm_32)
  .value("VST4LNdWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST4LNdWB_register_Asm_8)
  .value("VST4LNqAsm_16", LIEF::assembly::arm::OPCODE::VST4LNqAsm_16)
  .value("VST4LNqAsm_32", LIEF::assembly::arm::OPCODE::VST4LNqAsm_32)
  .value("VST4LNqWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST4LNqWB_fixed_Asm_16)
  .value("VST4LNqWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST4LNqWB_fixed_Asm_32)
  .value("VST4LNqWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST4LNqWB_register_Asm_16)
  .value("VST4LNqWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST4LNqWB_register_Asm_32)
  .value("VST4dAsm_16", LIEF::assembly::arm::OPCODE::VST4dAsm_16)
  .value("VST4dAsm_32", LIEF::assembly::arm::OPCODE::VST4dAsm_32)
  .value("VST4dAsm_8", LIEF::assembly::arm::OPCODE::VST4dAsm_8)
  .value("VST4dWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST4dWB_fixed_Asm_16)
  .value("VST4dWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST4dWB_fixed_Asm_32)
  .value("VST4dWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST4dWB_fixed_Asm_8)
  .value("VST4dWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST4dWB_register_Asm_16)
  .value("VST4dWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST4dWB_register_Asm_32)
  .value("VST4dWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST4dWB_register_Asm_8)
  .value("VST4qAsm_16", LIEF::assembly::arm::OPCODE::VST4qAsm_16)
  .value("VST4qAsm_32", LIEF::assembly::arm::OPCODE::VST4qAsm_32)
  .value("VST4qAsm_8", LIEF::assembly::arm::OPCODE::VST4qAsm_8)
  .value("VST4qWB_fixed_Asm_16", LIEF::assembly::arm::OPCODE::VST4qWB_fixed_Asm_16)
  .value("VST4qWB_fixed_Asm_32", LIEF::assembly::arm::OPCODE::VST4qWB_fixed_Asm_32)
  .value("VST4qWB_fixed_Asm_8", LIEF::assembly::arm::OPCODE::VST4qWB_fixed_Asm_8)
  .value("VST4qWB_register_Asm_16", LIEF::assembly::arm::OPCODE::VST4qWB_register_Asm_16)
  .value("VST4qWB_register_Asm_32", LIEF::assembly::arm::OPCODE::VST4qWB_register_Asm_32)
  .value("VST4qWB_register_Asm_8", LIEF::assembly::arm::OPCODE::VST4qWB_register_Asm_8)
  .value("WIN__CHKSTK", LIEF::assembly::arm::OPCODE::WIN__CHKSTK)
  .value("WIN__DBZCHK", LIEF::assembly::arm::OPCODE::WIN__DBZCHK)
  .value("t2ABS", LIEF::assembly::arm::OPCODE::t2ABS)
  .value("t2ADDSri", LIEF::assembly::arm::OPCODE::t2ADDSri)
  .value("t2ADDSrr", LIEF::assembly::arm::OPCODE::t2ADDSrr)
  .value("t2ADDSrs", LIEF::assembly::arm::OPCODE::t2ADDSrs)
  .value("t2BF_LabelPseudo", LIEF::assembly::arm::OPCODE::t2BF_LabelPseudo)
  .value("t2BR_JT", LIEF::assembly::arm::OPCODE::t2BR_JT)
  .value("t2CALL_BTI", LIEF::assembly::arm::OPCODE::t2CALL_BTI)
  .value("t2DoLoopStart", LIEF::assembly::arm::OPCODE::t2DoLoopStart)
  .value("t2DoLoopStartTP", LIEF::assembly::arm::OPCODE::t2DoLoopStartTP)
  .value("t2LDMIA_RET", LIEF::assembly::arm::OPCODE::t2LDMIA_RET)
  .value("t2LDRB_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2LDRB_OFFSET_imm)
  .value("t2LDRB_POST_imm", LIEF::assembly::arm::OPCODE::t2LDRB_POST_imm)
  .value("t2LDRB_PRE_imm", LIEF::assembly::arm::OPCODE::t2LDRB_PRE_imm)
  .value("t2LDRBpcrel", LIEF::assembly::arm::OPCODE::t2LDRBpcrel)
  .value("t2LDRConstPool", LIEF::assembly::arm::OPCODE::t2LDRConstPool)
  .value("t2LDRH_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2LDRH_OFFSET_imm)
  .value("t2LDRH_POST_imm", LIEF::assembly::arm::OPCODE::t2LDRH_POST_imm)
  .value("t2LDRH_PRE_imm", LIEF::assembly::arm::OPCODE::t2LDRH_PRE_imm)
  .value("t2LDRHpcrel", LIEF::assembly::arm::OPCODE::t2LDRHpcrel)
  .value("t2LDRLIT_ga_pcrel", LIEF::assembly::arm::OPCODE::t2LDRLIT_ga_pcrel)
  .value("t2LDRSB_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2LDRSB_OFFSET_imm)
  .value("t2LDRSB_POST_imm", LIEF::assembly::arm::OPCODE::t2LDRSB_POST_imm)
  .value("t2LDRSB_PRE_imm", LIEF::assembly::arm::OPCODE::t2LDRSB_PRE_imm)
  .value("t2LDRSBpcrel", LIEF::assembly::arm::OPCODE::t2LDRSBpcrel)
  .value("t2LDRSH_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2LDRSH_OFFSET_imm)
  .value("t2LDRSH_POST_imm", LIEF::assembly::arm::OPCODE::t2LDRSH_POST_imm)
  .value("t2LDRSH_PRE_imm", LIEF::assembly::arm::OPCODE::t2LDRSH_PRE_imm)
  .value("t2LDRSHpcrel", LIEF::assembly::arm::OPCODE::t2LDRSHpcrel)
  .value("t2LDR_POST_imm", LIEF::assembly::arm::OPCODE::t2LDR_POST_imm)
  .value("t2LDR_PRE_imm", LIEF::assembly::arm::OPCODE::t2LDR_PRE_imm)
  .value("t2LDRpci_pic", LIEF::assembly::arm::OPCODE::t2LDRpci_pic)
  .value("t2LDRpcrel", LIEF::assembly::arm::OPCODE::t2LDRpcrel)
  .value("t2LEApcrel", LIEF::assembly::arm::OPCODE::t2LEApcrel)
  .value("t2LEApcrelJT", LIEF::assembly::arm::OPCODE::t2LEApcrelJT)
  .value("t2LoopDec", LIEF::assembly::arm::OPCODE::t2LoopDec)
  .value("t2LoopEnd", LIEF::assembly::arm::OPCODE::t2LoopEnd)
  .value("t2LoopEndDec", LIEF::assembly::arm::OPCODE::t2LoopEndDec)
  .value("t2MOVCCasr", LIEF::assembly::arm::OPCODE::t2MOVCCasr)
  .value("t2MOVCCi", LIEF::assembly::arm::OPCODE::t2MOVCCi)
  .value("t2MOVCCi16", LIEF::assembly::arm::OPCODE::t2MOVCCi16)
  .value("t2MOVCCi32imm", LIEF::assembly::arm::OPCODE::t2MOVCCi32imm)
  .value("t2MOVCClsl", LIEF::assembly::arm::OPCODE::t2MOVCClsl)
  .value("t2MOVCClsr", LIEF::assembly::arm::OPCODE::t2MOVCClsr)
  .value("t2MOVCCr", LIEF::assembly::arm::OPCODE::t2MOVCCr)
  .value("t2MOVCCror", LIEF::assembly::arm::OPCODE::t2MOVCCror)
  .value("t2MOVSsi", LIEF::assembly::arm::OPCODE::t2MOVSsi)
  .value("t2MOVSsr", LIEF::assembly::arm::OPCODE::t2MOVSsr)
  .value("t2MOVTi16_ga_pcrel", LIEF::assembly::arm::OPCODE::t2MOVTi16_ga_pcrel)
  .value("t2MOV_ga_pcrel", LIEF::assembly::arm::OPCODE::t2MOV_ga_pcrel)
  .value("t2MOVi16_ga_pcrel", LIEF::assembly::arm::OPCODE::t2MOVi16_ga_pcrel)
  .value("t2MOVi32imm", LIEF::assembly::arm::OPCODE::t2MOVi32imm)
  .value("t2MOVsi", LIEF::assembly::arm::OPCODE::t2MOVsi)
  .value("t2MOVsr", LIEF::assembly::arm::OPCODE::t2MOVsr)
  .value("t2MVNCCi", LIEF::assembly::arm::OPCODE::t2MVNCCi)
  .value("t2RSBSri", LIEF::assembly::arm::OPCODE::t2RSBSri)
  .value("t2RSBSrs", LIEF::assembly::arm::OPCODE::t2RSBSrs)
  .value("t2STRB_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2STRB_OFFSET_imm)
  .value("t2STRB_POST_imm", LIEF::assembly::arm::OPCODE::t2STRB_POST_imm)
  .value("t2STRB_PRE_imm", LIEF::assembly::arm::OPCODE::t2STRB_PRE_imm)
  .value("t2STRB_preidx", LIEF::assembly::arm::OPCODE::t2STRB_preidx)
  .value("t2STRH_OFFSET_imm", LIEF::assembly::arm::OPCODE::t2STRH_OFFSET_imm)
  .value("t2STRH_POST_imm", LIEF::assembly::arm::OPCODE::t2STRH_POST_imm)
  .value("t2STRH_PRE_imm", LIEF::assembly::arm::OPCODE::t2STRH_PRE_imm)
  .value("t2STRH_preidx", LIEF::assembly::arm::OPCODE::t2STRH_preidx)
  .value("t2STR_POST_imm", LIEF::assembly::arm::OPCODE::t2STR_POST_imm)
  .value("t2STR_PRE_imm", LIEF::assembly::arm::OPCODE::t2STR_PRE_imm)
  .value("t2STR_preidx", LIEF::assembly::arm::OPCODE::t2STR_preidx)
  .value("t2SUBSri", LIEF::assembly::arm::OPCODE::t2SUBSri)
  .value("t2SUBSrr", LIEF::assembly::arm::OPCODE::t2SUBSrr)
  .value("t2SUBSrs", LIEF::assembly::arm::OPCODE::t2SUBSrs)
  .value("t2SpeculationBarrierISBDSBEndBB", LIEF::assembly::arm::OPCODE::t2SpeculationBarrierISBDSBEndBB)
  .value("t2SpeculationBarrierSBEndBB", LIEF::assembly::arm::OPCODE::t2SpeculationBarrierSBEndBB)
  .value("t2TBB_JT", LIEF::assembly::arm::OPCODE::t2TBB_JT)
  .value("t2TBH_JT", LIEF::assembly::arm::OPCODE::t2TBH_JT)
  .value("t2WhileLoopSetup", LIEF::assembly::arm::OPCODE::t2WhileLoopSetup)
  .value("t2WhileLoopStart", LIEF::assembly::arm::OPCODE::t2WhileLoopStart)
  .value("t2WhileLoopStartLR", LIEF::assembly::arm::OPCODE::t2WhileLoopStartLR)
  .value("t2WhileLoopStartTP", LIEF::assembly::arm::OPCODE::t2WhileLoopStartTP)
  .value("tADCS", LIEF::assembly::arm::OPCODE::tADCS)
  .value("tADDSi3", LIEF::assembly::arm::OPCODE::tADDSi3)
  .value("tADDSi8", LIEF::assembly::arm::OPCODE::tADDSi8)
  .value("tADDSrr", LIEF::assembly::arm::OPCODE::tADDSrr)
  .value("tADDframe", LIEF::assembly::arm::OPCODE::tADDframe)
  .value("tADJCALLSTACKDOWN", LIEF::assembly::arm::OPCODE::tADJCALLSTACKDOWN)
  .value("tADJCALLSTACKUP", LIEF::assembly::arm::OPCODE::tADJCALLSTACKUP)
  .value("tBLXNS_CALL", LIEF::assembly::arm::OPCODE::tBLXNS_CALL)
  .value("tBLXr_noip", LIEF::assembly::arm::OPCODE::tBLXr_noip)
  .value("tBL_PUSHLR", LIEF::assembly::arm::OPCODE::tBL_PUSHLR)
  .value("tBRIND", LIEF::assembly::arm::OPCODE::tBRIND)
  .value("tBR_JTr", LIEF::assembly::arm::OPCODE::tBR_JTr)
  .value("tBXNS_RET", LIEF::assembly::arm::OPCODE::tBXNS_RET)
  .value("tBX_CALL", LIEF::assembly::arm::OPCODE::tBX_CALL)
  .value("tBX_RET", LIEF::assembly::arm::OPCODE::tBX_RET)
  .value("tBX_RET_vararg", LIEF::assembly::arm::OPCODE::tBX_RET_vararg)
  .value("tBfar", LIEF::assembly::arm::OPCODE::tBfar)
  .value("tCMP_SWAP_16", LIEF::assembly::arm::OPCODE::tCMP_SWAP_16)
  .value("tCMP_SWAP_32", LIEF::assembly::arm::OPCODE::tCMP_SWAP_32)
  .value("tCMP_SWAP_8", LIEF::assembly::arm::OPCODE::tCMP_SWAP_8)
  .value("tLDMIA_UPD", LIEF::assembly::arm::OPCODE::tLDMIA_UPD)
  .value("tLDRConstPool", LIEF::assembly::arm::OPCODE::tLDRConstPool)
  .value("tLDRLIT_ga_abs", LIEF::assembly::arm::OPCODE::tLDRLIT_ga_abs)
  .value("tLDRLIT_ga_pcrel", LIEF::assembly::arm::OPCODE::tLDRLIT_ga_pcrel)
  .value("tLDR_postidx", LIEF::assembly::arm::OPCODE::tLDR_postidx)
  .value("tLDRpci_pic", LIEF::assembly::arm::OPCODE::tLDRpci_pic)
  .value("tLEApcrel", LIEF::assembly::arm::OPCODE::tLEApcrel)
  .value("tLEApcrelJT", LIEF::assembly::arm::OPCODE::tLEApcrelJT)
  .value("tLSLSri", LIEF::assembly::arm::OPCODE::tLSLSri)
  .value("tMOVCCr_pseudo", LIEF::assembly::arm::OPCODE::tMOVCCr_pseudo)
  .value("tMOVi32imm", LIEF::assembly::arm::OPCODE::tMOVi32imm)
  .value("tPOP_RET", LIEF::assembly::arm::OPCODE::tPOP_RET)
  .value("tRSBS", LIEF::assembly::arm::OPCODE::tRSBS)
  .value("tSBCS", LIEF::assembly::arm::OPCODE::tSBCS)
  .value("tSUBSi3", LIEF::assembly::arm::OPCODE::tSUBSi3)
  .value("tSUBSi8", LIEF::assembly::arm::OPCODE::tSUBSi8)
  .value("tSUBSrr", LIEF::assembly::arm::OPCODE::tSUBSrr)
  .value("tTAILJMPd", LIEF::assembly::arm::OPCODE::tTAILJMPd)
  .value("tTAILJMPdND", LIEF::assembly::arm::OPCODE::tTAILJMPdND)
  .value("tTAILJMPr", LIEF::assembly::arm::OPCODE::tTAILJMPr)
  .value("tTBB_JT", LIEF::assembly::arm::OPCODE::tTBB_JT)
  .value("tTBH_JT", LIEF::assembly::arm::OPCODE::tTBH_JT)
  .value("tTPsoft", LIEF::assembly::arm::OPCODE::tTPsoft)
  .value("ADCri", LIEF::assembly::arm::OPCODE::ADCri)
  .value("ADCrr", LIEF::assembly::arm::OPCODE::ADCrr)
  .value("ADCrsi", LIEF::assembly::arm::OPCODE::ADCrsi)
  .value("ADCrsr", LIEF::assembly::arm::OPCODE::ADCrsr)
  .value("ADDri", LIEF::assembly::arm::OPCODE::ADDri)
  .value("ADDrr", LIEF::assembly::arm::OPCODE::ADDrr)
  .value("ADDrsi", LIEF::assembly::arm::OPCODE::ADDrsi)
  .value("ADDrsr", LIEF::assembly::arm::OPCODE::ADDrsr)
  .value("ADR", LIEF::assembly::arm::OPCODE::ADR)
  .value("AESD", LIEF::assembly::arm::OPCODE::AESD)
  .value("AESE", LIEF::assembly::arm::OPCODE::AESE)
  .value("AESIMC", LIEF::assembly::arm::OPCODE::AESIMC)
  .value("AESMC", LIEF::assembly::arm::OPCODE::AESMC)
  .value("ANDri", LIEF::assembly::arm::OPCODE::ANDri)
  .value("ANDrr", LIEF::assembly::arm::OPCODE::ANDrr)
  .value("ANDrsi", LIEF::assembly::arm::OPCODE::ANDrsi)
  .value("ANDrsr", LIEF::assembly::arm::OPCODE::ANDrsr)
  .value("BF16VDOTI_VDOTD", LIEF::assembly::arm::OPCODE::BF16VDOTI_VDOTD)
  .value("BF16VDOTI_VDOTQ", LIEF::assembly::arm::OPCODE::BF16VDOTI_VDOTQ)
  .value("BF16VDOTS_VDOTD", LIEF::assembly::arm::OPCODE::BF16VDOTS_VDOTD)
  .value("BF16VDOTS_VDOTQ", LIEF::assembly::arm::OPCODE::BF16VDOTS_VDOTQ)
  .value("BF16_VCVT", LIEF::assembly::arm::OPCODE::BF16_VCVT)
  .value("BF16_VCVTB", LIEF::assembly::arm::OPCODE::BF16_VCVTB)
  .value("BF16_VCVTT", LIEF::assembly::arm::OPCODE::BF16_VCVTT)
  .value("BFC", LIEF::assembly::arm::OPCODE::BFC)
  .value("BFI", LIEF::assembly::arm::OPCODE::BFI)
  .value("BICri", LIEF::assembly::arm::OPCODE::BICri)
  .value("BICrr", LIEF::assembly::arm::OPCODE::BICrr)
  .value("BICrsi", LIEF::assembly::arm::OPCODE::BICrsi)
  .value("BICrsr", LIEF::assembly::arm::OPCODE::BICrsr)
  .value("BKPT", LIEF::assembly::arm::OPCODE::BKPT)
  .value("BL", LIEF::assembly::arm::OPCODE::BL)
  .value("BLX", LIEF::assembly::arm::OPCODE::BLX)
  .value("BLX_pred", LIEF::assembly::arm::OPCODE::BLX_pred)
  .value("BLXi", LIEF::assembly::arm::OPCODE::BLXi)
  .value("BL_pred", LIEF::assembly::arm::OPCODE::BL_pred)
  .value("BX", LIEF::assembly::arm::OPCODE::BX)
  .value("BXJ", LIEF::assembly::arm::OPCODE::BXJ)
  .value("BX_RET", LIEF::assembly::arm::OPCODE::BX_RET)
  .value("BX_pred", LIEF::assembly::arm::OPCODE::BX_pred)
  .value("Bcc", LIEF::assembly::arm::OPCODE::Bcc)
  .value("CDE_CX1", LIEF::assembly::arm::OPCODE::CDE_CX1)
  .value("CDE_CX1A", LIEF::assembly::arm::OPCODE::CDE_CX1A)
  .value("CDE_CX1D", LIEF::assembly::arm::OPCODE::CDE_CX1D)
  .value("CDE_CX1DA", LIEF::assembly::arm::OPCODE::CDE_CX1DA)
  .value("CDE_CX2", LIEF::assembly::arm::OPCODE::CDE_CX2)
  .value("CDE_CX2A", LIEF::assembly::arm::OPCODE::CDE_CX2A)
  .value("CDE_CX2D", LIEF::assembly::arm::OPCODE::CDE_CX2D)
  .value("CDE_CX2DA", LIEF::assembly::arm::OPCODE::CDE_CX2DA)
  .value("CDE_CX3", LIEF::assembly::arm::OPCODE::CDE_CX3)
  .value("CDE_CX3A", LIEF::assembly::arm::OPCODE::CDE_CX3A)
  .value("CDE_CX3D", LIEF::assembly::arm::OPCODE::CDE_CX3D)
  .value("CDE_CX3DA", LIEF::assembly::arm::OPCODE::CDE_CX3DA)
  .value("CDE_VCX1A_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX1A_fpdp)
  .value("CDE_VCX1A_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX1A_fpsp)
  .value("CDE_VCX1A_vec", LIEF::assembly::arm::OPCODE::CDE_VCX1A_vec)
  .value("CDE_VCX1_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX1_fpdp)
  .value("CDE_VCX1_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX1_fpsp)
  .value("CDE_VCX1_vec", LIEF::assembly::arm::OPCODE::CDE_VCX1_vec)
  .value("CDE_VCX2A_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX2A_fpdp)
  .value("CDE_VCX2A_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX2A_fpsp)
  .value("CDE_VCX2A_vec", LIEF::assembly::arm::OPCODE::CDE_VCX2A_vec)
  .value("CDE_VCX2_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX2_fpdp)
  .value("CDE_VCX2_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX2_fpsp)
  .value("CDE_VCX2_vec", LIEF::assembly::arm::OPCODE::CDE_VCX2_vec)
  .value("CDE_VCX3A_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX3A_fpdp)
  .value("CDE_VCX3A_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX3A_fpsp)
  .value("CDE_VCX3A_vec", LIEF::assembly::arm::OPCODE::CDE_VCX3A_vec)
  .value("CDE_VCX3_fpdp", LIEF::assembly::arm::OPCODE::CDE_VCX3_fpdp)
  .value("CDE_VCX3_fpsp", LIEF::assembly::arm::OPCODE::CDE_VCX3_fpsp)
  .value("CDE_VCX3_vec", LIEF::assembly::arm::OPCODE::CDE_VCX3_vec)
  .value("CDP", LIEF::assembly::arm::OPCODE::CDP)
  .value("CDP2", LIEF::assembly::arm::OPCODE::CDP2)
  .value("CLREX", LIEF::assembly::arm::OPCODE::CLREX)
  .value("CLZ", LIEF::assembly::arm::OPCODE::CLZ)
  .value("CMNri", LIEF::assembly::arm::OPCODE::CMNri)
  .value("CMNzrr", LIEF::assembly::arm::OPCODE::CMNzrr)
  .value("CMNzrsi", LIEF::assembly::arm::OPCODE::CMNzrsi)
  .value("CMNzrsr", LIEF::assembly::arm::OPCODE::CMNzrsr)
  .value("CMPri", LIEF::assembly::arm::OPCODE::CMPri)
  .value("CMPrr", LIEF::assembly::arm::OPCODE::CMPrr)
  .value("CMPrsi", LIEF::assembly::arm::OPCODE::CMPrsi)
  .value("CMPrsr", LIEF::assembly::arm::OPCODE::CMPrsr)
  .value("CPS1p", LIEF::assembly::arm::OPCODE::CPS1p)
  .value("CPS2p", LIEF::assembly::arm::OPCODE::CPS2p)
  .value("CPS3p", LIEF::assembly::arm::OPCODE::CPS3p)
  .value("CRC32B", LIEF::assembly::arm::OPCODE::CRC32B)
  .value("CRC32CB", LIEF::assembly::arm::OPCODE::CRC32CB)
  .value("CRC32CH", LIEF::assembly::arm::OPCODE::CRC32CH)
  .value("CRC32CW", LIEF::assembly::arm::OPCODE::CRC32CW)
  .value("CRC32H", LIEF::assembly::arm::OPCODE::CRC32H)
  .value("CRC32W", LIEF::assembly::arm::OPCODE::CRC32W)
  .value("DBG", LIEF::assembly::arm::OPCODE::DBG)
  .value("DMB", LIEF::assembly::arm::OPCODE::DMB)
  .value("DSB", LIEF::assembly::arm::OPCODE::DSB)
  .value("EORri", LIEF::assembly::arm::OPCODE::EORri)
  .value("EORrr", LIEF::assembly::arm::OPCODE::EORrr)
  .value("EORrsi", LIEF::assembly::arm::OPCODE::EORrsi)
  .value("EORrsr", LIEF::assembly::arm::OPCODE::EORrsr)
  .value("ERET", LIEF::assembly::arm::OPCODE::ERET)
  .value("FCONSTD", LIEF::assembly::arm::OPCODE::FCONSTD)
  .value("FCONSTH", LIEF::assembly::arm::OPCODE::FCONSTH)
  .value("FCONSTS", LIEF::assembly::arm::OPCODE::FCONSTS)
  .value("FLDMXDB_UPD", LIEF::assembly::arm::OPCODE::FLDMXDB_UPD)
  .value("FLDMXIA", LIEF::assembly::arm::OPCODE::FLDMXIA)
  .value("FLDMXIA_UPD", LIEF::assembly::arm::OPCODE::FLDMXIA_UPD)
  .value("FMSTAT", LIEF::assembly::arm::OPCODE::FMSTAT)
  .value("FSTMXDB_UPD", LIEF::assembly::arm::OPCODE::FSTMXDB_UPD)
  .value("FSTMXIA", LIEF::assembly::arm::OPCODE::FSTMXIA)
  .value("FSTMXIA_UPD", LIEF::assembly::arm::OPCODE::FSTMXIA_UPD)
  .value("HINT", LIEF::assembly::arm::OPCODE::HINT)
  .value("HLT", LIEF::assembly::arm::OPCODE::HLT)
  .value("HVC", LIEF::assembly::arm::OPCODE::HVC)
  .value("ISB", LIEF::assembly::arm::OPCODE::ISB)
  .value("LDA", LIEF::assembly::arm::OPCODE::LDA)
  .value("LDAB", LIEF::assembly::arm::OPCODE::LDAB);
  opcodes.value("LDAEX", LIEF::assembly::arm::OPCODE::LDAEX)
  .value("LDAEXB", LIEF::assembly::arm::OPCODE::LDAEXB)
  .value("LDAEXD", LIEF::assembly::arm::OPCODE::LDAEXD)
  .value("LDAEXH", LIEF::assembly::arm::OPCODE::LDAEXH)
  .value("LDAH", LIEF::assembly::arm::OPCODE::LDAH)
  .value("LDC2L_OFFSET", LIEF::assembly::arm::OPCODE::LDC2L_OFFSET)
  .value("LDC2L_OPTION", LIEF::assembly::arm::OPCODE::LDC2L_OPTION)
  .value("LDC2L_POST", LIEF::assembly::arm::OPCODE::LDC2L_POST)
  .value("LDC2L_PRE", LIEF::assembly::arm::OPCODE::LDC2L_PRE)
  .value("LDC2_OFFSET", LIEF::assembly::arm::OPCODE::LDC2_OFFSET)
  .value("LDC2_OPTION", LIEF::assembly::arm::OPCODE::LDC2_OPTION)
  .value("LDC2_POST", LIEF::assembly::arm::OPCODE::LDC2_POST)
  .value("LDC2_PRE", LIEF::assembly::arm::OPCODE::LDC2_PRE)
  .value("LDCL_OFFSET", LIEF::assembly::arm::OPCODE::LDCL_OFFSET)
  .value("LDCL_OPTION", LIEF::assembly::arm::OPCODE::LDCL_OPTION)
  .value("LDCL_POST", LIEF::assembly::arm::OPCODE::LDCL_POST)
  .value("LDCL_PRE", LIEF::assembly::arm::OPCODE::LDCL_PRE)
  .value("LDC_OFFSET", LIEF::assembly::arm::OPCODE::LDC_OFFSET)
  .value("LDC_OPTION", LIEF::assembly::arm::OPCODE::LDC_OPTION)
  .value("LDC_POST", LIEF::assembly::arm::OPCODE::LDC_POST)
  .value("LDC_PRE", LIEF::assembly::arm::OPCODE::LDC_PRE)
  .value("LDMDA", LIEF::assembly::arm::OPCODE::LDMDA)
  .value("LDMDA_UPD", LIEF::assembly::arm::OPCODE::LDMDA_UPD)
  .value("LDMDB", LIEF::assembly::arm::OPCODE::LDMDB)
  .value("LDMDB_UPD", LIEF::assembly::arm::OPCODE::LDMDB_UPD)
  .value("LDMIA", LIEF::assembly::arm::OPCODE::LDMIA)
  .value("LDMIA_UPD", LIEF::assembly::arm::OPCODE::LDMIA_UPD)
  .value("LDMIB", LIEF::assembly::arm::OPCODE::LDMIB)
  .value("LDMIB_UPD", LIEF::assembly::arm::OPCODE::LDMIB_UPD)
  .value("LDRBT_POST_IMM", LIEF::assembly::arm::OPCODE::LDRBT_POST_IMM)
  .value("LDRBT_POST_REG", LIEF::assembly::arm::OPCODE::LDRBT_POST_REG)
  .value("LDRB_POST_IMM", LIEF::assembly::arm::OPCODE::LDRB_POST_IMM)
  .value("LDRB_POST_REG", LIEF::assembly::arm::OPCODE::LDRB_POST_REG)
  .value("LDRB_PRE_IMM", LIEF::assembly::arm::OPCODE::LDRB_PRE_IMM)
  .value("LDRB_PRE_REG", LIEF::assembly::arm::OPCODE::LDRB_PRE_REG)
  .value("LDRBi12", LIEF::assembly::arm::OPCODE::LDRBi12)
  .value("LDRBrs", LIEF::assembly::arm::OPCODE::LDRBrs)
  .value("LDRD", LIEF::assembly::arm::OPCODE::LDRD)
  .value("LDRD_POST", LIEF::assembly::arm::OPCODE::LDRD_POST)
  .value("LDRD_PRE", LIEF::assembly::arm::OPCODE::LDRD_PRE)
  .value("LDREX", LIEF::assembly::arm::OPCODE::LDREX)
  .value("LDREXB", LIEF::assembly::arm::OPCODE::LDREXB)
  .value("LDREXD", LIEF::assembly::arm::OPCODE::LDREXD)
  .value("LDREXH", LIEF::assembly::arm::OPCODE::LDREXH)
  .value("LDRH", LIEF::assembly::arm::OPCODE::LDRH)
  .value("LDRHTi", LIEF::assembly::arm::OPCODE::LDRHTi)
  .value("LDRHTr", LIEF::assembly::arm::OPCODE::LDRHTr)
  .value("LDRH_POST", LIEF::assembly::arm::OPCODE::LDRH_POST)
  .value("LDRH_PRE", LIEF::assembly::arm::OPCODE::LDRH_PRE)
  .value("LDRSB", LIEF::assembly::arm::OPCODE::LDRSB)
  .value("LDRSBTi", LIEF::assembly::arm::OPCODE::LDRSBTi)
  .value("LDRSBTr", LIEF::assembly::arm::OPCODE::LDRSBTr)
  .value("LDRSB_POST", LIEF::assembly::arm::OPCODE::LDRSB_POST)
  .value("LDRSB_PRE", LIEF::assembly::arm::OPCODE::LDRSB_PRE)
  .value("LDRSH", LIEF::assembly::arm::OPCODE::LDRSH)
  .value("LDRSHTi", LIEF::assembly::arm::OPCODE::LDRSHTi)
  .value("LDRSHTr", LIEF::assembly::arm::OPCODE::LDRSHTr)
  .value("LDRSH_POST", LIEF::assembly::arm::OPCODE::LDRSH_POST)
  .value("LDRSH_PRE", LIEF::assembly::arm::OPCODE::LDRSH_PRE)
  .value("LDRT_POST_IMM", LIEF::assembly::arm::OPCODE::LDRT_POST_IMM)
  .value("LDRT_POST_REG", LIEF::assembly::arm::OPCODE::LDRT_POST_REG)
  .value("LDR_POST_IMM", LIEF::assembly::arm::OPCODE::LDR_POST_IMM)
  .value("LDR_POST_REG", LIEF::assembly::arm::OPCODE::LDR_POST_REG)
  .value("LDR_PRE_IMM", LIEF::assembly::arm::OPCODE::LDR_PRE_IMM)
  .value("LDR_PRE_REG", LIEF::assembly::arm::OPCODE::LDR_PRE_REG)
  .value("LDRcp", LIEF::assembly::arm::OPCODE::LDRcp)
  .value("LDRi12", LIEF::assembly::arm::OPCODE::LDRi12)
  .value("LDRrs", LIEF::assembly::arm::OPCODE::LDRrs)
  .value("MCR", LIEF::assembly::arm::OPCODE::MCR)
  .value("MCR2", LIEF::assembly::arm::OPCODE::MCR2)
  .value("MCRR", LIEF::assembly::arm::OPCODE::MCRR)
  .value("MCRR2", LIEF::assembly::arm::OPCODE::MCRR2)
  .value("MLA", LIEF::assembly::arm::OPCODE::MLA)
  .value("MLS", LIEF::assembly::arm::OPCODE::MLS)
  .value("MOVPCLR", LIEF::assembly::arm::OPCODE::MOVPCLR)
  .value("MOVTi16", LIEF::assembly::arm::OPCODE::MOVTi16)
  .value("MOVi", LIEF::assembly::arm::OPCODE::MOVi)
  .value("MOVi16", LIEF::assembly::arm::OPCODE::MOVi16)
  .value("MOVr", LIEF::assembly::arm::OPCODE::MOVr)
  .value("MOVr_TC", LIEF::assembly::arm::OPCODE::MOVr_TC)
  .value("MOVsi", LIEF::assembly::arm::OPCODE::MOVsi)
  .value("MOVsr", LIEF::assembly::arm::OPCODE::MOVsr)
  .value("MRC", LIEF::assembly::arm::OPCODE::MRC)
  .value("MRC2", LIEF::assembly::arm::OPCODE::MRC2)
  .value("MRRC", LIEF::assembly::arm::OPCODE::MRRC)
  .value("MRRC2", LIEF::assembly::arm::OPCODE::MRRC2)
  .value("MRS", LIEF::assembly::arm::OPCODE::MRS)
  .value("MRSbanked", LIEF::assembly::arm::OPCODE::MRSbanked)
  .value("MRSsys", LIEF::assembly::arm::OPCODE::MRSsys)
  .value("MSR", LIEF::assembly::arm::OPCODE::MSR)
  .value("MSRbanked", LIEF::assembly::arm::OPCODE::MSRbanked)
  .value("MSRi", LIEF::assembly::arm::OPCODE::MSRi)
  .value("MUL", LIEF::assembly::arm::OPCODE::MUL)
  .value("MVE_ASRLi", LIEF::assembly::arm::OPCODE::MVE_ASRLi)
  .value("MVE_ASRLr", LIEF::assembly::arm::OPCODE::MVE_ASRLr)
  .value("MVE_DLSTP_16", LIEF::assembly::arm::OPCODE::MVE_DLSTP_16)
  .value("MVE_DLSTP_32", LIEF::assembly::arm::OPCODE::MVE_DLSTP_32)
  .value("MVE_DLSTP_64", LIEF::assembly::arm::OPCODE::MVE_DLSTP_64)
  .value("MVE_DLSTP_8", LIEF::assembly::arm::OPCODE::MVE_DLSTP_8)
  .value("MVE_LCTP", LIEF::assembly::arm::OPCODE::MVE_LCTP)
  .value("MVE_LETP", LIEF::assembly::arm::OPCODE::MVE_LETP)
  .value("MVE_LSLLi", LIEF::assembly::arm::OPCODE::MVE_LSLLi)
  .value("MVE_LSLLr", LIEF::assembly::arm::OPCODE::MVE_LSLLr)
  .value("MVE_LSRL", LIEF::assembly::arm::OPCODE::MVE_LSRL)
  .value("MVE_SQRSHR", LIEF::assembly::arm::OPCODE::MVE_SQRSHR)
  .value("MVE_SQRSHRL", LIEF::assembly::arm::OPCODE::MVE_SQRSHRL)
  .value("MVE_SQSHL", LIEF::assembly::arm::OPCODE::MVE_SQSHL)
  .value("MVE_SQSHLL", LIEF::assembly::arm::OPCODE::MVE_SQSHLL)
  .value("MVE_SRSHR", LIEF::assembly::arm::OPCODE::MVE_SRSHR)
  .value("MVE_SRSHRL", LIEF::assembly::arm::OPCODE::MVE_SRSHRL)
  .value("MVE_UQRSHL", LIEF::assembly::arm::OPCODE::MVE_UQRSHL)
  .value("MVE_UQRSHLL", LIEF::assembly::arm::OPCODE::MVE_UQRSHLL)
  .value("MVE_UQSHL", LIEF::assembly::arm::OPCODE::MVE_UQSHL)
  .value("MVE_UQSHLL", LIEF::assembly::arm::OPCODE::MVE_UQSHLL)
  .value("MVE_URSHR", LIEF::assembly::arm::OPCODE::MVE_URSHR)
  .value("MVE_URSHRL", LIEF::assembly::arm::OPCODE::MVE_URSHRL)
  .value("MVE_VABAVs16", LIEF::assembly::arm::OPCODE::MVE_VABAVs16)
  .value("MVE_VABAVs32", LIEF::assembly::arm::OPCODE::MVE_VABAVs32)
  .value("MVE_VABAVs8", LIEF::assembly::arm::OPCODE::MVE_VABAVs8)
  .value("MVE_VABAVu16", LIEF::assembly::arm::OPCODE::MVE_VABAVu16)
  .value("MVE_VABAVu32", LIEF::assembly::arm::OPCODE::MVE_VABAVu32)
  .value("MVE_VABAVu8", LIEF::assembly::arm::OPCODE::MVE_VABAVu8)
  .value("MVE_VABDf16", LIEF::assembly::arm::OPCODE::MVE_VABDf16)
  .value("MVE_VABDf32", LIEF::assembly::arm::OPCODE::MVE_VABDf32)
  .value("MVE_VABDs16", LIEF::assembly::arm::OPCODE::MVE_VABDs16)
  .value("MVE_VABDs32", LIEF::assembly::arm::OPCODE::MVE_VABDs32)
  .value("MVE_VABDs8", LIEF::assembly::arm::OPCODE::MVE_VABDs8)
  .value("MVE_VABDu16", LIEF::assembly::arm::OPCODE::MVE_VABDu16)
  .value("MVE_VABDu32", LIEF::assembly::arm::OPCODE::MVE_VABDu32)
  .value("MVE_VABDu8", LIEF::assembly::arm::OPCODE::MVE_VABDu8)
  .value("MVE_VABSf16", LIEF::assembly::arm::OPCODE::MVE_VABSf16)
  .value("MVE_VABSf32", LIEF::assembly::arm::OPCODE::MVE_VABSf32)
  .value("MVE_VABSs16", LIEF::assembly::arm::OPCODE::MVE_VABSs16)
  .value("MVE_VABSs32", LIEF::assembly::arm::OPCODE::MVE_VABSs32)
  .value("MVE_VABSs8", LIEF::assembly::arm::OPCODE::MVE_VABSs8)
  .value("MVE_VADC", LIEF::assembly::arm::OPCODE::MVE_VADC)
  .value("MVE_VADCI", LIEF::assembly::arm::OPCODE::MVE_VADCI)
  .value("MVE_VADDLVs32acc", LIEF::assembly::arm::OPCODE::MVE_VADDLVs32acc)
  .value("MVE_VADDLVs32no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDLVs32no_acc)
  .value("MVE_VADDLVu32acc", LIEF::assembly::arm::OPCODE::MVE_VADDLVu32acc)
  .value("MVE_VADDLVu32no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDLVu32no_acc)
  .value("MVE_VADDVs16acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs16acc)
  .value("MVE_VADDVs16no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs16no_acc)
  .value("MVE_VADDVs32acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs32acc)
  .value("MVE_VADDVs32no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs32no_acc)
  .value("MVE_VADDVs8acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs8acc)
  .value("MVE_VADDVs8no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVs8no_acc)
  .value("MVE_VADDVu16acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu16acc)
  .value("MVE_VADDVu16no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu16no_acc)
  .value("MVE_VADDVu32acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu32acc)
  .value("MVE_VADDVu32no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu32no_acc)
  .value("MVE_VADDVu8acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu8acc)
  .value("MVE_VADDVu8no_acc", LIEF::assembly::arm::OPCODE::MVE_VADDVu8no_acc)
  .value("MVE_VADD_qr_f16", LIEF::assembly::arm::OPCODE::MVE_VADD_qr_f16)
  .value("MVE_VADD_qr_f32", LIEF::assembly::arm::OPCODE::MVE_VADD_qr_f32)
  .value("MVE_VADD_qr_i16", LIEF::assembly::arm::OPCODE::MVE_VADD_qr_i16)
  .value("MVE_VADD_qr_i32", LIEF::assembly::arm::OPCODE::MVE_VADD_qr_i32)
  .value("MVE_VADD_qr_i8", LIEF::assembly::arm::OPCODE::MVE_VADD_qr_i8)
  .value("MVE_VADDf16", LIEF::assembly::arm::OPCODE::MVE_VADDf16)
  .value("MVE_VADDf32", LIEF::assembly::arm::OPCODE::MVE_VADDf32)
  .value("MVE_VADDi16", LIEF::assembly::arm::OPCODE::MVE_VADDi16)
  .value("MVE_VADDi32", LIEF::assembly::arm::OPCODE::MVE_VADDi32)
  .value("MVE_VADDi8", LIEF::assembly::arm::OPCODE::MVE_VADDi8)
  .value("MVE_VAND", LIEF::assembly::arm::OPCODE::MVE_VAND)
  .value("MVE_VBIC", LIEF::assembly::arm::OPCODE::MVE_VBIC)
  .value("MVE_VBICimmi16", LIEF::assembly::arm::OPCODE::MVE_VBICimmi16)
  .value("MVE_VBICimmi32", LIEF::assembly::arm::OPCODE::MVE_VBICimmi32)
  .value("MVE_VBRSR16", LIEF::assembly::arm::OPCODE::MVE_VBRSR16)
  .value("MVE_VBRSR32", LIEF::assembly::arm::OPCODE::MVE_VBRSR32)
  .value("MVE_VBRSR8", LIEF::assembly::arm::OPCODE::MVE_VBRSR8)
  .value("MVE_VCADDf16", LIEF::assembly::arm::OPCODE::MVE_VCADDf16)
  .value("MVE_VCADDf32", LIEF::assembly::arm::OPCODE::MVE_VCADDf32)
  .value("MVE_VCADDi16", LIEF::assembly::arm::OPCODE::MVE_VCADDi16)
  .value("MVE_VCADDi32", LIEF::assembly::arm::OPCODE::MVE_VCADDi32)
  .value("MVE_VCADDi8", LIEF::assembly::arm::OPCODE::MVE_VCADDi8)
  .value("MVE_VCLSs16", LIEF::assembly::arm::OPCODE::MVE_VCLSs16)
  .value("MVE_VCLSs32", LIEF::assembly::arm::OPCODE::MVE_VCLSs32)
  .value("MVE_VCLSs8", LIEF::assembly::arm::OPCODE::MVE_VCLSs8)
  .value("MVE_VCLZs16", LIEF::assembly::arm::OPCODE::MVE_VCLZs16)
  .value("MVE_VCLZs32", LIEF::assembly::arm::OPCODE::MVE_VCLZs32)
  .value("MVE_VCLZs8", LIEF::assembly::arm::OPCODE::MVE_VCLZs8)
  .value("MVE_VCMLAf16", LIEF::assembly::arm::OPCODE::MVE_VCMLAf16)
  .value("MVE_VCMLAf32", LIEF::assembly::arm::OPCODE::MVE_VCMLAf32)
  .value("MVE_VCMPf16", LIEF::assembly::arm::OPCODE::MVE_VCMPf16)
  .value("MVE_VCMPf16r", LIEF::assembly::arm::OPCODE::MVE_VCMPf16r)
  .value("MVE_VCMPf32", LIEF::assembly::arm::OPCODE::MVE_VCMPf32)
  .value("MVE_VCMPf32r", LIEF::assembly::arm::OPCODE::MVE_VCMPf32r)
  .value("MVE_VCMPi16", LIEF::assembly::arm::OPCODE::MVE_VCMPi16)
  .value("MVE_VCMPi16r", LIEF::assembly::arm::OPCODE::MVE_VCMPi16r)
  .value("MVE_VCMPi32", LIEF::assembly::arm::OPCODE::MVE_VCMPi32)
  .value("MVE_VCMPi32r", LIEF::assembly::arm::OPCODE::MVE_VCMPi32r)
  .value("MVE_VCMPi8", LIEF::assembly::arm::OPCODE::MVE_VCMPi8)
  .value("MVE_VCMPi8r", LIEF::assembly::arm::OPCODE::MVE_VCMPi8r)
  .value("MVE_VCMPs16", LIEF::assembly::arm::OPCODE::MVE_VCMPs16)
  .value("MVE_VCMPs16r", LIEF::assembly::arm::OPCODE::MVE_VCMPs16r)
  .value("MVE_VCMPs32", LIEF::assembly::arm::OPCODE::MVE_VCMPs32)
  .value("MVE_VCMPs32r", LIEF::assembly::arm::OPCODE::MVE_VCMPs32r)
  .value("MVE_VCMPs8", LIEF::assembly::arm::OPCODE::MVE_VCMPs8)
  .value("MVE_VCMPs8r", LIEF::assembly::arm::OPCODE::MVE_VCMPs8r)
  .value("MVE_VCMPu16", LIEF::assembly::arm::OPCODE::MVE_VCMPu16)
  .value("MVE_VCMPu16r", LIEF::assembly::arm::OPCODE::MVE_VCMPu16r)
  .value("MVE_VCMPu32", LIEF::assembly::arm::OPCODE::MVE_VCMPu32)
  .value("MVE_VCMPu32r", LIEF::assembly::arm::OPCODE::MVE_VCMPu32r)
  .value("MVE_VCMPu8", LIEF::assembly::arm::OPCODE::MVE_VCMPu8)
  .value("MVE_VCMPu8r", LIEF::assembly::arm::OPCODE::MVE_VCMPu8r)
  .value("MVE_VCMULf16", LIEF::assembly::arm::OPCODE::MVE_VCMULf16)
  .value("MVE_VCMULf32", LIEF::assembly::arm::OPCODE::MVE_VCMULf32)
  .value("MVE_VCTP16", LIEF::assembly::arm::OPCODE::MVE_VCTP16)
  .value("MVE_VCTP32", LIEF::assembly::arm::OPCODE::MVE_VCTP32)
  .value("MVE_VCTP64", LIEF::assembly::arm::OPCODE::MVE_VCTP64)
  .value("MVE_VCTP8", LIEF::assembly::arm::OPCODE::MVE_VCTP8)
  .value("MVE_VCVTf16f32bh", LIEF::assembly::arm::OPCODE::MVE_VCVTf16f32bh)
  .value("MVE_VCVTf16f32th", LIEF::assembly::arm::OPCODE::MVE_VCVTf16f32th)
  .value("MVE_VCVTf16s16_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTf16s16_fix)
  .value("MVE_VCVTf16s16n", LIEF::assembly::arm::OPCODE::MVE_VCVTf16s16n)
  .value("MVE_VCVTf16u16_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTf16u16_fix)
  .value("MVE_VCVTf16u16n", LIEF::assembly::arm::OPCODE::MVE_VCVTf16u16n)
  .value("MVE_VCVTf32f16bh", LIEF::assembly::arm::OPCODE::MVE_VCVTf32f16bh)
  .value("MVE_VCVTf32f16th", LIEF::assembly::arm::OPCODE::MVE_VCVTf32f16th)
  .value("MVE_VCVTf32s32_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTf32s32_fix)
  .value("MVE_VCVTf32s32n", LIEF::assembly::arm::OPCODE::MVE_VCVTf32s32n)
  .value("MVE_VCVTf32u32_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTf32u32_fix)
  .value("MVE_VCVTf32u32n", LIEF::assembly::arm::OPCODE::MVE_VCVTf32u32n)
  .value("MVE_VCVTs16f16_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16_fix)
  .value("MVE_VCVTs16f16a", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16a)
  .value("MVE_VCVTs16f16m", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16m)
  .value("MVE_VCVTs16f16n", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16n)
  .value("MVE_VCVTs16f16p", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16p)
  .value("MVE_VCVTs16f16z", LIEF::assembly::arm::OPCODE::MVE_VCVTs16f16z)
  .value("MVE_VCVTs32f32_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32_fix)
  .value("MVE_VCVTs32f32a", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32a)
  .value("MVE_VCVTs32f32m", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32m)
  .value("MVE_VCVTs32f32n", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32n)
  .value("MVE_VCVTs32f32p", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32p)
  .value("MVE_VCVTs32f32z", LIEF::assembly::arm::OPCODE::MVE_VCVTs32f32z)
  .value("MVE_VCVTu16f16_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16_fix)
  .value("MVE_VCVTu16f16a", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16a)
  .value("MVE_VCVTu16f16m", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16m)
  .value("MVE_VCVTu16f16n", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16n)
  .value("MVE_VCVTu16f16p", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16p)
  .value("MVE_VCVTu16f16z", LIEF::assembly::arm::OPCODE::MVE_VCVTu16f16z)
  .value("MVE_VCVTu32f32_fix", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32_fix)
  .value("MVE_VCVTu32f32a", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32a)
  .value("MVE_VCVTu32f32m", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32m)
  .value("MVE_VCVTu32f32n", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32n)
  .value("MVE_VCVTu32f32p", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32p)
  .value("MVE_VCVTu32f32z", LIEF::assembly::arm::OPCODE::MVE_VCVTu32f32z)
  .value("MVE_VDDUPu16", LIEF::assembly::arm::OPCODE::MVE_VDDUPu16)
  .value("MVE_VDDUPu32", LIEF::assembly::arm::OPCODE::MVE_VDDUPu32)
  .value("MVE_VDDUPu8", LIEF::assembly::arm::OPCODE::MVE_VDDUPu8)
  .value("MVE_VDUP16", LIEF::assembly::arm::OPCODE::MVE_VDUP16)
  .value("MVE_VDUP32", LIEF::assembly::arm::OPCODE::MVE_VDUP32)
  .value("MVE_VDUP8", LIEF::assembly::arm::OPCODE::MVE_VDUP8)
  .value("MVE_VDWDUPu16", LIEF::assembly::arm::OPCODE::MVE_VDWDUPu16)
  .value("MVE_VDWDUPu32", LIEF::assembly::arm::OPCODE::MVE_VDWDUPu32)
  .value("MVE_VDWDUPu8", LIEF::assembly::arm::OPCODE::MVE_VDWDUPu8)
  .value("MVE_VEOR", LIEF::assembly::arm::OPCODE::MVE_VEOR)
  .value("MVE_VFMA_qr_Sf16", LIEF::assembly::arm::OPCODE::MVE_VFMA_qr_Sf16)
  .value("MVE_VFMA_qr_Sf32", LIEF::assembly::arm::OPCODE::MVE_VFMA_qr_Sf32)
  .value("MVE_VFMA_qr_f16", LIEF::assembly::arm::OPCODE::MVE_VFMA_qr_f16)
  .value("MVE_VFMA_qr_f32", LIEF::assembly::arm::OPCODE::MVE_VFMA_qr_f32)
  .value("MVE_VFMAf16", LIEF::assembly::arm::OPCODE::MVE_VFMAf16)
  .value("MVE_VFMAf32", LIEF::assembly::arm::OPCODE::MVE_VFMAf32)
  .value("MVE_VFMSf16", LIEF::assembly::arm::OPCODE::MVE_VFMSf16)
  .value("MVE_VFMSf32", LIEF::assembly::arm::OPCODE::MVE_VFMSf32)
  .value("MVE_VHADD_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_s16)
  .value("MVE_VHADD_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_s32)
  .value("MVE_VHADD_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_s8)
  .value("MVE_VHADD_qr_u16", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_u16)
  .value("MVE_VHADD_qr_u32", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_u32)
  .value("MVE_VHADD_qr_u8", LIEF::assembly::arm::OPCODE::MVE_VHADD_qr_u8)
  .value("MVE_VHADDs16", LIEF::assembly::arm::OPCODE::MVE_VHADDs16)
  .value("MVE_VHADDs32", LIEF::assembly::arm::OPCODE::MVE_VHADDs32)
  .value("MVE_VHADDs8", LIEF::assembly::arm::OPCODE::MVE_VHADDs8)
  .value("MVE_VHADDu16", LIEF::assembly::arm::OPCODE::MVE_VHADDu16)
  .value("MVE_VHADDu32", LIEF::assembly::arm::OPCODE::MVE_VHADDu32)
  .value("MVE_VHADDu8", LIEF::assembly::arm::OPCODE::MVE_VHADDu8)
  .value("MVE_VHCADDs16", LIEF::assembly::arm::OPCODE::MVE_VHCADDs16)
  .value("MVE_VHCADDs32", LIEF::assembly::arm::OPCODE::MVE_VHCADDs32)
  .value("MVE_VHCADDs8", LIEF::assembly::arm::OPCODE::MVE_VHCADDs8)
  .value("MVE_VHSUB_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_s16)
  .value("MVE_VHSUB_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_s32)
  .value("MVE_VHSUB_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_s8)
  .value("MVE_VHSUB_qr_u16", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_u16)
  .value("MVE_VHSUB_qr_u32", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_u32)
  .value("MVE_VHSUB_qr_u8", LIEF::assembly::arm::OPCODE::MVE_VHSUB_qr_u8)
  .value("MVE_VHSUBs16", LIEF::assembly::arm::OPCODE::MVE_VHSUBs16)
  .value("MVE_VHSUBs32", LIEF::assembly::arm::OPCODE::MVE_VHSUBs32)
  .value("MVE_VHSUBs8", LIEF::assembly::arm::OPCODE::MVE_VHSUBs8)
  .value("MVE_VHSUBu16", LIEF::assembly::arm::OPCODE::MVE_VHSUBu16)
  .value("MVE_VHSUBu32", LIEF::assembly::arm::OPCODE::MVE_VHSUBu32)
  .value("MVE_VHSUBu8", LIEF::assembly::arm::OPCODE::MVE_VHSUBu8)
  .value("MVE_VIDUPu16", LIEF::assembly::arm::OPCODE::MVE_VIDUPu16)
  .value("MVE_VIDUPu32", LIEF::assembly::arm::OPCODE::MVE_VIDUPu32)
  .value("MVE_VIDUPu8", LIEF::assembly::arm::OPCODE::MVE_VIDUPu8)
  .value("MVE_VIWDUPu16", LIEF::assembly::arm::OPCODE::MVE_VIWDUPu16)
  .value("MVE_VIWDUPu32", LIEF::assembly::arm::OPCODE::MVE_VIWDUPu32)
  .value("MVE_VIWDUPu8", LIEF::assembly::arm::OPCODE::MVE_VIWDUPu8)
  .value("MVE_VLD20_16", LIEF::assembly::arm::OPCODE::MVE_VLD20_16)
  .value("MVE_VLD20_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD20_16_wb);
  opcodes.value("MVE_VLD20_32", LIEF::assembly::arm::OPCODE::MVE_VLD20_32)
  .value("MVE_VLD20_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD20_32_wb)
  .value("MVE_VLD20_8", LIEF::assembly::arm::OPCODE::MVE_VLD20_8)
  .value("MVE_VLD20_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD20_8_wb)
  .value("MVE_VLD21_16", LIEF::assembly::arm::OPCODE::MVE_VLD21_16)
  .value("MVE_VLD21_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD21_16_wb)
  .value("MVE_VLD21_32", LIEF::assembly::arm::OPCODE::MVE_VLD21_32)
  .value("MVE_VLD21_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD21_32_wb)
  .value("MVE_VLD21_8", LIEF::assembly::arm::OPCODE::MVE_VLD21_8)
  .value("MVE_VLD21_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD21_8_wb)
  .value("MVE_VLD40_16", LIEF::assembly::arm::OPCODE::MVE_VLD40_16)
  .value("MVE_VLD40_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD40_16_wb)
  .value("MVE_VLD40_32", LIEF::assembly::arm::OPCODE::MVE_VLD40_32)
  .value("MVE_VLD40_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD40_32_wb)
  .value("MVE_VLD40_8", LIEF::assembly::arm::OPCODE::MVE_VLD40_8)
  .value("MVE_VLD40_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD40_8_wb)
  .value("MVE_VLD41_16", LIEF::assembly::arm::OPCODE::MVE_VLD41_16)
  .value("MVE_VLD41_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD41_16_wb)
  .value("MVE_VLD41_32", LIEF::assembly::arm::OPCODE::MVE_VLD41_32)
  .value("MVE_VLD41_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD41_32_wb)
  .value("MVE_VLD41_8", LIEF::assembly::arm::OPCODE::MVE_VLD41_8)
  .value("MVE_VLD41_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD41_8_wb)
  .value("MVE_VLD42_16", LIEF::assembly::arm::OPCODE::MVE_VLD42_16)
  .value("MVE_VLD42_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD42_16_wb)
  .value("MVE_VLD42_32", LIEF::assembly::arm::OPCODE::MVE_VLD42_32)
  .value("MVE_VLD42_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD42_32_wb)
  .value("MVE_VLD42_8", LIEF::assembly::arm::OPCODE::MVE_VLD42_8)
  .value("MVE_VLD42_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD42_8_wb)
  .value("MVE_VLD43_16", LIEF::assembly::arm::OPCODE::MVE_VLD43_16)
  .value("MVE_VLD43_16_wb", LIEF::assembly::arm::OPCODE::MVE_VLD43_16_wb)
  .value("MVE_VLD43_32", LIEF::assembly::arm::OPCODE::MVE_VLD43_32)
  .value("MVE_VLD43_32_wb", LIEF::assembly::arm::OPCODE::MVE_VLD43_32_wb)
  .value("MVE_VLD43_8", LIEF::assembly::arm::OPCODE::MVE_VLD43_8)
  .value("MVE_VLD43_8_wb", LIEF::assembly::arm::OPCODE::MVE_VLD43_8_wb)
  .value("MVE_VLDRBS16", LIEF::assembly::arm::OPCODE::MVE_VLDRBS16)
  .value("MVE_VLDRBS16_post", LIEF::assembly::arm::OPCODE::MVE_VLDRBS16_post)
  .value("MVE_VLDRBS16_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRBS16_pre)
  .value("MVE_VLDRBS16_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRBS16_rq)
  .value("MVE_VLDRBS32", LIEF::assembly::arm::OPCODE::MVE_VLDRBS32)
  .value("MVE_VLDRBS32_post", LIEF::assembly::arm::OPCODE::MVE_VLDRBS32_post)
  .value("MVE_VLDRBS32_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRBS32_pre)
  .value("MVE_VLDRBS32_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRBS32_rq)
  .value("MVE_VLDRBU16", LIEF::assembly::arm::OPCODE::MVE_VLDRBU16)
  .value("MVE_VLDRBU16_post", LIEF::assembly::arm::OPCODE::MVE_VLDRBU16_post)
  .value("MVE_VLDRBU16_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRBU16_pre)
  .value("MVE_VLDRBU16_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRBU16_rq)
  .value("MVE_VLDRBU32", LIEF::assembly::arm::OPCODE::MVE_VLDRBU32)
  .value("MVE_VLDRBU32_post", LIEF::assembly::arm::OPCODE::MVE_VLDRBU32_post)
  .value("MVE_VLDRBU32_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRBU32_pre)
  .value("MVE_VLDRBU32_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRBU32_rq)
  .value("MVE_VLDRBU8", LIEF::assembly::arm::OPCODE::MVE_VLDRBU8)
  .value("MVE_VLDRBU8_post", LIEF::assembly::arm::OPCODE::MVE_VLDRBU8_post)
  .value("MVE_VLDRBU8_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRBU8_pre)
  .value("MVE_VLDRBU8_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRBU8_rq)
  .value("MVE_VLDRDU64_qi", LIEF::assembly::arm::OPCODE::MVE_VLDRDU64_qi)
  .value("MVE_VLDRDU64_qi_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRDU64_qi_pre)
  .value("MVE_VLDRDU64_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRDU64_rq)
  .value("MVE_VLDRDU64_rq_u", LIEF::assembly::arm::OPCODE::MVE_VLDRDU64_rq_u)
  .value("MVE_VLDRHS32", LIEF::assembly::arm::OPCODE::MVE_VLDRHS32)
  .value("MVE_VLDRHS32_post", LIEF::assembly::arm::OPCODE::MVE_VLDRHS32_post)
  .value("MVE_VLDRHS32_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRHS32_pre)
  .value("MVE_VLDRHS32_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRHS32_rq)
  .value("MVE_VLDRHS32_rq_u", LIEF::assembly::arm::OPCODE::MVE_VLDRHS32_rq_u)
  .value("MVE_VLDRHU16", LIEF::assembly::arm::OPCODE::MVE_VLDRHU16)
  .value("MVE_VLDRHU16_post", LIEF::assembly::arm::OPCODE::MVE_VLDRHU16_post)
  .value("MVE_VLDRHU16_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRHU16_pre)
  .value("MVE_VLDRHU16_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRHU16_rq)
  .value("MVE_VLDRHU16_rq_u", LIEF::assembly::arm::OPCODE::MVE_VLDRHU16_rq_u)
  .value("MVE_VLDRHU32", LIEF::assembly::arm::OPCODE::MVE_VLDRHU32)
  .value("MVE_VLDRHU32_post", LIEF::assembly::arm::OPCODE::MVE_VLDRHU32_post)
  .value("MVE_VLDRHU32_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRHU32_pre)
  .value("MVE_VLDRHU32_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRHU32_rq)
  .value("MVE_VLDRHU32_rq_u", LIEF::assembly::arm::OPCODE::MVE_VLDRHU32_rq_u)
  .value("MVE_VLDRWU32", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32)
  .value("MVE_VLDRWU32_post", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_post)
  .value("MVE_VLDRWU32_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_pre)
  .value("MVE_VLDRWU32_qi", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_qi)
  .value("MVE_VLDRWU32_qi_pre", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_qi_pre)
  .value("MVE_VLDRWU32_rq", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_rq)
  .value("MVE_VLDRWU32_rq_u", LIEF::assembly::arm::OPCODE::MVE_VLDRWU32_rq_u)
  .value("MVE_VMAXAVs16", LIEF::assembly::arm::OPCODE::MVE_VMAXAVs16)
  .value("MVE_VMAXAVs32", LIEF::assembly::arm::OPCODE::MVE_VMAXAVs32)
  .value("MVE_VMAXAVs8", LIEF::assembly::arm::OPCODE::MVE_VMAXAVs8)
  .value("MVE_VMAXAs16", LIEF::assembly::arm::OPCODE::MVE_VMAXAs16)
  .value("MVE_VMAXAs32", LIEF::assembly::arm::OPCODE::MVE_VMAXAs32)
  .value("MVE_VMAXAs8", LIEF::assembly::arm::OPCODE::MVE_VMAXAs8)
  .value("MVE_VMAXNMAVf16", LIEF::assembly::arm::OPCODE::MVE_VMAXNMAVf16)
  .value("MVE_VMAXNMAVf32", LIEF::assembly::arm::OPCODE::MVE_VMAXNMAVf32)
  .value("MVE_VMAXNMAf16", LIEF::assembly::arm::OPCODE::MVE_VMAXNMAf16)
  .value("MVE_VMAXNMAf32", LIEF::assembly::arm::OPCODE::MVE_VMAXNMAf32)
  .value("MVE_VMAXNMVf16", LIEF::assembly::arm::OPCODE::MVE_VMAXNMVf16)
  .value("MVE_VMAXNMVf32", LIEF::assembly::arm::OPCODE::MVE_VMAXNMVf32)
  .value("MVE_VMAXNMf16", LIEF::assembly::arm::OPCODE::MVE_VMAXNMf16)
  .value("MVE_VMAXNMf32", LIEF::assembly::arm::OPCODE::MVE_VMAXNMf32)
  .value("MVE_VMAXVs16", LIEF::assembly::arm::OPCODE::MVE_VMAXVs16)
  .value("MVE_VMAXVs32", LIEF::assembly::arm::OPCODE::MVE_VMAXVs32)
  .value("MVE_VMAXVs8", LIEF::assembly::arm::OPCODE::MVE_VMAXVs8)
  .value("MVE_VMAXVu16", LIEF::assembly::arm::OPCODE::MVE_VMAXVu16)
  .value("MVE_VMAXVu32", LIEF::assembly::arm::OPCODE::MVE_VMAXVu32)
  .value("MVE_VMAXVu8", LIEF::assembly::arm::OPCODE::MVE_VMAXVu8)
  .value("MVE_VMAXs16", LIEF::assembly::arm::OPCODE::MVE_VMAXs16)
  .value("MVE_VMAXs32", LIEF::assembly::arm::OPCODE::MVE_VMAXs32)
  .value("MVE_VMAXs8", LIEF::assembly::arm::OPCODE::MVE_VMAXs8)
  .value("MVE_VMAXu16", LIEF::assembly::arm::OPCODE::MVE_VMAXu16)
  .value("MVE_VMAXu32", LIEF::assembly::arm::OPCODE::MVE_VMAXu32)
  .value("MVE_VMAXu8", LIEF::assembly::arm::OPCODE::MVE_VMAXu8)
  .value("MVE_VMINAVs16", LIEF::assembly::arm::OPCODE::MVE_VMINAVs16)
  .value("MVE_VMINAVs32", LIEF::assembly::arm::OPCODE::MVE_VMINAVs32)
  .value("MVE_VMINAVs8", LIEF::assembly::arm::OPCODE::MVE_VMINAVs8)
  .value("MVE_VMINAs16", LIEF::assembly::arm::OPCODE::MVE_VMINAs16)
  .value("MVE_VMINAs32", LIEF::assembly::arm::OPCODE::MVE_VMINAs32)
  .value("MVE_VMINAs8", LIEF::assembly::arm::OPCODE::MVE_VMINAs8)
  .value("MVE_VMINNMAVf16", LIEF::assembly::arm::OPCODE::MVE_VMINNMAVf16)
  .value("MVE_VMINNMAVf32", LIEF::assembly::arm::OPCODE::MVE_VMINNMAVf32)
  .value("MVE_VMINNMAf16", LIEF::assembly::arm::OPCODE::MVE_VMINNMAf16)
  .value("MVE_VMINNMAf32", LIEF::assembly::arm::OPCODE::MVE_VMINNMAf32)
  .value("MVE_VMINNMVf16", LIEF::assembly::arm::OPCODE::MVE_VMINNMVf16)
  .value("MVE_VMINNMVf32", LIEF::assembly::arm::OPCODE::MVE_VMINNMVf32)
  .value("MVE_VMINNMf16", LIEF::assembly::arm::OPCODE::MVE_VMINNMf16)
  .value("MVE_VMINNMf32", LIEF::assembly::arm::OPCODE::MVE_VMINNMf32)
  .value("MVE_VMINVs16", LIEF::assembly::arm::OPCODE::MVE_VMINVs16)
  .value("MVE_VMINVs32", LIEF::assembly::arm::OPCODE::MVE_VMINVs32)
  .value("MVE_VMINVs8", LIEF::assembly::arm::OPCODE::MVE_VMINVs8)
  .value("MVE_VMINVu16", LIEF::assembly::arm::OPCODE::MVE_VMINVu16)
  .value("MVE_VMINVu32", LIEF::assembly::arm::OPCODE::MVE_VMINVu32)
  .value("MVE_VMINVu8", LIEF::assembly::arm::OPCODE::MVE_VMINVu8)
  .value("MVE_VMINs16", LIEF::assembly::arm::OPCODE::MVE_VMINs16)
  .value("MVE_VMINs32", LIEF::assembly::arm::OPCODE::MVE_VMINs32)
  .value("MVE_VMINs8", LIEF::assembly::arm::OPCODE::MVE_VMINs8)
  .value("MVE_VMINu16", LIEF::assembly::arm::OPCODE::MVE_VMINu16)
  .value("MVE_VMINu32", LIEF::assembly::arm::OPCODE::MVE_VMINu32)
  .value("MVE_VMINu8", LIEF::assembly::arm::OPCODE::MVE_VMINu8)
  .value("MVE_VMLADAVas16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVas16)
  .value("MVE_VMLADAVas32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVas32)
  .value("MVE_VMLADAVas8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVas8)
  .value("MVE_VMLADAVau16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVau16)
  .value("MVE_VMLADAVau32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVau32)
  .value("MVE_VMLADAVau8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVau8)
  .value("MVE_VMLADAVaxs16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVaxs16)
  .value("MVE_VMLADAVaxs32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVaxs32)
  .value("MVE_VMLADAVaxs8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVaxs8)
  .value("MVE_VMLADAVs16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVs16)
  .value("MVE_VMLADAVs32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVs32)
  .value("MVE_VMLADAVs8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVs8)
  .value("MVE_VMLADAVu16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVu16)
  .value("MVE_VMLADAVu32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVu32)
  .value("MVE_VMLADAVu8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVu8)
  .value("MVE_VMLADAVxs16", LIEF::assembly::arm::OPCODE::MVE_VMLADAVxs16)
  .value("MVE_VMLADAVxs32", LIEF::assembly::arm::OPCODE::MVE_VMLADAVxs32)
  .value("MVE_VMLADAVxs8", LIEF::assembly::arm::OPCODE::MVE_VMLADAVxs8)
  .value("MVE_VMLALDAVas16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVas16)
  .value("MVE_VMLALDAVas32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVas32)
  .value("MVE_VMLALDAVau16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVau16)
  .value("MVE_VMLALDAVau32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVau32)
  .value("MVE_VMLALDAVaxs16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVaxs16)
  .value("MVE_VMLALDAVaxs32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVaxs32)
  .value("MVE_VMLALDAVs16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVs16)
  .value("MVE_VMLALDAVs32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVs32)
  .value("MVE_VMLALDAVu16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVu16)
  .value("MVE_VMLALDAVu32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVu32)
  .value("MVE_VMLALDAVxs16", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVxs16)
  .value("MVE_VMLALDAVxs32", LIEF::assembly::arm::OPCODE::MVE_VMLALDAVxs32)
  .value("MVE_VMLAS_qr_i16", LIEF::assembly::arm::OPCODE::MVE_VMLAS_qr_i16)
  .value("MVE_VMLAS_qr_i32", LIEF::assembly::arm::OPCODE::MVE_VMLAS_qr_i32)
  .value("MVE_VMLAS_qr_i8", LIEF::assembly::arm::OPCODE::MVE_VMLAS_qr_i8)
  .value("MVE_VMLA_qr_i16", LIEF::assembly::arm::OPCODE::MVE_VMLA_qr_i16)
  .value("MVE_VMLA_qr_i32", LIEF::assembly::arm::OPCODE::MVE_VMLA_qr_i32)
  .value("MVE_VMLA_qr_i8", LIEF::assembly::arm::OPCODE::MVE_VMLA_qr_i8)
  .value("MVE_VMLSDAVas16", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVas16)
  .value("MVE_VMLSDAVas32", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVas32)
  .value("MVE_VMLSDAVas8", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVas8)
  .value("MVE_VMLSDAVaxs16", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVaxs16)
  .value("MVE_VMLSDAVaxs32", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVaxs32)
  .value("MVE_VMLSDAVaxs8", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVaxs8)
  .value("MVE_VMLSDAVs16", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVs16)
  .value("MVE_VMLSDAVs32", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVs32)
  .value("MVE_VMLSDAVs8", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVs8)
  .value("MVE_VMLSDAVxs16", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVxs16)
  .value("MVE_VMLSDAVxs32", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVxs32)
  .value("MVE_VMLSDAVxs8", LIEF::assembly::arm::OPCODE::MVE_VMLSDAVxs8)
  .value("MVE_VMLSLDAVas16", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVas16)
  .value("MVE_VMLSLDAVas32", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVas32)
  .value("MVE_VMLSLDAVaxs16", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVaxs16)
  .value("MVE_VMLSLDAVaxs32", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVaxs32)
  .value("MVE_VMLSLDAVs16", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVs16)
  .value("MVE_VMLSLDAVs32", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVs32)
  .value("MVE_VMLSLDAVxs16", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVxs16)
  .value("MVE_VMLSLDAVxs32", LIEF::assembly::arm::OPCODE::MVE_VMLSLDAVxs32)
  .value("MVE_VMOVLs16bh", LIEF::assembly::arm::OPCODE::MVE_VMOVLs16bh)
  .value("MVE_VMOVLs16th", LIEF::assembly::arm::OPCODE::MVE_VMOVLs16th)
  .value("MVE_VMOVLs8bh", LIEF::assembly::arm::OPCODE::MVE_VMOVLs8bh)
  .value("MVE_VMOVLs8th", LIEF::assembly::arm::OPCODE::MVE_VMOVLs8th)
  .value("MVE_VMOVLu16bh", LIEF::assembly::arm::OPCODE::MVE_VMOVLu16bh)
  .value("MVE_VMOVLu16th", LIEF::assembly::arm::OPCODE::MVE_VMOVLu16th)
  .value("MVE_VMOVLu8bh", LIEF::assembly::arm::OPCODE::MVE_VMOVLu8bh)
  .value("MVE_VMOVLu8th", LIEF::assembly::arm::OPCODE::MVE_VMOVLu8th)
  .value("MVE_VMOVNi16bh", LIEF::assembly::arm::OPCODE::MVE_VMOVNi16bh)
  .value("MVE_VMOVNi16th", LIEF::assembly::arm::OPCODE::MVE_VMOVNi16th)
  .value("MVE_VMOVNi32bh", LIEF::assembly::arm::OPCODE::MVE_VMOVNi32bh)
  .value("MVE_VMOVNi32th", LIEF::assembly::arm::OPCODE::MVE_VMOVNi32th)
  .value("MVE_VMOV_from_lane_32", LIEF::assembly::arm::OPCODE::MVE_VMOV_from_lane_32)
  .value("MVE_VMOV_from_lane_s16", LIEF::assembly::arm::OPCODE::MVE_VMOV_from_lane_s16)
  .value("MVE_VMOV_from_lane_s8", LIEF::assembly::arm::OPCODE::MVE_VMOV_from_lane_s8)
  .value("MVE_VMOV_from_lane_u16", LIEF::assembly::arm::OPCODE::MVE_VMOV_from_lane_u16)
  .value("MVE_VMOV_from_lane_u8", LIEF::assembly::arm::OPCODE::MVE_VMOV_from_lane_u8)
  .value("MVE_VMOV_q_rr", LIEF::assembly::arm::OPCODE::MVE_VMOV_q_rr)
  .value("MVE_VMOV_rr_q", LIEF::assembly::arm::OPCODE::MVE_VMOV_rr_q)
  .value("MVE_VMOV_to_lane_16", LIEF::assembly::arm::OPCODE::MVE_VMOV_to_lane_16)
  .value("MVE_VMOV_to_lane_32", LIEF::assembly::arm::OPCODE::MVE_VMOV_to_lane_32)
  .value("MVE_VMOV_to_lane_8", LIEF::assembly::arm::OPCODE::MVE_VMOV_to_lane_8)
  .value("MVE_VMOVimmf32", LIEF::assembly::arm::OPCODE::MVE_VMOVimmf32)
  .value("MVE_VMOVimmi16", LIEF::assembly::arm::OPCODE::MVE_VMOVimmi16)
  .value("MVE_VMOVimmi32", LIEF::assembly::arm::OPCODE::MVE_VMOVimmi32)
  .value("MVE_VMOVimmi64", LIEF::assembly::arm::OPCODE::MVE_VMOVimmi64)
  .value("MVE_VMOVimmi8", LIEF::assembly::arm::OPCODE::MVE_VMOVimmi8)
  .value("MVE_VMULHs16", LIEF::assembly::arm::OPCODE::MVE_VMULHs16)
  .value("MVE_VMULHs32", LIEF::assembly::arm::OPCODE::MVE_VMULHs32)
  .value("MVE_VMULHs8", LIEF::assembly::arm::OPCODE::MVE_VMULHs8)
  .value("MVE_VMULHu16", LIEF::assembly::arm::OPCODE::MVE_VMULHu16)
  .value("MVE_VMULHu32", LIEF::assembly::arm::OPCODE::MVE_VMULHu32)
  .value("MVE_VMULHu8", LIEF::assembly::arm::OPCODE::MVE_VMULHu8)
  .value("MVE_VMULLBp16", LIEF::assembly::arm::OPCODE::MVE_VMULLBp16)
  .value("MVE_VMULLBp8", LIEF::assembly::arm::OPCODE::MVE_VMULLBp8)
  .value("MVE_VMULLBs16", LIEF::assembly::arm::OPCODE::MVE_VMULLBs16)
  .value("MVE_VMULLBs32", LIEF::assembly::arm::OPCODE::MVE_VMULLBs32)
  .value("MVE_VMULLBs8", LIEF::assembly::arm::OPCODE::MVE_VMULLBs8)
  .value("MVE_VMULLBu16", LIEF::assembly::arm::OPCODE::MVE_VMULLBu16)
  .value("MVE_VMULLBu32", LIEF::assembly::arm::OPCODE::MVE_VMULLBu32)
  .value("MVE_VMULLBu8", LIEF::assembly::arm::OPCODE::MVE_VMULLBu8)
  .value("MVE_VMULLTp16", LIEF::assembly::arm::OPCODE::MVE_VMULLTp16)
  .value("MVE_VMULLTp8", LIEF::assembly::arm::OPCODE::MVE_VMULLTp8)
  .value("MVE_VMULLTs16", LIEF::assembly::arm::OPCODE::MVE_VMULLTs16)
  .value("MVE_VMULLTs32", LIEF::assembly::arm::OPCODE::MVE_VMULLTs32)
  .value("MVE_VMULLTs8", LIEF::assembly::arm::OPCODE::MVE_VMULLTs8)
  .value("MVE_VMULLTu16", LIEF::assembly::arm::OPCODE::MVE_VMULLTu16)
  .value("MVE_VMULLTu32", LIEF::assembly::arm::OPCODE::MVE_VMULLTu32)
  .value("MVE_VMULLTu8", LIEF::assembly::arm::OPCODE::MVE_VMULLTu8)
  .value("MVE_VMUL_qr_f16", LIEF::assembly::arm::OPCODE::MVE_VMUL_qr_f16)
  .value("MVE_VMUL_qr_f32", LIEF::assembly::arm::OPCODE::MVE_VMUL_qr_f32)
  .value("MVE_VMUL_qr_i16", LIEF::assembly::arm::OPCODE::MVE_VMUL_qr_i16)
  .value("MVE_VMUL_qr_i32", LIEF::assembly::arm::OPCODE::MVE_VMUL_qr_i32)
  .value("MVE_VMUL_qr_i8", LIEF::assembly::arm::OPCODE::MVE_VMUL_qr_i8)
  .value("MVE_VMULf16", LIEF::assembly::arm::OPCODE::MVE_VMULf16)
  .value("MVE_VMULf32", LIEF::assembly::arm::OPCODE::MVE_VMULf32)
  .value("MVE_VMULi16", LIEF::assembly::arm::OPCODE::MVE_VMULi16)
  .value("MVE_VMULi32", LIEF::assembly::arm::OPCODE::MVE_VMULi32)
  .value("MVE_VMULi8", LIEF::assembly::arm::OPCODE::MVE_VMULi8)
  .value("MVE_VMVN", LIEF::assembly::arm::OPCODE::MVE_VMVN)
  .value("MVE_VMVNimmi16", LIEF::assembly::arm::OPCODE::MVE_VMVNimmi16)
  .value("MVE_VMVNimmi32", LIEF::assembly::arm::OPCODE::MVE_VMVNimmi32)
  .value("MVE_VNEGf16", LIEF::assembly::arm::OPCODE::MVE_VNEGf16)
  .value("MVE_VNEGf32", LIEF::assembly::arm::OPCODE::MVE_VNEGf32)
  .value("MVE_VNEGs16", LIEF::assembly::arm::OPCODE::MVE_VNEGs16)
  .value("MVE_VNEGs32", LIEF::assembly::arm::OPCODE::MVE_VNEGs32)
  .value("MVE_VNEGs8", LIEF::assembly::arm::OPCODE::MVE_VNEGs8)
  .value("MVE_VORN", LIEF::assembly::arm::OPCODE::MVE_VORN)
  .value("MVE_VORR", LIEF::assembly::arm::OPCODE::MVE_VORR)
  .value("MVE_VORRimmi16", LIEF::assembly::arm::OPCODE::MVE_VORRimmi16)
  .value("MVE_VORRimmi32", LIEF::assembly::arm::OPCODE::MVE_VORRimmi32)
  .value("MVE_VPNOT", LIEF::assembly::arm::OPCODE::MVE_VPNOT)
  .value("MVE_VPSEL", LIEF::assembly::arm::OPCODE::MVE_VPSEL)
  .value("MVE_VPST", LIEF::assembly::arm::OPCODE::MVE_VPST)
  .value("MVE_VPTv16i8", LIEF::assembly::arm::OPCODE::MVE_VPTv16i8)
  .value("MVE_VPTv16i8r", LIEF::assembly::arm::OPCODE::MVE_VPTv16i8r)
  .value("MVE_VPTv16s8", LIEF::assembly::arm::OPCODE::MVE_VPTv16s8)
  .value("MVE_VPTv16s8r", LIEF::assembly::arm::OPCODE::MVE_VPTv16s8r)
  .value("MVE_VPTv16u8", LIEF::assembly::arm::OPCODE::MVE_VPTv16u8)
  .value("MVE_VPTv16u8r", LIEF::assembly::arm::OPCODE::MVE_VPTv16u8r)
  .value("MVE_VPTv4f32", LIEF::assembly::arm::OPCODE::MVE_VPTv4f32)
  .value("MVE_VPTv4f32r", LIEF::assembly::arm::OPCODE::MVE_VPTv4f32r)
  .value("MVE_VPTv4i32", LIEF::assembly::arm::OPCODE::MVE_VPTv4i32)
  .value("MVE_VPTv4i32r", LIEF::assembly::arm::OPCODE::MVE_VPTv4i32r)
  .value("MVE_VPTv4s32", LIEF::assembly::arm::OPCODE::MVE_VPTv4s32)
  .value("MVE_VPTv4s32r", LIEF::assembly::arm::OPCODE::MVE_VPTv4s32r)
  .value("MVE_VPTv4u32", LIEF::assembly::arm::OPCODE::MVE_VPTv4u32)
  .value("MVE_VPTv4u32r", LIEF::assembly::arm::OPCODE::MVE_VPTv4u32r)
  .value("MVE_VPTv8f16", LIEF::assembly::arm::OPCODE::MVE_VPTv8f16)
  .value("MVE_VPTv8f16r", LIEF::assembly::arm::OPCODE::MVE_VPTv8f16r)
  .value("MVE_VPTv8i16", LIEF::assembly::arm::OPCODE::MVE_VPTv8i16)
  .value("MVE_VPTv8i16r", LIEF::assembly::arm::OPCODE::MVE_VPTv8i16r)
  .value("MVE_VPTv8s16", LIEF::assembly::arm::OPCODE::MVE_VPTv8s16)
  .value("MVE_VPTv8s16r", LIEF::assembly::arm::OPCODE::MVE_VPTv8s16r)
  .value("MVE_VPTv8u16", LIEF::assembly::arm::OPCODE::MVE_VPTv8u16)
  .value("MVE_VPTv8u16r", LIEF::assembly::arm::OPCODE::MVE_VPTv8u16r)
  .value("MVE_VQABSs16", LIEF::assembly::arm::OPCODE::MVE_VQABSs16)
  .value("MVE_VQABSs32", LIEF::assembly::arm::OPCODE::MVE_VQABSs32)
  .value("MVE_VQABSs8", LIEF::assembly::arm::OPCODE::MVE_VQABSs8)
  .value("MVE_VQADD_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_s16)
  .value("MVE_VQADD_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_s32)
  .value("MVE_VQADD_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_s8)
  .value("MVE_VQADD_qr_u16", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_u16)
  .value("MVE_VQADD_qr_u32", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_u32)
  .value("MVE_VQADD_qr_u8", LIEF::assembly::arm::OPCODE::MVE_VQADD_qr_u8)
  .value("MVE_VQADDs16", LIEF::assembly::arm::OPCODE::MVE_VQADDs16)
  .value("MVE_VQADDs32", LIEF::assembly::arm::OPCODE::MVE_VQADDs32)
  .value("MVE_VQADDs8", LIEF::assembly::arm::OPCODE::MVE_VQADDs8)
  .value("MVE_VQADDu16", LIEF::assembly::arm::OPCODE::MVE_VQADDu16)
  .value("MVE_VQADDu32", LIEF::assembly::arm::OPCODE::MVE_VQADDu32)
  .value("MVE_VQADDu8", LIEF::assembly::arm::OPCODE::MVE_VQADDu8)
  .value("MVE_VQDMLADHXs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHXs16);
  opcodes.value("MVE_VQDMLADHXs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHXs32)
  .value("MVE_VQDMLADHXs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHXs8)
  .value("MVE_VQDMLADHs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHs16)
  .value("MVE_VQDMLADHs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHs32)
  .value("MVE_VQDMLADHs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLADHs8)
  .value("MVE_VQDMLAH_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLAH_qrs16)
  .value("MVE_VQDMLAH_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLAH_qrs32)
  .value("MVE_VQDMLAH_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLAH_qrs8)
  .value("MVE_VQDMLASH_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLASH_qrs16)
  .value("MVE_VQDMLASH_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLASH_qrs32)
  .value("MVE_VQDMLASH_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLASH_qrs8)
  .value("MVE_VQDMLSDHXs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHXs16)
  .value("MVE_VQDMLSDHXs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHXs32)
  .value("MVE_VQDMLSDHXs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHXs8)
  .value("MVE_VQDMLSDHs16", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHs16)
  .value("MVE_VQDMLSDHs32", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHs32)
  .value("MVE_VQDMLSDHs8", LIEF::assembly::arm::OPCODE::MVE_VQDMLSDHs8)
  .value("MVE_VQDMULH_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VQDMULH_qr_s16)
  .value("MVE_VQDMULH_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VQDMULH_qr_s32)
  .value("MVE_VQDMULH_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VQDMULH_qr_s8)
  .value("MVE_VQDMULHi16", LIEF::assembly::arm::OPCODE::MVE_VQDMULHi16)
  .value("MVE_VQDMULHi32", LIEF::assembly::arm::OPCODE::MVE_VQDMULHi32)
  .value("MVE_VQDMULHi8", LIEF::assembly::arm::OPCODE::MVE_VQDMULHi8)
  .value("MVE_VQDMULL_qr_s16bh", LIEF::assembly::arm::OPCODE::MVE_VQDMULL_qr_s16bh)
  .value("MVE_VQDMULL_qr_s16th", LIEF::assembly::arm::OPCODE::MVE_VQDMULL_qr_s16th)
  .value("MVE_VQDMULL_qr_s32bh", LIEF::assembly::arm::OPCODE::MVE_VQDMULL_qr_s32bh)
  .value("MVE_VQDMULL_qr_s32th", LIEF::assembly::arm::OPCODE::MVE_VQDMULL_qr_s32th)
  .value("MVE_VQDMULLs16bh", LIEF::assembly::arm::OPCODE::MVE_VQDMULLs16bh)
  .value("MVE_VQDMULLs16th", LIEF::assembly::arm::OPCODE::MVE_VQDMULLs16th)
  .value("MVE_VQDMULLs32bh", LIEF::assembly::arm::OPCODE::MVE_VQDMULLs32bh)
  .value("MVE_VQDMULLs32th", LIEF::assembly::arm::OPCODE::MVE_VQDMULLs32th)
  .value("MVE_VQMOVNs16bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVNs16bh)
  .value("MVE_VQMOVNs16th", LIEF::assembly::arm::OPCODE::MVE_VQMOVNs16th)
  .value("MVE_VQMOVNs32bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVNs32bh)
  .value("MVE_VQMOVNs32th", LIEF::assembly::arm::OPCODE::MVE_VQMOVNs32th)
  .value("MVE_VQMOVNu16bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVNu16bh)
  .value("MVE_VQMOVNu16th", LIEF::assembly::arm::OPCODE::MVE_VQMOVNu16th)
  .value("MVE_VQMOVNu32bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVNu32bh)
  .value("MVE_VQMOVNu32th", LIEF::assembly::arm::OPCODE::MVE_VQMOVNu32th)
  .value("MVE_VQMOVUNs16bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVUNs16bh)
  .value("MVE_VQMOVUNs16th", LIEF::assembly::arm::OPCODE::MVE_VQMOVUNs16th)
  .value("MVE_VQMOVUNs32bh", LIEF::assembly::arm::OPCODE::MVE_VQMOVUNs32bh)
  .value("MVE_VQMOVUNs32th", LIEF::assembly::arm::OPCODE::MVE_VQMOVUNs32th)
  .value("MVE_VQNEGs16", LIEF::assembly::arm::OPCODE::MVE_VQNEGs16)
  .value("MVE_VQNEGs32", LIEF::assembly::arm::OPCODE::MVE_VQNEGs32)
  .value("MVE_VQNEGs8", LIEF::assembly::arm::OPCODE::MVE_VQNEGs8)
  .value("MVE_VQRDMLADHXs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHXs16)
  .value("MVE_VQRDMLADHXs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHXs32)
  .value("MVE_VQRDMLADHXs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHXs8)
  .value("MVE_VQRDMLADHs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHs16)
  .value("MVE_VQRDMLADHs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHs32)
  .value("MVE_VQRDMLADHs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLADHs8)
  .value("MVE_VQRDMLAH_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLAH_qrs16)
  .value("MVE_VQRDMLAH_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLAH_qrs32)
  .value("MVE_VQRDMLAH_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLAH_qrs8)
  .value("MVE_VQRDMLASH_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLASH_qrs16)
  .value("MVE_VQRDMLASH_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLASH_qrs32)
  .value("MVE_VQRDMLASH_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLASH_qrs8)
  .value("MVE_VQRDMLSDHXs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHXs16)
  .value("MVE_VQRDMLSDHXs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHXs32)
  .value("MVE_VQRDMLSDHXs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHXs8)
  .value("MVE_VQRDMLSDHs16", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHs16)
  .value("MVE_VQRDMLSDHs32", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHs32)
  .value("MVE_VQRDMLSDHs8", LIEF::assembly::arm::OPCODE::MVE_VQRDMLSDHs8)
  .value("MVE_VQRDMULH_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VQRDMULH_qr_s16)
  .value("MVE_VQRDMULH_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VQRDMULH_qr_s32)
  .value("MVE_VQRDMULH_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VQRDMULH_qr_s8)
  .value("MVE_VQRDMULHi16", LIEF::assembly::arm::OPCODE::MVE_VQRDMULHi16)
  .value("MVE_VQRDMULHi32", LIEF::assembly::arm::OPCODE::MVE_VQRDMULHi32)
  .value("MVE_VQRDMULHi8", LIEF::assembly::arm::OPCODE::MVE_VQRDMULHi8)
  .value("MVE_VQRSHL_by_vecs16", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecs16)
  .value("MVE_VQRSHL_by_vecs32", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecs32)
  .value("MVE_VQRSHL_by_vecs8", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecs8)
  .value("MVE_VQRSHL_by_vecu16", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecu16)
  .value("MVE_VQRSHL_by_vecu32", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecu32)
  .value("MVE_VQRSHL_by_vecu8", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_by_vecu8)
  .value("MVE_VQRSHL_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qrs16)
  .value("MVE_VQRSHL_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qrs32)
  .value("MVE_VQRSHL_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qrs8)
  .value("MVE_VQRSHL_qru16", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qru16)
  .value("MVE_VQRSHL_qru32", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qru32)
  .value("MVE_VQRSHL_qru8", LIEF::assembly::arm::OPCODE::MVE_VQRSHL_qru8)
  .value("MVE_VQRSHRNbhs16", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNbhs16)
  .value("MVE_VQRSHRNbhs32", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNbhs32)
  .value("MVE_VQRSHRNbhu16", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNbhu16)
  .value("MVE_VQRSHRNbhu32", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNbhu32)
  .value("MVE_VQRSHRNths16", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNths16)
  .value("MVE_VQRSHRNths32", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNths32)
  .value("MVE_VQRSHRNthu16", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNthu16)
  .value("MVE_VQRSHRNthu32", LIEF::assembly::arm::OPCODE::MVE_VQRSHRNthu32)
  .value("MVE_VQRSHRUNs16bh", LIEF::assembly::arm::OPCODE::MVE_VQRSHRUNs16bh)
  .value("MVE_VQRSHRUNs16th", LIEF::assembly::arm::OPCODE::MVE_VQRSHRUNs16th)
  .value("MVE_VQRSHRUNs32bh", LIEF::assembly::arm::OPCODE::MVE_VQRSHRUNs32bh)
  .value("MVE_VQRSHRUNs32th", LIEF::assembly::arm::OPCODE::MVE_VQRSHRUNs32th)
  .value("MVE_VQSHLU_imms16", LIEF::assembly::arm::OPCODE::MVE_VQSHLU_imms16)
  .value("MVE_VQSHLU_imms32", LIEF::assembly::arm::OPCODE::MVE_VQSHLU_imms32)
  .value("MVE_VQSHLU_imms8", LIEF::assembly::arm::OPCODE::MVE_VQSHLU_imms8)
  .value("MVE_VQSHL_by_vecs16", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecs16)
  .value("MVE_VQSHL_by_vecs32", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecs32)
  .value("MVE_VQSHL_by_vecs8", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecs8)
  .value("MVE_VQSHL_by_vecu16", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecu16)
  .value("MVE_VQSHL_by_vecu32", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecu32)
  .value("MVE_VQSHL_by_vecu8", LIEF::assembly::arm::OPCODE::MVE_VQSHL_by_vecu8)
  .value("MVE_VQSHL_qrs16", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qrs16)
  .value("MVE_VQSHL_qrs32", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qrs32)
  .value("MVE_VQSHL_qrs8", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qrs8)
  .value("MVE_VQSHL_qru16", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qru16)
  .value("MVE_VQSHL_qru32", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qru32)
  .value("MVE_VQSHL_qru8", LIEF::assembly::arm::OPCODE::MVE_VQSHL_qru8)
  .value("MVE_VQSHLimms16", LIEF::assembly::arm::OPCODE::MVE_VQSHLimms16)
  .value("MVE_VQSHLimms32", LIEF::assembly::arm::OPCODE::MVE_VQSHLimms32)
  .value("MVE_VQSHLimms8", LIEF::assembly::arm::OPCODE::MVE_VQSHLimms8)
  .value("MVE_VQSHLimmu16", LIEF::assembly::arm::OPCODE::MVE_VQSHLimmu16)
  .value("MVE_VQSHLimmu32", LIEF::assembly::arm::OPCODE::MVE_VQSHLimmu32)
  .value("MVE_VQSHLimmu8", LIEF::assembly::arm::OPCODE::MVE_VQSHLimmu8)
  .value("MVE_VQSHRNbhs16", LIEF::assembly::arm::OPCODE::MVE_VQSHRNbhs16)
  .value("MVE_VQSHRNbhs32", LIEF::assembly::arm::OPCODE::MVE_VQSHRNbhs32)
  .value("MVE_VQSHRNbhu16", LIEF::assembly::arm::OPCODE::MVE_VQSHRNbhu16)
  .value("MVE_VQSHRNbhu32", LIEF::assembly::arm::OPCODE::MVE_VQSHRNbhu32)
  .value("MVE_VQSHRNths16", LIEF::assembly::arm::OPCODE::MVE_VQSHRNths16)
  .value("MVE_VQSHRNths32", LIEF::assembly::arm::OPCODE::MVE_VQSHRNths32)
  .value("MVE_VQSHRNthu16", LIEF::assembly::arm::OPCODE::MVE_VQSHRNthu16)
  .value("MVE_VQSHRNthu32", LIEF::assembly::arm::OPCODE::MVE_VQSHRNthu32)
  .value("MVE_VQSHRUNs16bh", LIEF::assembly::arm::OPCODE::MVE_VQSHRUNs16bh)
  .value("MVE_VQSHRUNs16th", LIEF::assembly::arm::OPCODE::MVE_VQSHRUNs16th)
  .value("MVE_VQSHRUNs32bh", LIEF::assembly::arm::OPCODE::MVE_VQSHRUNs32bh)
  .value("MVE_VQSHRUNs32th", LIEF::assembly::arm::OPCODE::MVE_VQSHRUNs32th)
  .value("MVE_VQSUB_qr_s16", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_s16)
  .value("MVE_VQSUB_qr_s32", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_s32)
  .value("MVE_VQSUB_qr_s8", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_s8)
  .value("MVE_VQSUB_qr_u16", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_u16)
  .value("MVE_VQSUB_qr_u32", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_u32)
  .value("MVE_VQSUB_qr_u8", LIEF::assembly::arm::OPCODE::MVE_VQSUB_qr_u8)
  .value("MVE_VQSUBs16", LIEF::assembly::arm::OPCODE::MVE_VQSUBs16)
  .value("MVE_VQSUBs32", LIEF::assembly::arm::OPCODE::MVE_VQSUBs32)
  .value("MVE_VQSUBs8", LIEF::assembly::arm::OPCODE::MVE_VQSUBs8)
  .value("MVE_VQSUBu16", LIEF::assembly::arm::OPCODE::MVE_VQSUBu16)
  .value("MVE_VQSUBu32", LIEF::assembly::arm::OPCODE::MVE_VQSUBu32)
  .value("MVE_VQSUBu8", LIEF::assembly::arm::OPCODE::MVE_VQSUBu8)
  .value("MVE_VREV16_8", LIEF::assembly::arm::OPCODE::MVE_VREV16_8)
  .value("MVE_VREV32_16", LIEF::assembly::arm::OPCODE::MVE_VREV32_16)
  .value("MVE_VREV32_8", LIEF::assembly::arm::OPCODE::MVE_VREV32_8)
  .value("MVE_VREV64_16", LIEF::assembly::arm::OPCODE::MVE_VREV64_16)
  .value("MVE_VREV64_32", LIEF::assembly::arm::OPCODE::MVE_VREV64_32)
  .value("MVE_VREV64_8", LIEF::assembly::arm::OPCODE::MVE_VREV64_8)
  .value("MVE_VRHADDs16", LIEF::assembly::arm::OPCODE::MVE_VRHADDs16)
  .value("MVE_VRHADDs32", LIEF::assembly::arm::OPCODE::MVE_VRHADDs32)
  .value("MVE_VRHADDs8", LIEF::assembly::arm::OPCODE::MVE_VRHADDs8)
  .value("MVE_VRHADDu16", LIEF::assembly::arm::OPCODE::MVE_VRHADDu16)
  .value("MVE_VRHADDu32", LIEF::assembly::arm::OPCODE::MVE_VRHADDu32)
  .value("MVE_VRHADDu8", LIEF::assembly::arm::OPCODE::MVE_VRHADDu8)
  .value("MVE_VRINTf16A", LIEF::assembly::arm::OPCODE::MVE_VRINTf16A)
  .value("MVE_VRINTf16M", LIEF::assembly::arm::OPCODE::MVE_VRINTf16M)
  .value("MVE_VRINTf16N", LIEF::assembly::arm::OPCODE::MVE_VRINTf16N)
  .value("MVE_VRINTf16P", LIEF::assembly::arm::OPCODE::MVE_VRINTf16P)
  .value("MVE_VRINTf16X", LIEF::assembly::arm::OPCODE::MVE_VRINTf16X)
  .value("MVE_VRINTf16Z", LIEF::assembly::arm::OPCODE::MVE_VRINTf16Z)
  .value("MVE_VRINTf32A", LIEF::assembly::arm::OPCODE::MVE_VRINTf32A)
  .value("MVE_VRINTf32M", LIEF::assembly::arm::OPCODE::MVE_VRINTf32M)
  .value("MVE_VRINTf32N", LIEF::assembly::arm::OPCODE::MVE_VRINTf32N)
  .value("MVE_VRINTf32P", LIEF::assembly::arm::OPCODE::MVE_VRINTf32P)
  .value("MVE_VRINTf32X", LIEF::assembly::arm::OPCODE::MVE_VRINTf32X)
  .value("MVE_VRINTf32Z", LIEF::assembly::arm::OPCODE::MVE_VRINTf32Z)
  .value("MVE_VRMLALDAVHas32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHas32)
  .value("MVE_VRMLALDAVHau32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHau32)
  .value("MVE_VRMLALDAVHaxs32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHaxs32)
  .value("MVE_VRMLALDAVHs32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHs32)
  .value("MVE_VRMLALDAVHu32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHu32)
  .value("MVE_VRMLALDAVHxs32", LIEF::assembly::arm::OPCODE::MVE_VRMLALDAVHxs32)
  .value("MVE_VRMLSLDAVHas32", LIEF::assembly::arm::OPCODE::MVE_VRMLSLDAVHas32)
  .value("MVE_VRMLSLDAVHaxs32", LIEF::assembly::arm::OPCODE::MVE_VRMLSLDAVHaxs32)
  .value("MVE_VRMLSLDAVHs32", LIEF::assembly::arm::OPCODE::MVE_VRMLSLDAVHs32)
  .value("MVE_VRMLSLDAVHxs32", LIEF::assembly::arm::OPCODE::MVE_VRMLSLDAVHxs32)
  .value("MVE_VRMULHs16", LIEF::assembly::arm::OPCODE::MVE_VRMULHs16)
  .value("MVE_VRMULHs32", LIEF::assembly::arm::OPCODE::MVE_VRMULHs32)
  .value("MVE_VRMULHs8", LIEF::assembly::arm::OPCODE::MVE_VRMULHs8)
  .value("MVE_VRMULHu16", LIEF::assembly::arm::OPCODE::MVE_VRMULHu16)
  .value("MVE_VRMULHu32", LIEF::assembly::arm::OPCODE::MVE_VRMULHu32)
  .value("MVE_VRMULHu8", LIEF::assembly::arm::OPCODE::MVE_VRMULHu8)
  .value("MVE_VRSHL_by_vecs16", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecs16)
  .value("MVE_VRSHL_by_vecs32", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecs32)
  .value("MVE_VRSHL_by_vecs8", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecs8)
  .value("MVE_VRSHL_by_vecu16", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecu16)
  .value("MVE_VRSHL_by_vecu32", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecu32)
  .value("MVE_VRSHL_by_vecu8", LIEF::assembly::arm::OPCODE::MVE_VRSHL_by_vecu8)
  .value("MVE_VRSHL_qrs16", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qrs16)
  .value("MVE_VRSHL_qrs32", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qrs32)
  .value("MVE_VRSHL_qrs8", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qrs8)
  .value("MVE_VRSHL_qru16", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qru16)
  .value("MVE_VRSHL_qru32", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qru32)
  .value("MVE_VRSHL_qru8", LIEF::assembly::arm::OPCODE::MVE_VRSHL_qru8)
  .value("MVE_VRSHRNi16bh", LIEF::assembly::arm::OPCODE::MVE_VRSHRNi16bh)
  .value("MVE_VRSHRNi16th", LIEF::assembly::arm::OPCODE::MVE_VRSHRNi16th)
  .value("MVE_VRSHRNi32bh", LIEF::assembly::arm::OPCODE::MVE_VRSHRNi32bh)
  .value("MVE_VRSHRNi32th", LIEF::assembly::arm::OPCODE::MVE_VRSHRNi32th)
  .value("MVE_VRSHR_imms16", LIEF::assembly::arm::OPCODE::MVE_VRSHR_imms16)
  .value("MVE_VRSHR_imms32", LIEF::assembly::arm::OPCODE::MVE_VRSHR_imms32)
  .value("MVE_VRSHR_imms8", LIEF::assembly::arm::OPCODE::MVE_VRSHR_imms8)
  .value("MVE_VRSHR_immu16", LIEF::assembly::arm::OPCODE::MVE_VRSHR_immu16)
  .value("MVE_VRSHR_immu32", LIEF::assembly::arm::OPCODE::MVE_VRSHR_immu32)
  .value("MVE_VRSHR_immu8", LIEF::assembly::arm::OPCODE::MVE_VRSHR_immu8)
  .value("MVE_VSBC", LIEF::assembly::arm::OPCODE::MVE_VSBC)
  .value("MVE_VSBCI", LIEF::assembly::arm::OPCODE::MVE_VSBCI)
  .value("MVE_VSHLC", LIEF::assembly::arm::OPCODE::MVE_VSHLC)
  .value("MVE_VSHLL_imms16bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_imms16bh)
  .value("MVE_VSHLL_imms16th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_imms16th)
  .value("MVE_VSHLL_imms8bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_imms8bh)
  .value("MVE_VSHLL_imms8th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_imms8th)
  .value("MVE_VSHLL_immu16bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_immu16bh)
  .value("MVE_VSHLL_immu16th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_immu16th)
  .value("MVE_VSHLL_immu8bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_immu8bh)
  .value("MVE_VSHLL_immu8th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_immu8th)
  .value("MVE_VSHLL_lws16bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lws16bh)
  .value("MVE_VSHLL_lws16th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lws16th)
  .value("MVE_VSHLL_lws8bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lws8bh)
  .value("MVE_VSHLL_lws8th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lws8th)
  .value("MVE_VSHLL_lwu16bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lwu16bh)
  .value("MVE_VSHLL_lwu16th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lwu16th)
  .value("MVE_VSHLL_lwu8bh", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lwu8bh)
  .value("MVE_VSHLL_lwu8th", LIEF::assembly::arm::OPCODE::MVE_VSHLL_lwu8th)
  .value("MVE_VSHL_by_vecs16", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecs16)
  .value("MVE_VSHL_by_vecs32", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecs32)
  .value("MVE_VSHL_by_vecs8", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecs8)
  .value("MVE_VSHL_by_vecu16", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecu16)
  .value("MVE_VSHL_by_vecu32", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecu32)
  .value("MVE_VSHL_by_vecu8", LIEF::assembly::arm::OPCODE::MVE_VSHL_by_vecu8)
  .value("MVE_VSHL_immi16", LIEF::assembly::arm::OPCODE::MVE_VSHL_immi16)
  .value("MVE_VSHL_immi32", LIEF::assembly::arm::OPCODE::MVE_VSHL_immi32)
  .value("MVE_VSHL_immi8", LIEF::assembly::arm::OPCODE::MVE_VSHL_immi8)
  .value("MVE_VSHL_qrs16", LIEF::assembly::arm::OPCODE::MVE_VSHL_qrs16)
  .value("MVE_VSHL_qrs32", LIEF::assembly::arm::OPCODE::MVE_VSHL_qrs32)
  .value("MVE_VSHL_qrs8", LIEF::assembly::arm::OPCODE::MVE_VSHL_qrs8)
  .value("MVE_VSHL_qru16", LIEF::assembly::arm::OPCODE::MVE_VSHL_qru16)
  .value("MVE_VSHL_qru32", LIEF::assembly::arm::OPCODE::MVE_VSHL_qru32)
  .value("MVE_VSHL_qru8", LIEF::assembly::arm::OPCODE::MVE_VSHL_qru8)
  .value("MVE_VSHRNi16bh", LIEF::assembly::arm::OPCODE::MVE_VSHRNi16bh)
  .value("MVE_VSHRNi16th", LIEF::assembly::arm::OPCODE::MVE_VSHRNi16th)
  .value("MVE_VSHRNi32bh", LIEF::assembly::arm::OPCODE::MVE_VSHRNi32bh)
  .value("MVE_VSHRNi32th", LIEF::assembly::arm::OPCODE::MVE_VSHRNi32th)
  .value("MVE_VSHR_imms16", LIEF::assembly::arm::OPCODE::MVE_VSHR_imms16)
  .value("MVE_VSHR_imms32", LIEF::assembly::arm::OPCODE::MVE_VSHR_imms32)
  .value("MVE_VSHR_imms8", LIEF::assembly::arm::OPCODE::MVE_VSHR_imms8)
  .value("MVE_VSHR_immu16", LIEF::assembly::arm::OPCODE::MVE_VSHR_immu16)
  .value("MVE_VSHR_immu32", LIEF::assembly::arm::OPCODE::MVE_VSHR_immu32)
  .value("MVE_VSHR_immu8", LIEF::assembly::arm::OPCODE::MVE_VSHR_immu8)
  .value("MVE_VSLIimm16", LIEF::assembly::arm::OPCODE::MVE_VSLIimm16)
  .value("MVE_VSLIimm32", LIEF::assembly::arm::OPCODE::MVE_VSLIimm32)
  .value("MVE_VSLIimm8", LIEF::assembly::arm::OPCODE::MVE_VSLIimm8)
  .value("MVE_VSRIimm16", LIEF::assembly::arm::OPCODE::MVE_VSRIimm16)
  .value("MVE_VSRIimm32", LIEF::assembly::arm::OPCODE::MVE_VSRIimm32)
  .value("MVE_VSRIimm8", LIEF::assembly::arm::OPCODE::MVE_VSRIimm8)
  .value("MVE_VST20_16", LIEF::assembly::arm::OPCODE::MVE_VST20_16)
  .value("MVE_VST20_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST20_16_wb)
  .value("MVE_VST20_32", LIEF::assembly::arm::OPCODE::MVE_VST20_32)
  .value("MVE_VST20_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST20_32_wb)
  .value("MVE_VST20_8", LIEF::assembly::arm::OPCODE::MVE_VST20_8)
  .value("MVE_VST20_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST20_8_wb)
  .value("MVE_VST21_16", LIEF::assembly::arm::OPCODE::MVE_VST21_16)
  .value("MVE_VST21_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST21_16_wb)
  .value("MVE_VST21_32", LIEF::assembly::arm::OPCODE::MVE_VST21_32)
  .value("MVE_VST21_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST21_32_wb)
  .value("MVE_VST21_8", LIEF::assembly::arm::OPCODE::MVE_VST21_8)
  .value("MVE_VST21_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST21_8_wb)
  .value("MVE_VST40_16", LIEF::assembly::arm::OPCODE::MVE_VST40_16)
  .value("MVE_VST40_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST40_16_wb)
  .value("MVE_VST40_32", LIEF::assembly::arm::OPCODE::MVE_VST40_32)
  .value("MVE_VST40_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST40_32_wb)
  .value("MVE_VST40_8", LIEF::assembly::arm::OPCODE::MVE_VST40_8)
  .value("MVE_VST40_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST40_8_wb)
  .value("MVE_VST41_16", LIEF::assembly::arm::OPCODE::MVE_VST41_16)
  .value("MVE_VST41_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST41_16_wb)
  .value("MVE_VST41_32", LIEF::assembly::arm::OPCODE::MVE_VST41_32)
  .value("MVE_VST41_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST41_32_wb)
  .value("MVE_VST41_8", LIEF::assembly::arm::OPCODE::MVE_VST41_8)
  .value("MVE_VST41_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST41_8_wb)
  .value("MVE_VST42_16", LIEF::assembly::arm::OPCODE::MVE_VST42_16)
  .value("MVE_VST42_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST42_16_wb)
  .value("MVE_VST42_32", LIEF::assembly::arm::OPCODE::MVE_VST42_32)
  .value("MVE_VST42_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST42_32_wb)
  .value("MVE_VST42_8", LIEF::assembly::arm::OPCODE::MVE_VST42_8)
  .value("MVE_VST42_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST42_8_wb)
  .value("MVE_VST43_16", LIEF::assembly::arm::OPCODE::MVE_VST43_16)
  .value("MVE_VST43_16_wb", LIEF::assembly::arm::OPCODE::MVE_VST43_16_wb)
  .value("MVE_VST43_32", LIEF::assembly::arm::OPCODE::MVE_VST43_32)
  .value("MVE_VST43_32_wb", LIEF::assembly::arm::OPCODE::MVE_VST43_32_wb)
  .value("MVE_VST43_8", LIEF::assembly::arm::OPCODE::MVE_VST43_8)
  .value("MVE_VST43_8_wb", LIEF::assembly::arm::OPCODE::MVE_VST43_8_wb)
  .value("MVE_VSTRB16", LIEF::assembly::arm::OPCODE::MVE_VSTRB16)
  .value("MVE_VSTRB16_post", LIEF::assembly::arm::OPCODE::MVE_VSTRB16_post)
  .value("MVE_VSTRB16_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRB16_pre)
  .value("MVE_VSTRB16_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRB16_rq)
  .value("MVE_VSTRB32", LIEF::assembly::arm::OPCODE::MVE_VSTRB32)
  .value("MVE_VSTRB32_post", LIEF::assembly::arm::OPCODE::MVE_VSTRB32_post)
  .value("MVE_VSTRB32_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRB32_pre)
  .value("MVE_VSTRB32_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRB32_rq)
  .value("MVE_VSTRB8_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRB8_rq)
  .value("MVE_VSTRBU8", LIEF::assembly::arm::OPCODE::MVE_VSTRBU8)
  .value("MVE_VSTRBU8_post", LIEF::assembly::arm::OPCODE::MVE_VSTRBU8_post)
  .value("MVE_VSTRBU8_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRBU8_pre)
  .value("MVE_VSTRD64_qi", LIEF::assembly::arm::OPCODE::MVE_VSTRD64_qi);
  opcodes.value("MVE_VSTRD64_qi_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRD64_qi_pre)
  .value("MVE_VSTRD64_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRD64_rq)
  .value("MVE_VSTRD64_rq_u", LIEF::assembly::arm::OPCODE::MVE_VSTRD64_rq_u)
  .value("MVE_VSTRH16_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRH16_rq)
  .value("MVE_VSTRH16_rq_u", LIEF::assembly::arm::OPCODE::MVE_VSTRH16_rq_u)
  .value("MVE_VSTRH32", LIEF::assembly::arm::OPCODE::MVE_VSTRH32)
  .value("MVE_VSTRH32_post", LIEF::assembly::arm::OPCODE::MVE_VSTRH32_post)
  .value("MVE_VSTRH32_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRH32_pre)
  .value("MVE_VSTRH32_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRH32_rq)
  .value("MVE_VSTRH32_rq_u", LIEF::assembly::arm::OPCODE::MVE_VSTRH32_rq_u)
  .value("MVE_VSTRHU16", LIEF::assembly::arm::OPCODE::MVE_VSTRHU16)
  .value("MVE_VSTRHU16_post", LIEF::assembly::arm::OPCODE::MVE_VSTRHU16_post)
  .value("MVE_VSTRHU16_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRHU16_pre)
  .value("MVE_VSTRW32_qi", LIEF::assembly::arm::OPCODE::MVE_VSTRW32_qi)
  .value("MVE_VSTRW32_qi_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRW32_qi_pre)
  .value("MVE_VSTRW32_rq", LIEF::assembly::arm::OPCODE::MVE_VSTRW32_rq)
  .value("MVE_VSTRW32_rq_u", LIEF::assembly::arm::OPCODE::MVE_VSTRW32_rq_u)
  .value("MVE_VSTRWU32", LIEF::assembly::arm::OPCODE::MVE_VSTRWU32)
  .value("MVE_VSTRWU32_post", LIEF::assembly::arm::OPCODE::MVE_VSTRWU32_post)
  .value("MVE_VSTRWU32_pre", LIEF::assembly::arm::OPCODE::MVE_VSTRWU32_pre)
  .value("MVE_VSUB_qr_f16", LIEF::assembly::arm::OPCODE::MVE_VSUB_qr_f16)
  .value("MVE_VSUB_qr_f32", LIEF::assembly::arm::OPCODE::MVE_VSUB_qr_f32)
  .value("MVE_VSUB_qr_i16", LIEF::assembly::arm::OPCODE::MVE_VSUB_qr_i16)
  .value("MVE_VSUB_qr_i32", LIEF::assembly::arm::OPCODE::MVE_VSUB_qr_i32)
  .value("MVE_VSUB_qr_i8", LIEF::assembly::arm::OPCODE::MVE_VSUB_qr_i8)
  .value("MVE_VSUBf16", LIEF::assembly::arm::OPCODE::MVE_VSUBf16)
  .value("MVE_VSUBf32", LIEF::assembly::arm::OPCODE::MVE_VSUBf32)
  .value("MVE_VSUBi16", LIEF::assembly::arm::OPCODE::MVE_VSUBi16)
  .value("MVE_VSUBi32", LIEF::assembly::arm::OPCODE::MVE_VSUBi32)
  .value("MVE_VSUBi8", LIEF::assembly::arm::OPCODE::MVE_VSUBi8)
  .value("MVE_WLSTP_16", LIEF::assembly::arm::OPCODE::MVE_WLSTP_16)
  .value("MVE_WLSTP_32", LIEF::assembly::arm::OPCODE::MVE_WLSTP_32)
  .value("MVE_WLSTP_64", LIEF::assembly::arm::OPCODE::MVE_WLSTP_64)
  .value("MVE_WLSTP_8", LIEF::assembly::arm::OPCODE::MVE_WLSTP_8)
  .value("MVNi", LIEF::assembly::arm::OPCODE::MVNi)
  .value("MVNr", LIEF::assembly::arm::OPCODE::MVNr)
  .value("MVNsi", LIEF::assembly::arm::OPCODE::MVNsi)
  .value("MVNsr", LIEF::assembly::arm::OPCODE::MVNsr)
  .value("NEON_VMAXNMNDf", LIEF::assembly::arm::OPCODE::NEON_VMAXNMNDf)
  .value("NEON_VMAXNMNDh", LIEF::assembly::arm::OPCODE::NEON_VMAXNMNDh)
  .value("NEON_VMAXNMNQf", LIEF::assembly::arm::OPCODE::NEON_VMAXNMNQf)
  .value("NEON_VMAXNMNQh", LIEF::assembly::arm::OPCODE::NEON_VMAXNMNQh)
  .value("NEON_VMINNMNDf", LIEF::assembly::arm::OPCODE::NEON_VMINNMNDf)
  .value("NEON_VMINNMNDh", LIEF::assembly::arm::OPCODE::NEON_VMINNMNDh)
  .value("NEON_VMINNMNQf", LIEF::assembly::arm::OPCODE::NEON_VMINNMNQf)
  .value("NEON_VMINNMNQh", LIEF::assembly::arm::OPCODE::NEON_VMINNMNQh)
  .value("ORRri", LIEF::assembly::arm::OPCODE::ORRri)
  .value("ORRrr", LIEF::assembly::arm::OPCODE::ORRrr)
  .value("ORRrsi", LIEF::assembly::arm::OPCODE::ORRrsi)
  .value("ORRrsr", LIEF::assembly::arm::OPCODE::ORRrsr)
  .value("PKHBT", LIEF::assembly::arm::OPCODE::PKHBT)
  .value("PKHTB", LIEF::assembly::arm::OPCODE::PKHTB)
  .value("PLDWi12", LIEF::assembly::arm::OPCODE::PLDWi12)
  .value("PLDWrs", LIEF::assembly::arm::OPCODE::PLDWrs)
  .value("PLDi12", LIEF::assembly::arm::OPCODE::PLDi12)
  .value("PLDrs", LIEF::assembly::arm::OPCODE::PLDrs)
  .value("PLIi12", LIEF::assembly::arm::OPCODE::PLIi12)
  .value("PLIrs", LIEF::assembly::arm::OPCODE::PLIrs)
  .value("QADD", LIEF::assembly::arm::OPCODE::QADD)
  .value("QADD16", LIEF::assembly::arm::OPCODE::QADD16)
  .value("QADD8", LIEF::assembly::arm::OPCODE::QADD8)
  .value("QASX", LIEF::assembly::arm::OPCODE::QASX)
  .value("QDADD", LIEF::assembly::arm::OPCODE::QDADD)
  .value("QDSUB", LIEF::assembly::arm::OPCODE::QDSUB)
  .value("QSAX", LIEF::assembly::arm::OPCODE::QSAX)
  .value("QSUB", LIEF::assembly::arm::OPCODE::QSUB)
  .value("QSUB16", LIEF::assembly::arm::OPCODE::QSUB16)
  .value("QSUB8", LIEF::assembly::arm::OPCODE::QSUB8)
  .value("RBIT", LIEF::assembly::arm::OPCODE::RBIT)
  .value("REV", LIEF::assembly::arm::OPCODE::REV)
  .value("REV16", LIEF::assembly::arm::OPCODE::REV16)
  .value("REVSH", LIEF::assembly::arm::OPCODE::REVSH)
  .value("RFEDA", LIEF::assembly::arm::OPCODE::RFEDA)
  .value("RFEDA_UPD", LIEF::assembly::arm::OPCODE::RFEDA_UPD)
  .value("RFEDB", LIEF::assembly::arm::OPCODE::RFEDB)
  .value("RFEDB_UPD", LIEF::assembly::arm::OPCODE::RFEDB_UPD)
  .value("RFEIA", LIEF::assembly::arm::OPCODE::RFEIA)
  .value("RFEIA_UPD", LIEF::assembly::arm::OPCODE::RFEIA_UPD)
  .value("RFEIB", LIEF::assembly::arm::OPCODE::RFEIB)
  .value("RFEIB_UPD", LIEF::assembly::arm::OPCODE::RFEIB_UPD)
  .value("RSBri", LIEF::assembly::arm::OPCODE::RSBri)
  .value("RSBrr", LIEF::assembly::arm::OPCODE::RSBrr)
  .value("RSBrsi", LIEF::assembly::arm::OPCODE::RSBrsi)
  .value("RSBrsr", LIEF::assembly::arm::OPCODE::RSBrsr)
  .value("RSCri", LIEF::assembly::arm::OPCODE::RSCri)
  .value("RSCrr", LIEF::assembly::arm::OPCODE::RSCrr)
  .value("RSCrsi", LIEF::assembly::arm::OPCODE::RSCrsi)
  .value("RSCrsr", LIEF::assembly::arm::OPCODE::RSCrsr)
  .value("SADD16", LIEF::assembly::arm::OPCODE::SADD16)
  .value("SADD8", LIEF::assembly::arm::OPCODE::SADD8)
  .value("SASX", LIEF::assembly::arm::OPCODE::SASX)
  .value("SB", LIEF::assembly::arm::OPCODE::SB)
  .value("SBCri", LIEF::assembly::arm::OPCODE::SBCri)
  .value("SBCrr", LIEF::assembly::arm::OPCODE::SBCrr)
  .value("SBCrsi", LIEF::assembly::arm::OPCODE::SBCrsi)
  .value("SBCrsr", LIEF::assembly::arm::OPCODE::SBCrsr)
  .value("SBFX", LIEF::assembly::arm::OPCODE::SBFX)
  .value("SDIV", LIEF::assembly::arm::OPCODE::SDIV)
  .value("SEL", LIEF::assembly::arm::OPCODE::SEL)
  .value("SETEND", LIEF::assembly::arm::OPCODE::SETEND)
  .value("SETPAN", LIEF::assembly::arm::OPCODE::SETPAN)
  .value("SHA1C", LIEF::assembly::arm::OPCODE::SHA1C)
  .value("SHA1H", LIEF::assembly::arm::OPCODE::SHA1H)
  .value("SHA1M", LIEF::assembly::arm::OPCODE::SHA1M)
  .value("SHA1P", LIEF::assembly::arm::OPCODE::SHA1P)
  .value("SHA1SU0", LIEF::assembly::arm::OPCODE::SHA1SU0)
  .value("SHA1SU1", LIEF::assembly::arm::OPCODE::SHA1SU1)
  .value("SHA256H", LIEF::assembly::arm::OPCODE::SHA256H)
  .value("SHA256H2", LIEF::assembly::arm::OPCODE::SHA256H2)
  .value("SHA256SU0", LIEF::assembly::arm::OPCODE::SHA256SU0)
  .value("SHA256SU1", LIEF::assembly::arm::OPCODE::SHA256SU1)
  .value("SHADD16", LIEF::assembly::arm::OPCODE::SHADD16)
  .value("SHADD8", LIEF::assembly::arm::OPCODE::SHADD8)
  .value("SHASX", LIEF::assembly::arm::OPCODE::SHASX)
  .value("SHSAX", LIEF::assembly::arm::OPCODE::SHSAX)
  .value("SHSUB16", LIEF::assembly::arm::OPCODE::SHSUB16)
  .value("SHSUB8", LIEF::assembly::arm::OPCODE::SHSUB8)
  .value("SMC", LIEF::assembly::arm::OPCODE::SMC)
  .value("SMLABB", LIEF::assembly::arm::OPCODE::SMLABB)
  .value("SMLABT", LIEF::assembly::arm::OPCODE::SMLABT)
  .value("SMLAD", LIEF::assembly::arm::OPCODE::SMLAD)
  .value("SMLADX", LIEF::assembly::arm::OPCODE::SMLADX)
  .value("SMLAL", LIEF::assembly::arm::OPCODE::SMLAL)
  .value("SMLALBB", LIEF::assembly::arm::OPCODE::SMLALBB)
  .value("SMLALBT", LIEF::assembly::arm::OPCODE::SMLALBT)
  .value("SMLALD", LIEF::assembly::arm::OPCODE::SMLALD)
  .value("SMLALDX", LIEF::assembly::arm::OPCODE::SMLALDX)
  .value("SMLALTB", LIEF::assembly::arm::OPCODE::SMLALTB)
  .value("SMLALTT", LIEF::assembly::arm::OPCODE::SMLALTT)
  .value("SMLATB", LIEF::assembly::arm::OPCODE::SMLATB)
  .value("SMLATT", LIEF::assembly::arm::OPCODE::SMLATT)
  .value("SMLAWB", LIEF::assembly::arm::OPCODE::SMLAWB)
  .value("SMLAWT", LIEF::assembly::arm::OPCODE::SMLAWT)
  .value("SMLSD", LIEF::assembly::arm::OPCODE::SMLSD)
  .value("SMLSDX", LIEF::assembly::arm::OPCODE::SMLSDX)
  .value("SMLSLD", LIEF::assembly::arm::OPCODE::SMLSLD)
  .value("SMLSLDX", LIEF::assembly::arm::OPCODE::SMLSLDX)
  .value("SMMLA", LIEF::assembly::arm::OPCODE::SMMLA)
  .value("SMMLAR", LIEF::assembly::arm::OPCODE::SMMLAR)
  .value("SMMLS", LIEF::assembly::arm::OPCODE::SMMLS)
  .value("SMMLSR", LIEF::assembly::arm::OPCODE::SMMLSR)
  .value("SMMUL", LIEF::assembly::arm::OPCODE::SMMUL)
  .value("SMMULR", LIEF::assembly::arm::OPCODE::SMMULR)
  .value("SMUAD", LIEF::assembly::arm::OPCODE::SMUAD)
  .value("SMUADX", LIEF::assembly::arm::OPCODE::SMUADX)
  .value("SMULBB", LIEF::assembly::arm::OPCODE::SMULBB)
  .value("SMULBT", LIEF::assembly::arm::OPCODE::SMULBT)
  .value("SMULL", LIEF::assembly::arm::OPCODE::SMULL)
  .value("SMULTB", LIEF::assembly::arm::OPCODE::SMULTB)
  .value("SMULTT", LIEF::assembly::arm::OPCODE::SMULTT)
  .value("SMULWB", LIEF::assembly::arm::OPCODE::SMULWB)
  .value("SMULWT", LIEF::assembly::arm::OPCODE::SMULWT)
  .value("SMUSD", LIEF::assembly::arm::OPCODE::SMUSD)
  .value("SMUSDX", LIEF::assembly::arm::OPCODE::SMUSDX)
  .value("SRSDA", LIEF::assembly::arm::OPCODE::SRSDA)
  .value("SRSDA_UPD", LIEF::assembly::arm::OPCODE::SRSDA_UPD)
  .value("SRSDB", LIEF::assembly::arm::OPCODE::SRSDB)
  .value("SRSDB_UPD", LIEF::assembly::arm::OPCODE::SRSDB_UPD)
  .value("SRSIA", LIEF::assembly::arm::OPCODE::SRSIA)
  .value("SRSIA_UPD", LIEF::assembly::arm::OPCODE::SRSIA_UPD)
  .value("SRSIB", LIEF::assembly::arm::OPCODE::SRSIB)
  .value("SRSIB_UPD", LIEF::assembly::arm::OPCODE::SRSIB_UPD)
  .value("SSAT", LIEF::assembly::arm::OPCODE::SSAT)
  .value("SSAT16", LIEF::assembly::arm::OPCODE::SSAT16)
  .value("SSAX", LIEF::assembly::arm::OPCODE::SSAX)
  .value("SSUB16", LIEF::assembly::arm::OPCODE::SSUB16)
  .value("SSUB8", LIEF::assembly::arm::OPCODE::SSUB8)
  .value("STC2L_OFFSET", LIEF::assembly::arm::OPCODE::STC2L_OFFSET)
  .value("STC2L_OPTION", LIEF::assembly::arm::OPCODE::STC2L_OPTION)
  .value("STC2L_POST", LIEF::assembly::arm::OPCODE::STC2L_POST)
  .value("STC2L_PRE", LIEF::assembly::arm::OPCODE::STC2L_PRE)
  .value("STC2_OFFSET", LIEF::assembly::arm::OPCODE::STC2_OFFSET)
  .value("STC2_OPTION", LIEF::assembly::arm::OPCODE::STC2_OPTION)
  .value("STC2_POST", LIEF::assembly::arm::OPCODE::STC2_POST)
  .value("STC2_PRE", LIEF::assembly::arm::OPCODE::STC2_PRE)
  .value("STCL_OFFSET", LIEF::assembly::arm::OPCODE::STCL_OFFSET)
  .value("STCL_OPTION", LIEF::assembly::arm::OPCODE::STCL_OPTION)
  .value("STCL_POST", LIEF::assembly::arm::OPCODE::STCL_POST)
  .value("STCL_PRE", LIEF::assembly::arm::OPCODE::STCL_PRE)
  .value("STC_OFFSET", LIEF::assembly::arm::OPCODE::STC_OFFSET)
  .value("STC_OPTION", LIEF::assembly::arm::OPCODE::STC_OPTION)
  .value("STC_POST", LIEF::assembly::arm::OPCODE::STC_POST)
  .value("STC_PRE", LIEF::assembly::arm::OPCODE::STC_PRE)
  .value("STL", LIEF::assembly::arm::OPCODE::STL)
  .value("STLB", LIEF::assembly::arm::OPCODE::STLB)
  .value("STLEX", LIEF::assembly::arm::OPCODE::STLEX)
  .value("STLEXB", LIEF::assembly::arm::OPCODE::STLEXB)
  .value("STLEXD", LIEF::assembly::arm::OPCODE::STLEXD)
  .value("STLEXH", LIEF::assembly::arm::OPCODE::STLEXH)
  .value("STLH", LIEF::assembly::arm::OPCODE::STLH)
  .value("STMDA", LIEF::assembly::arm::OPCODE::STMDA)
  .value("STMDA_UPD", LIEF::assembly::arm::OPCODE::STMDA_UPD)
  .value("STMDB", LIEF::assembly::arm::OPCODE::STMDB)
  .value("STMDB_UPD", LIEF::assembly::arm::OPCODE::STMDB_UPD)
  .value("STMIA", LIEF::assembly::arm::OPCODE::STMIA)
  .value("STMIA_UPD", LIEF::assembly::arm::OPCODE::STMIA_UPD)
  .value("STMIB", LIEF::assembly::arm::OPCODE::STMIB)
  .value("STMIB_UPD", LIEF::assembly::arm::OPCODE::STMIB_UPD)
  .value("STRBT_POST_IMM", LIEF::assembly::arm::OPCODE::STRBT_POST_IMM)
  .value("STRBT_POST_REG", LIEF::assembly::arm::OPCODE::STRBT_POST_REG)
  .value("STRB_POST_IMM", LIEF::assembly::arm::OPCODE::STRB_POST_IMM)
  .value("STRB_POST_REG", LIEF::assembly::arm::OPCODE::STRB_POST_REG)
  .value("STRB_PRE_IMM", LIEF::assembly::arm::OPCODE::STRB_PRE_IMM)
  .value("STRB_PRE_REG", LIEF::assembly::arm::OPCODE::STRB_PRE_REG)
  .value("STRBi12", LIEF::assembly::arm::OPCODE::STRBi12)
  .value("STRBrs", LIEF::assembly::arm::OPCODE::STRBrs)
  .value("STRD", LIEF::assembly::arm::OPCODE::STRD)
  .value("STRD_POST", LIEF::assembly::arm::OPCODE::STRD_POST)
  .value("STRD_PRE", LIEF::assembly::arm::OPCODE::STRD_PRE)
  .value("STREX", LIEF::assembly::arm::OPCODE::STREX)
  .value("STREXB", LIEF::assembly::arm::OPCODE::STREXB)
  .value("STREXD", LIEF::assembly::arm::OPCODE::STREXD)
  .value("STREXH", LIEF::assembly::arm::OPCODE::STREXH)
  .value("STRH", LIEF::assembly::arm::OPCODE::STRH)
  .value("STRHTi", LIEF::assembly::arm::OPCODE::STRHTi)
  .value("STRHTr", LIEF::assembly::arm::OPCODE::STRHTr)
  .value("STRH_POST", LIEF::assembly::arm::OPCODE::STRH_POST)
  .value("STRH_PRE", LIEF::assembly::arm::OPCODE::STRH_PRE)
  .value("STRT_POST_IMM", LIEF::assembly::arm::OPCODE::STRT_POST_IMM)
  .value("STRT_POST_REG", LIEF::assembly::arm::OPCODE::STRT_POST_REG)
  .value("STR_POST_IMM", LIEF::assembly::arm::OPCODE::STR_POST_IMM)
  .value("STR_POST_REG", LIEF::assembly::arm::OPCODE::STR_POST_REG)
  .value("STR_PRE_IMM", LIEF::assembly::arm::OPCODE::STR_PRE_IMM)
  .value("STR_PRE_REG", LIEF::assembly::arm::OPCODE::STR_PRE_REG)
  .value("STRi12", LIEF::assembly::arm::OPCODE::STRi12)
  .value("STRrs", LIEF::assembly::arm::OPCODE::STRrs)
  .value("SUBri", LIEF::assembly::arm::OPCODE::SUBri)
  .value("SUBrr", LIEF::assembly::arm::OPCODE::SUBrr)
  .value("SUBrsi", LIEF::assembly::arm::OPCODE::SUBrsi)
  .value("SUBrsr", LIEF::assembly::arm::OPCODE::SUBrsr)
  .value("SVC", LIEF::assembly::arm::OPCODE::SVC)
  .value("SWP", LIEF::assembly::arm::OPCODE::SWP)
  .value("SWPB", LIEF::assembly::arm::OPCODE::SWPB)
  .value("SXTAB", LIEF::assembly::arm::OPCODE::SXTAB)
  .value("SXTAB16", LIEF::assembly::arm::OPCODE::SXTAB16)
  .value("SXTAH", LIEF::assembly::arm::OPCODE::SXTAH)
  .value("SXTB", LIEF::assembly::arm::OPCODE::SXTB)
  .value("SXTB16", LIEF::assembly::arm::OPCODE::SXTB16)
  .value("SXTH", LIEF::assembly::arm::OPCODE::SXTH)
  .value("TEQri", LIEF::assembly::arm::OPCODE::TEQri)
  .value("TEQrr", LIEF::assembly::arm::OPCODE::TEQrr)
  .value("TEQrsi", LIEF::assembly::arm::OPCODE::TEQrsi)
  .value("TEQrsr", LIEF::assembly::arm::OPCODE::TEQrsr)
  .value("TRAP", LIEF::assembly::arm::OPCODE::TRAP)
  .value("TRAPNaCl", LIEF::assembly::arm::OPCODE::TRAPNaCl)
  .value("TSB", LIEF::assembly::arm::OPCODE::TSB)
  .value("TSTri", LIEF::assembly::arm::OPCODE::TSTri)
  .value("TSTrr", LIEF::assembly::arm::OPCODE::TSTrr)
  .value("TSTrsi", LIEF::assembly::arm::OPCODE::TSTrsi)
  .value("TSTrsr", LIEF::assembly::arm::OPCODE::TSTrsr)
  .value("UADD16", LIEF::assembly::arm::OPCODE::UADD16)
  .value("UADD8", LIEF::assembly::arm::OPCODE::UADD8)
  .value("UASX", LIEF::assembly::arm::OPCODE::UASX)
  .value("UBFX", LIEF::assembly::arm::OPCODE::UBFX)
  .value("UDF", LIEF::assembly::arm::OPCODE::UDF)
  .value("UDIV", LIEF::assembly::arm::OPCODE::UDIV)
  .value("UHADD16", LIEF::assembly::arm::OPCODE::UHADD16)
  .value("UHADD8", LIEF::assembly::arm::OPCODE::UHADD8)
  .value("UHASX", LIEF::assembly::arm::OPCODE::UHASX)
  .value("UHSAX", LIEF::assembly::arm::OPCODE::UHSAX)
  .value("UHSUB16", LIEF::assembly::arm::OPCODE::UHSUB16)
  .value("UHSUB8", LIEF::assembly::arm::OPCODE::UHSUB8)
  .value("UMAAL", LIEF::assembly::arm::OPCODE::UMAAL)
  .value("UMLAL", LIEF::assembly::arm::OPCODE::UMLAL)
  .value("UMULL", LIEF::assembly::arm::OPCODE::UMULL)
  .value("UQADD16", LIEF::assembly::arm::OPCODE::UQADD16)
  .value("UQADD8", LIEF::assembly::arm::OPCODE::UQADD8)
  .value("UQASX", LIEF::assembly::arm::OPCODE::UQASX)
  .value("UQSAX", LIEF::assembly::arm::OPCODE::UQSAX)
  .value("UQSUB16", LIEF::assembly::arm::OPCODE::UQSUB16)
  .value("UQSUB8", LIEF::assembly::arm::OPCODE::UQSUB8)
  .value("USAD8", LIEF::assembly::arm::OPCODE::USAD8)
  .value("USADA8", LIEF::assembly::arm::OPCODE::USADA8)
  .value("USAT", LIEF::assembly::arm::OPCODE::USAT)
  .value("USAT16", LIEF::assembly::arm::OPCODE::USAT16)
  .value("USAX", LIEF::assembly::arm::OPCODE::USAX)
  .value("USUB16", LIEF::assembly::arm::OPCODE::USUB16)
  .value("USUB8", LIEF::assembly::arm::OPCODE::USUB8)
  .value("UXTAB", LIEF::assembly::arm::OPCODE::UXTAB)
  .value("UXTAB16", LIEF::assembly::arm::OPCODE::UXTAB16)
  .value("UXTAH", LIEF::assembly::arm::OPCODE::UXTAH)
  .value("UXTB", LIEF::assembly::arm::OPCODE::UXTB)
  .value("UXTB16", LIEF::assembly::arm::OPCODE::UXTB16)
  .value("UXTH", LIEF::assembly::arm::OPCODE::UXTH)
  .value("VABALsv2i64", LIEF::assembly::arm::OPCODE::VABALsv2i64)
  .value("VABALsv4i32", LIEF::assembly::arm::OPCODE::VABALsv4i32)
  .value("VABALsv8i16", LIEF::assembly::arm::OPCODE::VABALsv8i16)
  .value("VABALuv2i64", LIEF::assembly::arm::OPCODE::VABALuv2i64)
  .value("VABALuv4i32", LIEF::assembly::arm::OPCODE::VABALuv4i32)
  .value("VABALuv8i16", LIEF::assembly::arm::OPCODE::VABALuv8i16)
  .value("VABAsv16i8", LIEF::assembly::arm::OPCODE::VABAsv16i8)
  .value("VABAsv2i32", LIEF::assembly::arm::OPCODE::VABAsv2i32)
  .value("VABAsv4i16", LIEF::assembly::arm::OPCODE::VABAsv4i16)
  .value("VABAsv4i32", LIEF::assembly::arm::OPCODE::VABAsv4i32)
  .value("VABAsv8i16", LIEF::assembly::arm::OPCODE::VABAsv8i16)
  .value("VABAsv8i8", LIEF::assembly::arm::OPCODE::VABAsv8i8)
  .value("VABAuv16i8", LIEF::assembly::arm::OPCODE::VABAuv16i8)
  .value("VABAuv2i32", LIEF::assembly::arm::OPCODE::VABAuv2i32)
  .value("VABAuv4i16", LIEF::assembly::arm::OPCODE::VABAuv4i16)
  .value("VABAuv4i32", LIEF::assembly::arm::OPCODE::VABAuv4i32);
  opcodes.value("VABAuv8i16", LIEF::assembly::arm::OPCODE::VABAuv8i16)
  .value("VABAuv8i8", LIEF::assembly::arm::OPCODE::VABAuv8i8)
  .value("VABDLsv2i64", LIEF::assembly::arm::OPCODE::VABDLsv2i64)
  .value("VABDLsv4i32", LIEF::assembly::arm::OPCODE::VABDLsv4i32)
  .value("VABDLsv8i16", LIEF::assembly::arm::OPCODE::VABDLsv8i16)
  .value("VABDLuv2i64", LIEF::assembly::arm::OPCODE::VABDLuv2i64)
  .value("VABDLuv4i32", LIEF::assembly::arm::OPCODE::VABDLuv4i32)
  .value("VABDLuv8i16", LIEF::assembly::arm::OPCODE::VABDLuv8i16)
  .value("VABDfd", LIEF::assembly::arm::OPCODE::VABDfd)
  .value("VABDfq", LIEF::assembly::arm::OPCODE::VABDfq)
  .value("VABDhd", LIEF::assembly::arm::OPCODE::VABDhd)
  .value("VABDhq", LIEF::assembly::arm::OPCODE::VABDhq)
  .value("VABDsv16i8", LIEF::assembly::arm::OPCODE::VABDsv16i8)
  .value("VABDsv2i32", LIEF::assembly::arm::OPCODE::VABDsv2i32)
  .value("VABDsv4i16", LIEF::assembly::arm::OPCODE::VABDsv4i16)
  .value("VABDsv4i32", LIEF::assembly::arm::OPCODE::VABDsv4i32)
  .value("VABDsv8i16", LIEF::assembly::arm::OPCODE::VABDsv8i16)
  .value("VABDsv8i8", LIEF::assembly::arm::OPCODE::VABDsv8i8)
  .value("VABDuv16i8", LIEF::assembly::arm::OPCODE::VABDuv16i8)
  .value("VABDuv2i32", LIEF::assembly::arm::OPCODE::VABDuv2i32)
  .value("VABDuv4i16", LIEF::assembly::arm::OPCODE::VABDuv4i16)
  .value("VABDuv4i32", LIEF::assembly::arm::OPCODE::VABDuv4i32)
  .value("VABDuv8i16", LIEF::assembly::arm::OPCODE::VABDuv8i16)
  .value("VABDuv8i8", LIEF::assembly::arm::OPCODE::VABDuv8i8)
  .value("VABSD", LIEF::assembly::arm::OPCODE::VABSD)
  .value("VABSH", LIEF::assembly::arm::OPCODE::VABSH)
  .value("VABSS", LIEF::assembly::arm::OPCODE::VABSS)
  .value("VABSfd", LIEF::assembly::arm::OPCODE::VABSfd)
  .value("VABSfq", LIEF::assembly::arm::OPCODE::VABSfq)
  .value("VABShd", LIEF::assembly::arm::OPCODE::VABShd)
  .value("VABShq", LIEF::assembly::arm::OPCODE::VABShq)
  .value("VABSv16i8", LIEF::assembly::arm::OPCODE::VABSv16i8)
  .value("VABSv2i32", LIEF::assembly::arm::OPCODE::VABSv2i32)
  .value("VABSv4i16", LIEF::assembly::arm::OPCODE::VABSv4i16)
  .value("VABSv4i32", LIEF::assembly::arm::OPCODE::VABSv4i32)
  .value("VABSv8i16", LIEF::assembly::arm::OPCODE::VABSv8i16)
  .value("VABSv8i8", LIEF::assembly::arm::OPCODE::VABSv8i8)
  .value("VACGEfd", LIEF::assembly::arm::OPCODE::VACGEfd)
  .value("VACGEfq", LIEF::assembly::arm::OPCODE::VACGEfq)
  .value("VACGEhd", LIEF::assembly::arm::OPCODE::VACGEhd)
  .value("VACGEhq", LIEF::assembly::arm::OPCODE::VACGEhq)
  .value("VACGTfd", LIEF::assembly::arm::OPCODE::VACGTfd)
  .value("VACGTfq", LIEF::assembly::arm::OPCODE::VACGTfq)
  .value("VACGThd", LIEF::assembly::arm::OPCODE::VACGThd)
  .value("VACGThq", LIEF::assembly::arm::OPCODE::VACGThq)
  .value("VADDD", LIEF::assembly::arm::OPCODE::VADDD)
  .value("VADDH", LIEF::assembly::arm::OPCODE::VADDH)
  .value("VADDHNv2i32", LIEF::assembly::arm::OPCODE::VADDHNv2i32)
  .value("VADDHNv4i16", LIEF::assembly::arm::OPCODE::VADDHNv4i16)
  .value("VADDHNv8i8", LIEF::assembly::arm::OPCODE::VADDHNv8i8)
  .value("VADDLsv2i64", LIEF::assembly::arm::OPCODE::VADDLsv2i64)
  .value("VADDLsv4i32", LIEF::assembly::arm::OPCODE::VADDLsv4i32)
  .value("VADDLsv8i16", LIEF::assembly::arm::OPCODE::VADDLsv8i16)
  .value("VADDLuv2i64", LIEF::assembly::arm::OPCODE::VADDLuv2i64)
  .value("VADDLuv4i32", LIEF::assembly::arm::OPCODE::VADDLuv4i32)
  .value("VADDLuv8i16", LIEF::assembly::arm::OPCODE::VADDLuv8i16)
  .value("VADDS", LIEF::assembly::arm::OPCODE::VADDS)
  .value("VADDWsv2i64", LIEF::assembly::arm::OPCODE::VADDWsv2i64)
  .value("VADDWsv4i32", LIEF::assembly::arm::OPCODE::VADDWsv4i32)
  .value("VADDWsv8i16", LIEF::assembly::arm::OPCODE::VADDWsv8i16)
  .value("VADDWuv2i64", LIEF::assembly::arm::OPCODE::VADDWuv2i64)
  .value("VADDWuv4i32", LIEF::assembly::arm::OPCODE::VADDWuv4i32)
  .value("VADDWuv8i16", LIEF::assembly::arm::OPCODE::VADDWuv8i16)
  .value("VADDfd", LIEF::assembly::arm::OPCODE::VADDfd)
  .value("VADDfq", LIEF::assembly::arm::OPCODE::VADDfq)
  .value("VADDhd", LIEF::assembly::arm::OPCODE::VADDhd)
  .value("VADDhq", LIEF::assembly::arm::OPCODE::VADDhq)
  .value("VADDv16i8", LIEF::assembly::arm::OPCODE::VADDv16i8)
  .value("VADDv1i64", LIEF::assembly::arm::OPCODE::VADDv1i64)
  .value("VADDv2i32", LIEF::assembly::arm::OPCODE::VADDv2i32)
  .value("VADDv2i64", LIEF::assembly::arm::OPCODE::VADDv2i64)
  .value("VADDv4i16", LIEF::assembly::arm::OPCODE::VADDv4i16)
  .value("VADDv4i32", LIEF::assembly::arm::OPCODE::VADDv4i32)
  .value("VADDv8i16", LIEF::assembly::arm::OPCODE::VADDv8i16)
  .value("VADDv8i8", LIEF::assembly::arm::OPCODE::VADDv8i8)
  .value("VANDd", LIEF::assembly::arm::OPCODE::VANDd)
  .value("VANDq", LIEF::assembly::arm::OPCODE::VANDq)
  .value("VBF16MALBQ", LIEF::assembly::arm::OPCODE::VBF16MALBQ)
  .value("VBF16MALBQI", LIEF::assembly::arm::OPCODE::VBF16MALBQI)
  .value("VBF16MALTQ", LIEF::assembly::arm::OPCODE::VBF16MALTQ)
  .value("VBF16MALTQI", LIEF::assembly::arm::OPCODE::VBF16MALTQI)
  .value("VBICd", LIEF::assembly::arm::OPCODE::VBICd)
  .value("VBICiv2i32", LIEF::assembly::arm::OPCODE::VBICiv2i32)
  .value("VBICiv4i16", LIEF::assembly::arm::OPCODE::VBICiv4i16)
  .value("VBICiv4i32", LIEF::assembly::arm::OPCODE::VBICiv4i32)
  .value("VBICiv8i16", LIEF::assembly::arm::OPCODE::VBICiv8i16)
  .value("VBICq", LIEF::assembly::arm::OPCODE::VBICq)
  .value("VBIFd", LIEF::assembly::arm::OPCODE::VBIFd)
  .value("VBIFq", LIEF::assembly::arm::OPCODE::VBIFq)
  .value("VBITd", LIEF::assembly::arm::OPCODE::VBITd)
  .value("VBITq", LIEF::assembly::arm::OPCODE::VBITq)
  .value("VBSLd", LIEF::assembly::arm::OPCODE::VBSLd)
  .value("VBSLq", LIEF::assembly::arm::OPCODE::VBSLq)
  .value("VBSPd", LIEF::assembly::arm::OPCODE::VBSPd)
  .value("VBSPq", LIEF::assembly::arm::OPCODE::VBSPq)
  .value("VCADDv2f32", LIEF::assembly::arm::OPCODE::VCADDv2f32)
  .value("VCADDv4f16", LIEF::assembly::arm::OPCODE::VCADDv4f16)
  .value("VCADDv4f32", LIEF::assembly::arm::OPCODE::VCADDv4f32)
  .value("VCADDv8f16", LIEF::assembly::arm::OPCODE::VCADDv8f16)
  .value("VCEQfd", LIEF::assembly::arm::OPCODE::VCEQfd)
  .value("VCEQfq", LIEF::assembly::arm::OPCODE::VCEQfq)
  .value("VCEQhd", LIEF::assembly::arm::OPCODE::VCEQhd)
  .value("VCEQhq", LIEF::assembly::arm::OPCODE::VCEQhq)
  .value("VCEQv16i8", LIEF::assembly::arm::OPCODE::VCEQv16i8)
  .value("VCEQv2i32", LIEF::assembly::arm::OPCODE::VCEQv2i32)
  .value("VCEQv4i16", LIEF::assembly::arm::OPCODE::VCEQv4i16)
  .value("VCEQv4i32", LIEF::assembly::arm::OPCODE::VCEQv4i32)
  .value("VCEQv8i16", LIEF::assembly::arm::OPCODE::VCEQv8i16)
  .value("VCEQv8i8", LIEF::assembly::arm::OPCODE::VCEQv8i8)
  .value("VCEQzv16i8", LIEF::assembly::arm::OPCODE::VCEQzv16i8)
  .value("VCEQzv2f32", LIEF::assembly::arm::OPCODE::VCEQzv2f32)
  .value("VCEQzv2i32", LIEF::assembly::arm::OPCODE::VCEQzv2i32)
  .value("VCEQzv4f16", LIEF::assembly::arm::OPCODE::VCEQzv4f16)
  .value("VCEQzv4f32", LIEF::assembly::arm::OPCODE::VCEQzv4f32)
  .value("VCEQzv4i16", LIEF::assembly::arm::OPCODE::VCEQzv4i16)
  .value("VCEQzv4i32", LIEF::assembly::arm::OPCODE::VCEQzv4i32)
  .value("VCEQzv8f16", LIEF::assembly::arm::OPCODE::VCEQzv8f16)
  .value("VCEQzv8i16", LIEF::assembly::arm::OPCODE::VCEQzv8i16)
  .value("VCEQzv8i8", LIEF::assembly::arm::OPCODE::VCEQzv8i8)
  .value("VCGEfd", LIEF::assembly::arm::OPCODE::VCGEfd)
  .value("VCGEfq", LIEF::assembly::arm::OPCODE::VCGEfq)
  .value("VCGEhd", LIEF::assembly::arm::OPCODE::VCGEhd)
  .value("VCGEhq", LIEF::assembly::arm::OPCODE::VCGEhq)
  .value("VCGEsv16i8", LIEF::assembly::arm::OPCODE::VCGEsv16i8)
  .value("VCGEsv2i32", LIEF::assembly::arm::OPCODE::VCGEsv2i32)
  .value("VCGEsv4i16", LIEF::assembly::arm::OPCODE::VCGEsv4i16)
  .value("VCGEsv4i32", LIEF::assembly::arm::OPCODE::VCGEsv4i32)
  .value("VCGEsv8i16", LIEF::assembly::arm::OPCODE::VCGEsv8i16)
  .value("VCGEsv8i8", LIEF::assembly::arm::OPCODE::VCGEsv8i8)
  .value("VCGEuv16i8", LIEF::assembly::arm::OPCODE::VCGEuv16i8)
  .value("VCGEuv2i32", LIEF::assembly::arm::OPCODE::VCGEuv2i32)
  .value("VCGEuv4i16", LIEF::assembly::arm::OPCODE::VCGEuv4i16)
  .value("VCGEuv4i32", LIEF::assembly::arm::OPCODE::VCGEuv4i32)
  .value("VCGEuv8i16", LIEF::assembly::arm::OPCODE::VCGEuv8i16)
  .value("VCGEuv8i8", LIEF::assembly::arm::OPCODE::VCGEuv8i8)
  .value("VCGEzv16i8", LIEF::assembly::arm::OPCODE::VCGEzv16i8)
  .value("VCGEzv2f32", LIEF::assembly::arm::OPCODE::VCGEzv2f32)
  .value("VCGEzv2i32", LIEF::assembly::arm::OPCODE::VCGEzv2i32)
  .value("VCGEzv4f16", LIEF::assembly::arm::OPCODE::VCGEzv4f16)
  .value("VCGEzv4f32", LIEF::assembly::arm::OPCODE::VCGEzv4f32)
  .value("VCGEzv4i16", LIEF::assembly::arm::OPCODE::VCGEzv4i16)
  .value("VCGEzv4i32", LIEF::assembly::arm::OPCODE::VCGEzv4i32)
  .value("VCGEzv8f16", LIEF::assembly::arm::OPCODE::VCGEzv8f16)
  .value("VCGEzv8i16", LIEF::assembly::arm::OPCODE::VCGEzv8i16)
  .value("VCGEzv8i8", LIEF::assembly::arm::OPCODE::VCGEzv8i8)
  .value("VCGTfd", LIEF::assembly::arm::OPCODE::VCGTfd)
  .value("VCGTfq", LIEF::assembly::arm::OPCODE::VCGTfq)
  .value("VCGThd", LIEF::assembly::arm::OPCODE::VCGThd)
  .value("VCGThq", LIEF::assembly::arm::OPCODE::VCGThq)
  .value("VCGTsv16i8", LIEF::assembly::arm::OPCODE::VCGTsv16i8)
  .value("VCGTsv2i32", LIEF::assembly::arm::OPCODE::VCGTsv2i32)
  .value("VCGTsv4i16", LIEF::assembly::arm::OPCODE::VCGTsv4i16)
  .value("VCGTsv4i32", LIEF::assembly::arm::OPCODE::VCGTsv4i32)
  .value("VCGTsv8i16", LIEF::assembly::arm::OPCODE::VCGTsv8i16)
  .value("VCGTsv8i8", LIEF::assembly::arm::OPCODE::VCGTsv8i8)
  .value("VCGTuv16i8", LIEF::assembly::arm::OPCODE::VCGTuv16i8)
  .value("VCGTuv2i32", LIEF::assembly::arm::OPCODE::VCGTuv2i32)
  .value("VCGTuv4i16", LIEF::assembly::arm::OPCODE::VCGTuv4i16)
  .value("VCGTuv4i32", LIEF::assembly::arm::OPCODE::VCGTuv4i32)
  .value("VCGTuv8i16", LIEF::assembly::arm::OPCODE::VCGTuv8i16)
  .value("VCGTuv8i8", LIEF::assembly::arm::OPCODE::VCGTuv8i8)
  .value("VCGTzv16i8", LIEF::assembly::arm::OPCODE::VCGTzv16i8)
  .value("VCGTzv2f32", LIEF::assembly::arm::OPCODE::VCGTzv2f32)
  .value("VCGTzv2i32", LIEF::assembly::arm::OPCODE::VCGTzv2i32)
  .value("VCGTzv4f16", LIEF::assembly::arm::OPCODE::VCGTzv4f16)
  .value("VCGTzv4f32", LIEF::assembly::arm::OPCODE::VCGTzv4f32)
  .value("VCGTzv4i16", LIEF::assembly::arm::OPCODE::VCGTzv4i16)
  .value("VCGTzv4i32", LIEF::assembly::arm::OPCODE::VCGTzv4i32)
  .value("VCGTzv8f16", LIEF::assembly::arm::OPCODE::VCGTzv8f16)
  .value("VCGTzv8i16", LIEF::assembly::arm::OPCODE::VCGTzv8i16)
  .value("VCGTzv8i8", LIEF::assembly::arm::OPCODE::VCGTzv8i8)
  .value("VCLEzv16i8", LIEF::assembly::arm::OPCODE::VCLEzv16i8)
  .value("VCLEzv2f32", LIEF::assembly::arm::OPCODE::VCLEzv2f32)
  .value("VCLEzv2i32", LIEF::assembly::arm::OPCODE::VCLEzv2i32)
  .value("VCLEzv4f16", LIEF::assembly::arm::OPCODE::VCLEzv4f16)
  .value("VCLEzv4f32", LIEF::assembly::arm::OPCODE::VCLEzv4f32)
  .value("VCLEzv4i16", LIEF::assembly::arm::OPCODE::VCLEzv4i16)
  .value("VCLEzv4i32", LIEF::assembly::arm::OPCODE::VCLEzv4i32)
  .value("VCLEzv8f16", LIEF::assembly::arm::OPCODE::VCLEzv8f16)
  .value("VCLEzv8i16", LIEF::assembly::arm::OPCODE::VCLEzv8i16)
  .value("VCLEzv8i8", LIEF::assembly::arm::OPCODE::VCLEzv8i8)
  .value("VCLSv16i8", LIEF::assembly::arm::OPCODE::VCLSv16i8)
  .value("VCLSv2i32", LIEF::assembly::arm::OPCODE::VCLSv2i32)
  .value("VCLSv4i16", LIEF::assembly::arm::OPCODE::VCLSv4i16)
  .value("VCLSv4i32", LIEF::assembly::arm::OPCODE::VCLSv4i32)
  .value("VCLSv8i16", LIEF::assembly::arm::OPCODE::VCLSv8i16)
  .value("VCLSv8i8", LIEF::assembly::arm::OPCODE::VCLSv8i8)
  .value("VCLTzv16i8", LIEF::assembly::arm::OPCODE::VCLTzv16i8)
  .value("VCLTzv2f32", LIEF::assembly::arm::OPCODE::VCLTzv2f32)
  .value("VCLTzv2i32", LIEF::assembly::arm::OPCODE::VCLTzv2i32)
  .value("VCLTzv4f16", LIEF::assembly::arm::OPCODE::VCLTzv4f16)
  .value("VCLTzv4f32", LIEF::assembly::arm::OPCODE::VCLTzv4f32)
  .value("VCLTzv4i16", LIEF::assembly::arm::OPCODE::VCLTzv4i16)
  .value("VCLTzv4i32", LIEF::assembly::arm::OPCODE::VCLTzv4i32)
  .value("VCLTzv8f16", LIEF::assembly::arm::OPCODE::VCLTzv8f16)
  .value("VCLTzv8i16", LIEF::assembly::arm::OPCODE::VCLTzv8i16)
  .value("VCLTzv8i8", LIEF::assembly::arm::OPCODE::VCLTzv8i8)
  .value("VCLZv16i8", LIEF::assembly::arm::OPCODE::VCLZv16i8)
  .value("VCLZv2i32", LIEF::assembly::arm::OPCODE::VCLZv2i32)
  .value("VCLZv4i16", LIEF::assembly::arm::OPCODE::VCLZv4i16)
  .value("VCLZv4i32", LIEF::assembly::arm::OPCODE::VCLZv4i32)
  .value("VCLZv8i16", LIEF::assembly::arm::OPCODE::VCLZv8i16)
  .value("VCLZv8i8", LIEF::assembly::arm::OPCODE::VCLZv8i8)
  .value("VCMLAv2f32", LIEF::assembly::arm::OPCODE::VCMLAv2f32)
  .value("VCMLAv2f32_indexed", LIEF::assembly::arm::OPCODE::VCMLAv2f32_indexed)
  .value("VCMLAv4f16", LIEF::assembly::arm::OPCODE::VCMLAv4f16)
  .value("VCMLAv4f16_indexed", LIEF::assembly::arm::OPCODE::VCMLAv4f16_indexed)
  .value("VCMLAv4f32", LIEF::assembly::arm::OPCODE::VCMLAv4f32)
  .value("VCMLAv4f32_indexed", LIEF::assembly::arm::OPCODE::VCMLAv4f32_indexed)
  .value("VCMLAv8f16", LIEF::assembly::arm::OPCODE::VCMLAv8f16)
  .value("VCMLAv8f16_indexed", LIEF::assembly::arm::OPCODE::VCMLAv8f16_indexed)
  .value("VCMPD", LIEF::assembly::arm::OPCODE::VCMPD)
  .value("VCMPED", LIEF::assembly::arm::OPCODE::VCMPED)
  .value("VCMPEH", LIEF::assembly::arm::OPCODE::VCMPEH)
  .value("VCMPES", LIEF::assembly::arm::OPCODE::VCMPES)
  .value("VCMPEZD", LIEF::assembly::arm::OPCODE::VCMPEZD)
  .value("VCMPEZH", LIEF::assembly::arm::OPCODE::VCMPEZH)
  .value("VCMPEZS", LIEF::assembly::arm::OPCODE::VCMPEZS)
  .value("VCMPH", LIEF::assembly::arm::OPCODE::VCMPH)
  .value("VCMPS", LIEF::assembly::arm::OPCODE::VCMPS)
  .value("VCMPZD", LIEF::assembly::arm::OPCODE::VCMPZD)
  .value("VCMPZH", LIEF::assembly::arm::OPCODE::VCMPZH)
  .value("VCMPZS", LIEF::assembly::arm::OPCODE::VCMPZS)
  .value("VCNTd", LIEF::assembly::arm::OPCODE::VCNTd)
  .value("VCNTq", LIEF::assembly::arm::OPCODE::VCNTq)
  .value("VCVTANSDf", LIEF::assembly::arm::OPCODE::VCVTANSDf)
  .value("VCVTANSDh", LIEF::assembly::arm::OPCODE::VCVTANSDh)
  .value("VCVTANSQf", LIEF::assembly::arm::OPCODE::VCVTANSQf)
  .value("VCVTANSQh", LIEF::assembly::arm::OPCODE::VCVTANSQh)
  .value("VCVTANUDf", LIEF::assembly::arm::OPCODE::VCVTANUDf)
  .value("VCVTANUDh", LIEF::assembly::arm::OPCODE::VCVTANUDh)
  .value("VCVTANUQf", LIEF::assembly::arm::OPCODE::VCVTANUQf)
  .value("VCVTANUQh", LIEF::assembly::arm::OPCODE::VCVTANUQh)
  .value("VCVTASD", LIEF::assembly::arm::OPCODE::VCVTASD)
  .value("VCVTASH", LIEF::assembly::arm::OPCODE::VCVTASH)
  .value("VCVTASS", LIEF::assembly::arm::OPCODE::VCVTASS)
  .value("VCVTAUD", LIEF::assembly::arm::OPCODE::VCVTAUD)
  .value("VCVTAUH", LIEF::assembly::arm::OPCODE::VCVTAUH)
  .value("VCVTAUS", LIEF::assembly::arm::OPCODE::VCVTAUS)
  .value("VCVTBDH", LIEF::assembly::arm::OPCODE::VCVTBDH)
  .value("VCVTBHD", LIEF::assembly::arm::OPCODE::VCVTBHD)
  .value("VCVTBHS", LIEF::assembly::arm::OPCODE::VCVTBHS)
  .value("VCVTBSH", LIEF::assembly::arm::OPCODE::VCVTBSH)
  .value("VCVTDS", LIEF::assembly::arm::OPCODE::VCVTDS)
  .value("VCVTMNSDf", LIEF::assembly::arm::OPCODE::VCVTMNSDf)
  .value("VCVTMNSDh", LIEF::assembly::arm::OPCODE::VCVTMNSDh)
  .value("VCVTMNSQf", LIEF::assembly::arm::OPCODE::VCVTMNSQf)
  .value("VCVTMNSQh", LIEF::assembly::arm::OPCODE::VCVTMNSQh)
  .value("VCVTMNUDf", LIEF::assembly::arm::OPCODE::VCVTMNUDf)
  .value("VCVTMNUDh", LIEF::assembly::arm::OPCODE::VCVTMNUDh)
  .value("VCVTMNUQf", LIEF::assembly::arm::OPCODE::VCVTMNUQf)
  .value("VCVTMNUQh", LIEF::assembly::arm::OPCODE::VCVTMNUQh)
  .value("VCVTMSD", LIEF::assembly::arm::OPCODE::VCVTMSD)
  .value("VCVTMSH", LIEF::assembly::arm::OPCODE::VCVTMSH)
  .value("VCVTMSS", LIEF::assembly::arm::OPCODE::VCVTMSS)
  .value("VCVTMUD", LIEF::assembly::arm::OPCODE::VCVTMUD)
  .value("VCVTMUH", LIEF::assembly::arm::OPCODE::VCVTMUH)
  .value("VCVTMUS", LIEF::assembly::arm::OPCODE::VCVTMUS)
  .value("VCVTNNSDf", LIEF::assembly::arm::OPCODE::VCVTNNSDf)
  .value("VCVTNNSDh", LIEF::assembly::arm::OPCODE::VCVTNNSDh)
  .value("VCVTNNSQf", LIEF::assembly::arm::OPCODE::VCVTNNSQf)
  .value("VCVTNNSQh", LIEF::assembly::arm::OPCODE::VCVTNNSQh)
  .value("VCVTNNUDf", LIEF::assembly::arm::OPCODE::VCVTNNUDf)
  .value("VCVTNNUDh", LIEF::assembly::arm::OPCODE::VCVTNNUDh)
  .value("VCVTNNUQf", LIEF::assembly::arm::OPCODE::VCVTNNUQf)
  .value("VCVTNNUQh", LIEF::assembly::arm::OPCODE::VCVTNNUQh)
  .value("VCVTNSD", LIEF::assembly::arm::OPCODE::VCVTNSD)
  .value("VCVTNSH", LIEF::assembly::arm::OPCODE::VCVTNSH)
  .value("VCVTNSS", LIEF::assembly::arm::OPCODE::VCVTNSS)
  .value("VCVTNUD", LIEF::assembly::arm::OPCODE::VCVTNUD)
  .value("VCVTNUH", LIEF::assembly::arm::OPCODE::VCVTNUH)
  .value("VCVTNUS", LIEF::assembly::arm::OPCODE::VCVTNUS)
  .value("VCVTPNSDf", LIEF::assembly::arm::OPCODE::VCVTPNSDf)
  .value("VCVTPNSDh", LIEF::assembly::arm::OPCODE::VCVTPNSDh)
  .value("VCVTPNSQf", LIEF::assembly::arm::OPCODE::VCVTPNSQf)
  .value("VCVTPNSQh", LIEF::assembly::arm::OPCODE::VCVTPNSQh)
  .value("VCVTPNUDf", LIEF::assembly::arm::OPCODE::VCVTPNUDf)
  .value("VCVTPNUDh", LIEF::assembly::arm::OPCODE::VCVTPNUDh)
  .value("VCVTPNUQf", LIEF::assembly::arm::OPCODE::VCVTPNUQf)
  .value("VCVTPNUQh", LIEF::assembly::arm::OPCODE::VCVTPNUQh)
  .value("VCVTPSD", LIEF::assembly::arm::OPCODE::VCVTPSD)
  .value("VCVTPSH", LIEF::assembly::arm::OPCODE::VCVTPSH)
  .value("VCVTPSS", LIEF::assembly::arm::OPCODE::VCVTPSS)
  .value("VCVTPUD", LIEF::assembly::arm::OPCODE::VCVTPUD)
  .value("VCVTPUH", LIEF::assembly::arm::OPCODE::VCVTPUH)
  .value("VCVTPUS", LIEF::assembly::arm::OPCODE::VCVTPUS)
  .value("VCVTSD", LIEF::assembly::arm::OPCODE::VCVTSD)
  .value("VCVTTDH", LIEF::assembly::arm::OPCODE::VCVTTDH)
  .value("VCVTTHD", LIEF::assembly::arm::OPCODE::VCVTTHD)
  .value("VCVTTHS", LIEF::assembly::arm::OPCODE::VCVTTHS)
  .value("VCVTTSH", LIEF::assembly::arm::OPCODE::VCVTTSH)
  .value("VCVTf2h", LIEF::assembly::arm::OPCODE::VCVTf2h)
  .value("VCVTf2sd", LIEF::assembly::arm::OPCODE::VCVTf2sd)
  .value("VCVTf2sq", LIEF::assembly::arm::OPCODE::VCVTf2sq)
  .value("VCVTf2ud", LIEF::assembly::arm::OPCODE::VCVTf2ud)
  .value("VCVTf2uq", LIEF::assembly::arm::OPCODE::VCVTf2uq)
  .value("VCVTf2xsd", LIEF::assembly::arm::OPCODE::VCVTf2xsd)
  .value("VCVTf2xsq", LIEF::assembly::arm::OPCODE::VCVTf2xsq)
  .value("VCVTf2xud", LIEF::assembly::arm::OPCODE::VCVTf2xud)
  .value("VCVTf2xuq", LIEF::assembly::arm::OPCODE::VCVTf2xuq);
  opcodes.value("VCVTh2f", LIEF::assembly::arm::OPCODE::VCVTh2f)
  .value("VCVTh2sd", LIEF::assembly::arm::OPCODE::VCVTh2sd)
  .value("VCVTh2sq", LIEF::assembly::arm::OPCODE::VCVTh2sq)
  .value("VCVTh2ud", LIEF::assembly::arm::OPCODE::VCVTh2ud)
  .value("VCVTh2uq", LIEF::assembly::arm::OPCODE::VCVTh2uq)
  .value("VCVTh2xsd", LIEF::assembly::arm::OPCODE::VCVTh2xsd)
  .value("VCVTh2xsq", LIEF::assembly::arm::OPCODE::VCVTh2xsq)
  .value("VCVTh2xud", LIEF::assembly::arm::OPCODE::VCVTh2xud)
  .value("VCVTh2xuq", LIEF::assembly::arm::OPCODE::VCVTh2xuq)
  .value("VCVTs2fd", LIEF::assembly::arm::OPCODE::VCVTs2fd)
  .value("VCVTs2fq", LIEF::assembly::arm::OPCODE::VCVTs2fq)
  .value("VCVTs2hd", LIEF::assembly::arm::OPCODE::VCVTs2hd)
  .value("VCVTs2hq", LIEF::assembly::arm::OPCODE::VCVTs2hq)
  .value("VCVTu2fd", LIEF::assembly::arm::OPCODE::VCVTu2fd)
  .value("VCVTu2fq", LIEF::assembly::arm::OPCODE::VCVTu2fq)
  .value("VCVTu2hd", LIEF::assembly::arm::OPCODE::VCVTu2hd)
  .value("VCVTu2hq", LIEF::assembly::arm::OPCODE::VCVTu2hq)
  .value("VCVTxs2fd", LIEF::assembly::arm::OPCODE::VCVTxs2fd)
  .value("VCVTxs2fq", LIEF::assembly::arm::OPCODE::VCVTxs2fq)
  .value("VCVTxs2hd", LIEF::assembly::arm::OPCODE::VCVTxs2hd)
  .value("VCVTxs2hq", LIEF::assembly::arm::OPCODE::VCVTxs2hq)
  .value("VCVTxu2fd", LIEF::assembly::arm::OPCODE::VCVTxu2fd)
  .value("VCVTxu2fq", LIEF::assembly::arm::OPCODE::VCVTxu2fq)
  .value("VCVTxu2hd", LIEF::assembly::arm::OPCODE::VCVTxu2hd)
  .value("VCVTxu2hq", LIEF::assembly::arm::OPCODE::VCVTxu2hq)
  .value("VDIVD", LIEF::assembly::arm::OPCODE::VDIVD)
  .value("VDIVH", LIEF::assembly::arm::OPCODE::VDIVH)
  .value("VDIVS", LIEF::assembly::arm::OPCODE::VDIVS)
  .value("VDUP16d", LIEF::assembly::arm::OPCODE::VDUP16d)
  .value("VDUP16q", LIEF::assembly::arm::OPCODE::VDUP16q)
  .value("VDUP32d", LIEF::assembly::arm::OPCODE::VDUP32d)
  .value("VDUP32q", LIEF::assembly::arm::OPCODE::VDUP32q)
  .value("VDUP8d", LIEF::assembly::arm::OPCODE::VDUP8d)
  .value("VDUP8q", LIEF::assembly::arm::OPCODE::VDUP8q)
  .value("VDUPLN16d", LIEF::assembly::arm::OPCODE::VDUPLN16d)
  .value("VDUPLN16q", LIEF::assembly::arm::OPCODE::VDUPLN16q)
  .value("VDUPLN32d", LIEF::assembly::arm::OPCODE::VDUPLN32d)
  .value("VDUPLN32q", LIEF::assembly::arm::OPCODE::VDUPLN32q)
  .value("VDUPLN8d", LIEF::assembly::arm::OPCODE::VDUPLN8d)
  .value("VDUPLN8q", LIEF::assembly::arm::OPCODE::VDUPLN8q)
  .value("VEORd", LIEF::assembly::arm::OPCODE::VEORd)
  .value("VEORq", LIEF::assembly::arm::OPCODE::VEORq)
  .value("VEXTd16", LIEF::assembly::arm::OPCODE::VEXTd16)
  .value("VEXTd32", LIEF::assembly::arm::OPCODE::VEXTd32)
  .value("VEXTd8", LIEF::assembly::arm::OPCODE::VEXTd8)
  .value("VEXTq16", LIEF::assembly::arm::OPCODE::VEXTq16)
  .value("VEXTq32", LIEF::assembly::arm::OPCODE::VEXTq32)
  .value("VEXTq64", LIEF::assembly::arm::OPCODE::VEXTq64)
  .value("VEXTq8", LIEF::assembly::arm::OPCODE::VEXTq8)
  .value("VFMAD", LIEF::assembly::arm::OPCODE::VFMAD)
  .value("VFMAH", LIEF::assembly::arm::OPCODE::VFMAH)
  .value("VFMALD", LIEF::assembly::arm::OPCODE::VFMALD)
  .value("VFMALDI", LIEF::assembly::arm::OPCODE::VFMALDI)
  .value("VFMALQ", LIEF::assembly::arm::OPCODE::VFMALQ)
  .value("VFMALQI", LIEF::assembly::arm::OPCODE::VFMALQI)
  .value("VFMAS", LIEF::assembly::arm::OPCODE::VFMAS)
  .value("VFMAfd", LIEF::assembly::arm::OPCODE::VFMAfd)
  .value("VFMAfq", LIEF::assembly::arm::OPCODE::VFMAfq)
  .value("VFMAhd", LIEF::assembly::arm::OPCODE::VFMAhd)
  .value("VFMAhq", LIEF::assembly::arm::OPCODE::VFMAhq)
  .value("VFMSD", LIEF::assembly::arm::OPCODE::VFMSD)
  .value("VFMSH", LIEF::assembly::arm::OPCODE::VFMSH)
  .value("VFMSLD", LIEF::assembly::arm::OPCODE::VFMSLD)
  .value("VFMSLDI", LIEF::assembly::arm::OPCODE::VFMSLDI)
  .value("VFMSLQ", LIEF::assembly::arm::OPCODE::VFMSLQ)
  .value("VFMSLQI", LIEF::assembly::arm::OPCODE::VFMSLQI)
  .value("VFMSS", LIEF::assembly::arm::OPCODE::VFMSS)
  .value("VFMSfd", LIEF::assembly::arm::OPCODE::VFMSfd)
  .value("VFMSfq", LIEF::assembly::arm::OPCODE::VFMSfq)
  .value("VFMShd", LIEF::assembly::arm::OPCODE::VFMShd)
  .value("VFMShq", LIEF::assembly::arm::OPCODE::VFMShq)
  .value("VFNMAD", LIEF::assembly::arm::OPCODE::VFNMAD)
  .value("VFNMAH", LIEF::assembly::arm::OPCODE::VFNMAH)
  .value("VFNMAS", LIEF::assembly::arm::OPCODE::VFNMAS)
  .value("VFNMSD", LIEF::assembly::arm::OPCODE::VFNMSD)
  .value("VFNMSH", LIEF::assembly::arm::OPCODE::VFNMSH)
  .value("VFNMSS", LIEF::assembly::arm::OPCODE::VFNMSS)
  .value("VFP_VMAXNMD", LIEF::assembly::arm::OPCODE::VFP_VMAXNMD)
  .value("VFP_VMAXNMH", LIEF::assembly::arm::OPCODE::VFP_VMAXNMH)
  .value("VFP_VMAXNMS", LIEF::assembly::arm::OPCODE::VFP_VMAXNMS)
  .value("VFP_VMINNMD", LIEF::assembly::arm::OPCODE::VFP_VMINNMD)
  .value("VFP_VMINNMH", LIEF::assembly::arm::OPCODE::VFP_VMINNMH)
  .value("VFP_VMINNMS", LIEF::assembly::arm::OPCODE::VFP_VMINNMS)
  .value("VGETLNi32", LIEF::assembly::arm::OPCODE::VGETLNi32)
  .value("VGETLNs16", LIEF::assembly::arm::OPCODE::VGETLNs16)
  .value("VGETLNs8", LIEF::assembly::arm::OPCODE::VGETLNs8)
  .value("VGETLNu16", LIEF::assembly::arm::OPCODE::VGETLNu16)
  .value("VGETLNu8", LIEF::assembly::arm::OPCODE::VGETLNu8)
  .value("VHADDsv16i8", LIEF::assembly::arm::OPCODE::VHADDsv16i8)
  .value("VHADDsv2i32", LIEF::assembly::arm::OPCODE::VHADDsv2i32)
  .value("VHADDsv4i16", LIEF::assembly::arm::OPCODE::VHADDsv4i16)
  .value("VHADDsv4i32", LIEF::assembly::arm::OPCODE::VHADDsv4i32)
  .value("VHADDsv8i16", LIEF::assembly::arm::OPCODE::VHADDsv8i16)
  .value("VHADDsv8i8", LIEF::assembly::arm::OPCODE::VHADDsv8i8)
  .value("VHADDuv16i8", LIEF::assembly::arm::OPCODE::VHADDuv16i8)
  .value("VHADDuv2i32", LIEF::assembly::arm::OPCODE::VHADDuv2i32)
  .value("VHADDuv4i16", LIEF::assembly::arm::OPCODE::VHADDuv4i16)
  .value("VHADDuv4i32", LIEF::assembly::arm::OPCODE::VHADDuv4i32)
  .value("VHADDuv8i16", LIEF::assembly::arm::OPCODE::VHADDuv8i16)
  .value("VHADDuv8i8", LIEF::assembly::arm::OPCODE::VHADDuv8i8)
  .value("VHSUBsv16i8", LIEF::assembly::arm::OPCODE::VHSUBsv16i8)
  .value("VHSUBsv2i32", LIEF::assembly::arm::OPCODE::VHSUBsv2i32)
  .value("VHSUBsv4i16", LIEF::assembly::arm::OPCODE::VHSUBsv4i16)
  .value("VHSUBsv4i32", LIEF::assembly::arm::OPCODE::VHSUBsv4i32)
  .value("VHSUBsv8i16", LIEF::assembly::arm::OPCODE::VHSUBsv8i16)
  .value("VHSUBsv8i8", LIEF::assembly::arm::OPCODE::VHSUBsv8i8)
  .value("VHSUBuv16i8", LIEF::assembly::arm::OPCODE::VHSUBuv16i8)
  .value("VHSUBuv2i32", LIEF::assembly::arm::OPCODE::VHSUBuv2i32)
  .value("VHSUBuv4i16", LIEF::assembly::arm::OPCODE::VHSUBuv4i16)
  .value("VHSUBuv4i32", LIEF::assembly::arm::OPCODE::VHSUBuv4i32)
  .value("VHSUBuv8i16", LIEF::assembly::arm::OPCODE::VHSUBuv8i16)
  .value("VHSUBuv8i8", LIEF::assembly::arm::OPCODE::VHSUBuv8i8)
  .value("VINSH", LIEF::assembly::arm::OPCODE::VINSH)
  .value("VJCVT", LIEF::assembly::arm::OPCODE::VJCVT)
  .value("VLD1DUPd16", LIEF::assembly::arm::OPCODE::VLD1DUPd16)
  .value("VLD1DUPd16wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPd16wb_fixed)
  .value("VLD1DUPd16wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPd16wb_register)
  .value("VLD1DUPd32", LIEF::assembly::arm::OPCODE::VLD1DUPd32)
  .value("VLD1DUPd32wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPd32wb_fixed)
  .value("VLD1DUPd32wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPd32wb_register)
  .value("VLD1DUPd8", LIEF::assembly::arm::OPCODE::VLD1DUPd8)
  .value("VLD1DUPd8wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPd8wb_fixed)
  .value("VLD1DUPd8wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPd8wb_register)
  .value("VLD1DUPq16", LIEF::assembly::arm::OPCODE::VLD1DUPq16)
  .value("VLD1DUPq16wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPq16wb_fixed)
  .value("VLD1DUPq16wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPq16wb_register)
  .value("VLD1DUPq32", LIEF::assembly::arm::OPCODE::VLD1DUPq32)
  .value("VLD1DUPq32wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPq32wb_fixed)
  .value("VLD1DUPq32wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPq32wb_register)
  .value("VLD1DUPq8", LIEF::assembly::arm::OPCODE::VLD1DUPq8)
  .value("VLD1DUPq8wb_fixed", LIEF::assembly::arm::OPCODE::VLD1DUPq8wb_fixed)
  .value("VLD1DUPq8wb_register", LIEF::assembly::arm::OPCODE::VLD1DUPq8wb_register)
  .value("VLD1LNd16", LIEF::assembly::arm::OPCODE::VLD1LNd16)
  .value("VLD1LNd16_UPD", LIEF::assembly::arm::OPCODE::VLD1LNd16_UPD)
  .value("VLD1LNd32", LIEF::assembly::arm::OPCODE::VLD1LNd32)
  .value("VLD1LNd32_UPD", LIEF::assembly::arm::OPCODE::VLD1LNd32_UPD)
  .value("VLD1LNd8", LIEF::assembly::arm::OPCODE::VLD1LNd8)
  .value("VLD1LNd8_UPD", LIEF::assembly::arm::OPCODE::VLD1LNd8_UPD)
  .value("VLD1LNq16Pseudo", LIEF::assembly::arm::OPCODE::VLD1LNq16Pseudo)
  .value("VLD1LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1LNq16Pseudo_UPD)
  .value("VLD1LNq32Pseudo", LIEF::assembly::arm::OPCODE::VLD1LNq32Pseudo)
  .value("VLD1LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1LNq32Pseudo_UPD)
  .value("VLD1LNq8Pseudo", LIEF::assembly::arm::OPCODE::VLD1LNq8Pseudo)
  .value("VLD1LNq8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1LNq8Pseudo_UPD)
  .value("VLD1d16", LIEF::assembly::arm::OPCODE::VLD1d16)
  .value("VLD1d16Q", LIEF::assembly::arm::OPCODE::VLD1d16Q)
  .value("VLD1d16QPseudo", LIEF::assembly::arm::OPCODE::VLD1d16QPseudo)
  .value("VLD1d16QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d16QPseudoWB_fixed)
  .value("VLD1d16QPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d16QPseudoWB_register)
  .value("VLD1d16Qwb_fixed", LIEF::assembly::arm::OPCODE::VLD1d16Qwb_fixed)
  .value("VLD1d16Qwb_register", LIEF::assembly::arm::OPCODE::VLD1d16Qwb_register)
  .value("VLD1d16T", LIEF::assembly::arm::OPCODE::VLD1d16T)
  .value("VLD1d16TPseudo", LIEF::assembly::arm::OPCODE::VLD1d16TPseudo)
  .value("VLD1d16TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d16TPseudoWB_fixed)
  .value("VLD1d16TPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d16TPseudoWB_register)
  .value("VLD1d16Twb_fixed", LIEF::assembly::arm::OPCODE::VLD1d16Twb_fixed)
  .value("VLD1d16Twb_register", LIEF::assembly::arm::OPCODE::VLD1d16Twb_register)
  .value("VLD1d16wb_fixed", LIEF::assembly::arm::OPCODE::VLD1d16wb_fixed)
  .value("VLD1d16wb_register", LIEF::assembly::arm::OPCODE::VLD1d16wb_register)
  .value("VLD1d32", LIEF::assembly::arm::OPCODE::VLD1d32)
  .value("VLD1d32Q", LIEF::assembly::arm::OPCODE::VLD1d32Q)
  .value("VLD1d32QPseudo", LIEF::assembly::arm::OPCODE::VLD1d32QPseudo)
  .value("VLD1d32QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d32QPseudoWB_fixed)
  .value("VLD1d32QPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d32QPseudoWB_register)
  .value("VLD1d32Qwb_fixed", LIEF::assembly::arm::OPCODE::VLD1d32Qwb_fixed)
  .value("VLD1d32Qwb_register", LIEF::assembly::arm::OPCODE::VLD1d32Qwb_register)
  .value("VLD1d32T", LIEF::assembly::arm::OPCODE::VLD1d32T)
  .value("VLD1d32TPseudo", LIEF::assembly::arm::OPCODE::VLD1d32TPseudo)
  .value("VLD1d32TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d32TPseudoWB_fixed)
  .value("VLD1d32TPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d32TPseudoWB_register)
  .value("VLD1d32Twb_fixed", LIEF::assembly::arm::OPCODE::VLD1d32Twb_fixed)
  .value("VLD1d32Twb_register", LIEF::assembly::arm::OPCODE::VLD1d32Twb_register)
  .value("VLD1d32wb_fixed", LIEF::assembly::arm::OPCODE::VLD1d32wb_fixed)
  .value("VLD1d32wb_register", LIEF::assembly::arm::OPCODE::VLD1d32wb_register)
  .value("VLD1d64", LIEF::assembly::arm::OPCODE::VLD1d64)
  .value("VLD1d64Q", LIEF::assembly::arm::OPCODE::VLD1d64Q)
  .value("VLD1d64QPseudo", LIEF::assembly::arm::OPCODE::VLD1d64QPseudo)
  .value("VLD1d64QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d64QPseudoWB_fixed)
  .value("VLD1d64QPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d64QPseudoWB_register)
  .value("VLD1d64Qwb_fixed", LIEF::assembly::arm::OPCODE::VLD1d64Qwb_fixed)
  .value("VLD1d64Qwb_register", LIEF::assembly::arm::OPCODE::VLD1d64Qwb_register)
  .value("VLD1d64T", LIEF::assembly::arm::OPCODE::VLD1d64T)
  .value("VLD1d64TPseudo", LIEF::assembly::arm::OPCODE::VLD1d64TPseudo)
  .value("VLD1d64TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d64TPseudoWB_fixed)
  .value("VLD1d64TPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d64TPseudoWB_register)
  .value("VLD1d64Twb_fixed", LIEF::assembly::arm::OPCODE::VLD1d64Twb_fixed)
  .value("VLD1d64Twb_register", LIEF::assembly::arm::OPCODE::VLD1d64Twb_register)
  .value("VLD1d64wb_fixed", LIEF::assembly::arm::OPCODE::VLD1d64wb_fixed)
  .value("VLD1d64wb_register", LIEF::assembly::arm::OPCODE::VLD1d64wb_register)
  .value("VLD1d8", LIEF::assembly::arm::OPCODE::VLD1d8)
  .value("VLD1d8Q", LIEF::assembly::arm::OPCODE::VLD1d8Q)
  .value("VLD1d8QPseudo", LIEF::assembly::arm::OPCODE::VLD1d8QPseudo)
  .value("VLD1d8QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d8QPseudoWB_fixed)
  .value("VLD1d8QPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d8QPseudoWB_register)
  .value("VLD1d8Qwb_fixed", LIEF::assembly::arm::OPCODE::VLD1d8Qwb_fixed)
  .value("VLD1d8Qwb_register", LIEF::assembly::arm::OPCODE::VLD1d8Qwb_register)
  .value("VLD1d8T", LIEF::assembly::arm::OPCODE::VLD1d8T)
  .value("VLD1d8TPseudo", LIEF::assembly::arm::OPCODE::VLD1d8TPseudo)
  .value("VLD1d8TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD1d8TPseudoWB_fixed)
  .value("VLD1d8TPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD1d8TPseudoWB_register)
  .value("VLD1d8Twb_fixed", LIEF::assembly::arm::OPCODE::VLD1d8Twb_fixed)
  .value("VLD1d8Twb_register", LIEF::assembly::arm::OPCODE::VLD1d8Twb_register)
  .value("VLD1d8wb_fixed", LIEF::assembly::arm::OPCODE::VLD1d8wb_fixed)
  .value("VLD1d8wb_register", LIEF::assembly::arm::OPCODE::VLD1d8wb_register)
  .value("VLD1q16", LIEF::assembly::arm::OPCODE::VLD1q16)
  .value("VLD1q16HighQPseudo", LIEF::assembly::arm::OPCODE::VLD1q16HighQPseudo)
  .value("VLD1q16HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q16HighQPseudo_UPD)
  .value("VLD1q16HighTPseudo", LIEF::assembly::arm::OPCODE::VLD1q16HighTPseudo)
  .value("VLD1q16HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q16HighTPseudo_UPD)
  .value("VLD1q16LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q16LowQPseudo_UPD)
  .value("VLD1q16LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q16LowTPseudo_UPD)
  .value("VLD1q16wb_fixed", LIEF::assembly::arm::OPCODE::VLD1q16wb_fixed)
  .value("VLD1q16wb_register", LIEF::assembly::arm::OPCODE::VLD1q16wb_register)
  .value("VLD1q32", LIEF::assembly::arm::OPCODE::VLD1q32)
  .value("VLD1q32HighQPseudo", LIEF::assembly::arm::OPCODE::VLD1q32HighQPseudo)
  .value("VLD1q32HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q32HighQPseudo_UPD)
  .value("VLD1q32HighTPseudo", LIEF::assembly::arm::OPCODE::VLD1q32HighTPseudo)
  .value("VLD1q32HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q32HighTPseudo_UPD)
  .value("VLD1q32LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q32LowQPseudo_UPD)
  .value("VLD1q32LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q32LowTPseudo_UPD)
  .value("VLD1q32wb_fixed", LIEF::assembly::arm::OPCODE::VLD1q32wb_fixed)
  .value("VLD1q32wb_register", LIEF::assembly::arm::OPCODE::VLD1q32wb_register)
  .value("VLD1q64", LIEF::assembly::arm::OPCODE::VLD1q64)
  .value("VLD1q64HighQPseudo", LIEF::assembly::arm::OPCODE::VLD1q64HighQPseudo)
  .value("VLD1q64HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q64HighQPseudo_UPD)
  .value("VLD1q64HighTPseudo", LIEF::assembly::arm::OPCODE::VLD1q64HighTPseudo)
  .value("VLD1q64HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q64HighTPseudo_UPD)
  .value("VLD1q64LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q64LowQPseudo_UPD)
  .value("VLD1q64LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q64LowTPseudo_UPD)
  .value("VLD1q64wb_fixed", LIEF::assembly::arm::OPCODE::VLD1q64wb_fixed)
  .value("VLD1q64wb_register", LIEF::assembly::arm::OPCODE::VLD1q64wb_register)
  .value("VLD1q8", LIEF::assembly::arm::OPCODE::VLD1q8)
  .value("VLD1q8HighQPseudo", LIEF::assembly::arm::OPCODE::VLD1q8HighQPseudo)
  .value("VLD1q8HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q8HighQPseudo_UPD)
  .value("VLD1q8HighTPseudo", LIEF::assembly::arm::OPCODE::VLD1q8HighTPseudo)
  .value("VLD1q8HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q8HighTPseudo_UPD)
  .value("VLD1q8LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q8LowQPseudo_UPD)
  .value("VLD1q8LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD1q8LowTPseudo_UPD)
  .value("VLD1q8wb_fixed", LIEF::assembly::arm::OPCODE::VLD1q8wb_fixed)
  .value("VLD1q8wb_register", LIEF::assembly::arm::OPCODE::VLD1q8wb_register)
  .value("VLD2DUPd16", LIEF::assembly::arm::OPCODE::VLD2DUPd16)
  .value("VLD2DUPd16wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd16wb_fixed)
  .value("VLD2DUPd16wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd16wb_register)
  .value("VLD2DUPd16x2", LIEF::assembly::arm::OPCODE::VLD2DUPd16x2)
  .value("VLD2DUPd16x2wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd16x2wb_fixed)
  .value("VLD2DUPd16x2wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd16x2wb_register)
  .value("VLD2DUPd32", LIEF::assembly::arm::OPCODE::VLD2DUPd32)
  .value("VLD2DUPd32wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd32wb_fixed)
  .value("VLD2DUPd32wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd32wb_register)
  .value("VLD2DUPd32x2", LIEF::assembly::arm::OPCODE::VLD2DUPd32x2)
  .value("VLD2DUPd32x2wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd32x2wb_fixed)
  .value("VLD2DUPd32x2wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd32x2wb_register)
  .value("VLD2DUPd8", LIEF::assembly::arm::OPCODE::VLD2DUPd8)
  .value("VLD2DUPd8wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd8wb_fixed)
  .value("VLD2DUPd8wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd8wb_register)
  .value("VLD2DUPd8x2", LIEF::assembly::arm::OPCODE::VLD2DUPd8x2)
  .value("VLD2DUPd8x2wb_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPd8x2wb_fixed)
  .value("VLD2DUPd8x2wb_register", LIEF::assembly::arm::OPCODE::VLD2DUPd8x2wb_register)
  .value("VLD2DUPq16EvenPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq16EvenPseudo)
  .value("VLD2DUPq16OddPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq16OddPseudo)
  .value("VLD2DUPq16OddPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPq16OddPseudoWB_fixed)
  .value("VLD2DUPq16OddPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2DUPq16OddPseudoWB_register)
  .value("VLD2DUPq32EvenPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq32EvenPseudo)
  .value("VLD2DUPq32OddPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq32OddPseudo)
  .value("VLD2DUPq32OddPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPq32OddPseudoWB_fixed)
  .value("VLD2DUPq32OddPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2DUPq32OddPseudoWB_register)
  .value("VLD2DUPq8EvenPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq8EvenPseudo)
  .value("VLD2DUPq8OddPseudo", LIEF::assembly::arm::OPCODE::VLD2DUPq8OddPseudo)
  .value("VLD2DUPq8OddPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2DUPq8OddPseudoWB_fixed)
  .value("VLD2DUPq8OddPseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2DUPq8OddPseudoWB_register)
  .value("VLD2LNd16", LIEF::assembly::arm::OPCODE::VLD2LNd16)
  .value("VLD2LNd16Pseudo", LIEF::assembly::arm::OPCODE::VLD2LNd16Pseudo)
  .value("VLD2LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd16Pseudo_UPD)
  .value("VLD2LNd16_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd16_UPD)
  .value("VLD2LNd32", LIEF::assembly::arm::OPCODE::VLD2LNd32)
  .value("VLD2LNd32Pseudo", LIEF::assembly::arm::OPCODE::VLD2LNd32Pseudo)
  .value("VLD2LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd32Pseudo_UPD)
  .value("VLD2LNd32_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd32_UPD)
  .value("VLD2LNd8", LIEF::assembly::arm::OPCODE::VLD2LNd8)
  .value("VLD2LNd8Pseudo", LIEF::assembly::arm::OPCODE::VLD2LNd8Pseudo)
  .value("VLD2LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd8Pseudo_UPD)
  .value("VLD2LNd8_UPD", LIEF::assembly::arm::OPCODE::VLD2LNd8_UPD)
  .value("VLD2LNq16", LIEF::assembly::arm::OPCODE::VLD2LNq16)
  .value("VLD2LNq16Pseudo", LIEF::assembly::arm::OPCODE::VLD2LNq16Pseudo)
  .value("VLD2LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD2LNq16Pseudo_UPD)
  .value("VLD2LNq16_UPD", LIEF::assembly::arm::OPCODE::VLD2LNq16_UPD)
  .value("VLD2LNq32", LIEF::assembly::arm::OPCODE::VLD2LNq32)
  .value("VLD2LNq32Pseudo", LIEF::assembly::arm::OPCODE::VLD2LNq32Pseudo)
  .value("VLD2LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD2LNq32Pseudo_UPD)
  .value("VLD2LNq32_UPD", LIEF::assembly::arm::OPCODE::VLD2LNq32_UPD)
  .value("VLD2b16", LIEF::assembly::arm::OPCODE::VLD2b16)
  .value("VLD2b16wb_fixed", LIEF::assembly::arm::OPCODE::VLD2b16wb_fixed)
  .value("VLD2b16wb_register", LIEF::assembly::arm::OPCODE::VLD2b16wb_register)
  .value("VLD2b32", LIEF::assembly::arm::OPCODE::VLD2b32)
  .value("VLD2b32wb_fixed", LIEF::assembly::arm::OPCODE::VLD2b32wb_fixed)
  .value("VLD2b32wb_register", LIEF::assembly::arm::OPCODE::VLD2b32wb_register)
  .value("VLD2b8", LIEF::assembly::arm::OPCODE::VLD2b8)
  .value("VLD2b8wb_fixed", LIEF::assembly::arm::OPCODE::VLD2b8wb_fixed)
  .value("VLD2b8wb_register", LIEF::assembly::arm::OPCODE::VLD2b8wb_register)
  .value("VLD2d16", LIEF::assembly::arm::OPCODE::VLD2d16);
  opcodes.value("VLD2d16wb_fixed", LIEF::assembly::arm::OPCODE::VLD2d16wb_fixed)
  .value("VLD2d16wb_register", LIEF::assembly::arm::OPCODE::VLD2d16wb_register)
  .value("VLD2d32", LIEF::assembly::arm::OPCODE::VLD2d32)
  .value("VLD2d32wb_fixed", LIEF::assembly::arm::OPCODE::VLD2d32wb_fixed)
  .value("VLD2d32wb_register", LIEF::assembly::arm::OPCODE::VLD2d32wb_register)
  .value("VLD2d8", LIEF::assembly::arm::OPCODE::VLD2d8)
  .value("VLD2d8wb_fixed", LIEF::assembly::arm::OPCODE::VLD2d8wb_fixed)
  .value("VLD2d8wb_register", LIEF::assembly::arm::OPCODE::VLD2d8wb_register)
  .value("VLD2q16", LIEF::assembly::arm::OPCODE::VLD2q16)
  .value("VLD2q16Pseudo", LIEF::assembly::arm::OPCODE::VLD2q16Pseudo)
  .value("VLD2q16PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2q16PseudoWB_fixed)
  .value("VLD2q16PseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2q16PseudoWB_register)
  .value("VLD2q16wb_fixed", LIEF::assembly::arm::OPCODE::VLD2q16wb_fixed)
  .value("VLD2q16wb_register", LIEF::assembly::arm::OPCODE::VLD2q16wb_register)
  .value("VLD2q32", LIEF::assembly::arm::OPCODE::VLD2q32)
  .value("VLD2q32Pseudo", LIEF::assembly::arm::OPCODE::VLD2q32Pseudo)
  .value("VLD2q32PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2q32PseudoWB_fixed)
  .value("VLD2q32PseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2q32PseudoWB_register)
  .value("VLD2q32wb_fixed", LIEF::assembly::arm::OPCODE::VLD2q32wb_fixed)
  .value("VLD2q32wb_register", LIEF::assembly::arm::OPCODE::VLD2q32wb_register)
  .value("VLD2q8", LIEF::assembly::arm::OPCODE::VLD2q8)
  .value("VLD2q8Pseudo", LIEF::assembly::arm::OPCODE::VLD2q8Pseudo)
  .value("VLD2q8PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VLD2q8PseudoWB_fixed)
  .value("VLD2q8PseudoWB_register", LIEF::assembly::arm::OPCODE::VLD2q8PseudoWB_register)
  .value("VLD2q8wb_fixed", LIEF::assembly::arm::OPCODE::VLD2q8wb_fixed)
  .value("VLD2q8wb_register", LIEF::assembly::arm::OPCODE::VLD2q8wb_register)
  .value("VLD3DUPd16", LIEF::assembly::arm::OPCODE::VLD3DUPd16)
  .value("VLD3DUPd16Pseudo", LIEF::assembly::arm::OPCODE::VLD3DUPd16Pseudo)
  .value("VLD3DUPd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd16Pseudo_UPD)
  .value("VLD3DUPd16_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd16_UPD)
  .value("VLD3DUPd32", LIEF::assembly::arm::OPCODE::VLD3DUPd32)
  .value("VLD3DUPd32Pseudo", LIEF::assembly::arm::OPCODE::VLD3DUPd32Pseudo)
  .value("VLD3DUPd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd32Pseudo_UPD)
  .value("VLD3DUPd32_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd32_UPD)
  .value("VLD3DUPd8", LIEF::assembly::arm::OPCODE::VLD3DUPd8)
  .value("VLD3DUPd8Pseudo", LIEF::assembly::arm::OPCODE::VLD3DUPd8Pseudo)
  .value("VLD3DUPd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd8Pseudo_UPD)
  .value("VLD3DUPd8_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPd8_UPD)
  .value("VLD3DUPq16", LIEF::assembly::arm::OPCODE::VLD3DUPq16)
  .value("VLD3DUPq16EvenPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq16EvenPseudo)
  .value("VLD3DUPq16OddPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq16OddPseudo)
  .value("VLD3DUPq16OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq16OddPseudo_UPD)
  .value("VLD3DUPq16_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq16_UPD)
  .value("VLD3DUPq32", LIEF::assembly::arm::OPCODE::VLD3DUPq32)
  .value("VLD3DUPq32EvenPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq32EvenPseudo)
  .value("VLD3DUPq32OddPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq32OddPseudo)
  .value("VLD3DUPq32OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq32OddPseudo_UPD)
  .value("VLD3DUPq32_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq32_UPD)
  .value("VLD3DUPq8", LIEF::assembly::arm::OPCODE::VLD3DUPq8)
  .value("VLD3DUPq8EvenPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq8EvenPseudo)
  .value("VLD3DUPq8OddPseudo", LIEF::assembly::arm::OPCODE::VLD3DUPq8OddPseudo)
  .value("VLD3DUPq8OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq8OddPseudo_UPD)
  .value("VLD3DUPq8_UPD", LIEF::assembly::arm::OPCODE::VLD3DUPq8_UPD)
  .value("VLD3LNd16", LIEF::assembly::arm::OPCODE::VLD3LNd16)
  .value("VLD3LNd16Pseudo", LIEF::assembly::arm::OPCODE::VLD3LNd16Pseudo)
  .value("VLD3LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd16Pseudo_UPD)
  .value("VLD3LNd16_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd16_UPD)
  .value("VLD3LNd32", LIEF::assembly::arm::OPCODE::VLD3LNd32)
  .value("VLD3LNd32Pseudo", LIEF::assembly::arm::OPCODE::VLD3LNd32Pseudo)
  .value("VLD3LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd32Pseudo_UPD)
  .value("VLD3LNd32_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd32_UPD)
  .value("VLD3LNd8", LIEF::assembly::arm::OPCODE::VLD3LNd8)
  .value("VLD3LNd8Pseudo", LIEF::assembly::arm::OPCODE::VLD3LNd8Pseudo)
  .value("VLD3LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd8Pseudo_UPD)
  .value("VLD3LNd8_UPD", LIEF::assembly::arm::OPCODE::VLD3LNd8_UPD)
  .value("VLD3LNq16", LIEF::assembly::arm::OPCODE::VLD3LNq16)
  .value("VLD3LNq16Pseudo", LIEF::assembly::arm::OPCODE::VLD3LNq16Pseudo)
  .value("VLD3LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3LNq16Pseudo_UPD)
  .value("VLD3LNq16_UPD", LIEF::assembly::arm::OPCODE::VLD3LNq16_UPD)
  .value("VLD3LNq32", LIEF::assembly::arm::OPCODE::VLD3LNq32)
  .value("VLD3LNq32Pseudo", LIEF::assembly::arm::OPCODE::VLD3LNq32Pseudo)
  .value("VLD3LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3LNq32Pseudo_UPD)
  .value("VLD3LNq32_UPD", LIEF::assembly::arm::OPCODE::VLD3LNq32_UPD)
  .value("VLD3d16", LIEF::assembly::arm::OPCODE::VLD3d16)
  .value("VLD3d16Pseudo", LIEF::assembly::arm::OPCODE::VLD3d16Pseudo)
  .value("VLD3d16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3d16Pseudo_UPD)
  .value("VLD3d16_UPD", LIEF::assembly::arm::OPCODE::VLD3d16_UPD)
  .value("VLD3d32", LIEF::assembly::arm::OPCODE::VLD3d32)
  .value("VLD3d32Pseudo", LIEF::assembly::arm::OPCODE::VLD3d32Pseudo)
  .value("VLD3d32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3d32Pseudo_UPD)
  .value("VLD3d32_UPD", LIEF::assembly::arm::OPCODE::VLD3d32_UPD)
  .value("VLD3d8", LIEF::assembly::arm::OPCODE::VLD3d8)
  .value("VLD3d8Pseudo", LIEF::assembly::arm::OPCODE::VLD3d8Pseudo)
  .value("VLD3d8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3d8Pseudo_UPD)
  .value("VLD3d8_UPD", LIEF::assembly::arm::OPCODE::VLD3d8_UPD)
  .value("VLD3q16", LIEF::assembly::arm::OPCODE::VLD3q16)
  .value("VLD3q16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q16Pseudo_UPD)
  .value("VLD3q16_UPD", LIEF::assembly::arm::OPCODE::VLD3q16_UPD)
  .value("VLD3q16oddPseudo", LIEF::assembly::arm::OPCODE::VLD3q16oddPseudo)
  .value("VLD3q16oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q16oddPseudo_UPD)
  .value("VLD3q32", LIEF::assembly::arm::OPCODE::VLD3q32)
  .value("VLD3q32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q32Pseudo_UPD)
  .value("VLD3q32_UPD", LIEF::assembly::arm::OPCODE::VLD3q32_UPD)
  .value("VLD3q32oddPseudo", LIEF::assembly::arm::OPCODE::VLD3q32oddPseudo)
  .value("VLD3q32oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q32oddPseudo_UPD)
  .value("VLD3q8", LIEF::assembly::arm::OPCODE::VLD3q8)
  .value("VLD3q8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q8Pseudo_UPD)
  .value("VLD3q8_UPD", LIEF::assembly::arm::OPCODE::VLD3q8_UPD)
  .value("VLD3q8oddPseudo", LIEF::assembly::arm::OPCODE::VLD3q8oddPseudo)
  .value("VLD3q8oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD3q8oddPseudo_UPD)
  .value("VLD4DUPd16", LIEF::assembly::arm::OPCODE::VLD4DUPd16)
  .value("VLD4DUPd16Pseudo", LIEF::assembly::arm::OPCODE::VLD4DUPd16Pseudo)
  .value("VLD4DUPd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd16Pseudo_UPD)
  .value("VLD4DUPd16_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd16_UPD)
  .value("VLD4DUPd32", LIEF::assembly::arm::OPCODE::VLD4DUPd32)
  .value("VLD4DUPd32Pseudo", LIEF::assembly::arm::OPCODE::VLD4DUPd32Pseudo)
  .value("VLD4DUPd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd32Pseudo_UPD)
  .value("VLD4DUPd32_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd32_UPD)
  .value("VLD4DUPd8", LIEF::assembly::arm::OPCODE::VLD4DUPd8)
  .value("VLD4DUPd8Pseudo", LIEF::assembly::arm::OPCODE::VLD4DUPd8Pseudo)
  .value("VLD4DUPd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd8Pseudo_UPD)
  .value("VLD4DUPd8_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPd8_UPD)
  .value("VLD4DUPq16", LIEF::assembly::arm::OPCODE::VLD4DUPq16)
  .value("VLD4DUPq16EvenPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq16EvenPseudo)
  .value("VLD4DUPq16OddPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq16OddPseudo)
  .value("VLD4DUPq16OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq16OddPseudo_UPD)
  .value("VLD4DUPq16_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq16_UPD)
  .value("VLD4DUPq32", LIEF::assembly::arm::OPCODE::VLD4DUPq32)
  .value("VLD4DUPq32EvenPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq32EvenPseudo)
  .value("VLD4DUPq32OddPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq32OddPseudo)
  .value("VLD4DUPq32OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq32OddPseudo_UPD)
  .value("VLD4DUPq32_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq32_UPD)
  .value("VLD4DUPq8", LIEF::assembly::arm::OPCODE::VLD4DUPq8)
  .value("VLD4DUPq8EvenPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq8EvenPseudo)
  .value("VLD4DUPq8OddPseudo", LIEF::assembly::arm::OPCODE::VLD4DUPq8OddPseudo)
  .value("VLD4DUPq8OddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq8OddPseudo_UPD)
  .value("VLD4DUPq8_UPD", LIEF::assembly::arm::OPCODE::VLD4DUPq8_UPD)
  .value("VLD4LNd16", LIEF::assembly::arm::OPCODE::VLD4LNd16)
  .value("VLD4LNd16Pseudo", LIEF::assembly::arm::OPCODE::VLD4LNd16Pseudo)
  .value("VLD4LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd16Pseudo_UPD)
  .value("VLD4LNd16_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd16_UPD)
  .value("VLD4LNd32", LIEF::assembly::arm::OPCODE::VLD4LNd32)
  .value("VLD4LNd32Pseudo", LIEF::assembly::arm::OPCODE::VLD4LNd32Pseudo)
  .value("VLD4LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd32Pseudo_UPD)
  .value("VLD4LNd32_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd32_UPD)
  .value("VLD4LNd8", LIEF::assembly::arm::OPCODE::VLD4LNd8)
  .value("VLD4LNd8Pseudo", LIEF::assembly::arm::OPCODE::VLD4LNd8Pseudo)
  .value("VLD4LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd8Pseudo_UPD)
  .value("VLD4LNd8_UPD", LIEF::assembly::arm::OPCODE::VLD4LNd8_UPD)
  .value("VLD4LNq16", LIEF::assembly::arm::OPCODE::VLD4LNq16)
  .value("VLD4LNq16Pseudo", LIEF::assembly::arm::OPCODE::VLD4LNq16Pseudo)
  .value("VLD4LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4LNq16Pseudo_UPD)
  .value("VLD4LNq16_UPD", LIEF::assembly::arm::OPCODE::VLD4LNq16_UPD)
  .value("VLD4LNq32", LIEF::assembly::arm::OPCODE::VLD4LNq32)
  .value("VLD4LNq32Pseudo", LIEF::assembly::arm::OPCODE::VLD4LNq32Pseudo)
  .value("VLD4LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4LNq32Pseudo_UPD)
  .value("VLD4LNq32_UPD", LIEF::assembly::arm::OPCODE::VLD4LNq32_UPD)
  .value("VLD4d16", LIEF::assembly::arm::OPCODE::VLD4d16)
  .value("VLD4d16Pseudo", LIEF::assembly::arm::OPCODE::VLD4d16Pseudo)
  .value("VLD4d16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4d16Pseudo_UPD)
  .value("VLD4d16_UPD", LIEF::assembly::arm::OPCODE::VLD4d16_UPD)
  .value("VLD4d32", LIEF::assembly::arm::OPCODE::VLD4d32)
  .value("VLD4d32Pseudo", LIEF::assembly::arm::OPCODE::VLD4d32Pseudo)
  .value("VLD4d32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4d32Pseudo_UPD)
  .value("VLD4d32_UPD", LIEF::assembly::arm::OPCODE::VLD4d32_UPD)
  .value("VLD4d8", LIEF::assembly::arm::OPCODE::VLD4d8)
  .value("VLD4d8Pseudo", LIEF::assembly::arm::OPCODE::VLD4d8Pseudo)
  .value("VLD4d8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4d8Pseudo_UPD)
  .value("VLD4d8_UPD", LIEF::assembly::arm::OPCODE::VLD4d8_UPD)
  .value("VLD4q16", LIEF::assembly::arm::OPCODE::VLD4q16)
  .value("VLD4q16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q16Pseudo_UPD)
  .value("VLD4q16_UPD", LIEF::assembly::arm::OPCODE::VLD4q16_UPD)
  .value("VLD4q16oddPseudo", LIEF::assembly::arm::OPCODE::VLD4q16oddPseudo)
  .value("VLD4q16oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q16oddPseudo_UPD)
  .value("VLD4q32", LIEF::assembly::arm::OPCODE::VLD4q32)
  .value("VLD4q32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q32Pseudo_UPD)
  .value("VLD4q32_UPD", LIEF::assembly::arm::OPCODE::VLD4q32_UPD)
  .value("VLD4q32oddPseudo", LIEF::assembly::arm::OPCODE::VLD4q32oddPseudo)
  .value("VLD4q32oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q32oddPseudo_UPD)
  .value("VLD4q8", LIEF::assembly::arm::OPCODE::VLD4q8)
  .value("VLD4q8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q8Pseudo_UPD)
  .value("VLD4q8_UPD", LIEF::assembly::arm::OPCODE::VLD4q8_UPD)
  .value("VLD4q8oddPseudo", LIEF::assembly::arm::OPCODE::VLD4q8oddPseudo)
  .value("VLD4q8oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VLD4q8oddPseudo_UPD)
  .value("VLDMDDB_UPD", LIEF::assembly::arm::OPCODE::VLDMDDB_UPD)
  .value("VLDMDIA", LIEF::assembly::arm::OPCODE::VLDMDIA)
  .value("VLDMDIA_UPD", LIEF::assembly::arm::OPCODE::VLDMDIA_UPD)
  .value("VLDMQIA", LIEF::assembly::arm::OPCODE::VLDMQIA)
  .value("VLDMSDB_UPD", LIEF::assembly::arm::OPCODE::VLDMSDB_UPD)
  .value("VLDMSIA", LIEF::assembly::arm::OPCODE::VLDMSIA)
  .value("VLDMSIA_UPD", LIEF::assembly::arm::OPCODE::VLDMSIA_UPD)
  .value("VLDRD", LIEF::assembly::arm::OPCODE::VLDRD)
  .value("VLDRH", LIEF::assembly::arm::OPCODE::VLDRH)
  .value("VLDRS", LIEF::assembly::arm::OPCODE::VLDRS)
  .value("VLDR_FPCXTNS_off", LIEF::assembly::arm::OPCODE::VLDR_FPCXTNS_off)
  .value("VLDR_FPCXTNS_post", LIEF::assembly::arm::OPCODE::VLDR_FPCXTNS_post)
  .value("VLDR_FPCXTNS_pre", LIEF::assembly::arm::OPCODE::VLDR_FPCXTNS_pre)
  .value("VLDR_FPCXTS_off", LIEF::assembly::arm::OPCODE::VLDR_FPCXTS_off)
  .value("VLDR_FPCXTS_post", LIEF::assembly::arm::OPCODE::VLDR_FPCXTS_post)
  .value("VLDR_FPCXTS_pre", LIEF::assembly::arm::OPCODE::VLDR_FPCXTS_pre)
  .value("VLDR_FPSCR_NZCVQC_off", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_NZCVQC_off)
  .value("VLDR_FPSCR_NZCVQC_post", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_NZCVQC_post)
  .value("VLDR_FPSCR_NZCVQC_pre", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_NZCVQC_pre)
  .value("VLDR_FPSCR_off", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_off)
  .value("VLDR_FPSCR_post", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_post)
  .value("VLDR_FPSCR_pre", LIEF::assembly::arm::OPCODE::VLDR_FPSCR_pre)
  .value("VLDR_P0_off", LIEF::assembly::arm::OPCODE::VLDR_P0_off)
  .value("VLDR_P0_post", LIEF::assembly::arm::OPCODE::VLDR_P0_post)
  .value("VLDR_P0_pre", LIEF::assembly::arm::OPCODE::VLDR_P0_pre)
  .value("VLDR_VPR_off", LIEF::assembly::arm::OPCODE::VLDR_VPR_off)
  .value("VLDR_VPR_post", LIEF::assembly::arm::OPCODE::VLDR_VPR_post)
  .value("VLDR_VPR_pre", LIEF::assembly::arm::OPCODE::VLDR_VPR_pre)
  .value("VLLDM", LIEF::assembly::arm::OPCODE::VLLDM)
  .value("VLLDM_T2", LIEF::assembly::arm::OPCODE::VLLDM_T2)
  .value("VLSTM", LIEF::assembly::arm::OPCODE::VLSTM)
  .value("VLSTM_T2", LIEF::assembly::arm::OPCODE::VLSTM_T2)
  .value("VMAXfd", LIEF::assembly::arm::OPCODE::VMAXfd)
  .value("VMAXfq", LIEF::assembly::arm::OPCODE::VMAXfq)
  .value("VMAXhd", LIEF::assembly::arm::OPCODE::VMAXhd)
  .value("VMAXhq", LIEF::assembly::arm::OPCODE::VMAXhq)
  .value("VMAXsv16i8", LIEF::assembly::arm::OPCODE::VMAXsv16i8)
  .value("VMAXsv2i32", LIEF::assembly::arm::OPCODE::VMAXsv2i32)
  .value("VMAXsv4i16", LIEF::assembly::arm::OPCODE::VMAXsv4i16)
  .value("VMAXsv4i32", LIEF::assembly::arm::OPCODE::VMAXsv4i32)
  .value("VMAXsv8i16", LIEF::assembly::arm::OPCODE::VMAXsv8i16)
  .value("VMAXsv8i8", LIEF::assembly::arm::OPCODE::VMAXsv8i8)
  .value("VMAXuv16i8", LIEF::assembly::arm::OPCODE::VMAXuv16i8)
  .value("VMAXuv2i32", LIEF::assembly::arm::OPCODE::VMAXuv2i32)
  .value("VMAXuv4i16", LIEF::assembly::arm::OPCODE::VMAXuv4i16)
  .value("VMAXuv4i32", LIEF::assembly::arm::OPCODE::VMAXuv4i32)
  .value("VMAXuv8i16", LIEF::assembly::arm::OPCODE::VMAXuv8i16)
  .value("VMAXuv8i8", LIEF::assembly::arm::OPCODE::VMAXuv8i8)
  .value("VMINfd", LIEF::assembly::arm::OPCODE::VMINfd)
  .value("VMINfq", LIEF::assembly::arm::OPCODE::VMINfq)
  .value("VMINhd", LIEF::assembly::arm::OPCODE::VMINhd)
  .value("VMINhq", LIEF::assembly::arm::OPCODE::VMINhq)
  .value("VMINsv16i8", LIEF::assembly::arm::OPCODE::VMINsv16i8)
  .value("VMINsv2i32", LIEF::assembly::arm::OPCODE::VMINsv2i32)
  .value("VMINsv4i16", LIEF::assembly::arm::OPCODE::VMINsv4i16)
  .value("VMINsv4i32", LIEF::assembly::arm::OPCODE::VMINsv4i32)
  .value("VMINsv8i16", LIEF::assembly::arm::OPCODE::VMINsv8i16)
  .value("VMINsv8i8", LIEF::assembly::arm::OPCODE::VMINsv8i8)
  .value("VMINuv16i8", LIEF::assembly::arm::OPCODE::VMINuv16i8)
  .value("VMINuv2i32", LIEF::assembly::arm::OPCODE::VMINuv2i32)
  .value("VMINuv4i16", LIEF::assembly::arm::OPCODE::VMINuv4i16)
  .value("VMINuv4i32", LIEF::assembly::arm::OPCODE::VMINuv4i32)
  .value("VMINuv8i16", LIEF::assembly::arm::OPCODE::VMINuv8i16)
  .value("VMINuv8i8", LIEF::assembly::arm::OPCODE::VMINuv8i8)
  .value("VMLAD", LIEF::assembly::arm::OPCODE::VMLAD)
  .value("VMLAH", LIEF::assembly::arm::OPCODE::VMLAH)
  .value("VMLALslsv2i32", LIEF::assembly::arm::OPCODE::VMLALslsv2i32)
  .value("VMLALslsv4i16", LIEF::assembly::arm::OPCODE::VMLALslsv4i16)
  .value("VMLALsluv2i32", LIEF::assembly::arm::OPCODE::VMLALsluv2i32)
  .value("VMLALsluv4i16", LIEF::assembly::arm::OPCODE::VMLALsluv4i16)
  .value("VMLALsv2i64", LIEF::assembly::arm::OPCODE::VMLALsv2i64)
  .value("VMLALsv4i32", LIEF::assembly::arm::OPCODE::VMLALsv4i32)
  .value("VMLALsv8i16", LIEF::assembly::arm::OPCODE::VMLALsv8i16)
  .value("VMLALuv2i64", LIEF::assembly::arm::OPCODE::VMLALuv2i64)
  .value("VMLALuv4i32", LIEF::assembly::arm::OPCODE::VMLALuv4i32)
  .value("VMLALuv8i16", LIEF::assembly::arm::OPCODE::VMLALuv8i16)
  .value("VMLAS", LIEF::assembly::arm::OPCODE::VMLAS)
  .value("VMLAfd", LIEF::assembly::arm::OPCODE::VMLAfd)
  .value("VMLAfq", LIEF::assembly::arm::OPCODE::VMLAfq)
  .value("VMLAhd", LIEF::assembly::arm::OPCODE::VMLAhd)
  .value("VMLAhq", LIEF::assembly::arm::OPCODE::VMLAhq)
  .value("VMLAslfd", LIEF::assembly::arm::OPCODE::VMLAslfd)
  .value("VMLAslfq", LIEF::assembly::arm::OPCODE::VMLAslfq)
  .value("VMLAslhd", LIEF::assembly::arm::OPCODE::VMLAslhd)
  .value("VMLAslhq", LIEF::assembly::arm::OPCODE::VMLAslhq)
  .value("VMLAslv2i32", LIEF::assembly::arm::OPCODE::VMLAslv2i32)
  .value("VMLAslv4i16", LIEF::assembly::arm::OPCODE::VMLAslv4i16)
  .value("VMLAslv4i32", LIEF::assembly::arm::OPCODE::VMLAslv4i32)
  .value("VMLAslv8i16", LIEF::assembly::arm::OPCODE::VMLAslv8i16)
  .value("VMLAv16i8", LIEF::assembly::arm::OPCODE::VMLAv16i8)
  .value("VMLAv2i32", LIEF::assembly::arm::OPCODE::VMLAv2i32)
  .value("VMLAv4i16", LIEF::assembly::arm::OPCODE::VMLAv4i16)
  .value("VMLAv4i32", LIEF::assembly::arm::OPCODE::VMLAv4i32)
  .value("VMLAv8i16", LIEF::assembly::arm::OPCODE::VMLAv8i16)
  .value("VMLAv8i8", LIEF::assembly::arm::OPCODE::VMLAv8i8)
  .value("VMLSD", LIEF::assembly::arm::OPCODE::VMLSD)
  .value("VMLSH", LIEF::assembly::arm::OPCODE::VMLSH)
  .value("VMLSLslsv2i32", LIEF::assembly::arm::OPCODE::VMLSLslsv2i32)
  .value("VMLSLslsv4i16", LIEF::assembly::arm::OPCODE::VMLSLslsv4i16)
  .value("VMLSLsluv2i32", LIEF::assembly::arm::OPCODE::VMLSLsluv2i32)
  .value("VMLSLsluv4i16", LIEF::assembly::arm::OPCODE::VMLSLsluv4i16)
  .value("VMLSLsv2i64", LIEF::assembly::arm::OPCODE::VMLSLsv2i64)
  .value("VMLSLsv4i32", LIEF::assembly::arm::OPCODE::VMLSLsv4i32)
  .value("VMLSLsv8i16", LIEF::assembly::arm::OPCODE::VMLSLsv8i16)
  .value("VMLSLuv2i64", LIEF::assembly::arm::OPCODE::VMLSLuv2i64)
  .value("VMLSLuv4i32", LIEF::assembly::arm::OPCODE::VMLSLuv4i32)
  .value("VMLSLuv8i16", LIEF::assembly::arm::OPCODE::VMLSLuv8i16)
  .value("VMLSS", LIEF::assembly::arm::OPCODE::VMLSS)
  .value("VMLSfd", LIEF::assembly::arm::OPCODE::VMLSfd)
  .value("VMLSfq", LIEF::assembly::arm::OPCODE::VMLSfq)
  .value("VMLShd", LIEF::assembly::arm::OPCODE::VMLShd)
  .value("VMLShq", LIEF::assembly::arm::OPCODE::VMLShq)
  .value("VMLSslfd", LIEF::assembly::arm::OPCODE::VMLSslfd)
  .value("VMLSslfq", LIEF::assembly::arm::OPCODE::VMLSslfq)
  .value("VMLSslhd", LIEF::assembly::arm::OPCODE::VMLSslhd)
  .value("VMLSslhq", LIEF::assembly::arm::OPCODE::VMLSslhq)
  .value("VMLSslv2i32", LIEF::assembly::arm::OPCODE::VMLSslv2i32)
  .value("VMLSslv4i16", LIEF::assembly::arm::OPCODE::VMLSslv4i16)
  .value("VMLSslv4i32", LIEF::assembly::arm::OPCODE::VMLSslv4i32)
  .value("VMLSslv8i16", LIEF::assembly::arm::OPCODE::VMLSslv8i16)
  .value("VMLSv16i8", LIEF::assembly::arm::OPCODE::VMLSv16i8)
  .value("VMLSv2i32", LIEF::assembly::arm::OPCODE::VMLSv2i32)
  .value("VMLSv4i16", LIEF::assembly::arm::OPCODE::VMLSv4i16)
  .value("VMLSv4i32", LIEF::assembly::arm::OPCODE::VMLSv4i32)
  .value("VMLSv8i16", LIEF::assembly::arm::OPCODE::VMLSv8i16)
  .value("VMLSv8i8", LIEF::assembly::arm::OPCODE::VMLSv8i8);
  opcodes.value("VMMLA", LIEF::assembly::arm::OPCODE::VMMLA)
  .value("VMOVD", LIEF::assembly::arm::OPCODE::VMOVD)
  .value("VMOVDRR", LIEF::assembly::arm::OPCODE::VMOVDRR)
  .value("VMOVH", LIEF::assembly::arm::OPCODE::VMOVH)
  .value("VMOVHR", LIEF::assembly::arm::OPCODE::VMOVHR)
  .value("VMOVLsv2i64", LIEF::assembly::arm::OPCODE::VMOVLsv2i64)
  .value("VMOVLsv4i32", LIEF::assembly::arm::OPCODE::VMOVLsv4i32)
  .value("VMOVLsv8i16", LIEF::assembly::arm::OPCODE::VMOVLsv8i16)
  .value("VMOVLuv2i64", LIEF::assembly::arm::OPCODE::VMOVLuv2i64)
  .value("VMOVLuv4i32", LIEF::assembly::arm::OPCODE::VMOVLuv4i32)
  .value("VMOVLuv8i16", LIEF::assembly::arm::OPCODE::VMOVLuv8i16)
  .value("VMOVNv2i32", LIEF::assembly::arm::OPCODE::VMOVNv2i32)
  .value("VMOVNv4i16", LIEF::assembly::arm::OPCODE::VMOVNv4i16)
  .value("VMOVNv8i8", LIEF::assembly::arm::OPCODE::VMOVNv8i8)
  .value("VMOVRH", LIEF::assembly::arm::OPCODE::VMOVRH)
  .value("VMOVRRD", LIEF::assembly::arm::OPCODE::VMOVRRD)
  .value("VMOVRRS", LIEF::assembly::arm::OPCODE::VMOVRRS)
  .value("VMOVRS", LIEF::assembly::arm::OPCODE::VMOVRS)
  .value("VMOVS", LIEF::assembly::arm::OPCODE::VMOVS)
  .value("VMOVSR", LIEF::assembly::arm::OPCODE::VMOVSR)
  .value("VMOVSRR", LIEF::assembly::arm::OPCODE::VMOVSRR)
  .value("VMOVv16i8", LIEF::assembly::arm::OPCODE::VMOVv16i8)
  .value("VMOVv1i64", LIEF::assembly::arm::OPCODE::VMOVv1i64)
  .value("VMOVv2f32", LIEF::assembly::arm::OPCODE::VMOVv2f32)
  .value("VMOVv2i32", LIEF::assembly::arm::OPCODE::VMOVv2i32)
  .value("VMOVv2i64", LIEF::assembly::arm::OPCODE::VMOVv2i64)
  .value("VMOVv4f32", LIEF::assembly::arm::OPCODE::VMOVv4f32)
  .value("VMOVv4i16", LIEF::assembly::arm::OPCODE::VMOVv4i16)
  .value("VMOVv4i32", LIEF::assembly::arm::OPCODE::VMOVv4i32)
  .value("VMOVv8i16", LIEF::assembly::arm::OPCODE::VMOVv8i16)
  .value("VMOVv8i8", LIEF::assembly::arm::OPCODE::VMOVv8i8)
  .value("VMRS", LIEF::assembly::arm::OPCODE::VMRS)
  .value("VMRS_FPCXTNS", LIEF::assembly::arm::OPCODE::VMRS_FPCXTNS)
  .value("VMRS_FPCXTS", LIEF::assembly::arm::OPCODE::VMRS_FPCXTS)
  .value("VMRS_FPEXC", LIEF::assembly::arm::OPCODE::VMRS_FPEXC)
  .value("VMRS_FPINST", LIEF::assembly::arm::OPCODE::VMRS_FPINST)
  .value("VMRS_FPINST2", LIEF::assembly::arm::OPCODE::VMRS_FPINST2)
  .value("VMRS_FPSCR_NZCVQC", LIEF::assembly::arm::OPCODE::VMRS_FPSCR_NZCVQC)
  .value("VMRS_FPSID", LIEF::assembly::arm::OPCODE::VMRS_FPSID)
  .value("VMRS_MVFR0", LIEF::assembly::arm::OPCODE::VMRS_MVFR0)
  .value("VMRS_MVFR1", LIEF::assembly::arm::OPCODE::VMRS_MVFR1)
  .value("VMRS_MVFR2", LIEF::assembly::arm::OPCODE::VMRS_MVFR2)
  .value("VMRS_P0", LIEF::assembly::arm::OPCODE::VMRS_P0)
  .value("VMRS_VPR", LIEF::assembly::arm::OPCODE::VMRS_VPR)
  .value("VMSR", LIEF::assembly::arm::OPCODE::VMSR)
  .value("VMSR_FPCXTNS", LIEF::assembly::arm::OPCODE::VMSR_FPCXTNS)
  .value("VMSR_FPCXTS", LIEF::assembly::arm::OPCODE::VMSR_FPCXTS)
  .value("VMSR_FPEXC", LIEF::assembly::arm::OPCODE::VMSR_FPEXC)
  .value("VMSR_FPINST", LIEF::assembly::arm::OPCODE::VMSR_FPINST)
  .value("VMSR_FPINST2", LIEF::assembly::arm::OPCODE::VMSR_FPINST2)
  .value("VMSR_FPSCR_NZCVQC", LIEF::assembly::arm::OPCODE::VMSR_FPSCR_NZCVQC)
  .value("VMSR_FPSID", LIEF::assembly::arm::OPCODE::VMSR_FPSID)
  .value("VMSR_P0", LIEF::assembly::arm::OPCODE::VMSR_P0)
  .value("VMSR_VPR", LIEF::assembly::arm::OPCODE::VMSR_VPR)
  .value("VMULD", LIEF::assembly::arm::OPCODE::VMULD)
  .value("VMULH", LIEF::assembly::arm::OPCODE::VMULH)
  .value("VMULLp64", LIEF::assembly::arm::OPCODE::VMULLp64)
  .value("VMULLp8", LIEF::assembly::arm::OPCODE::VMULLp8)
  .value("VMULLslsv2i32", LIEF::assembly::arm::OPCODE::VMULLslsv2i32)
  .value("VMULLslsv4i16", LIEF::assembly::arm::OPCODE::VMULLslsv4i16)
  .value("VMULLsluv2i32", LIEF::assembly::arm::OPCODE::VMULLsluv2i32)
  .value("VMULLsluv4i16", LIEF::assembly::arm::OPCODE::VMULLsluv4i16)
  .value("VMULLsv2i64", LIEF::assembly::arm::OPCODE::VMULLsv2i64)
  .value("VMULLsv4i32", LIEF::assembly::arm::OPCODE::VMULLsv4i32)
  .value("VMULLsv8i16", LIEF::assembly::arm::OPCODE::VMULLsv8i16)
  .value("VMULLuv2i64", LIEF::assembly::arm::OPCODE::VMULLuv2i64)
  .value("VMULLuv4i32", LIEF::assembly::arm::OPCODE::VMULLuv4i32)
  .value("VMULLuv8i16", LIEF::assembly::arm::OPCODE::VMULLuv8i16)
  .value("VMULS", LIEF::assembly::arm::OPCODE::VMULS)
  .value("VMULfd", LIEF::assembly::arm::OPCODE::VMULfd)
  .value("VMULfq", LIEF::assembly::arm::OPCODE::VMULfq)
  .value("VMULhd", LIEF::assembly::arm::OPCODE::VMULhd)
  .value("VMULhq", LIEF::assembly::arm::OPCODE::VMULhq)
  .value("VMULpd", LIEF::assembly::arm::OPCODE::VMULpd)
  .value("VMULpq", LIEF::assembly::arm::OPCODE::VMULpq)
  .value("VMULslfd", LIEF::assembly::arm::OPCODE::VMULslfd)
  .value("VMULslfq", LIEF::assembly::arm::OPCODE::VMULslfq)
  .value("VMULslhd", LIEF::assembly::arm::OPCODE::VMULslhd)
  .value("VMULslhq", LIEF::assembly::arm::OPCODE::VMULslhq)
  .value("VMULslv2i32", LIEF::assembly::arm::OPCODE::VMULslv2i32)
  .value("VMULslv4i16", LIEF::assembly::arm::OPCODE::VMULslv4i16)
  .value("VMULslv4i32", LIEF::assembly::arm::OPCODE::VMULslv4i32)
  .value("VMULslv8i16", LIEF::assembly::arm::OPCODE::VMULslv8i16)
  .value("VMULv16i8", LIEF::assembly::arm::OPCODE::VMULv16i8)
  .value("VMULv2i32", LIEF::assembly::arm::OPCODE::VMULv2i32)
  .value("VMULv4i16", LIEF::assembly::arm::OPCODE::VMULv4i16)
  .value("VMULv4i32", LIEF::assembly::arm::OPCODE::VMULv4i32)
  .value("VMULv8i16", LIEF::assembly::arm::OPCODE::VMULv8i16)
  .value("VMULv8i8", LIEF::assembly::arm::OPCODE::VMULv8i8)
  .value("VMVNd", LIEF::assembly::arm::OPCODE::VMVNd)
  .value("VMVNq", LIEF::assembly::arm::OPCODE::VMVNq)
  .value("VMVNv2i32", LIEF::assembly::arm::OPCODE::VMVNv2i32)
  .value("VMVNv4i16", LIEF::assembly::arm::OPCODE::VMVNv4i16)
  .value("VMVNv4i32", LIEF::assembly::arm::OPCODE::VMVNv4i32)
  .value("VMVNv8i16", LIEF::assembly::arm::OPCODE::VMVNv8i16)
  .value("VNEGD", LIEF::assembly::arm::OPCODE::VNEGD)
  .value("VNEGH", LIEF::assembly::arm::OPCODE::VNEGH)
  .value("VNEGS", LIEF::assembly::arm::OPCODE::VNEGS)
  .value("VNEGf32q", LIEF::assembly::arm::OPCODE::VNEGf32q)
  .value("VNEGfd", LIEF::assembly::arm::OPCODE::VNEGfd)
  .value("VNEGhd", LIEF::assembly::arm::OPCODE::VNEGhd)
  .value("VNEGhq", LIEF::assembly::arm::OPCODE::VNEGhq)
  .value("VNEGs16d", LIEF::assembly::arm::OPCODE::VNEGs16d)
  .value("VNEGs16q", LIEF::assembly::arm::OPCODE::VNEGs16q)
  .value("VNEGs32d", LIEF::assembly::arm::OPCODE::VNEGs32d)
  .value("VNEGs32q", LIEF::assembly::arm::OPCODE::VNEGs32q)
  .value("VNEGs8d", LIEF::assembly::arm::OPCODE::VNEGs8d)
  .value("VNEGs8q", LIEF::assembly::arm::OPCODE::VNEGs8q)
  .value("VNMLAD", LIEF::assembly::arm::OPCODE::VNMLAD)
  .value("VNMLAH", LIEF::assembly::arm::OPCODE::VNMLAH)
  .value("VNMLAS", LIEF::assembly::arm::OPCODE::VNMLAS)
  .value("VNMLSD", LIEF::assembly::arm::OPCODE::VNMLSD)
  .value("VNMLSH", LIEF::assembly::arm::OPCODE::VNMLSH)
  .value("VNMLSS", LIEF::assembly::arm::OPCODE::VNMLSS)
  .value("VNMULD", LIEF::assembly::arm::OPCODE::VNMULD)
  .value("VNMULH", LIEF::assembly::arm::OPCODE::VNMULH)
  .value("VNMULS", LIEF::assembly::arm::OPCODE::VNMULS)
  .value("VORNd", LIEF::assembly::arm::OPCODE::VORNd)
  .value("VORNq", LIEF::assembly::arm::OPCODE::VORNq)
  .value("VORRd", LIEF::assembly::arm::OPCODE::VORRd)
  .value("VORRiv2i32", LIEF::assembly::arm::OPCODE::VORRiv2i32)
  .value("VORRiv4i16", LIEF::assembly::arm::OPCODE::VORRiv4i16)
  .value("VORRiv4i32", LIEF::assembly::arm::OPCODE::VORRiv4i32)
  .value("VORRiv8i16", LIEF::assembly::arm::OPCODE::VORRiv8i16)
  .value("VORRq", LIEF::assembly::arm::OPCODE::VORRq)
  .value("VPADALsv16i8", LIEF::assembly::arm::OPCODE::VPADALsv16i8)
  .value("VPADALsv2i32", LIEF::assembly::arm::OPCODE::VPADALsv2i32)
  .value("VPADALsv4i16", LIEF::assembly::arm::OPCODE::VPADALsv4i16)
  .value("VPADALsv4i32", LIEF::assembly::arm::OPCODE::VPADALsv4i32)
  .value("VPADALsv8i16", LIEF::assembly::arm::OPCODE::VPADALsv8i16)
  .value("VPADALsv8i8", LIEF::assembly::arm::OPCODE::VPADALsv8i8)
  .value("VPADALuv16i8", LIEF::assembly::arm::OPCODE::VPADALuv16i8)
  .value("VPADALuv2i32", LIEF::assembly::arm::OPCODE::VPADALuv2i32)
  .value("VPADALuv4i16", LIEF::assembly::arm::OPCODE::VPADALuv4i16)
  .value("VPADALuv4i32", LIEF::assembly::arm::OPCODE::VPADALuv4i32)
  .value("VPADALuv8i16", LIEF::assembly::arm::OPCODE::VPADALuv8i16)
  .value("VPADALuv8i8", LIEF::assembly::arm::OPCODE::VPADALuv8i8)
  .value("VPADDLsv16i8", LIEF::assembly::arm::OPCODE::VPADDLsv16i8)
  .value("VPADDLsv2i32", LIEF::assembly::arm::OPCODE::VPADDLsv2i32)
  .value("VPADDLsv4i16", LIEF::assembly::arm::OPCODE::VPADDLsv4i16)
  .value("VPADDLsv4i32", LIEF::assembly::arm::OPCODE::VPADDLsv4i32)
  .value("VPADDLsv8i16", LIEF::assembly::arm::OPCODE::VPADDLsv8i16)
  .value("VPADDLsv8i8", LIEF::assembly::arm::OPCODE::VPADDLsv8i8)
  .value("VPADDLuv16i8", LIEF::assembly::arm::OPCODE::VPADDLuv16i8)
  .value("VPADDLuv2i32", LIEF::assembly::arm::OPCODE::VPADDLuv2i32)
  .value("VPADDLuv4i16", LIEF::assembly::arm::OPCODE::VPADDLuv4i16)
  .value("VPADDLuv4i32", LIEF::assembly::arm::OPCODE::VPADDLuv4i32)
  .value("VPADDLuv8i16", LIEF::assembly::arm::OPCODE::VPADDLuv8i16)
  .value("VPADDLuv8i8", LIEF::assembly::arm::OPCODE::VPADDLuv8i8)
  .value("VPADDf", LIEF::assembly::arm::OPCODE::VPADDf)
  .value("VPADDh", LIEF::assembly::arm::OPCODE::VPADDh)
  .value("VPADDi16", LIEF::assembly::arm::OPCODE::VPADDi16)
  .value("VPADDi32", LIEF::assembly::arm::OPCODE::VPADDi32)
  .value("VPADDi8", LIEF::assembly::arm::OPCODE::VPADDi8)
  .value("VPMAXf", LIEF::assembly::arm::OPCODE::VPMAXf)
  .value("VPMAXh", LIEF::assembly::arm::OPCODE::VPMAXh)
  .value("VPMAXs16", LIEF::assembly::arm::OPCODE::VPMAXs16)
  .value("VPMAXs32", LIEF::assembly::arm::OPCODE::VPMAXs32)
  .value("VPMAXs8", LIEF::assembly::arm::OPCODE::VPMAXs8)
  .value("VPMAXu16", LIEF::assembly::arm::OPCODE::VPMAXu16)
  .value("VPMAXu32", LIEF::assembly::arm::OPCODE::VPMAXu32)
  .value("VPMAXu8", LIEF::assembly::arm::OPCODE::VPMAXu8)
  .value("VPMINf", LIEF::assembly::arm::OPCODE::VPMINf)
  .value("VPMINh", LIEF::assembly::arm::OPCODE::VPMINh)
  .value("VPMINs16", LIEF::assembly::arm::OPCODE::VPMINs16)
  .value("VPMINs32", LIEF::assembly::arm::OPCODE::VPMINs32)
  .value("VPMINs8", LIEF::assembly::arm::OPCODE::VPMINs8)
  .value("VPMINu16", LIEF::assembly::arm::OPCODE::VPMINu16)
  .value("VPMINu32", LIEF::assembly::arm::OPCODE::VPMINu32)
  .value("VPMINu8", LIEF::assembly::arm::OPCODE::VPMINu8)
  .value("VQABSv16i8", LIEF::assembly::arm::OPCODE::VQABSv16i8)
  .value("VQABSv2i32", LIEF::assembly::arm::OPCODE::VQABSv2i32)
  .value("VQABSv4i16", LIEF::assembly::arm::OPCODE::VQABSv4i16)
  .value("VQABSv4i32", LIEF::assembly::arm::OPCODE::VQABSv4i32)
  .value("VQABSv8i16", LIEF::assembly::arm::OPCODE::VQABSv8i16)
  .value("VQABSv8i8", LIEF::assembly::arm::OPCODE::VQABSv8i8)
  .value("VQADDsv16i8", LIEF::assembly::arm::OPCODE::VQADDsv16i8)
  .value("VQADDsv1i64", LIEF::assembly::arm::OPCODE::VQADDsv1i64)
  .value("VQADDsv2i32", LIEF::assembly::arm::OPCODE::VQADDsv2i32)
  .value("VQADDsv2i64", LIEF::assembly::arm::OPCODE::VQADDsv2i64)
  .value("VQADDsv4i16", LIEF::assembly::arm::OPCODE::VQADDsv4i16)
  .value("VQADDsv4i32", LIEF::assembly::arm::OPCODE::VQADDsv4i32)
  .value("VQADDsv8i16", LIEF::assembly::arm::OPCODE::VQADDsv8i16)
  .value("VQADDsv8i8", LIEF::assembly::arm::OPCODE::VQADDsv8i8)
  .value("VQADDuv16i8", LIEF::assembly::arm::OPCODE::VQADDuv16i8)
  .value("VQADDuv1i64", LIEF::assembly::arm::OPCODE::VQADDuv1i64)
  .value("VQADDuv2i32", LIEF::assembly::arm::OPCODE::VQADDuv2i32)
  .value("VQADDuv2i64", LIEF::assembly::arm::OPCODE::VQADDuv2i64)
  .value("VQADDuv4i16", LIEF::assembly::arm::OPCODE::VQADDuv4i16)
  .value("VQADDuv4i32", LIEF::assembly::arm::OPCODE::VQADDuv4i32)
  .value("VQADDuv8i16", LIEF::assembly::arm::OPCODE::VQADDuv8i16)
  .value("VQADDuv8i8", LIEF::assembly::arm::OPCODE::VQADDuv8i8)
  .value("VQDMLALslv2i32", LIEF::assembly::arm::OPCODE::VQDMLALslv2i32)
  .value("VQDMLALslv4i16", LIEF::assembly::arm::OPCODE::VQDMLALslv4i16)
  .value("VQDMLALv2i64", LIEF::assembly::arm::OPCODE::VQDMLALv2i64)
  .value("VQDMLALv4i32", LIEF::assembly::arm::OPCODE::VQDMLALv4i32)
  .value("VQDMLSLslv2i32", LIEF::assembly::arm::OPCODE::VQDMLSLslv2i32)
  .value("VQDMLSLslv4i16", LIEF::assembly::arm::OPCODE::VQDMLSLslv4i16)
  .value("VQDMLSLv2i64", LIEF::assembly::arm::OPCODE::VQDMLSLv2i64)
  .value("VQDMLSLv4i32", LIEF::assembly::arm::OPCODE::VQDMLSLv4i32)
  .value("VQDMULHslv2i32", LIEF::assembly::arm::OPCODE::VQDMULHslv2i32)
  .value("VQDMULHslv4i16", LIEF::assembly::arm::OPCODE::VQDMULHslv4i16)
  .value("VQDMULHslv4i32", LIEF::assembly::arm::OPCODE::VQDMULHslv4i32)
  .value("VQDMULHslv8i16", LIEF::assembly::arm::OPCODE::VQDMULHslv8i16)
  .value("VQDMULHv2i32", LIEF::assembly::arm::OPCODE::VQDMULHv2i32)
  .value("VQDMULHv4i16", LIEF::assembly::arm::OPCODE::VQDMULHv4i16)
  .value("VQDMULHv4i32", LIEF::assembly::arm::OPCODE::VQDMULHv4i32)
  .value("VQDMULHv8i16", LIEF::assembly::arm::OPCODE::VQDMULHv8i16)
  .value("VQDMULLslv2i32", LIEF::assembly::arm::OPCODE::VQDMULLslv2i32)
  .value("VQDMULLslv4i16", LIEF::assembly::arm::OPCODE::VQDMULLslv4i16)
  .value("VQDMULLv2i64", LIEF::assembly::arm::OPCODE::VQDMULLv2i64)
  .value("VQDMULLv4i32", LIEF::assembly::arm::OPCODE::VQDMULLv4i32)
  .value("VQMOVNsuv2i32", LIEF::assembly::arm::OPCODE::VQMOVNsuv2i32)
  .value("VQMOVNsuv4i16", LIEF::assembly::arm::OPCODE::VQMOVNsuv4i16)
  .value("VQMOVNsuv8i8", LIEF::assembly::arm::OPCODE::VQMOVNsuv8i8)
  .value("VQMOVNsv2i32", LIEF::assembly::arm::OPCODE::VQMOVNsv2i32)
  .value("VQMOVNsv4i16", LIEF::assembly::arm::OPCODE::VQMOVNsv4i16)
  .value("VQMOVNsv8i8", LIEF::assembly::arm::OPCODE::VQMOVNsv8i8)
  .value("VQMOVNuv2i32", LIEF::assembly::arm::OPCODE::VQMOVNuv2i32)
  .value("VQMOVNuv4i16", LIEF::assembly::arm::OPCODE::VQMOVNuv4i16)
  .value("VQMOVNuv8i8", LIEF::assembly::arm::OPCODE::VQMOVNuv8i8)
  .value("VQNEGv16i8", LIEF::assembly::arm::OPCODE::VQNEGv16i8)
  .value("VQNEGv2i32", LIEF::assembly::arm::OPCODE::VQNEGv2i32)
  .value("VQNEGv4i16", LIEF::assembly::arm::OPCODE::VQNEGv4i16)
  .value("VQNEGv4i32", LIEF::assembly::arm::OPCODE::VQNEGv4i32)
  .value("VQNEGv8i16", LIEF::assembly::arm::OPCODE::VQNEGv8i16)
  .value("VQNEGv8i8", LIEF::assembly::arm::OPCODE::VQNEGv8i8)
  .value("VQRDMLAHslv2i32", LIEF::assembly::arm::OPCODE::VQRDMLAHslv2i32)
  .value("VQRDMLAHslv4i16", LIEF::assembly::arm::OPCODE::VQRDMLAHslv4i16)
  .value("VQRDMLAHslv4i32", LIEF::assembly::arm::OPCODE::VQRDMLAHslv4i32)
  .value("VQRDMLAHslv8i16", LIEF::assembly::arm::OPCODE::VQRDMLAHslv8i16)
  .value("VQRDMLAHv2i32", LIEF::assembly::arm::OPCODE::VQRDMLAHv2i32)
  .value("VQRDMLAHv4i16", LIEF::assembly::arm::OPCODE::VQRDMLAHv4i16)
  .value("VQRDMLAHv4i32", LIEF::assembly::arm::OPCODE::VQRDMLAHv4i32)
  .value("VQRDMLAHv8i16", LIEF::assembly::arm::OPCODE::VQRDMLAHv8i16)
  .value("VQRDMLSHslv2i32", LIEF::assembly::arm::OPCODE::VQRDMLSHslv2i32)
  .value("VQRDMLSHslv4i16", LIEF::assembly::arm::OPCODE::VQRDMLSHslv4i16)
  .value("VQRDMLSHslv4i32", LIEF::assembly::arm::OPCODE::VQRDMLSHslv4i32)
  .value("VQRDMLSHslv8i16", LIEF::assembly::arm::OPCODE::VQRDMLSHslv8i16)
  .value("VQRDMLSHv2i32", LIEF::assembly::arm::OPCODE::VQRDMLSHv2i32)
  .value("VQRDMLSHv4i16", LIEF::assembly::arm::OPCODE::VQRDMLSHv4i16)
  .value("VQRDMLSHv4i32", LIEF::assembly::arm::OPCODE::VQRDMLSHv4i32)
  .value("VQRDMLSHv8i16", LIEF::assembly::arm::OPCODE::VQRDMLSHv8i16)
  .value("VQRDMULHslv2i32", LIEF::assembly::arm::OPCODE::VQRDMULHslv2i32)
  .value("VQRDMULHslv4i16", LIEF::assembly::arm::OPCODE::VQRDMULHslv4i16)
  .value("VQRDMULHslv4i32", LIEF::assembly::arm::OPCODE::VQRDMULHslv4i32)
  .value("VQRDMULHslv8i16", LIEF::assembly::arm::OPCODE::VQRDMULHslv8i16)
  .value("VQRDMULHv2i32", LIEF::assembly::arm::OPCODE::VQRDMULHv2i32)
  .value("VQRDMULHv4i16", LIEF::assembly::arm::OPCODE::VQRDMULHv4i16)
  .value("VQRDMULHv4i32", LIEF::assembly::arm::OPCODE::VQRDMULHv4i32)
  .value("VQRDMULHv8i16", LIEF::assembly::arm::OPCODE::VQRDMULHv8i16)
  .value("VQRSHLsv16i8", LIEF::assembly::arm::OPCODE::VQRSHLsv16i8)
  .value("VQRSHLsv1i64", LIEF::assembly::arm::OPCODE::VQRSHLsv1i64)
  .value("VQRSHLsv2i32", LIEF::assembly::arm::OPCODE::VQRSHLsv2i32)
  .value("VQRSHLsv2i64", LIEF::assembly::arm::OPCODE::VQRSHLsv2i64)
  .value("VQRSHLsv4i16", LIEF::assembly::arm::OPCODE::VQRSHLsv4i16)
  .value("VQRSHLsv4i32", LIEF::assembly::arm::OPCODE::VQRSHLsv4i32)
  .value("VQRSHLsv8i16", LIEF::assembly::arm::OPCODE::VQRSHLsv8i16)
  .value("VQRSHLsv8i8", LIEF::assembly::arm::OPCODE::VQRSHLsv8i8)
  .value("VQRSHLuv16i8", LIEF::assembly::arm::OPCODE::VQRSHLuv16i8)
  .value("VQRSHLuv1i64", LIEF::assembly::arm::OPCODE::VQRSHLuv1i64)
  .value("VQRSHLuv2i32", LIEF::assembly::arm::OPCODE::VQRSHLuv2i32)
  .value("VQRSHLuv2i64", LIEF::assembly::arm::OPCODE::VQRSHLuv2i64)
  .value("VQRSHLuv4i16", LIEF::assembly::arm::OPCODE::VQRSHLuv4i16)
  .value("VQRSHLuv4i32", LIEF::assembly::arm::OPCODE::VQRSHLuv4i32)
  .value("VQRSHLuv8i16", LIEF::assembly::arm::OPCODE::VQRSHLuv8i16)
  .value("VQRSHLuv8i8", LIEF::assembly::arm::OPCODE::VQRSHLuv8i8)
  .value("VQRSHRNsv2i32", LIEF::assembly::arm::OPCODE::VQRSHRNsv2i32)
  .value("VQRSHRNsv4i16", LIEF::assembly::arm::OPCODE::VQRSHRNsv4i16)
  .value("VQRSHRNsv8i8", LIEF::assembly::arm::OPCODE::VQRSHRNsv8i8)
  .value("VQRSHRNuv2i32", LIEF::assembly::arm::OPCODE::VQRSHRNuv2i32)
  .value("VQRSHRNuv4i16", LIEF::assembly::arm::OPCODE::VQRSHRNuv4i16)
  .value("VQRSHRNuv8i8", LIEF::assembly::arm::OPCODE::VQRSHRNuv8i8)
  .value("VQRSHRUNv2i32", LIEF::assembly::arm::OPCODE::VQRSHRUNv2i32)
  .value("VQRSHRUNv4i16", LIEF::assembly::arm::OPCODE::VQRSHRUNv4i16)
  .value("VQRSHRUNv8i8", LIEF::assembly::arm::OPCODE::VQRSHRUNv8i8)
  .value("VQSHLsiv16i8", LIEF::assembly::arm::OPCODE::VQSHLsiv16i8)
  .value("VQSHLsiv1i64", LIEF::assembly::arm::OPCODE::VQSHLsiv1i64)
  .value("VQSHLsiv2i32", LIEF::assembly::arm::OPCODE::VQSHLsiv2i32)
  .value("VQSHLsiv2i64", LIEF::assembly::arm::OPCODE::VQSHLsiv2i64)
  .value("VQSHLsiv4i16", LIEF::assembly::arm::OPCODE::VQSHLsiv4i16)
  .value("VQSHLsiv4i32", LIEF::assembly::arm::OPCODE::VQSHLsiv4i32)
  .value("VQSHLsiv8i16", LIEF::assembly::arm::OPCODE::VQSHLsiv8i16)
  .value("VQSHLsiv8i8", LIEF::assembly::arm::OPCODE::VQSHLsiv8i8)
  .value("VQSHLsuv16i8", LIEF::assembly::arm::OPCODE::VQSHLsuv16i8)
  .value("VQSHLsuv1i64", LIEF::assembly::arm::OPCODE::VQSHLsuv1i64)
  .value("VQSHLsuv2i32", LIEF::assembly::arm::OPCODE::VQSHLsuv2i32)
  .value("VQSHLsuv2i64", LIEF::assembly::arm::OPCODE::VQSHLsuv2i64)
  .value("VQSHLsuv4i16", LIEF::assembly::arm::OPCODE::VQSHLsuv4i16)
  .value("VQSHLsuv4i32", LIEF::assembly::arm::OPCODE::VQSHLsuv4i32)
  .value("VQSHLsuv8i16", LIEF::assembly::arm::OPCODE::VQSHLsuv8i16)
  .value("VQSHLsuv8i8", LIEF::assembly::arm::OPCODE::VQSHLsuv8i8)
  .value("VQSHLsv16i8", LIEF::assembly::arm::OPCODE::VQSHLsv16i8)
  .value("VQSHLsv1i64", LIEF::assembly::arm::OPCODE::VQSHLsv1i64)
  .value("VQSHLsv2i32", LIEF::assembly::arm::OPCODE::VQSHLsv2i32)
  .value("VQSHLsv2i64", LIEF::assembly::arm::OPCODE::VQSHLsv2i64)
  .value("VQSHLsv4i16", LIEF::assembly::arm::OPCODE::VQSHLsv4i16)
  .value("VQSHLsv4i32", LIEF::assembly::arm::OPCODE::VQSHLsv4i32)
  .value("VQSHLsv8i16", LIEF::assembly::arm::OPCODE::VQSHLsv8i16)
  .value("VQSHLsv8i8", LIEF::assembly::arm::OPCODE::VQSHLsv8i8);
  opcodes.value("VQSHLuiv16i8", LIEF::assembly::arm::OPCODE::VQSHLuiv16i8)
  .value("VQSHLuiv1i64", LIEF::assembly::arm::OPCODE::VQSHLuiv1i64)
  .value("VQSHLuiv2i32", LIEF::assembly::arm::OPCODE::VQSHLuiv2i32)
  .value("VQSHLuiv2i64", LIEF::assembly::arm::OPCODE::VQSHLuiv2i64)
  .value("VQSHLuiv4i16", LIEF::assembly::arm::OPCODE::VQSHLuiv4i16)
  .value("VQSHLuiv4i32", LIEF::assembly::arm::OPCODE::VQSHLuiv4i32)
  .value("VQSHLuiv8i16", LIEF::assembly::arm::OPCODE::VQSHLuiv8i16)
  .value("VQSHLuiv8i8", LIEF::assembly::arm::OPCODE::VQSHLuiv8i8)
  .value("VQSHLuv16i8", LIEF::assembly::arm::OPCODE::VQSHLuv16i8)
  .value("VQSHLuv1i64", LIEF::assembly::arm::OPCODE::VQSHLuv1i64)
  .value("VQSHLuv2i32", LIEF::assembly::arm::OPCODE::VQSHLuv2i32)
  .value("VQSHLuv2i64", LIEF::assembly::arm::OPCODE::VQSHLuv2i64)
  .value("VQSHLuv4i16", LIEF::assembly::arm::OPCODE::VQSHLuv4i16)
  .value("VQSHLuv4i32", LIEF::assembly::arm::OPCODE::VQSHLuv4i32)
  .value("VQSHLuv8i16", LIEF::assembly::arm::OPCODE::VQSHLuv8i16)
  .value("VQSHLuv8i8", LIEF::assembly::arm::OPCODE::VQSHLuv8i8)
  .value("VQSHRNsv2i32", LIEF::assembly::arm::OPCODE::VQSHRNsv2i32)
  .value("VQSHRNsv4i16", LIEF::assembly::arm::OPCODE::VQSHRNsv4i16)
  .value("VQSHRNsv8i8", LIEF::assembly::arm::OPCODE::VQSHRNsv8i8)
  .value("VQSHRNuv2i32", LIEF::assembly::arm::OPCODE::VQSHRNuv2i32)
  .value("VQSHRNuv4i16", LIEF::assembly::arm::OPCODE::VQSHRNuv4i16)
  .value("VQSHRNuv8i8", LIEF::assembly::arm::OPCODE::VQSHRNuv8i8)
  .value("VQSHRUNv2i32", LIEF::assembly::arm::OPCODE::VQSHRUNv2i32)
  .value("VQSHRUNv4i16", LIEF::assembly::arm::OPCODE::VQSHRUNv4i16)
  .value("VQSHRUNv8i8", LIEF::assembly::arm::OPCODE::VQSHRUNv8i8)
  .value("VQSUBsv16i8", LIEF::assembly::arm::OPCODE::VQSUBsv16i8)
  .value("VQSUBsv1i64", LIEF::assembly::arm::OPCODE::VQSUBsv1i64)
  .value("VQSUBsv2i32", LIEF::assembly::arm::OPCODE::VQSUBsv2i32)
  .value("VQSUBsv2i64", LIEF::assembly::arm::OPCODE::VQSUBsv2i64)
  .value("VQSUBsv4i16", LIEF::assembly::arm::OPCODE::VQSUBsv4i16)
  .value("VQSUBsv4i32", LIEF::assembly::arm::OPCODE::VQSUBsv4i32)
  .value("VQSUBsv8i16", LIEF::assembly::arm::OPCODE::VQSUBsv8i16)
  .value("VQSUBsv8i8", LIEF::assembly::arm::OPCODE::VQSUBsv8i8)
  .value("VQSUBuv16i8", LIEF::assembly::arm::OPCODE::VQSUBuv16i8)
  .value("VQSUBuv1i64", LIEF::assembly::arm::OPCODE::VQSUBuv1i64)
  .value("VQSUBuv2i32", LIEF::assembly::arm::OPCODE::VQSUBuv2i32)
  .value("VQSUBuv2i64", LIEF::assembly::arm::OPCODE::VQSUBuv2i64)
  .value("VQSUBuv4i16", LIEF::assembly::arm::OPCODE::VQSUBuv4i16)
  .value("VQSUBuv4i32", LIEF::assembly::arm::OPCODE::VQSUBuv4i32)
  .value("VQSUBuv8i16", LIEF::assembly::arm::OPCODE::VQSUBuv8i16)
  .value("VQSUBuv8i8", LIEF::assembly::arm::OPCODE::VQSUBuv8i8)
  .value("VRADDHNv2i32", LIEF::assembly::arm::OPCODE::VRADDHNv2i32)
  .value("VRADDHNv4i16", LIEF::assembly::arm::OPCODE::VRADDHNv4i16)
  .value("VRADDHNv8i8", LIEF::assembly::arm::OPCODE::VRADDHNv8i8)
  .value("VRECPEd", LIEF::assembly::arm::OPCODE::VRECPEd)
  .value("VRECPEfd", LIEF::assembly::arm::OPCODE::VRECPEfd)
  .value("VRECPEfq", LIEF::assembly::arm::OPCODE::VRECPEfq)
  .value("VRECPEhd", LIEF::assembly::arm::OPCODE::VRECPEhd)
  .value("VRECPEhq", LIEF::assembly::arm::OPCODE::VRECPEhq)
  .value("VRECPEq", LIEF::assembly::arm::OPCODE::VRECPEq)
  .value("VRECPSfd", LIEF::assembly::arm::OPCODE::VRECPSfd)
  .value("VRECPSfq", LIEF::assembly::arm::OPCODE::VRECPSfq)
  .value("VRECPShd", LIEF::assembly::arm::OPCODE::VRECPShd)
  .value("VRECPShq", LIEF::assembly::arm::OPCODE::VRECPShq)
  .value("VREV16d8", LIEF::assembly::arm::OPCODE::VREV16d8)
  .value("VREV16q8", LIEF::assembly::arm::OPCODE::VREV16q8)
  .value("VREV32d16", LIEF::assembly::arm::OPCODE::VREV32d16)
  .value("VREV32d8", LIEF::assembly::arm::OPCODE::VREV32d8)
  .value("VREV32q16", LIEF::assembly::arm::OPCODE::VREV32q16)
  .value("VREV32q8", LIEF::assembly::arm::OPCODE::VREV32q8)
  .value("VREV64d16", LIEF::assembly::arm::OPCODE::VREV64d16)
  .value("VREV64d32", LIEF::assembly::arm::OPCODE::VREV64d32)
  .value("VREV64d8", LIEF::assembly::arm::OPCODE::VREV64d8)
  .value("VREV64q16", LIEF::assembly::arm::OPCODE::VREV64q16)
  .value("VREV64q32", LIEF::assembly::arm::OPCODE::VREV64q32)
  .value("VREV64q8", LIEF::assembly::arm::OPCODE::VREV64q8)
  .value("VRHADDsv16i8", LIEF::assembly::arm::OPCODE::VRHADDsv16i8)
  .value("VRHADDsv2i32", LIEF::assembly::arm::OPCODE::VRHADDsv2i32)
  .value("VRHADDsv4i16", LIEF::assembly::arm::OPCODE::VRHADDsv4i16)
  .value("VRHADDsv4i32", LIEF::assembly::arm::OPCODE::VRHADDsv4i32)
  .value("VRHADDsv8i16", LIEF::assembly::arm::OPCODE::VRHADDsv8i16)
  .value("VRHADDsv8i8", LIEF::assembly::arm::OPCODE::VRHADDsv8i8)
  .value("VRHADDuv16i8", LIEF::assembly::arm::OPCODE::VRHADDuv16i8)
  .value("VRHADDuv2i32", LIEF::assembly::arm::OPCODE::VRHADDuv2i32)
  .value("VRHADDuv4i16", LIEF::assembly::arm::OPCODE::VRHADDuv4i16)
  .value("VRHADDuv4i32", LIEF::assembly::arm::OPCODE::VRHADDuv4i32)
  .value("VRHADDuv8i16", LIEF::assembly::arm::OPCODE::VRHADDuv8i16)
  .value("VRHADDuv8i8", LIEF::assembly::arm::OPCODE::VRHADDuv8i8)
  .value("VRINTAD", LIEF::assembly::arm::OPCODE::VRINTAD)
  .value("VRINTAH", LIEF::assembly::arm::OPCODE::VRINTAH)
  .value("VRINTANDf", LIEF::assembly::arm::OPCODE::VRINTANDf)
  .value("VRINTANDh", LIEF::assembly::arm::OPCODE::VRINTANDh)
  .value("VRINTANQf", LIEF::assembly::arm::OPCODE::VRINTANQf)
  .value("VRINTANQh", LIEF::assembly::arm::OPCODE::VRINTANQh)
  .value("VRINTAS", LIEF::assembly::arm::OPCODE::VRINTAS)
  .value("VRINTMD", LIEF::assembly::arm::OPCODE::VRINTMD)
  .value("VRINTMH", LIEF::assembly::arm::OPCODE::VRINTMH)
  .value("VRINTMNDf", LIEF::assembly::arm::OPCODE::VRINTMNDf)
  .value("VRINTMNDh", LIEF::assembly::arm::OPCODE::VRINTMNDh)
  .value("VRINTMNQf", LIEF::assembly::arm::OPCODE::VRINTMNQf)
  .value("VRINTMNQh", LIEF::assembly::arm::OPCODE::VRINTMNQh)
  .value("VRINTMS", LIEF::assembly::arm::OPCODE::VRINTMS)
  .value("VRINTND", LIEF::assembly::arm::OPCODE::VRINTND)
  .value("VRINTNH", LIEF::assembly::arm::OPCODE::VRINTNH)
  .value("VRINTNNDf", LIEF::assembly::arm::OPCODE::VRINTNNDf)
  .value("VRINTNNDh", LIEF::assembly::arm::OPCODE::VRINTNNDh)
  .value("VRINTNNQf", LIEF::assembly::arm::OPCODE::VRINTNNQf)
  .value("VRINTNNQh", LIEF::assembly::arm::OPCODE::VRINTNNQh)
  .value("VRINTNS", LIEF::assembly::arm::OPCODE::VRINTNS)
  .value("VRINTPD", LIEF::assembly::arm::OPCODE::VRINTPD)
  .value("VRINTPH", LIEF::assembly::arm::OPCODE::VRINTPH)
  .value("VRINTPNDf", LIEF::assembly::arm::OPCODE::VRINTPNDf)
  .value("VRINTPNDh", LIEF::assembly::arm::OPCODE::VRINTPNDh)
  .value("VRINTPNQf", LIEF::assembly::arm::OPCODE::VRINTPNQf)
  .value("VRINTPNQh", LIEF::assembly::arm::OPCODE::VRINTPNQh)
  .value("VRINTPS", LIEF::assembly::arm::OPCODE::VRINTPS)
  .value("VRINTRD", LIEF::assembly::arm::OPCODE::VRINTRD)
  .value("VRINTRH", LIEF::assembly::arm::OPCODE::VRINTRH)
  .value("VRINTRS", LIEF::assembly::arm::OPCODE::VRINTRS)
  .value("VRINTXD", LIEF::assembly::arm::OPCODE::VRINTXD)
  .value("VRINTXH", LIEF::assembly::arm::OPCODE::VRINTXH)
  .value("VRINTXNDf", LIEF::assembly::arm::OPCODE::VRINTXNDf)
  .value("VRINTXNDh", LIEF::assembly::arm::OPCODE::VRINTXNDh)
  .value("VRINTXNQf", LIEF::assembly::arm::OPCODE::VRINTXNQf)
  .value("VRINTXNQh", LIEF::assembly::arm::OPCODE::VRINTXNQh)
  .value("VRINTXS", LIEF::assembly::arm::OPCODE::VRINTXS)
  .value("VRINTZD", LIEF::assembly::arm::OPCODE::VRINTZD)
  .value("VRINTZH", LIEF::assembly::arm::OPCODE::VRINTZH)
  .value("VRINTZNDf", LIEF::assembly::arm::OPCODE::VRINTZNDf)
  .value("VRINTZNDh", LIEF::assembly::arm::OPCODE::VRINTZNDh)
  .value("VRINTZNQf", LIEF::assembly::arm::OPCODE::VRINTZNQf)
  .value("VRINTZNQh", LIEF::assembly::arm::OPCODE::VRINTZNQh)
  .value("VRINTZS", LIEF::assembly::arm::OPCODE::VRINTZS)
  .value("VRSHLsv16i8", LIEF::assembly::arm::OPCODE::VRSHLsv16i8)
  .value("VRSHLsv1i64", LIEF::assembly::arm::OPCODE::VRSHLsv1i64)
  .value("VRSHLsv2i32", LIEF::assembly::arm::OPCODE::VRSHLsv2i32)
  .value("VRSHLsv2i64", LIEF::assembly::arm::OPCODE::VRSHLsv2i64)
  .value("VRSHLsv4i16", LIEF::assembly::arm::OPCODE::VRSHLsv4i16)
  .value("VRSHLsv4i32", LIEF::assembly::arm::OPCODE::VRSHLsv4i32)
  .value("VRSHLsv8i16", LIEF::assembly::arm::OPCODE::VRSHLsv8i16)
  .value("VRSHLsv8i8", LIEF::assembly::arm::OPCODE::VRSHLsv8i8)
  .value("VRSHLuv16i8", LIEF::assembly::arm::OPCODE::VRSHLuv16i8)
  .value("VRSHLuv1i64", LIEF::assembly::arm::OPCODE::VRSHLuv1i64)
  .value("VRSHLuv2i32", LIEF::assembly::arm::OPCODE::VRSHLuv2i32)
  .value("VRSHLuv2i64", LIEF::assembly::arm::OPCODE::VRSHLuv2i64)
  .value("VRSHLuv4i16", LIEF::assembly::arm::OPCODE::VRSHLuv4i16)
  .value("VRSHLuv4i32", LIEF::assembly::arm::OPCODE::VRSHLuv4i32)
  .value("VRSHLuv8i16", LIEF::assembly::arm::OPCODE::VRSHLuv8i16)
  .value("VRSHLuv8i8", LIEF::assembly::arm::OPCODE::VRSHLuv8i8)
  .value("VRSHRNv2i32", LIEF::assembly::arm::OPCODE::VRSHRNv2i32)
  .value("VRSHRNv4i16", LIEF::assembly::arm::OPCODE::VRSHRNv4i16)
  .value("VRSHRNv8i8", LIEF::assembly::arm::OPCODE::VRSHRNv8i8)
  .value("VRSHRsv16i8", LIEF::assembly::arm::OPCODE::VRSHRsv16i8)
  .value("VRSHRsv1i64", LIEF::assembly::arm::OPCODE::VRSHRsv1i64)
  .value("VRSHRsv2i32", LIEF::assembly::arm::OPCODE::VRSHRsv2i32)
  .value("VRSHRsv2i64", LIEF::assembly::arm::OPCODE::VRSHRsv2i64)
  .value("VRSHRsv4i16", LIEF::assembly::arm::OPCODE::VRSHRsv4i16)
  .value("VRSHRsv4i32", LIEF::assembly::arm::OPCODE::VRSHRsv4i32)
  .value("VRSHRsv8i16", LIEF::assembly::arm::OPCODE::VRSHRsv8i16)
  .value("VRSHRsv8i8", LIEF::assembly::arm::OPCODE::VRSHRsv8i8)
  .value("VRSHRuv16i8", LIEF::assembly::arm::OPCODE::VRSHRuv16i8)
  .value("VRSHRuv1i64", LIEF::assembly::arm::OPCODE::VRSHRuv1i64)
  .value("VRSHRuv2i32", LIEF::assembly::arm::OPCODE::VRSHRuv2i32)
  .value("VRSHRuv2i64", LIEF::assembly::arm::OPCODE::VRSHRuv2i64)
  .value("VRSHRuv4i16", LIEF::assembly::arm::OPCODE::VRSHRuv4i16)
  .value("VRSHRuv4i32", LIEF::assembly::arm::OPCODE::VRSHRuv4i32)
  .value("VRSHRuv8i16", LIEF::assembly::arm::OPCODE::VRSHRuv8i16)
  .value("VRSHRuv8i8", LIEF::assembly::arm::OPCODE::VRSHRuv8i8)
  .value("VRSQRTEd", LIEF::assembly::arm::OPCODE::VRSQRTEd)
  .value("VRSQRTEfd", LIEF::assembly::arm::OPCODE::VRSQRTEfd)
  .value("VRSQRTEfq", LIEF::assembly::arm::OPCODE::VRSQRTEfq)
  .value("VRSQRTEhd", LIEF::assembly::arm::OPCODE::VRSQRTEhd)
  .value("VRSQRTEhq", LIEF::assembly::arm::OPCODE::VRSQRTEhq)
  .value("VRSQRTEq", LIEF::assembly::arm::OPCODE::VRSQRTEq)
  .value("VRSQRTSfd", LIEF::assembly::arm::OPCODE::VRSQRTSfd)
  .value("VRSQRTSfq", LIEF::assembly::arm::OPCODE::VRSQRTSfq)
  .value("VRSQRTShd", LIEF::assembly::arm::OPCODE::VRSQRTShd)
  .value("VRSQRTShq", LIEF::assembly::arm::OPCODE::VRSQRTShq)
  .value("VRSRAsv16i8", LIEF::assembly::arm::OPCODE::VRSRAsv16i8)
  .value("VRSRAsv1i64", LIEF::assembly::arm::OPCODE::VRSRAsv1i64)
  .value("VRSRAsv2i32", LIEF::assembly::arm::OPCODE::VRSRAsv2i32)
  .value("VRSRAsv2i64", LIEF::assembly::arm::OPCODE::VRSRAsv2i64)
  .value("VRSRAsv4i16", LIEF::assembly::arm::OPCODE::VRSRAsv4i16)
  .value("VRSRAsv4i32", LIEF::assembly::arm::OPCODE::VRSRAsv4i32)
  .value("VRSRAsv8i16", LIEF::assembly::arm::OPCODE::VRSRAsv8i16)
  .value("VRSRAsv8i8", LIEF::assembly::arm::OPCODE::VRSRAsv8i8)
  .value("VRSRAuv16i8", LIEF::assembly::arm::OPCODE::VRSRAuv16i8)
  .value("VRSRAuv1i64", LIEF::assembly::arm::OPCODE::VRSRAuv1i64)
  .value("VRSRAuv2i32", LIEF::assembly::arm::OPCODE::VRSRAuv2i32)
  .value("VRSRAuv2i64", LIEF::assembly::arm::OPCODE::VRSRAuv2i64)
  .value("VRSRAuv4i16", LIEF::assembly::arm::OPCODE::VRSRAuv4i16)
  .value("VRSRAuv4i32", LIEF::assembly::arm::OPCODE::VRSRAuv4i32)
  .value("VRSRAuv8i16", LIEF::assembly::arm::OPCODE::VRSRAuv8i16)
  .value("VRSRAuv8i8", LIEF::assembly::arm::OPCODE::VRSRAuv8i8)
  .value("VRSUBHNv2i32", LIEF::assembly::arm::OPCODE::VRSUBHNv2i32)
  .value("VRSUBHNv4i16", LIEF::assembly::arm::OPCODE::VRSUBHNv4i16)
  .value("VRSUBHNv8i8", LIEF::assembly::arm::OPCODE::VRSUBHNv8i8)
  .value("VSCCLRMD", LIEF::assembly::arm::OPCODE::VSCCLRMD)
  .value("VSCCLRMS", LIEF::assembly::arm::OPCODE::VSCCLRMS)
  .value("VSDOTD", LIEF::assembly::arm::OPCODE::VSDOTD)
  .value("VSDOTDI", LIEF::assembly::arm::OPCODE::VSDOTDI)
  .value("VSDOTQ", LIEF::assembly::arm::OPCODE::VSDOTQ)
  .value("VSDOTQI", LIEF::assembly::arm::OPCODE::VSDOTQI)
  .value("VSELEQD", LIEF::assembly::arm::OPCODE::VSELEQD)
  .value("VSELEQH", LIEF::assembly::arm::OPCODE::VSELEQH)
  .value("VSELEQS", LIEF::assembly::arm::OPCODE::VSELEQS)
  .value("VSELGED", LIEF::assembly::arm::OPCODE::VSELGED)
  .value("VSELGEH", LIEF::assembly::arm::OPCODE::VSELGEH)
  .value("VSELGES", LIEF::assembly::arm::OPCODE::VSELGES)
  .value("VSELGTD", LIEF::assembly::arm::OPCODE::VSELGTD)
  .value("VSELGTH", LIEF::assembly::arm::OPCODE::VSELGTH)
  .value("VSELGTS", LIEF::assembly::arm::OPCODE::VSELGTS)
  .value("VSELVSD", LIEF::assembly::arm::OPCODE::VSELVSD)
  .value("VSELVSH", LIEF::assembly::arm::OPCODE::VSELVSH)
  .value("VSELVSS", LIEF::assembly::arm::OPCODE::VSELVSS)
  .value("VSETLNi16", LIEF::assembly::arm::OPCODE::VSETLNi16)
  .value("VSETLNi32", LIEF::assembly::arm::OPCODE::VSETLNi32)
  .value("VSETLNi8", LIEF::assembly::arm::OPCODE::VSETLNi8)
  .value("VSHLLi16", LIEF::assembly::arm::OPCODE::VSHLLi16)
  .value("VSHLLi32", LIEF::assembly::arm::OPCODE::VSHLLi32)
  .value("VSHLLi8", LIEF::assembly::arm::OPCODE::VSHLLi8)
  .value("VSHLLsv2i64", LIEF::assembly::arm::OPCODE::VSHLLsv2i64)
  .value("VSHLLsv4i32", LIEF::assembly::arm::OPCODE::VSHLLsv4i32)
  .value("VSHLLsv8i16", LIEF::assembly::arm::OPCODE::VSHLLsv8i16)
  .value("VSHLLuv2i64", LIEF::assembly::arm::OPCODE::VSHLLuv2i64)
  .value("VSHLLuv4i32", LIEF::assembly::arm::OPCODE::VSHLLuv4i32)
  .value("VSHLLuv8i16", LIEF::assembly::arm::OPCODE::VSHLLuv8i16)
  .value("VSHLiv16i8", LIEF::assembly::arm::OPCODE::VSHLiv16i8)
  .value("VSHLiv1i64", LIEF::assembly::arm::OPCODE::VSHLiv1i64)
  .value("VSHLiv2i32", LIEF::assembly::arm::OPCODE::VSHLiv2i32)
  .value("VSHLiv2i64", LIEF::assembly::arm::OPCODE::VSHLiv2i64)
  .value("VSHLiv4i16", LIEF::assembly::arm::OPCODE::VSHLiv4i16)
  .value("VSHLiv4i32", LIEF::assembly::arm::OPCODE::VSHLiv4i32)
  .value("VSHLiv8i16", LIEF::assembly::arm::OPCODE::VSHLiv8i16)
  .value("VSHLiv8i8", LIEF::assembly::arm::OPCODE::VSHLiv8i8)
  .value("VSHLsv16i8", LIEF::assembly::arm::OPCODE::VSHLsv16i8)
  .value("VSHLsv1i64", LIEF::assembly::arm::OPCODE::VSHLsv1i64)
  .value("VSHLsv2i32", LIEF::assembly::arm::OPCODE::VSHLsv2i32)
  .value("VSHLsv2i64", LIEF::assembly::arm::OPCODE::VSHLsv2i64)
  .value("VSHLsv4i16", LIEF::assembly::arm::OPCODE::VSHLsv4i16)
  .value("VSHLsv4i32", LIEF::assembly::arm::OPCODE::VSHLsv4i32)
  .value("VSHLsv8i16", LIEF::assembly::arm::OPCODE::VSHLsv8i16)
  .value("VSHLsv8i8", LIEF::assembly::arm::OPCODE::VSHLsv8i8)
  .value("VSHLuv16i8", LIEF::assembly::arm::OPCODE::VSHLuv16i8)
  .value("VSHLuv1i64", LIEF::assembly::arm::OPCODE::VSHLuv1i64)
  .value("VSHLuv2i32", LIEF::assembly::arm::OPCODE::VSHLuv2i32)
  .value("VSHLuv2i64", LIEF::assembly::arm::OPCODE::VSHLuv2i64)
  .value("VSHLuv4i16", LIEF::assembly::arm::OPCODE::VSHLuv4i16)
  .value("VSHLuv4i32", LIEF::assembly::arm::OPCODE::VSHLuv4i32)
  .value("VSHLuv8i16", LIEF::assembly::arm::OPCODE::VSHLuv8i16)
  .value("VSHLuv8i8", LIEF::assembly::arm::OPCODE::VSHLuv8i8)
  .value("VSHRNv2i32", LIEF::assembly::arm::OPCODE::VSHRNv2i32)
  .value("VSHRNv4i16", LIEF::assembly::arm::OPCODE::VSHRNv4i16)
  .value("VSHRNv8i8", LIEF::assembly::arm::OPCODE::VSHRNv8i8)
  .value("VSHRsv16i8", LIEF::assembly::arm::OPCODE::VSHRsv16i8)
  .value("VSHRsv1i64", LIEF::assembly::arm::OPCODE::VSHRsv1i64)
  .value("VSHRsv2i32", LIEF::assembly::arm::OPCODE::VSHRsv2i32)
  .value("VSHRsv2i64", LIEF::assembly::arm::OPCODE::VSHRsv2i64)
  .value("VSHRsv4i16", LIEF::assembly::arm::OPCODE::VSHRsv4i16)
  .value("VSHRsv4i32", LIEF::assembly::arm::OPCODE::VSHRsv4i32)
  .value("VSHRsv8i16", LIEF::assembly::arm::OPCODE::VSHRsv8i16)
  .value("VSHRsv8i8", LIEF::assembly::arm::OPCODE::VSHRsv8i8)
  .value("VSHRuv16i8", LIEF::assembly::arm::OPCODE::VSHRuv16i8)
  .value("VSHRuv1i64", LIEF::assembly::arm::OPCODE::VSHRuv1i64)
  .value("VSHRuv2i32", LIEF::assembly::arm::OPCODE::VSHRuv2i32)
  .value("VSHRuv2i64", LIEF::assembly::arm::OPCODE::VSHRuv2i64)
  .value("VSHRuv4i16", LIEF::assembly::arm::OPCODE::VSHRuv4i16)
  .value("VSHRuv4i32", LIEF::assembly::arm::OPCODE::VSHRuv4i32)
  .value("VSHRuv8i16", LIEF::assembly::arm::OPCODE::VSHRuv8i16)
  .value("VSHRuv8i8", LIEF::assembly::arm::OPCODE::VSHRuv8i8)
  .value("VSHTOD", LIEF::assembly::arm::OPCODE::VSHTOD)
  .value("VSHTOH", LIEF::assembly::arm::OPCODE::VSHTOH)
  .value("VSHTOS", LIEF::assembly::arm::OPCODE::VSHTOS)
  .value("VSITOD", LIEF::assembly::arm::OPCODE::VSITOD)
  .value("VSITOH", LIEF::assembly::arm::OPCODE::VSITOH)
  .value("VSITOS", LIEF::assembly::arm::OPCODE::VSITOS)
  .value("VSLIv16i8", LIEF::assembly::arm::OPCODE::VSLIv16i8)
  .value("VSLIv1i64", LIEF::assembly::arm::OPCODE::VSLIv1i64)
  .value("VSLIv2i32", LIEF::assembly::arm::OPCODE::VSLIv2i32)
  .value("VSLIv2i64", LIEF::assembly::arm::OPCODE::VSLIv2i64)
  .value("VSLIv4i16", LIEF::assembly::arm::OPCODE::VSLIv4i16)
  .value("VSLIv4i32", LIEF::assembly::arm::OPCODE::VSLIv4i32)
  .value("VSLIv8i16", LIEF::assembly::arm::OPCODE::VSLIv8i16)
  .value("VSLIv8i8", LIEF::assembly::arm::OPCODE::VSLIv8i8)
  .value("VSLTOD", LIEF::assembly::arm::OPCODE::VSLTOD)
  .value("VSLTOH", LIEF::assembly::arm::OPCODE::VSLTOH)
  .value("VSLTOS", LIEF::assembly::arm::OPCODE::VSLTOS)
  .value("VSMMLA", LIEF::assembly::arm::OPCODE::VSMMLA)
  .value("VSQRTD", LIEF::assembly::arm::OPCODE::VSQRTD)
  .value("VSQRTH", LIEF::assembly::arm::OPCODE::VSQRTH)
  .value("VSQRTS", LIEF::assembly::arm::OPCODE::VSQRTS)
  .value("VSRAsv16i8", LIEF::assembly::arm::OPCODE::VSRAsv16i8)
  .value("VSRAsv1i64", LIEF::assembly::arm::OPCODE::VSRAsv1i64)
  .value("VSRAsv2i32", LIEF::assembly::arm::OPCODE::VSRAsv2i32)
  .value("VSRAsv2i64", LIEF::assembly::arm::OPCODE::VSRAsv2i64)
  .value("VSRAsv4i16", LIEF::assembly::arm::OPCODE::VSRAsv4i16)
  .value("VSRAsv4i32", LIEF::assembly::arm::OPCODE::VSRAsv4i32)
  .value("VSRAsv8i16", LIEF::assembly::arm::OPCODE::VSRAsv8i16)
  .value("VSRAsv8i8", LIEF::assembly::arm::OPCODE::VSRAsv8i8)
  .value("VSRAuv16i8", LIEF::assembly::arm::OPCODE::VSRAuv16i8)
  .value("VSRAuv1i64", LIEF::assembly::arm::OPCODE::VSRAuv1i64)
  .value("VSRAuv2i32", LIEF::assembly::arm::OPCODE::VSRAuv2i32)
  .value("VSRAuv2i64", LIEF::assembly::arm::OPCODE::VSRAuv2i64)
  .value("VSRAuv4i16", LIEF::assembly::arm::OPCODE::VSRAuv4i16)
  .value("VSRAuv4i32", LIEF::assembly::arm::OPCODE::VSRAuv4i32)
  .value("VSRAuv8i16", LIEF::assembly::arm::OPCODE::VSRAuv8i16)
  .value("VSRAuv8i8", LIEF::assembly::arm::OPCODE::VSRAuv8i8)
  .value("VSRIv16i8", LIEF::assembly::arm::OPCODE::VSRIv16i8)
  .value("VSRIv1i64", LIEF::assembly::arm::OPCODE::VSRIv1i64)
  .value("VSRIv2i32", LIEF::assembly::arm::OPCODE::VSRIv2i32);
  opcodes.value("VSRIv2i64", LIEF::assembly::arm::OPCODE::VSRIv2i64)
  .value("VSRIv4i16", LIEF::assembly::arm::OPCODE::VSRIv4i16)
  .value("VSRIv4i32", LIEF::assembly::arm::OPCODE::VSRIv4i32)
  .value("VSRIv8i16", LIEF::assembly::arm::OPCODE::VSRIv8i16)
  .value("VSRIv8i8", LIEF::assembly::arm::OPCODE::VSRIv8i8)
  .value("VST1LNd16", LIEF::assembly::arm::OPCODE::VST1LNd16)
  .value("VST1LNd16_UPD", LIEF::assembly::arm::OPCODE::VST1LNd16_UPD)
  .value("VST1LNd32", LIEF::assembly::arm::OPCODE::VST1LNd32)
  .value("VST1LNd32_UPD", LIEF::assembly::arm::OPCODE::VST1LNd32_UPD)
  .value("VST1LNd8", LIEF::assembly::arm::OPCODE::VST1LNd8)
  .value("VST1LNd8_UPD", LIEF::assembly::arm::OPCODE::VST1LNd8_UPD)
  .value("VST1LNq16Pseudo", LIEF::assembly::arm::OPCODE::VST1LNq16Pseudo)
  .value("VST1LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST1LNq16Pseudo_UPD)
  .value("VST1LNq32Pseudo", LIEF::assembly::arm::OPCODE::VST1LNq32Pseudo)
  .value("VST1LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST1LNq32Pseudo_UPD)
  .value("VST1LNq8Pseudo", LIEF::assembly::arm::OPCODE::VST1LNq8Pseudo)
  .value("VST1LNq8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST1LNq8Pseudo_UPD)
  .value("VST1d16", LIEF::assembly::arm::OPCODE::VST1d16)
  .value("VST1d16Q", LIEF::assembly::arm::OPCODE::VST1d16Q)
  .value("VST1d16QPseudo", LIEF::assembly::arm::OPCODE::VST1d16QPseudo)
  .value("VST1d16QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d16QPseudoWB_fixed)
  .value("VST1d16QPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d16QPseudoWB_register)
  .value("VST1d16Qwb_fixed", LIEF::assembly::arm::OPCODE::VST1d16Qwb_fixed)
  .value("VST1d16Qwb_register", LIEF::assembly::arm::OPCODE::VST1d16Qwb_register)
  .value("VST1d16T", LIEF::assembly::arm::OPCODE::VST1d16T)
  .value("VST1d16TPseudo", LIEF::assembly::arm::OPCODE::VST1d16TPseudo)
  .value("VST1d16TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d16TPseudoWB_fixed)
  .value("VST1d16TPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d16TPseudoWB_register)
  .value("VST1d16Twb_fixed", LIEF::assembly::arm::OPCODE::VST1d16Twb_fixed)
  .value("VST1d16Twb_register", LIEF::assembly::arm::OPCODE::VST1d16Twb_register)
  .value("VST1d16wb_fixed", LIEF::assembly::arm::OPCODE::VST1d16wb_fixed)
  .value("VST1d16wb_register", LIEF::assembly::arm::OPCODE::VST1d16wb_register)
  .value("VST1d32", LIEF::assembly::arm::OPCODE::VST1d32)
  .value("VST1d32Q", LIEF::assembly::arm::OPCODE::VST1d32Q)
  .value("VST1d32QPseudo", LIEF::assembly::arm::OPCODE::VST1d32QPseudo)
  .value("VST1d32QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d32QPseudoWB_fixed)
  .value("VST1d32QPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d32QPseudoWB_register)
  .value("VST1d32Qwb_fixed", LIEF::assembly::arm::OPCODE::VST1d32Qwb_fixed)
  .value("VST1d32Qwb_register", LIEF::assembly::arm::OPCODE::VST1d32Qwb_register)
  .value("VST1d32T", LIEF::assembly::arm::OPCODE::VST1d32T)
  .value("VST1d32TPseudo", LIEF::assembly::arm::OPCODE::VST1d32TPseudo)
  .value("VST1d32TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d32TPseudoWB_fixed)
  .value("VST1d32TPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d32TPseudoWB_register)
  .value("VST1d32Twb_fixed", LIEF::assembly::arm::OPCODE::VST1d32Twb_fixed)
  .value("VST1d32Twb_register", LIEF::assembly::arm::OPCODE::VST1d32Twb_register)
  .value("VST1d32wb_fixed", LIEF::assembly::arm::OPCODE::VST1d32wb_fixed)
  .value("VST1d32wb_register", LIEF::assembly::arm::OPCODE::VST1d32wb_register)
  .value("VST1d64", LIEF::assembly::arm::OPCODE::VST1d64)
  .value("VST1d64Q", LIEF::assembly::arm::OPCODE::VST1d64Q)
  .value("VST1d64QPseudo", LIEF::assembly::arm::OPCODE::VST1d64QPseudo)
  .value("VST1d64QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d64QPseudoWB_fixed)
  .value("VST1d64QPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d64QPseudoWB_register)
  .value("VST1d64Qwb_fixed", LIEF::assembly::arm::OPCODE::VST1d64Qwb_fixed)
  .value("VST1d64Qwb_register", LIEF::assembly::arm::OPCODE::VST1d64Qwb_register)
  .value("VST1d64T", LIEF::assembly::arm::OPCODE::VST1d64T)
  .value("VST1d64TPseudo", LIEF::assembly::arm::OPCODE::VST1d64TPseudo)
  .value("VST1d64TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d64TPseudoWB_fixed)
  .value("VST1d64TPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d64TPseudoWB_register)
  .value("VST1d64Twb_fixed", LIEF::assembly::arm::OPCODE::VST1d64Twb_fixed)
  .value("VST1d64Twb_register", LIEF::assembly::arm::OPCODE::VST1d64Twb_register)
  .value("VST1d64wb_fixed", LIEF::assembly::arm::OPCODE::VST1d64wb_fixed)
  .value("VST1d64wb_register", LIEF::assembly::arm::OPCODE::VST1d64wb_register)
  .value("VST1d8", LIEF::assembly::arm::OPCODE::VST1d8)
  .value("VST1d8Q", LIEF::assembly::arm::OPCODE::VST1d8Q)
  .value("VST1d8QPseudo", LIEF::assembly::arm::OPCODE::VST1d8QPseudo)
  .value("VST1d8QPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d8QPseudoWB_fixed)
  .value("VST1d8QPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d8QPseudoWB_register)
  .value("VST1d8Qwb_fixed", LIEF::assembly::arm::OPCODE::VST1d8Qwb_fixed)
  .value("VST1d8Qwb_register", LIEF::assembly::arm::OPCODE::VST1d8Qwb_register)
  .value("VST1d8T", LIEF::assembly::arm::OPCODE::VST1d8T)
  .value("VST1d8TPseudo", LIEF::assembly::arm::OPCODE::VST1d8TPseudo)
  .value("VST1d8TPseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST1d8TPseudoWB_fixed)
  .value("VST1d8TPseudoWB_register", LIEF::assembly::arm::OPCODE::VST1d8TPseudoWB_register)
  .value("VST1d8Twb_fixed", LIEF::assembly::arm::OPCODE::VST1d8Twb_fixed)
  .value("VST1d8Twb_register", LIEF::assembly::arm::OPCODE::VST1d8Twb_register)
  .value("VST1d8wb_fixed", LIEF::assembly::arm::OPCODE::VST1d8wb_fixed)
  .value("VST1d8wb_register", LIEF::assembly::arm::OPCODE::VST1d8wb_register)
  .value("VST1q16", LIEF::assembly::arm::OPCODE::VST1q16)
  .value("VST1q16HighQPseudo", LIEF::assembly::arm::OPCODE::VST1q16HighQPseudo)
  .value("VST1q16HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q16HighQPseudo_UPD)
  .value("VST1q16HighTPseudo", LIEF::assembly::arm::OPCODE::VST1q16HighTPseudo)
  .value("VST1q16HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q16HighTPseudo_UPD)
  .value("VST1q16LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q16LowQPseudo_UPD)
  .value("VST1q16LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q16LowTPseudo_UPD)
  .value("VST1q16wb_fixed", LIEF::assembly::arm::OPCODE::VST1q16wb_fixed)
  .value("VST1q16wb_register", LIEF::assembly::arm::OPCODE::VST1q16wb_register)
  .value("VST1q32", LIEF::assembly::arm::OPCODE::VST1q32)
  .value("VST1q32HighQPseudo", LIEF::assembly::arm::OPCODE::VST1q32HighQPseudo)
  .value("VST1q32HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q32HighQPseudo_UPD)
  .value("VST1q32HighTPseudo", LIEF::assembly::arm::OPCODE::VST1q32HighTPseudo)
  .value("VST1q32HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q32HighTPseudo_UPD)
  .value("VST1q32LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q32LowQPseudo_UPD)
  .value("VST1q32LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q32LowTPseudo_UPD)
  .value("VST1q32wb_fixed", LIEF::assembly::arm::OPCODE::VST1q32wb_fixed)
  .value("VST1q32wb_register", LIEF::assembly::arm::OPCODE::VST1q32wb_register)
  .value("VST1q64", LIEF::assembly::arm::OPCODE::VST1q64)
  .value("VST1q64HighQPseudo", LIEF::assembly::arm::OPCODE::VST1q64HighQPseudo)
  .value("VST1q64HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q64HighQPseudo_UPD)
  .value("VST1q64HighTPseudo", LIEF::assembly::arm::OPCODE::VST1q64HighTPseudo)
  .value("VST1q64HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q64HighTPseudo_UPD)
  .value("VST1q64LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q64LowQPseudo_UPD)
  .value("VST1q64LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q64LowTPseudo_UPD)
  .value("VST1q64wb_fixed", LIEF::assembly::arm::OPCODE::VST1q64wb_fixed)
  .value("VST1q64wb_register", LIEF::assembly::arm::OPCODE::VST1q64wb_register)
  .value("VST1q8", LIEF::assembly::arm::OPCODE::VST1q8)
  .value("VST1q8HighQPseudo", LIEF::assembly::arm::OPCODE::VST1q8HighQPseudo)
  .value("VST1q8HighQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q8HighQPseudo_UPD)
  .value("VST1q8HighTPseudo", LIEF::assembly::arm::OPCODE::VST1q8HighTPseudo)
  .value("VST1q8HighTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q8HighTPseudo_UPD)
  .value("VST1q8LowQPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q8LowQPseudo_UPD)
  .value("VST1q8LowTPseudo_UPD", LIEF::assembly::arm::OPCODE::VST1q8LowTPseudo_UPD)
  .value("VST1q8wb_fixed", LIEF::assembly::arm::OPCODE::VST1q8wb_fixed)
  .value("VST1q8wb_register", LIEF::assembly::arm::OPCODE::VST1q8wb_register)
  .value("VST2LNd16", LIEF::assembly::arm::OPCODE::VST2LNd16)
  .value("VST2LNd16Pseudo", LIEF::assembly::arm::OPCODE::VST2LNd16Pseudo)
  .value("VST2LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST2LNd16Pseudo_UPD)
  .value("VST2LNd16_UPD", LIEF::assembly::arm::OPCODE::VST2LNd16_UPD)
  .value("VST2LNd32", LIEF::assembly::arm::OPCODE::VST2LNd32)
  .value("VST2LNd32Pseudo", LIEF::assembly::arm::OPCODE::VST2LNd32Pseudo)
  .value("VST2LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST2LNd32Pseudo_UPD)
  .value("VST2LNd32_UPD", LIEF::assembly::arm::OPCODE::VST2LNd32_UPD)
  .value("VST2LNd8", LIEF::assembly::arm::OPCODE::VST2LNd8)
  .value("VST2LNd8Pseudo", LIEF::assembly::arm::OPCODE::VST2LNd8Pseudo)
  .value("VST2LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST2LNd8Pseudo_UPD)
  .value("VST2LNd8_UPD", LIEF::assembly::arm::OPCODE::VST2LNd8_UPD)
  .value("VST2LNq16", LIEF::assembly::arm::OPCODE::VST2LNq16)
  .value("VST2LNq16Pseudo", LIEF::assembly::arm::OPCODE::VST2LNq16Pseudo)
  .value("VST2LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST2LNq16Pseudo_UPD)
  .value("VST2LNq16_UPD", LIEF::assembly::arm::OPCODE::VST2LNq16_UPD)
  .value("VST2LNq32", LIEF::assembly::arm::OPCODE::VST2LNq32)
  .value("VST2LNq32Pseudo", LIEF::assembly::arm::OPCODE::VST2LNq32Pseudo)
  .value("VST2LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST2LNq32Pseudo_UPD)
  .value("VST2LNq32_UPD", LIEF::assembly::arm::OPCODE::VST2LNq32_UPD)
  .value("VST2b16", LIEF::assembly::arm::OPCODE::VST2b16)
  .value("VST2b16wb_fixed", LIEF::assembly::arm::OPCODE::VST2b16wb_fixed)
  .value("VST2b16wb_register", LIEF::assembly::arm::OPCODE::VST2b16wb_register)
  .value("VST2b32", LIEF::assembly::arm::OPCODE::VST2b32)
  .value("VST2b32wb_fixed", LIEF::assembly::arm::OPCODE::VST2b32wb_fixed)
  .value("VST2b32wb_register", LIEF::assembly::arm::OPCODE::VST2b32wb_register)
  .value("VST2b8", LIEF::assembly::arm::OPCODE::VST2b8)
  .value("VST2b8wb_fixed", LIEF::assembly::arm::OPCODE::VST2b8wb_fixed)
  .value("VST2b8wb_register", LIEF::assembly::arm::OPCODE::VST2b8wb_register)
  .value("VST2d16", LIEF::assembly::arm::OPCODE::VST2d16)
  .value("VST2d16wb_fixed", LIEF::assembly::arm::OPCODE::VST2d16wb_fixed)
  .value("VST2d16wb_register", LIEF::assembly::arm::OPCODE::VST2d16wb_register)
  .value("VST2d32", LIEF::assembly::arm::OPCODE::VST2d32)
  .value("VST2d32wb_fixed", LIEF::assembly::arm::OPCODE::VST2d32wb_fixed)
  .value("VST2d32wb_register", LIEF::assembly::arm::OPCODE::VST2d32wb_register)
  .value("VST2d8", LIEF::assembly::arm::OPCODE::VST2d8)
  .value("VST2d8wb_fixed", LIEF::assembly::arm::OPCODE::VST2d8wb_fixed)
  .value("VST2d8wb_register", LIEF::assembly::arm::OPCODE::VST2d8wb_register)
  .value("VST2q16", LIEF::assembly::arm::OPCODE::VST2q16)
  .value("VST2q16Pseudo", LIEF::assembly::arm::OPCODE::VST2q16Pseudo)
  .value("VST2q16PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST2q16PseudoWB_fixed)
  .value("VST2q16PseudoWB_register", LIEF::assembly::arm::OPCODE::VST2q16PseudoWB_register)
  .value("VST2q16wb_fixed", LIEF::assembly::arm::OPCODE::VST2q16wb_fixed)
  .value("VST2q16wb_register", LIEF::assembly::arm::OPCODE::VST2q16wb_register)
  .value("VST2q32", LIEF::assembly::arm::OPCODE::VST2q32)
  .value("VST2q32Pseudo", LIEF::assembly::arm::OPCODE::VST2q32Pseudo)
  .value("VST2q32PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST2q32PseudoWB_fixed)
  .value("VST2q32PseudoWB_register", LIEF::assembly::arm::OPCODE::VST2q32PseudoWB_register)
  .value("VST2q32wb_fixed", LIEF::assembly::arm::OPCODE::VST2q32wb_fixed)
  .value("VST2q32wb_register", LIEF::assembly::arm::OPCODE::VST2q32wb_register)
  .value("VST2q8", LIEF::assembly::arm::OPCODE::VST2q8)
  .value("VST2q8Pseudo", LIEF::assembly::arm::OPCODE::VST2q8Pseudo)
  .value("VST2q8PseudoWB_fixed", LIEF::assembly::arm::OPCODE::VST2q8PseudoWB_fixed)
  .value("VST2q8PseudoWB_register", LIEF::assembly::arm::OPCODE::VST2q8PseudoWB_register)
  .value("VST2q8wb_fixed", LIEF::assembly::arm::OPCODE::VST2q8wb_fixed)
  .value("VST2q8wb_register", LIEF::assembly::arm::OPCODE::VST2q8wb_register)
  .value("VST3LNd16", LIEF::assembly::arm::OPCODE::VST3LNd16)
  .value("VST3LNd16Pseudo", LIEF::assembly::arm::OPCODE::VST3LNd16Pseudo)
  .value("VST3LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3LNd16Pseudo_UPD)
  .value("VST3LNd16_UPD", LIEF::assembly::arm::OPCODE::VST3LNd16_UPD)
  .value("VST3LNd32", LIEF::assembly::arm::OPCODE::VST3LNd32)
  .value("VST3LNd32Pseudo", LIEF::assembly::arm::OPCODE::VST3LNd32Pseudo)
  .value("VST3LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3LNd32Pseudo_UPD)
  .value("VST3LNd32_UPD", LIEF::assembly::arm::OPCODE::VST3LNd32_UPD)
  .value("VST3LNd8", LIEF::assembly::arm::OPCODE::VST3LNd8)
  .value("VST3LNd8Pseudo", LIEF::assembly::arm::OPCODE::VST3LNd8Pseudo)
  .value("VST3LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3LNd8Pseudo_UPD)
  .value("VST3LNd8_UPD", LIEF::assembly::arm::OPCODE::VST3LNd8_UPD)
  .value("VST3LNq16", LIEF::assembly::arm::OPCODE::VST3LNq16)
  .value("VST3LNq16Pseudo", LIEF::assembly::arm::OPCODE::VST3LNq16Pseudo)
  .value("VST3LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3LNq16Pseudo_UPD)
  .value("VST3LNq16_UPD", LIEF::assembly::arm::OPCODE::VST3LNq16_UPD)
  .value("VST3LNq32", LIEF::assembly::arm::OPCODE::VST3LNq32)
  .value("VST3LNq32Pseudo", LIEF::assembly::arm::OPCODE::VST3LNq32Pseudo)
  .value("VST3LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3LNq32Pseudo_UPD)
  .value("VST3LNq32_UPD", LIEF::assembly::arm::OPCODE::VST3LNq32_UPD)
  .value("VST3d16", LIEF::assembly::arm::OPCODE::VST3d16)
  .value("VST3d16Pseudo", LIEF::assembly::arm::OPCODE::VST3d16Pseudo)
  .value("VST3d16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3d16Pseudo_UPD)
  .value("VST3d16_UPD", LIEF::assembly::arm::OPCODE::VST3d16_UPD)
  .value("VST3d32", LIEF::assembly::arm::OPCODE::VST3d32)
  .value("VST3d32Pseudo", LIEF::assembly::arm::OPCODE::VST3d32Pseudo)
  .value("VST3d32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3d32Pseudo_UPD)
  .value("VST3d32_UPD", LIEF::assembly::arm::OPCODE::VST3d32_UPD)
  .value("VST3d8", LIEF::assembly::arm::OPCODE::VST3d8)
  .value("VST3d8Pseudo", LIEF::assembly::arm::OPCODE::VST3d8Pseudo)
  .value("VST3d8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3d8Pseudo_UPD)
  .value("VST3d8_UPD", LIEF::assembly::arm::OPCODE::VST3d8_UPD)
  .value("VST3q16", LIEF::assembly::arm::OPCODE::VST3q16)
  .value("VST3q16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q16Pseudo_UPD)
  .value("VST3q16_UPD", LIEF::assembly::arm::OPCODE::VST3q16_UPD)
  .value("VST3q16oddPseudo", LIEF::assembly::arm::OPCODE::VST3q16oddPseudo)
  .value("VST3q16oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q16oddPseudo_UPD)
  .value("VST3q32", LIEF::assembly::arm::OPCODE::VST3q32)
  .value("VST3q32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q32Pseudo_UPD)
  .value("VST3q32_UPD", LIEF::assembly::arm::OPCODE::VST3q32_UPD)
  .value("VST3q32oddPseudo", LIEF::assembly::arm::OPCODE::VST3q32oddPseudo)
  .value("VST3q32oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q32oddPseudo_UPD)
  .value("VST3q8", LIEF::assembly::arm::OPCODE::VST3q8)
  .value("VST3q8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q8Pseudo_UPD)
  .value("VST3q8_UPD", LIEF::assembly::arm::OPCODE::VST3q8_UPD)
  .value("VST3q8oddPseudo", LIEF::assembly::arm::OPCODE::VST3q8oddPseudo)
  .value("VST3q8oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST3q8oddPseudo_UPD)
  .value("VST4LNd16", LIEF::assembly::arm::OPCODE::VST4LNd16)
  .value("VST4LNd16Pseudo", LIEF::assembly::arm::OPCODE::VST4LNd16Pseudo)
  .value("VST4LNd16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4LNd16Pseudo_UPD)
  .value("VST4LNd16_UPD", LIEF::assembly::arm::OPCODE::VST4LNd16_UPD)
  .value("VST4LNd32", LIEF::assembly::arm::OPCODE::VST4LNd32)
  .value("VST4LNd32Pseudo", LIEF::assembly::arm::OPCODE::VST4LNd32Pseudo)
  .value("VST4LNd32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4LNd32Pseudo_UPD)
  .value("VST4LNd32_UPD", LIEF::assembly::arm::OPCODE::VST4LNd32_UPD)
  .value("VST4LNd8", LIEF::assembly::arm::OPCODE::VST4LNd8)
  .value("VST4LNd8Pseudo", LIEF::assembly::arm::OPCODE::VST4LNd8Pseudo)
  .value("VST4LNd8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4LNd8Pseudo_UPD)
  .value("VST4LNd8_UPD", LIEF::assembly::arm::OPCODE::VST4LNd8_UPD)
  .value("VST4LNq16", LIEF::assembly::arm::OPCODE::VST4LNq16)
  .value("VST4LNq16Pseudo", LIEF::assembly::arm::OPCODE::VST4LNq16Pseudo)
  .value("VST4LNq16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4LNq16Pseudo_UPD)
  .value("VST4LNq16_UPD", LIEF::assembly::arm::OPCODE::VST4LNq16_UPD)
  .value("VST4LNq32", LIEF::assembly::arm::OPCODE::VST4LNq32)
  .value("VST4LNq32Pseudo", LIEF::assembly::arm::OPCODE::VST4LNq32Pseudo)
  .value("VST4LNq32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4LNq32Pseudo_UPD)
  .value("VST4LNq32_UPD", LIEF::assembly::arm::OPCODE::VST4LNq32_UPD)
  .value("VST4d16", LIEF::assembly::arm::OPCODE::VST4d16)
  .value("VST4d16Pseudo", LIEF::assembly::arm::OPCODE::VST4d16Pseudo)
  .value("VST4d16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4d16Pseudo_UPD)
  .value("VST4d16_UPD", LIEF::assembly::arm::OPCODE::VST4d16_UPD)
  .value("VST4d32", LIEF::assembly::arm::OPCODE::VST4d32)
  .value("VST4d32Pseudo", LIEF::assembly::arm::OPCODE::VST4d32Pseudo)
  .value("VST4d32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4d32Pseudo_UPD)
  .value("VST4d32_UPD", LIEF::assembly::arm::OPCODE::VST4d32_UPD)
  .value("VST4d8", LIEF::assembly::arm::OPCODE::VST4d8)
  .value("VST4d8Pseudo", LIEF::assembly::arm::OPCODE::VST4d8Pseudo)
  .value("VST4d8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4d8Pseudo_UPD)
  .value("VST4d8_UPD", LIEF::assembly::arm::OPCODE::VST4d8_UPD)
  .value("VST4q16", LIEF::assembly::arm::OPCODE::VST4q16)
  .value("VST4q16Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q16Pseudo_UPD)
  .value("VST4q16_UPD", LIEF::assembly::arm::OPCODE::VST4q16_UPD)
  .value("VST4q16oddPseudo", LIEF::assembly::arm::OPCODE::VST4q16oddPseudo)
  .value("VST4q16oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q16oddPseudo_UPD)
  .value("VST4q32", LIEF::assembly::arm::OPCODE::VST4q32)
  .value("VST4q32Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q32Pseudo_UPD)
  .value("VST4q32_UPD", LIEF::assembly::arm::OPCODE::VST4q32_UPD)
  .value("VST4q32oddPseudo", LIEF::assembly::arm::OPCODE::VST4q32oddPseudo)
  .value("VST4q32oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q32oddPseudo_UPD)
  .value("VST4q8", LIEF::assembly::arm::OPCODE::VST4q8)
  .value("VST4q8Pseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q8Pseudo_UPD)
  .value("VST4q8_UPD", LIEF::assembly::arm::OPCODE::VST4q8_UPD)
  .value("VST4q8oddPseudo", LIEF::assembly::arm::OPCODE::VST4q8oddPseudo)
  .value("VST4q8oddPseudo_UPD", LIEF::assembly::arm::OPCODE::VST4q8oddPseudo_UPD)
  .value("VSTMDDB_UPD", LIEF::assembly::arm::OPCODE::VSTMDDB_UPD)
  .value("VSTMDIA", LIEF::assembly::arm::OPCODE::VSTMDIA)
  .value("VSTMDIA_UPD", LIEF::assembly::arm::OPCODE::VSTMDIA_UPD)
  .value("VSTMQIA", LIEF::assembly::arm::OPCODE::VSTMQIA)
  .value("VSTMSDB_UPD", LIEF::assembly::arm::OPCODE::VSTMSDB_UPD)
  .value("VSTMSIA", LIEF::assembly::arm::OPCODE::VSTMSIA)
  .value("VSTMSIA_UPD", LIEF::assembly::arm::OPCODE::VSTMSIA_UPD)
  .value("VSTRD", LIEF::assembly::arm::OPCODE::VSTRD)
  .value("VSTRH", LIEF::assembly::arm::OPCODE::VSTRH)
  .value("VSTRS", LIEF::assembly::arm::OPCODE::VSTRS)
  .value("VSTR_FPCXTNS_off", LIEF::assembly::arm::OPCODE::VSTR_FPCXTNS_off)
  .value("VSTR_FPCXTNS_post", LIEF::assembly::arm::OPCODE::VSTR_FPCXTNS_post)
  .value("VSTR_FPCXTNS_pre", LIEF::assembly::arm::OPCODE::VSTR_FPCXTNS_pre)
  .value("VSTR_FPCXTS_off", LIEF::assembly::arm::OPCODE::VSTR_FPCXTS_off)
  .value("VSTR_FPCXTS_post", LIEF::assembly::arm::OPCODE::VSTR_FPCXTS_post)
  .value("VSTR_FPCXTS_pre", LIEF::assembly::arm::OPCODE::VSTR_FPCXTS_pre)
  .value("VSTR_FPSCR_NZCVQC_off", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_NZCVQC_off)
  .value("VSTR_FPSCR_NZCVQC_post", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_NZCVQC_post)
  .value("VSTR_FPSCR_NZCVQC_pre", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_NZCVQC_pre)
  .value("VSTR_FPSCR_off", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_off)
  .value("VSTR_FPSCR_post", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_post)
  .value("VSTR_FPSCR_pre", LIEF::assembly::arm::OPCODE::VSTR_FPSCR_pre)
  .value("VSTR_P0_off", LIEF::assembly::arm::OPCODE::VSTR_P0_off)
  .value("VSTR_P0_post", LIEF::assembly::arm::OPCODE::VSTR_P0_post)
  .value("VSTR_P0_pre", LIEF::assembly::arm::OPCODE::VSTR_P0_pre)
  .value("VSTR_VPR_off", LIEF::assembly::arm::OPCODE::VSTR_VPR_off)
  .value("VSTR_VPR_post", LIEF::assembly::arm::OPCODE::VSTR_VPR_post)
  .value("VSTR_VPR_pre", LIEF::assembly::arm::OPCODE::VSTR_VPR_pre)
  .value("VSUBD", LIEF::assembly::arm::OPCODE::VSUBD)
  .value("VSUBH", LIEF::assembly::arm::OPCODE::VSUBH)
  .value("VSUBHNv2i32", LIEF::assembly::arm::OPCODE::VSUBHNv2i32)
  .value("VSUBHNv4i16", LIEF::assembly::arm::OPCODE::VSUBHNv4i16)
  .value("VSUBHNv8i8", LIEF::assembly::arm::OPCODE::VSUBHNv8i8)
  .value("VSUBLsv2i64", LIEF::assembly::arm::OPCODE::VSUBLsv2i64)
  .value("VSUBLsv4i32", LIEF::assembly::arm::OPCODE::VSUBLsv4i32)
  .value("VSUBLsv8i16", LIEF::assembly::arm::OPCODE::VSUBLsv8i16)
  .value("VSUBLuv2i64", LIEF::assembly::arm::OPCODE::VSUBLuv2i64);
  opcodes.value("VSUBLuv4i32", LIEF::assembly::arm::OPCODE::VSUBLuv4i32)
  .value("VSUBLuv8i16", LIEF::assembly::arm::OPCODE::VSUBLuv8i16)
  .value("VSUBS", LIEF::assembly::arm::OPCODE::VSUBS)
  .value("VSUBWsv2i64", LIEF::assembly::arm::OPCODE::VSUBWsv2i64)
  .value("VSUBWsv4i32", LIEF::assembly::arm::OPCODE::VSUBWsv4i32)
  .value("VSUBWsv8i16", LIEF::assembly::arm::OPCODE::VSUBWsv8i16)
  .value("VSUBWuv2i64", LIEF::assembly::arm::OPCODE::VSUBWuv2i64)
  .value("VSUBWuv4i32", LIEF::assembly::arm::OPCODE::VSUBWuv4i32)
  .value("VSUBWuv8i16", LIEF::assembly::arm::OPCODE::VSUBWuv8i16)
  .value("VSUBfd", LIEF::assembly::arm::OPCODE::VSUBfd)
  .value("VSUBfq", LIEF::assembly::arm::OPCODE::VSUBfq)
  .value("VSUBhd", LIEF::assembly::arm::OPCODE::VSUBhd)
  .value("VSUBhq", LIEF::assembly::arm::OPCODE::VSUBhq)
  .value("VSUBv16i8", LIEF::assembly::arm::OPCODE::VSUBv16i8)
  .value("VSUBv1i64", LIEF::assembly::arm::OPCODE::VSUBv1i64)
  .value("VSUBv2i32", LIEF::assembly::arm::OPCODE::VSUBv2i32)
  .value("VSUBv2i64", LIEF::assembly::arm::OPCODE::VSUBv2i64)
  .value("VSUBv4i16", LIEF::assembly::arm::OPCODE::VSUBv4i16)
  .value("VSUBv4i32", LIEF::assembly::arm::OPCODE::VSUBv4i32)
  .value("VSUBv8i16", LIEF::assembly::arm::OPCODE::VSUBv8i16)
  .value("VSUBv8i8", LIEF::assembly::arm::OPCODE::VSUBv8i8)
  .value("VSUDOTDI", LIEF::assembly::arm::OPCODE::VSUDOTDI)
  .value("VSUDOTQI", LIEF::assembly::arm::OPCODE::VSUDOTQI)
  .value("VSWPd", LIEF::assembly::arm::OPCODE::VSWPd)
  .value("VSWPq", LIEF::assembly::arm::OPCODE::VSWPq)
  .value("VTBL1", LIEF::assembly::arm::OPCODE::VTBL1)
  .value("VTBL2", LIEF::assembly::arm::OPCODE::VTBL2)
  .value("VTBL3", LIEF::assembly::arm::OPCODE::VTBL3)
  .value("VTBL3Pseudo", LIEF::assembly::arm::OPCODE::VTBL3Pseudo)
  .value("VTBL4", LIEF::assembly::arm::OPCODE::VTBL4)
  .value("VTBL4Pseudo", LIEF::assembly::arm::OPCODE::VTBL4Pseudo)
  .value("VTBX1", LIEF::assembly::arm::OPCODE::VTBX1)
  .value("VTBX2", LIEF::assembly::arm::OPCODE::VTBX2)
  .value("VTBX3", LIEF::assembly::arm::OPCODE::VTBX3)
  .value("VTBX3Pseudo", LIEF::assembly::arm::OPCODE::VTBX3Pseudo)
  .value("VTBX4", LIEF::assembly::arm::OPCODE::VTBX4)
  .value("VTBX4Pseudo", LIEF::assembly::arm::OPCODE::VTBX4Pseudo)
  .value("VTOSHD", LIEF::assembly::arm::OPCODE::VTOSHD)
  .value("VTOSHH", LIEF::assembly::arm::OPCODE::VTOSHH)
  .value("VTOSHS", LIEF::assembly::arm::OPCODE::VTOSHS)
  .value("VTOSIRD", LIEF::assembly::arm::OPCODE::VTOSIRD)
  .value("VTOSIRH", LIEF::assembly::arm::OPCODE::VTOSIRH)
  .value("VTOSIRS", LIEF::assembly::arm::OPCODE::VTOSIRS)
  .value("VTOSIZD", LIEF::assembly::arm::OPCODE::VTOSIZD)
  .value("VTOSIZH", LIEF::assembly::arm::OPCODE::VTOSIZH)
  .value("VTOSIZS", LIEF::assembly::arm::OPCODE::VTOSIZS)
  .value("VTOSLD", LIEF::assembly::arm::OPCODE::VTOSLD)
  .value("VTOSLH", LIEF::assembly::arm::OPCODE::VTOSLH)
  .value("VTOSLS", LIEF::assembly::arm::OPCODE::VTOSLS)
  .value("VTOUHD", LIEF::assembly::arm::OPCODE::VTOUHD)
  .value("VTOUHH", LIEF::assembly::arm::OPCODE::VTOUHH)
  .value("VTOUHS", LIEF::assembly::arm::OPCODE::VTOUHS)
  .value("VTOUIRD", LIEF::assembly::arm::OPCODE::VTOUIRD)
  .value("VTOUIRH", LIEF::assembly::arm::OPCODE::VTOUIRH)
  .value("VTOUIRS", LIEF::assembly::arm::OPCODE::VTOUIRS)
  .value("VTOUIZD", LIEF::assembly::arm::OPCODE::VTOUIZD)
  .value("VTOUIZH", LIEF::assembly::arm::OPCODE::VTOUIZH)
  .value("VTOUIZS", LIEF::assembly::arm::OPCODE::VTOUIZS)
  .value("VTOULD", LIEF::assembly::arm::OPCODE::VTOULD)
  .value("VTOULH", LIEF::assembly::arm::OPCODE::VTOULH)
  .value("VTOULS", LIEF::assembly::arm::OPCODE::VTOULS)
  .value("VTRNd16", LIEF::assembly::arm::OPCODE::VTRNd16)
  .value("VTRNd32", LIEF::assembly::arm::OPCODE::VTRNd32)
  .value("VTRNd8", LIEF::assembly::arm::OPCODE::VTRNd8)
  .value("VTRNq16", LIEF::assembly::arm::OPCODE::VTRNq16)
  .value("VTRNq32", LIEF::assembly::arm::OPCODE::VTRNq32)
  .value("VTRNq8", LIEF::assembly::arm::OPCODE::VTRNq8)
  .value("VTSTv16i8", LIEF::assembly::arm::OPCODE::VTSTv16i8)
  .value("VTSTv2i32", LIEF::assembly::arm::OPCODE::VTSTv2i32)
  .value("VTSTv4i16", LIEF::assembly::arm::OPCODE::VTSTv4i16)
  .value("VTSTv4i32", LIEF::assembly::arm::OPCODE::VTSTv4i32)
  .value("VTSTv8i16", LIEF::assembly::arm::OPCODE::VTSTv8i16)
  .value("VTSTv8i8", LIEF::assembly::arm::OPCODE::VTSTv8i8)
  .value("VUDOTD", LIEF::assembly::arm::OPCODE::VUDOTD)
  .value("VUDOTDI", LIEF::assembly::arm::OPCODE::VUDOTDI)
  .value("VUDOTQ", LIEF::assembly::arm::OPCODE::VUDOTQ)
  .value("VUDOTQI", LIEF::assembly::arm::OPCODE::VUDOTQI)
  .value("VUHTOD", LIEF::assembly::arm::OPCODE::VUHTOD)
  .value("VUHTOH", LIEF::assembly::arm::OPCODE::VUHTOH)
  .value("VUHTOS", LIEF::assembly::arm::OPCODE::VUHTOS)
  .value("VUITOD", LIEF::assembly::arm::OPCODE::VUITOD)
  .value("VUITOH", LIEF::assembly::arm::OPCODE::VUITOH)
  .value("VUITOS", LIEF::assembly::arm::OPCODE::VUITOS)
  .value("VULTOD", LIEF::assembly::arm::OPCODE::VULTOD)
  .value("VULTOH", LIEF::assembly::arm::OPCODE::VULTOH)
  .value("VULTOS", LIEF::assembly::arm::OPCODE::VULTOS)
  .value("VUMMLA", LIEF::assembly::arm::OPCODE::VUMMLA)
  .value("VUSDOTD", LIEF::assembly::arm::OPCODE::VUSDOTD)
  .value("VUSDOTDI", LIEF::assembly::arm::OPCODE::VUSDOTDI)
  .value("VUSDOTQ", LIEF::assembly::arm::OPCODE::VUSDOTQ)
  .value("VUSDOTQI", LIEF::assembly::arm::OPCODE::VUSDOTQI)
  .value("VUSMMLA", LIEF::assembly::arm::OPCODE::VUSMMLA)
  .value("VUZPd16", LIEF::assembly::arm::OPCODE::VUZPd16)
  .value("VUZPd8", LIEF::assembly::arm::OPCODE::VUZPd8)
  .value("VUZPq16", LIEF::assembly::arm::OPCODE::VUZPq16)
  .value("VUZPq32", LIEF::assembly::arm::OPCODE::VUZPq32)
  .value("VUZPq8", LIEF::assembly::arm::OPCODE::VUZPq8)
  .value("VZIPd16", LIEF::assembly::arm::OPCODE::VZIPd16)
  .value("VZIPd8", LIEF::assembly::arm::OPCODE::VZIPd8)
  .value("VZIPq16", LIEF::assembly::arm::OPCODE::VZIPq16)
  .value("VZIPq32", LIEF::assembly::arm::OPCODE::VZIPq32)
  .value("VZIPq8", LIEF::assembly::arm::OPCODE::VZIPq8)
  .value("sysLDMDA", LIEF::assembly::arm::OPCODE::sysLDMDA)
  .value("sysLDMDA_UPD", LIEF::assembly::arm::OPCODE::sysLDMDA_UPD)
  .value("sysLDMDB", LIEF::assembly::arm::OPCODE::sysLDMDB)
  .value("sysLDMDB_UPD", LIEF::assembly::arm::OPCODE::sysLDMDB_UPD)
  .value("sysLDMIA", LIEF::assembly::arm::OPCODE::sysLDMIA)
  .value("sysLDMIA_UPD", LIEF::assembly::arm::OPCODE::sysLDMIA_UPD)
  .value("sysLDMIB", LIEF::assembly::arm::OPCODE::sysLDMIB)
  .value("sysLDMIB_UPD", LIEF::assembly::arm::OPCODE::sysLDMIB_UPD)
  .value("sysSTMDA", LIEF::assembly::arm::OPCODE::sysSTMDA)
  .value("sysSTMDA_UPD", LIEF::assembly::arm::OPCODE::sysSTMDA_UPD)
  .value("sysSTMDB", LIEF::assembly::arm::OPCODE::sysSTMDB)
  .value("sysSTMDB_UPD", LIEF::assembly::arm::OPCODE::sysSTMDB_UPD)
  .value("sysSTMIA", LIEF::assembly::arm::OPCODE::sysSTMIA)
  .value("sysSTMIA_UPD", LIEF::assembly::arm::OPCODE::sysSTMIA_UPD)
  .value("sysSTMIB", LIEF::assembly::arm::OPCODE::sysSTMIB)
  .value("sysSTMIB_UPD", LIEF::assembly::arm::OPCODE::sysSTMIB_UPD)
  .value("t2ADCri", LIEF::assembly::arm::OPCODE::t2ADCri)
  .value("t2ADCrr", LIEF::assembly::arm::OPCODE::t2ADCrr)
  .value("t2ADCrs", LIEF::assembly::arm::OPCODE::t2ADCrs)
  .value("t2ADDri", LIEF::assembly::arm::OPCODE::t2ADDri)
  .value("t2ADDri12", LIEF::assembly::arm::OPCODE::t2ADDri12)
  .value("t2ADDrr", LIEF::assembly::arm::OPCODE::t2ADDrr)
  .value("t2ADDrs", LIEF::assembly::arm::OPCODE::t2ADDrs)
  .value("t2ADDspImm", LIEF::assembly::arm::OPCODE::t2ADDspImm)
  .value("t2ADDspImm12", LIEF::assembly::arm::OPCODE::t2ADDspImm12)
  .value("t2ADR", LIEF::assembly::arm::OPCODE::t2ADR)
  .value("t2ANDri", LIEF::assembly::arm::OPCODE::t2ANDri)
  .value("t2ANDrr", LIEF::assembly::arm::OPCODE::t2ANDrr)
  .value("t2ANDrs", LIEF::assembly::arm::OPCODE::t2ANDrs)
  .value("t2ASRri", LIEF::assembly::arm::OPCODE::t2ASRri)
  .value("t2ASRrr", LIEF::assembly::arm::OPCODE::t2ASRrr)
  .value("t2ASRs1", LIEF::assembly::arm::OPCODE::t2ASRs1)
  .value("t2AUT", LIEF::assembly::arm::OPCODE::t2AUT)
  .value("t2AUTG", LIEF::assembly::arm::OPCODE::t2AUTG)
  .value("t2B", LIEF::assembly::arm::OPCODE::t2B)
  .value("t2BFC", LIEF::assembly::arm::OPCODE::t2BFC)
  .value("t2BFI", LIEF::assembly::arm::OPCODE::t2BFI)
  .value("t2BFLi", LIEF::assembly::arm::OPCODE::t2BFLi)
  .value("t2BFLr", LIEF::assembly::arm::OPCODE::t2BFLr)
  .value("t2BFi", LIEF::assembly::arm::OPCODE::t2BFi)
  .value("t2BFic", LIEF::assembly::arm::OPCODE::t2BFic)
  .value("t2BFr", LIEF::assembly::arm::OPCODE::t2BFr)
  .value("t2BICri", LIEF::assembly::arm::OPCODE::t2BICri)
  .value("t2BICrr", LIEF::assembly::arm::OPCODE::t2BICrr)
  .value("t2BICrs", LIEF::assembly::arm::OPCODE::t2BICrs)
  .value("t2BTI", LIEF::assembly::arm::OPCODE::t2BTI)
  .value("t2BXAUT", LIEF::assembly::arm::OPCODE::t2BXAUT)
  .value("t2BXJ", LIEF::assembly::arm::OPCODE::t2BXJ)
  .value("t2Bcc", LIEF::assembly::arm::OPCODE::t2Bcc)
  .value("t2CDP", LIEF::assembly::arm::OPCODE::t2CDP)
  .value("t2CDP2", LIEF::assembly::arm::OPCODE::t2CDP2)
  .value("t2CLREX", LIEF::assembly::arm::OPCODE::t2CLREX)
  .value("t2CLRM", LIEF::assembly::arm::OPCODE::t2CLRM)
  .value("t2CLZ", LIEF::assembly::arm::OPCODE::t2CLZ)
  .value("t2CMNri", LIEF::assembly::arm::OPCODE::t2CMNri)
  .value("t2CMNzrr", LIEF::assembly::arm::OPCODE::t2CMNzrr)
  .value("t2CMNzrs", LIEF::assembly::arm::OPCODE::t2CMNzrs)
  .value("t2CMPri", LIEF::assembly::arm::OPCODE::t2CMPri)
  .value("t2CMPrr", LIEF::assembly::arm::OPCODE::t2CMPrr)
  .value("t2CMPrs", LIEF::assembly::arm::OPCODE::t2CMPrs)
  .value("t2CPS1p", LIEF::assembly::arm::OPCODE::t2CPS1p)
  .value("t2CPS2p", LIEF::assembly::arm::OPCODE::t2CPS2p)
  .value("t2CPS3p", LIEF::assembly::arm::OPCODE::t2CPS3p)
  .value("t2CRC32B", LIEF::assembly::arm::OPCODE::t2CRC32B)
  .value("t2CRC32CB", LIEF::assembly::arm::OPCODE::t2CRC32CB)
  .value("t2CRC32CH", LIEF::assembly::arm::OPCODE::t2CRC32CH)
  .value("t2CRC32CW", LIEF::assembly::arm::OPCODE::t2CRC32CW)
  .value("t2CRC32H", LIEF::assembly::arm::OPCODE::t2CRC32H)
  .value("t2CRC32W", LIEF::assembly::arm::OPCODE::t2CRC32W)
  .value("t2CSEL", LIEF::assembly::arm::OPCODE::t2CSEL)
  .value("t2CSINC", LIEF::assembly::arm::OPCODE::t2CSINC)
  .value("t2CSINV", LIEF::assembly::arm::OPCODE::t2CSINV)
  .value("t2CSNEG", LIEF::assembly::arm::OPCODE::t2CSNEG)
  .value("t2DBG", LIEF::assembly::arm::OPCODE::t2DBG)
  .value("t2DCPS1", LIEF::assembly::arm::OPCODE::t2DCPS1)
  .value("t2DCPS2", LIEF::assembly::arm::OPCODE::t2DCPS2)
  .value("t2DCPS3", LIEF::assembly::arm::OPCODE::t2DCPS3)
  .value("t2DLS", LIEF::assembly::arm::OPCODE::t2DLS)
  .value("t2DMB", LIEF::assembly::arm::OPCODE::t2DMB)
  .value("t2DSB", LIEF::assembly::arm::OPCODE::t2DSB)
  .value("t2EORri", LIEF::assembly::arm::OPCODE::t2EORri)
  .value("t2EORrr", LIEF::assembly::arm::OPCODE::t2EORrr)
  .value("t2EORrs", LIEF::assembly::arm::OPCODE::t2EORrs)
  .value("t2HINT", LIEF::assembly::arm::OPCODE::t2HINT)
  .value("t2HVC", LIEF::assembly::arm::OPCODE::t2HVC)
  .value("t2ISB", LIEF::assembly::arm::OPCODE::t2ISB)
  .value("t2IT", LIEF::assembly::arm::OPCODE::t2IT)
  .value("t2Int_eh_sjlj_setjmp", LIEF::assembly::arm::OPCODE::t2Int_eh_sjlj_setjmp)
  .value("t2Int_eh_sjlj_setjmp_nofp", LIEF::assembly::arm::OPCODE::t2Int_eh_sjlj_setjmp_nofp)
  .value("t2LDA", LIEF::assembly::arm::OPCODE::t2LDA)
  .value("t2LDAB", LIEF::assembly::arm::OPCODE::t2LDAB)
  .value("t2LDAEX", LIEF::assembly::arm::OPCODE::t2LDAEX)
  .value("t2LDAEXB", LIEF::assembly::arm::OPCODE::t2LDAEXB)
  .value("t2LDAEXD", LIEF::assembly::arm::OPCODE::t2LDAEXD)
  .value("t2LDAEXH", LIEF::assembly::arm::OPCODE::t2LDAEXH)
  .value("t2LDAH", LIEF::assembly::arm::OPCODE::t2LDAH)
  .value("t2LDC2L_OFFSET", LIEF::assembly::arm::OPCODE::t2LDC2L_OFFSET)
  .value("t2LDC2L_OPTION", LIEF::assembly::arm::OPCODE::t2LDC2L_OPTION)
  .value("t2LDC2L_POST", LIEF::assembly::arm::OPCODE::t2LDC2L_POST)
  .value("t2LDC2L_PRE", LIEF::assembly::arm::OPCODE::t2LDC2L_PRE)
  .value("t2LDC2_OFFSET", LIEF::assembly::arm::OPCODE::t2LDC2_OFFSET)
  .value("t2LDC2_OPTION", LIEF::assembly::arm::OPCODE::t2LDC2_OPTION)
  .value("t2LDC2_POST", LIEF::assembly::arm::OPCODE::t2LDC2_POST)
  .value("t2LDC2_PRE", LIEF::assembly::arm::OPCODE::t2LDC2_PRE)
  .value("t2LDCL_OFFSET", LIEF::assembly::arm::OPCODE::t2LDCL_OFFSET)
  .value("t2LDCL_OPTION", LIEF::assembly::arm::OPCODE::t2LDCL_OPTION)
  .value("t2LDCL_POST", LIEF::assembly::arm::OPCODE::t2LDCL_POST)
  .value("t2LDCL_PRE", LIEF::assembly::arm::OPCODE::t2LDCL_PRE)
  .value("t2LDC_OFFSET", LIEF::assembly::arm::OPCODE::t2LDC_OFFSET)
  .value("t2LDC_OPTION", LIEF::assembly::arm::OPCODE::t2LDC_OPTION)
  .value("t2LDC_POST", LIEF::assembly::arm::OPCODE::t2LDC_POST)
  .value("t2LDC_PRE", LIEF::assembly::arm::OPCODE::t2LDC_PRE)
  .value("t2LDMDB", LIEF::assembly::arm::OPCODE::t2LDMDB)
  .value("t2LDMDB_UPD", LIEF::assembly::arm::OPCODE::t2LDMDB_UPD)
  .value("t2LDMIA", LIEF::assembly::arm::OPCODE::t2LDMIA)
  .value("t2LDMIA_UPD", LIEF::assembly::arm::OPCODE::t2LDMIA_UPD)
  .value("t2LDRBT", LIEF::assembly::arm::OPCODE::t2LDRBT)
  .value("t2LDRB_POST", LIEF::assembly::arm::OPCODE::t2LDRB_POST)
  .value("t2LDRB_PRE", LIEF::assembly::arm::OPCODE::t2LDRB_PRE)
  .value("t2LDRBi12", LIEF::assembly::arm::OPCODE::t2LDRBi12)
  .value("t2LDRBi8", LIEF::assembly::arm::OPCODE::t2LDRBi8)
  .value("t2LDRBpci", LIEF::assembly::arm::OPCODE::t2LDRBpci)
  .value("t2LDRBs", LIEF::assembly::arm::OPCODE::t2LDRBs)
  .value("t2LDRD_POST", LIEF::assembly::arm::OPCODE::t2LDRD_POST)
  .value("t2LDRD_PRE", LIEF::assembly::arm::OPCODE::t2LDRD_PRE)
  .value("t2LDRDi8", LIEF::assembly::arm::OPCODE::t2LDRDi8)
  .value("t2LDREX", LIEF::assembly::arm::OPCODE::t2LDREX)
  .value("t2LDREXB", LIEF::assembly::arm::OPCODE::t2LDREXB)
  .value("t2LDREXD", LIEF::assembly::arm::OPCODE::t2LDREXD)
  .value("t2LDREXH", LIEF::assembly::arm::OPCODE::t2LDREXH)
  .value("t2LDRHT", LIEF::assembly::arm::OPCODE::t2LDRHT)
  .value("t2LDRH_POST", LIEF::assembly::arm::OPCODE::t2LDRH_POST)
  .value("t2LDRH_PRE", LIEF::assembly::arm::OPCODE::t2LDRH_PRE)
  .value("t2LDRHi12", LIEF::assembly::arm::OPCODE::t2LDRHi12)
  .value("t2LDRHi8", LIEF::assembly::arm::OPCODE::t2LDRHi8)
  .value("t2LDRHpci", LIEF::assembly::arm::OPCODE::t2LDRHpci)
  .value("t2LDRHs", LIEF::assembly::arm::OPCODE::t2LDRHs)
  .value("t2LDRSBT", LIEF::assembly::arm::OPCODE::t2LDRSBT)
  .value("t2LDRSB_POST", LIEF::assembly::arm::OPCODE::t2LDRSB_POST)
  .value("t2LDRSB_PRE", LIEF::assembly::arm::OPCODE::t2LDRSB_PRE)
  .value("t2LDRSBi12", LIEF::assembly::arm::OPCODE::t2LDRSBi12)
  .value("t2LDRSBi8", LIEF::assembly::arm::OPCODE::t2LDRSBi8)
  .value("t2LDRSBpci", LIEF::assembly::arm::OPCODE::t2LDRSBpci)
  .value("t2LDRSBs", LIEF::assembly::arm::OPCODE::t2LDRSBs)
  .value("t2LDRSHT", LIEF::assembly::arm::OPCODE::t2LDRSHT)
  .value("t2LDRSH_POST", LIEF::assembly::arm::OPCODE::t2LDRSH_POST)
  .value("t2LDRSH_PRE", LIEF::assembly::arm::OPCODE::t2LDRSH_PRE)
  .value("t2LDRSHi12", LIEF::assembly::arm::OPCODE::t2LDRSHi12)
  .value("t2LDRSHi8", LIEF::assembly::arm::OPCODE::t2LDRSHi8)
  .value("t2LDRSHpci", LIEF::assembly::arm::OPCODE::t2LDRSHpci)
  .value("t2LDRSHs", LIEF::assembly::arm::OPCODE::t2LDRSHs)
  .value("t2LDRT", LIEF::assembly::arm::OPCODE::t2LDRT)
  .value("t2LDR_POST", LIEF::assembly::arm::OPCODE::t2LDR_POST)
  .value("t2LDR_PRE", LIEF::assembly::arm::OPCODE::t2LDR_PRE)
  .value("t2LDRi12", LIEF::assembly::arm::OPCODE::t2LDRi12)
  .value("t2LDRi8", LIEF::assembly::arm::OPCODE::t2LDRi8)
  .value("t2LDRpci", LIEF::assembly::arm::OPCODE::t2LDRpci)
  .value("t2LDRs", LIEF::assembly::arm::OPCODE::t2LDRs)
  .value("t2LE", LIEF::assembly::arm::OPCODE::t2LE)
  .value("t2LEUpdate", LIEF::assembly::arm::OPCODE::t2LEUpdate)
  .value("t2LSLri", LIEF::assembly::arm::OPCODE::t2LSLri)
  .value("t2LSLrr", LIEF::assembly::arm::OPCODE::t2LSLrr)
  .value("t2LSRri", LIEF::assembly::arm::OPCODE::t2LSRri)
  .value("t2LSRrr", LIEF::assembly::arm::OPCODE::t2LSRrr)
  .value("t2LSRs1", LIEF::assembly::arm::OPCODE::t2LSRs1)
  .value("t2MCR", LIEF::assembly::arm::OPCODE::t2MCR)
  .value("t2MCR2", LIEF::assembly::arm::OPCODE::t2MCR2)
  .value("t2MCRR", LIEF::assembly::arm::OPCODE::t2MCRR)
  .value("t2MCRR2", LIEF::assembly::arm::OPCODE::t2MCRR2)
  .value("t2MLA", LIEF::assembly::arm::OPCODE::t2MLA)
  .value("t2MLS", LIEF::assembly::arm::OPCODE::t2MLS)
  .value("t2MOVTi16", LIEF::assembly::arm::OPCODE::t2MOVTi16)
  .value("t2MOVi", LIEF::assembly::arm::OPCODE::t2MOVi)
  .value("t2MOVi16", LIEF::assembly::arm::OPCODE::t2MOVi16)
  .value("t2MOVr", LIEF::assembly::arm::OPCODE::t2MOVr)
  .value("t2MRC", LIEF::assembly::arm::OPCODE::t2MRC)
  .value("t2MRC2", LIEF::assembly::arm::OPCODE::t2MRC2)
  .value("t2MRRC", LIEF::assembly::arm::OPCODE::t2MRRC)
  .value("t2MRRC2", LIEF::assembly::arm::OPCODE::t2MRRC2)
  .value("t2MRS_AR", LIEF::assembly::arm::OPCODE::t2MRS_AR)
  .value("t2MRS_M", LIEF::assembly::arm::OPCODE::t2MRS_M)
  .value("t2MRSbanked", LIEF::assembly::arm::OPCODE::t2MRSbanked)
  .value("t2MRSsys_AR", LIEF::assembly::arm::OPCODE::t2MRSsys_AR)
  .value("t2MSR_AR", LIEF::assembly::arm::OPCODE::t2MSR_AR)
  .value("t2MSR_M", LIEF::assembly::arm::OPCODE::t2MSR_M)
  .value("t2MSRbanked", LIEF::assembly::arm::OPCODE::t2MSRbanked)
  .value("t2MUL", LIEF::assembly::arm::OPCODE::t2MUL)
  .value("t2MVNi", LIEF::assembly::arm::OPCODE::t2MVNi)
  .value("t2MVNr", LIEF::assembly::arm::OPCODE::t2MVNr)
  .value("t2MVNs", LIEF::assembly::arm::OPCODE::t2MVNs)
  .value("t2ORNri", LIEF::assembly::arm::OPCODE::t2ORNri)
  .value("t2ORNrr", LIEF::assembly::arm::OPCODE::t2ORNrr)
  .value("t2ORNrs", LIEF::assembly::arm::OPCODE::t2ORNrs)
  .value("t2ORRri", LIEF::assembly::arm::OPCODE::t2ORRri)
  .value("t2ORRrr", LIEF::assembly::arm::OPCODE::t2ORRrr)
  .value("t2ORRrs", LIEF::assembly::arm::OPCODE::t2ORRrs)
  .value("t2PAC", LIEF::assembly::arm::OPCODE::t2PAC)
  .value("t2PACBTI", LIEF::assembly::arm::OPCODE::t2PACBTI);
  opcodes.value("t2PACG", LIEF::assembly::arm::OPCODE::t2PACG)
  .value("t2PKHBT", LIEF::assembly::arm::OPCODE::t2PKHBT)
  .value("t2PKHTB", LIEF::assembly::arm::OPCODE::t2PKHTB)
  .value("t2PLDWi12", LIEF::assembly::arm::OPCODE::t2PLDWi12)
  .value("t2PLDWi8", LIEF::assembly::arm::OPCODE::t2PLDWi8)
  .value("t2PLDWs", LIEF::assembly::arm::OPCODE::t2PLDWs)
  .value("t2PLDi12", LIEF::assembly::arm::OPCODE::t2PLDi12)
  .value("t2PLDi8", LIEF::assembly::arm::OPCODE::t2PLDi8)
  .value("t2PLDpci", LIEF::assembly::arm::OPCODE::t2PLDpci)
  .value("t2PLDs", LIEF::assembly::arm::OPCODE::t2PLDs)
  .value("t2PLIi12", LIEF::assembly::arm::OPCODE::t2PLIi12)
  .value("t2PLIi8", LIEF::assembly::arm::OPCODE::t2PLIi8)
  .value("t2PLIpci", LIEF::assembly::arm::OPCODE::t2PLIpci)
  .value("t2PLIs", LIEF::assembly::arm::OPCODE::t2PLIs)
  .value("t2QADD", LIEF::assembly::arm::OPCODE::t2QADD)
  .value("t2QADD16", LIEF::assembly::arm::OPCODE::t2QADD16)
  .value("t2QADD8", LIEF::assembly::arm::OPCODE::t2QADD8)
  .value("t2QASX", LIEF::assembly::arm::OPCODE::t2QASX)
  .value("t2QDADD", LIEF::assembly::arm::OPCODE::t2QDADD)
  .value("t2QDSUB", LIEF::assembly::arm::OPCODE::t2QDSUB)
  .value("t2QSAX", LIEF::assembly::arm::OPCODE::t2QSAX)
  .value("t2QSUB", LIEF::assembly::arm::OPCODE::t2QSUB)
  .value("t2QSUB16", LIEF::assembly::arm::OPCODE::t2QSUB16)
  .value("t2QSUB8", LIEF::assembly::arm::OPCODE::t2QSUB8)
  .value("t2RBIT", LIEF::assembly::arm::OPCODE::t2RBIT)
  .value("t2REV", LIEF::assembly::arm::OPCODE::t2REV)
  .value("t2REV16", LIEF::assembly::arm::OPCODE::t2REV16)
  .value("t2REVSH", LIEF::assembly::arm::OPCODE::t2REVSH)
  .value("t2RFEDB", LIEF::assembly::arm::OPCODE::t2RFEDB)
  .value("t2RFEDBW", LIEF::assembly::arm::OPCODE::t2RFEDBW)
  .value("t2RFEIA", LIEF::assembly::arm::OPCODE::t2RFEIA)
  .value("t2RFEIAW", LIEF::assembly::arm::OPCODE::t2RFEIAW)
  .value("t2RORri", LIEF::assembly::arm::OPCODE::t2RORri)
  .value("t2RORrr", LIEF::assembly::arm::OPCODE::t2RORrr)
  .value("t2RRX", LIEF::assembly::arm::OPCODE::t2RRX)
  .value("t2RSBri", LIEF::assembly::arm::OPCODE::t2RSBri)
  .value("t2RSBrr", LIEF::assembly::arm::OPCODE::t2RSBrr)
  .value("t2RSBrs", LIEF::assembly::arm::OPCODE::t2RSBrs)
  .value("t2SADD16", LIEF::assembly::arm::OPCODE::t2SADD16)
  .value("t2SADD8", LIEF::assembly::arm::OPCODE::t2SADD8)
  .value("t2SASX", LIEF::assembly::arm::OPCODE::t2SASX)
  .value("t2SB", LIEF::assembly::arm::OPCODE::t2SB)
  .value("t2SBCri", LIEF::assembly::arm::OPCODE::t2SBCri)
  .value("t2SBCrr", LIEF::assembly::arm::OPCODE::t2SBCrr)
  .value("t2SBCrs", LIEF::assembly::arm::OPCODE::t2SBCrs)
  .value("t2SBFX", LIEF::assembly::arm::OPCODE::t2SBFX)
  .value("t2SDIV", LIEF::assembly::arm::OPCODE::t2SDIV)
  .value("t2SEL", LIEF::assembly::arm::OPCODE::t2SEL)
  .value("t2SETPAN", LIEF::assembly::arm::OPCODE::t2SETPAN)
  .value("t2SG", LIEF::assembly::arm::OPCODE::t2SG)
  .value("t2SHADD16", LIEF::assembly::arm::OPCODE::t2SHADD16)
  .value("t2SHADD8", LIEF::assembly::arm::OPCODE::t2SHADD8)
  .value("t2SHASX", LIEF::assembly::arm::OPCODE::t2SHASX)
  .value("t2SHSAX", LIEF::assembly::arm::OPCODE::t2SHSAX)
  .value("t2SHSUB16", LIEF::assembly::arm::OPCODE::t2SHSUB16)
  .value("t2SHSUB8", LIEF::assembly::arm::OPCODE::t2SHSUB8)
  .value("t2SMC", LIEF::assembly::arm::OPCODE::t2SMC)
  .value("t2SMLABB", LIEF::assembly::arm::OPCODE::t2SMLABB)
  .value("t2SMLABT", LIEF::assembly::arm::OPCODE::t2SMLABT)
  .value("t2SMLAD", LIEF::assembly::arm::OPCODE::t2SMLAD)
  .value("t2SMLADX", LIEF::assembly::arm::OPCODE::t2SMLADX)
  .value("t2SMLAL", LIEF::assembly::arm::OPCODE::t2SMLAL)
  .value("t2SMLALBB", LIEF::assembly::arm::OPCODE::t2SMLALBB)
  .value("t2SMLALBT", LIEF::assembly::arm::OPCODE::t2SMLALBT)
  .value("t2SMLALD", LIEF::assembly::arm::OPCODE::t2SMLALD)
  .value("t2SMLALDX", LIEF::assembly::arm::OPCODE::t2SMLALDX)
  .value("t2SMLALTB", LIEF::assembly::arm::OPCODE::t2SMLALTB)
  .value("t2SMLALTT", LIEF::assembly::arm::OPCODE::t2SMLALTT)
  .value("t2SMLATB", LIEF::assembly::arm::OPCODE::t2SMLATB)
  .value("t2SMLATT", LIEF::assembly::arm::OPCODE::t2SMLATT)
  .value("t2SMLAWB", LIEF::assembly::arm::OPCODE::t2SMLAWB)
  .value("t2SMLAWT", LIEF::assembly::arm::OPCODE::t2SMLAWT)
  .value("t2SMLSD", LIEF::assembly::arm::OPCODE::t2SMLSD)
  .value("t2SMLSDX", LIEF::assembly::arm::OPCODE::t2SMLSDX)
  .value("t2SMLSLD", LIEF::assembly::arm::OPCODE::t2SMLSLD)
  .value("t2SMLSLDX", LIEF::assembly::arm::OPCODE::t2SMLSLDX)
  .value("t2SMMLA", LIEF::assembly::arm::OPCODE::t2SMMLA)
  .value("t2SMMLAR", LIEF::assembly::arm::OPCODE::t2SMMLAR)
  .value("t2SMMLS", LIEF::assembly::arm::OPCODE::t2SMMLS)
  .value("t2SMMLSR", LIEF::assembly::arm::OPCODE::t2SMMLSR)
  .value("t2SMMUL", LIEF::assembly::arm::OPCODE::t2SMMUL)
  .value("t2SMMULR", LIEF::assembly::arm::OPCODE::t2SMMULR)
  .value("t2SMUAD", LIEF::assembly::arm::OPCODE::t2SMUAD)
  .value("t2SMUADX", LIEF::assembly::arm::OPCODE::t2SMUADX)
  .value("t2SMULBB", LIEF::assembly::arm::OPCODE::t2SMULBB)
  .value("t2SMULBT", LIEF::assembly::arm::OPCODE::t2SMULBT)
  .value("t2SMULL", LIEF::assembly::arm::OPCODE::t2SMULL)
  .value("t2SMULTB", LIEF::assembly::arm::OPCODE::t2SMULTB)
  .value("t2SMULTT", LIEF::assembly::arm::OPCODE::t2SMULTT)
  .value("t2SMULWB", LIEF::assembly::arm::OPCODE::t2SMULWB)
  .value("t2SMULWT", LIEF::assembly::arm::OPCODE::t2SMULWT)
  .value("t2SMUSD", LIEF::assembly::arm::OPCODE::t2SMUSD)
  .value("t2SMUSDX", LIEF::assembly::arm::OPCODE::t2SMUSDX)
  .value("t2SRSDB", LIEF::assembly::arm::OPCODE::t2SRSDB)
  .value("t2SRSDB_UPD", LIEF::assembly::arm::OPCODE::t2SRSDB_UPD)
  .value("t2SRSIA", LIEF::assembly::arm::OPCODE::t2SRSIA)
  .value("t2SRSIA_UPD", LIEF::assembly::arm::OPCODE::t2SRSIA_UPD)
  .value("t2SSAT", LIEF::assembly::arm::OPCODE::t2SSAT)
  .value("t2SSAT16", LIEF::assembly::arm::OPCODE::t2SSAT16)
  .value("t2SSAX", LIEF::assembly::arm::OPCODE::t2SSAX)
  .value("t2SSUB16", LIEF::assembly::arm::OPCODE::t2SSUB16)
  .value("t2SSUB8", LIEF::assembly::arm::OPCODE::t2SSUB8)
  .value("t2STC2L_OFFSET", LIEF::assembly::arm::OPCODE::t2STC2L_OFFSET)
  .value("t2STC2L_OPTION", LIEF::assembly::arm::OPCODE::t2STC2L_OPTION)
  .value("t2STC2L_POST", LIEF::assembly::arm::OPCODE::t2STC2L_POST)
  .value("t2STC2L_PRE", LIEF::assembly::arm::OPCODE::t2STC2L_PRE)
  .value("t2STC2_OFFSET", LIEF::assembly::arm::OPCODE::t2STC2_OFFSET)
  .value("t2STC2_OPTION", LIEF::assembly::arm::OPCODE::t2STC2_OPTION)
  .value("t2STC2_POST", LIEF::assembly::arm::OPCODE::t2STC2_POST)
  .value("t2STC2_PRE", LIEF::assembly::arm::OPCODE::t2STC2_PRE)
  .value("t2STCL_OFFSET", LIEF::assembly::arm::OPCODE::t2STCL_OFFSET)
  .value("t2STCL_OPTION", LIEF::assembly::arm::OPCODE::t2STCL_OPTION)
  .value("t2STCL_POST", LIEF::assembly::arm::OPCODE::t2STCL_POST)
  .value("t2STCL_PRE", LIEF::assembly::arm::OPCODE::t2STCL_PRE)
  .value("t2STC_OFFSET", LIEF::assembly::arm::OPCODE::t2STC_OFFSET)
  .value("t2STC_OPTION", LIEF::assembly::arm::OPCODE::t2STC_OPTION)
  .value("t2STC_POST", LIEF::assembly::arm::OPCODE::t2STC_POST)
  .value("t2STC_PRE", LIEF::assembly::arm::OPCODE::t2STC_PRE)
  .value("t2STL", LIEF::assembly::arm::OPCODE::t2STL)
  .value("t2STLB", LIEF::assembly::arm::OPCODE::t2STLB)
  .value("t2STLEX", LIEF::assembly::arm::OPCODE::t2STLEX)
  .value("t2STLEXB", LIEF::assembly::arm::OPCODE::t2STLEXB)
  .value("t2STLEXD", LIEF::assembly::arm::OPCODE::t2STLEXD)
  .value("t2STLEXH", LIEF::assembly::arm::OPCODE::t2STLEXH)
  .value("t2STLH", LIEF::assembly::arm::OPCODE::t2STLH)
  .value("t2STMDB", LIEF::assembly::arm::OPCODE::t2STMDB)
  .value("t2STMDB_UPD", LIEF::assembly::arm::OPCODE::t2STMDB_UPD)
  .value("t2STMIA", LIEF::assembly::arm::OPCODE::t2STMIA)
  .value("t2STMIA_UPD", LIEF::assembly::arm::OPCODE::t2STMIA_UPD)
  .value("t2STRBT", LIEF::assembly::arm::OPCODE::t2STRBT)
  .value("t2STRB_POST", LIEF::assembly::arm::OPCODE::t2STRB_POST)
  .value("t2STRB_PRE", LIEF::assembly::arm::OPCODE::t2STRB_PRE)
  .value("t2STRBi12", LIEF::assembly::arm::OPCODE::t2STRBi12)
  .value("t2STRBi8", LIEF::assembly::arm::OPCODE::t2STRBi8)
  .value("t2STRBs", LIEF::assembly::arm::OPCODE::t2STRBs)
  .value("t2STRD_POST", LIEF::assembly::arm::OPCODE::t2STRD_POST)
  .value("t2STRD_PRE", LIEF::assembly::arm::OPCODE::t2STRD_PRE)
  .value("t2STRDi8", LIEF::assembly::arm::OPCODE::t2STRDi8)
  .value("t2STREX", LIEF::assembly::arm::OPCODE::t2STREX)
  .value("t2STREXB", LIEF::assembly::arm::OPCODE::t2STREXB)
  .value("t2STREXD", LIEF::assembly::arm::OPCODE::t2STREXD)
  .value("t2STREXH", LIEF::assembly::arm::OPCODE::t2STREXH)
  .value("t2STRHT", LIEF::assembly::arm::OPCODE::t2STRHT)
  .value("t2STRH_POST", LIEF::assembly::arm::OPCODE::t2STRH_POST)
  .value("t2STRH_PRE", LIEF::assembly::arm::OPCODE::t2STRH_PRE)
  .value("t2STRHi12", LIEF::assembly::arm::OPCODE::t2STRHi12)
  .value("t2STRHi8", LIEF::assembly::arm::OPCODE::t2STRHi8)
  .value("t2STRHs", LIEF::assembly::arm::OPCODE::t2STRHs)
  .value("t2STRT", LIEF::assembly::arm::OPCODE::t2STRT)
  .value("t2STR_POST", LIEF::assembly::arm::OPCODE::t2STR_POST)
  .value("t2STR_PRE", LIEF::assembly::arm::OPCODE::t2STR_PRE)
  .value("t2STRi12", LIEF::assembly::arm::OPCODE::t2STRi12)
  .value("t2STRi8", LIEF::assembly::arm::OPCODE::t2STRi8)
  .value("t2STRs", LIEF::assembly::arm::OPCODE::t2STRs)
  .value("t2SUBS_PC_LR", LIEF::assembly::arm::OPCODE::t2SUBS_PC_LR)
  .value("t2SUBri", LIEF::assembly::arm::OPCODE::t2SUBri)
  .value("t2SUBri12", LIEF::assembly::arm::OPCODE::t2SUBri12)
  .value("t2SUBrr", LIEF::assembly::arm::OPCODE::t2SUBrr)
  .value("t2SUBrs", LIEF::assembly::arm::OPCODE::t2SUBrs)
  .value("t2SUBspImm", LIEF::assembly::arm::OPCODE::t2SUBspImm)
  .value("t2SUBspImm12", LIEF::assembly::arm::OPCODE::t2SUBspImm12)
  .value("t2SXTAB", LIEF::assembly::arm::OPCODE::t2SXTAB)
  .value("t2SXTAB16", LIEF::assembly::arm::OPCODE::t2SXTAB16)
  .value("t2SXTAH", LIEF::assembly::arm::OPCODE::t2SXTAH)
  .value("t2SXTB", LIEF::assembly::arm::OPCODE::t2SXTB)
  .value("t2SXTB16", LIEF::assembly::arm::OPCODE::t2SXTB16)
  .value("t2SXTH", LIEF::assembly::arm::OPCODE::t2SXTH)
  .value("t2TBB", LIEF::assembly::arm::OPCODE::t2TBB)
  .value("t2TBH", LIEF::assembly::arm::OPCODE::t2TBH)
  .value("t2TEQri", LIEF::assembly::arm::OPCODE::t2TEQri)
  .value("t2TEQrr", LIEF::assembly::arm::OPCODE::t2TEQrr)
  .value("t2TEQrs", LIEF::assembly::arm::OPCODE::t2TEQrs)
  .value("t2TSB", LIEF::assembly::arm::OPCODE::t2TSB)
  .value("t2TSTri", LIEF::assembly::arm::OPCODE::t2TSTri)
  .value("t2TSTrr", LIEF::assembly::arm::OPCODE::t2TSTrr)
  .value("t2TSTrs", LIEF::assembly::arm::OPCODE::t2TSTrs)
  .value("t2TT", LIEF::assembly::arm::OPCODE::t2TT)
  .value("t2TTA", LIEF::assembly::arm::OPCODE::t2TTA)
  .value("t2TTAT", LIEF::assembly::arm::OPCODE::t2TTAT)
  .value("t2TTT", LIEF::assembly::arm::OPCODE::t2TTT)
  .value("t2UADD16", LIEF::assembly::arm::OPCODE::t2UADD16)
  .value("t2UADD8", LIEF::assembly::arm::OPCODE::t2UADD8)
  .value("t2UASX", LIEF::assembly::arm::OPCODE::t2UASX)
  .value("t2UBFX", LIEF::assembly::arm::OPCODE::t2UBFX)
  .value("t2UDF", LIEF::assembly::arm::OPCODE::t2UDF)
  .value("t2UDIV", LIEF::assembly::arm::OPCODE::t2UDIV)
  .value("t2UHADD16", LIEF::assembly::arm::OPCODE::t2UHADD16)
  .value("t2UHADD8", LIEF::assembly::arm::OPCODE::t2UHADD8)
  .value("t2UHASX", LIEF::assembly::arm::OPCODE::t2UHASX)
  .value("t2UHSAX", LIEF::assembly::arm::OPCODE::t2UHSAX)
  .value("t2UHSUB16", LIEF::assembly::arm::OPCODE::t2UHSUB16)
  .value("t2UHSUB8", LIEF::assembly::arm::OPCODE::t2UHSUB8)
  .value("t2UMAAL", LIEF::assembly::arm::OPCODE::t2UMAAL)
  .value("t2UMLAL", LIEF::assembly::arm::OPCODE::t2UMLAL)
  .value("t2UMULL", LIEF::assembly::arm::OPCODE::t2UMULL)
  .value("t2UQADD16", LIEF::assembly::arm::OPCODE::t2UQADD16)
  .value("t2UQADD8", LIEF::assembly::arm::OPCODE::t2UQADD8)
  .value("t2UQASX", LIEF::assembly::arm::OPCODE::t2UQASX)
  .value("t2UQSAX", LIEF::assembly::arm::OPCODE::t2UQSAX)
  .value("t2UQSUB16", LIEF::assembly::arm::OPCODE::t2UQSUB16)
  .value("t2UQSUB8", LIEF::assembly::arm::OPCODE::t2UQSUB8)
  .value("t2USAD8", LIEF::assembly::arm::OPCODE::t2USAD8)
  .value("t2USADA8", LIEF::assembly::arm::OPCODE::t2USADA8)
  .value("t2USAT", LIEF::assembly::arm::OPCODE::t2USAT)
  .value("t2USAT16", LIEF::assembly::arm::OPCODE::t2USAT16)
  .value("t2USAX", LIEF::assembly::arm::OPCODE::t2USAX)
  .value("t2USUB16", LIEF::assembly::arm::OPCODE::t2USUB16)
  .value("t2USUB8", LIEF::assembly::arm::OPCODE::t2USUB8)
  .value("t2UXTAB", LIEF::assembly::arm::OPCODE::t2UXTAB)
  .value("t2UXTAB16", LIEF::assembly::arm::OPCODE::t2UXTAB16)
  .value("t2UXTAH", LIEF::assembly::arm::OPCODE::t2UXTAH)
  .value("t2UXTB", LIEF::assembly::arm::OPCODE::t2UXTB)
  .value("t2UXTB16", LIEF::assembly::arm::OPCODE::t2UXTB16)
  .value("t2UXTH", LIEF::assembly::arm::OPCODE::t2UXTH)
  .value("t2WLS", LIEF::assembly::arm::OPCODE::t2WLS)
  .value("tADC", LIEF::assembly::arm::OPCODE::tADC)
  .value("tADDhirr", LIEF::assembly::arm::OPCODE::tADDhirr)
  .value("tADDi3", LIEF::assembly::arm::OPCODE::tADDi3)
  .value("tADDi8", LIEF::assembly::arm::OPCODE::tADDi8)
  .value("tADDrSP", LIEF::assembly::arm::OPCODE::tADDrSP)
  .value("tADDrSPi", LIEF::assembly::arm::OPCODE::tADDrSPi)
  .value("tADDrr", LIEF::assembly::arm::OPCODE::tADDrr)
  .value("tADDspi", LIEF::assembly::arm::OPCODE::tADDspi)
  .value("tADDspr", LIEF::assembly::arm::OPCODE::tADDspr)
  .value("tADR", LIEF::assembly::arm::OPCODE::tADR)
  .value("tAND", LIEF::assembly::arm::OPCODE::tAND)
  .value("tASRri", LIEF::assembly::arm::OPCODE::tASRri)
  .value("tASRrr", LIEF::assembly::arm::OPCODE::tASRrr)
  .value("tB", LIEF::assembly::arm::OPCODE::tB)
  .value("tBIC", LIEF::assembly::arm::OPCODE::tBIC)
  .value("tBKPT", LIEF::assembly::arm::OPCODE::tBKPT)
  .value("tBL", LIEF::assembly::arm::OPCODE::tBL)
  .value("tBLXNSr", LIEF::assembly::arm::OPCODE::tBLXNSr)
  .value("tBLXi", LIEF::assembly::arm::OPCODE::tBLXi)
  .value("tBLXr", LIEF::assembly::arm::OPCODE::tBLXr)
  .value("tBX", LIEF::assembly::arm::OPCODE::tBX)
  .value("tBXNS", LIEF::assembly::arm::OPCODE::tBXNS)
  .value("tBcc", LIEF::assembly::arm::OPCODE::tBcc)
  .value("tCBNZ", LIEF::assembly::arm::OPCODE::tCBNZ)
  .value("tCBZ", LIEF::assembly::arm::OPCODE::tCBZ)
  .value("tCMNz", LIEF::assembly::arm::OPCODE::tCMNz)
  .value("tCMPhir", LIEF::assembly::arm::OPCODE::tCMPhir)
  .value("tCMPi8", LIEF::assembly::arm::OPCODE::tCMPi8)
  .value("tCMPr", LIEF::assembly::arm::OPCODE::tCMPr)
  .value("tCPS", LIEF::assembly::arm::OPCODE::tCPS)
  .value("tEOR", LIEF::assembly::arm::OPCODE::tEOR)
  .value("tHINT", LIEF::assembly::arm::OPCODE::tHINT)
  .value("tHLT", LIEF::assembly::arm::OPCODE::tHLT)
  .value("tInt_WIN_eh_sjlj_longjmp", LIEF::assembly::arm::OPCODE::tInt_WIN_eh_sjlj_longjmp)
  .value("tInt_eh_sjlj_longjmp", LIEF::assembly::arm::OPCODE::tInt_eh_sjlj_longjmp)
  .value("tInt_eh_sjlj_setjmp", LIEF::assembly::arm::OPCODE::tInt_eh_sjlj_setjmp)
  .value("tLDMIA", LIEF::assembly::arm::OPCODE::tLDMIA)
  .value("tLDRBi", LIEF::assembly::arm::OPCODE::tLDRBi)
  .value("tLDRBr", LIEF::assembly::arm::OPCODE::tLDRBr)
  .value("tLDRHi", LIEF::assembly::arm::OPCODE::tLDRHi)
  .value("tLDRHr", LIEF::assembly::arm::OPCODE::tLDRHr)
  .value("tLDRSB", LIEF::assembly::arm::OPCODE::tLDRSB)
  .value("tLDRSH", LIEF::assembly::arm::OPCODE::tLDRSH)
  .value("tLDRi", LIEF::assembly::arm::OPCODE::tLDRi)
  .value("tLDRpci", LIEF::assembly::arm::OPCODE::tLDRpci)
  .value("tLDRr", LIEF::assembly::arm::OPCODE::tLDRr)
  .value("tLDRspi", LIEF::assembly::arm::OPCODE::tLDRspi)
  .value("tLSLri", LIEF::assembly::arm::OPCODE::tLSLri)
  .value("tLSLrr", LIEF::assembly::arm::OPCODE::tLSLrr)
  .value("tLSRri", LIEF::assembly::arm::OPCODE::tLSRri)
  .value("tLSRrr", LIEF::assembly::arm::OPCODE::tLSRrr)
  .value("tMOVSr", LIEF::assembly::arm::OPCODE::tMOVSr)
  .value("tMOVi8", LIEF::assembly::arm::OPCODE::tMOVi8)
  .value("tMOVr", LIEF::assembly::arm::OPCODE::tMOVr)
  .value("tMUL", LIEF::assembly::arm::OPCODE::tMUL)
  .value("tMVN", LIEF::assembly::arm::OPCODE::tMVN)
  .value("tORR", LIEF::assembly::arm::OPCODE::tORR)
  .value("tPICADD", LIEF::assembly::arm::OPCODE::tPICADD)
  .value("tPOP", LIEF::assembly::arm::OPCODE::tPOP)
  .value("tPUSH", LIEF::assembly::arm::OPCODE::tPUSH)
  .value("tREV", LIEF::assembly::arm::OPCODE::tREV)
  .value("tREV16", LIEF::assembly::arm::OPCODE::tREV16)
  .value("tREVSH", LIEF::assembly::arm::OPCODE::tREVSH)
  .value("tROR", LIEF::assembly::arm::OPCODE::tROR)
  .value("tRSB", LIEF::assembly::arm::OPCODE::tRSB)
  .value("tSBC", LIEF::assembly::arm::OPCODE::tSBC)
  .value("tSETEND", LIEF::assembly::arm::OPCODE::tSETEND)
  .value("tSTMIA_UPD", LIEF::assembly::arm::OPCODE::tSTMIA_UPD)
  .value("tSTRBi", LIEF::assembly::arm::OPCODE::tSTRBi)
  .value("tSTRBr", LIEF::assembly::arm::OPCODE::tSTRBr)
  .value("tSTRHi", LIEF::assembly::arm::OPCODE::tSTRHi)
  .value("tSTRHr", LIEF::assembly::arm::OPCODE::tSTRHr)
  .value("tSTRi", LIEF::assembly::arm::OPCODE::tSTRi)
  .value("tSTRr", LIEF::assembly::arm::OPCODE::tSTRr)
  .value("tSTRspi", LIEF::assembly::arm::OPCODE::tSTRspi)
  .value("tSUBi3", LIEF::assembly::arm::OPCODE::tSUBi3)
  .value("tSUBi8", LIEF::assembly::arm::OPCODE::tSUBi8)
  .value("tSUBrr", LIEF::assembly::arm::OPCODE::tSUBrr)
  .value("tSUBspi", LIEF::assembly::arm::OPCODE::tSUBspi)
  .value("tSVC", LIEF::assembly::arm::OPCODE::tSVC)
  .value("tSXTB", LIEF::assembly::arm::OPCODE::tSXTB)
  .value("tSXTH", LIEF::assembly::arm::OPCODE::tSXTH)
  .value("tTRAP", LIEF::assembly::arm::OPCODE::tTRAP)
  .value("tTST", LIEF::assembly::arm::OPCODE::tTST)
  .value("tUDF", LIEF::assembly::arm::OPCODE::tUDF);
  opcodes.value("tUXTB", LIEF::assembly::arm::OPCODE::tUXTB)
  .value("tUXTH", LIEF::assembly::arm::OPCODE::tUXTH)
  .value("t__brkdiv0", LIEF::assembly::arm::OPCODE::t__brkdiv0)
  .value("INSTRUCTION_LIST_END", LIEF::assembly::arm::OPCODE::INSTRUCTION_LIST_END)
  ;
}
}
