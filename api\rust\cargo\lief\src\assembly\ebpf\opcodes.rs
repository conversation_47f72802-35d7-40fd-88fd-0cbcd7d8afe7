#[allow(non_camel_case_types)]
#[derive(<PERSON>bug, <PERSON>lone, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Eq, <PERSON>ialOrd, Ord, Hash)]
pub enum Opcode {
  PHI,
  INLINEASM,
  INLINEASM_BR,
  CFI_INSTRUCTION,
  EH_LABEL,
  GC_LABEL,
  ANNOTATION_LABEL,
  K<PERSON><PERSON>,
  EXTRA<PERSON>_SUBREG,
  INSERT_SUBREG,
  IMPL<PERSON>IT_DEF,
  INIT_UNDEF,
  SUBREG_TO_REG,
  COPY_TO_REGCLASS,
  DBG_VALUE,
  DBG_VALUE_LIST,
  DBG_INSTR_REF,
  DBG_PHI,
  DBG_LABEL,
  REG_SEQUENCE,
  COPY,
  BUN<PERSON>LE,
  LIF<PERSON>IME_START,
  LIF<PERSON>IME_END,
  PSEUDO_PROBE,
  ARITH_FENCE,
  STACKMAP,
  FENTRY_CALL,
  PATCHPOINT,
  LOAD_STACK_GUARD,
  PREALLOCATED_SETUP,
  PREALLOCATED_ARG,
  STAT<PERSON>OINT,
  LOCAL_ESCAPE,
  FAULTING_OP,
  <PERSON>TCHABLE_OP,
  PATCHABLE_FUNCTION_ENTER,
  PATCHABLE_RET,
  PATCHABLE_FUNCTION_EXIT,
  PATCHABLE_TAIL_CALL,
  PATCHABLE_EVENT_CALL,
  PATCHABLE_TYPED_EVENT_CALL,
  ICALL_BRANCH_FUNNEL,
  FAKE_USE,
  MEMBARRIER,
  JUMP_TABLE_DEBUG_INFO,
  CONVERGENCECTRL_ENTRY,
  CONVERGENCECTRL_ANCHOR,
  CONVERGENCECTRL_LOOP,
  CONVERGENCECTRL_GLUE,
  G_ASSERT_SEXT,
  G_ASSERT_ZEXT,
  G_ASSERT_ALIGN,
  G_ADD,
  G_SUB,
  G_MUL,
  G_SDIV,
  G_UDIV,
  G_SREM,
  G_UREM,
  G_SDIVREM,
  G_UDIVREM,
  G_AND,
  G_OR,
  G_XOR,
  G_ABDS,
  G_ABDU,
  G_IMPLICIT_DEF,
  G_PHI,
  G_FRAME_INDEX,
  G_GLOBAL_VALUE,
  G_PTRAUTH_GLOBAL_VALUE,
  G_CONSTANT_POOL,
  G_EXTRACT,
  G_UNMERGE_VALUES,
  G_INSERT,
  G_MERGE_VALUES,
  G_BUILD_VECTOR,
  G_BUILD_VECTOR_TRUNC,
  G_CONCAT_VECTORS,
  G_PTRTOINT,
  G_INTTOPTR,
  G_BITCAST,
  G_FREEZE,
  G_CONSTANT_FOLD_BARRIER,
  G_INTRINSIC_FPTRUNC_ROUND,
  G_INTRINSIC_TRUNC,
  G_INTRINSIC_ROUND,
  G_INTRINSIC_LRINT,
  G_INTRINSIC_LLRINT,
  G_INTRINSIC_ROUNDEVEN,
  G_READCYCLECOUNTER,
  G_READSTEADYCOUNTER,
  G_LOAD,
  G_SEXTLOAD,
  G_ZEXTLOAD,
  G_INDEXED_LOAD,
  G_INDEXED_SEXTLOAD,
  G_INDEXED_ZEXTLOAD,
  G_STORE,
  G_INDEXED_STORE,
  G_ATOMIC_CMPXCHG_WITH_SUCCESS,
  G_ATOMIC_CMPXCHG,
  G_ATOMICRMW_XCHG,
  G_ATOMICRMW_ADD,
  G_ATOMICRMW_SUB,
  G_ATOMICRMW_AND,
  G_ATOMICRMW_NAND,
  G_ATOMICRMW_OR,
  G_ATOMICRMW_XOR,
  G_ATOMICRMW_MAX,
  G_ATOMICRMW_MIN,
  G_ATOMICRMW_UMAX,
  G_ATOMICRMW_UMIN,
  G_ATOMICRMW_FADD,
  G_ATOMICRMW_FSUB,
  G_ATOMICRMW_FMAX,
  G_ATOMICRMW_FMIN,
  G_ATOMICRMW_UINC_WRAP,
  G_ATOMICRMW_UDEC_WRAP,
  G_ATOMICRMW_USUB_COND,
  G_ATOMICRMW_USUB_SAT,
  G_FENCE,
  G_PREFETCH,
  G_BRCOND,
  G_BRINDIRECT,
  G_INVOKE_REGION_START,
  G_INTRINSIC,
  G_INTRINSIC_W_SIDE_EFFECTS,
  G_INTRINSIC_CONVERGENT,
  G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS,
  G_ANYEXT,
  G_TRUNC,
  G_CONSTANT,
  G_FCONSTANT,
  G_VASTART,
  G_VAARG,
  G_SEXT,
  G_SEXT_INREG,
  G_ZEXT,
  G_SHL,
  G_LSHR,
  G_ASHR,
  G_FSHL,
  G_FSHR,
  G_ROTR,
  G_ROTL,
  G_ICMP,
  G_FCMP,
  G_SCMP,
  G_UCMP,
  G_SELECT,
  G_UADDO,
  G_UADDE,
  G_USUBO,
  G_USUBE,
  G_SADDO,
  G_SADDE,
  G_SSUBO,
  G_SSUBE,
  G_UMULO,
  G_SMULO,
  G_UMULH,
  G_SMULH,
  G_UADDSAT,
  G_SADDSAT,
  G_USUBSAT,
  G_SSUBSAT,
  G_USHLSAT,
  G_SSHLSAT,
  G_SMULFIX,
  G_UMULFIX,
  G_SMULFIXSAT,
  G_UMULFIXSAT,
  G_SDIVFIX,
  G_UDIVFIX,
  G_SDIVFIXSAT,
  G_UDIVFIXSAT,
  G_FADD,
  G_FSUB,
  G_FMUL,
  G_FMA,
  G_FMAD,
  G_FDIV,
  G_FREM,
  G_FPOW,
  G_FPOWI,
  G_FEXP,
  G_FEXP2,
  G_FEXP10,
  G_FLOG,
  G_FLOG2,
  G_FLOG10,
  G_FLDEXP,
  G_FFREXP,
  G_FNEG,
  G_FPEXT,
  G_FPTRUNC,
  G_FPTOSI,
  G_FPTOUI,
  G_SITOFP,
  G_UITOFP,
  G_FPTOSI_SAT,
  G_FPTOUI_SAT,
  G_FABS,
  G_FCOPYSIGN,
  G_IS_FPCLASS,
  G_FCANONICALIZE,
  G_FMINNUM,
  G_FMAXNUM,
  G_FMINNUM_IEEE,
  G_FMAXNUM_IEEE,
  G_FMINIMUM,
  G_FMAXIMUM,
  G_GET_FPENV,
  G_SET_FPENV,
  G_RESET_FPENV,
  G_GET_FPMODE,
  G_SET_FPMODE,
  G_RESET_FPMODE,
  G_PTR_ADD,
  G_PTRMASK,
  G_SMIN,
  G_SMAX,
  G_UMIN,
  G_UMAX,
  G_ABS,
  G_LROUND,
  G_LLROUND,
  G_BR,
  G_BRJT,
  G_VSCALE,
  G_INSERT_SUBVECTOR,
  G_EXTRACT_SUBVECTOR,
  G_INSERT_VECTOR_ELT,
  G_EXTRACT_VECTOR_ELT,
  G_SHUFFLE_VECTOR,
  G_SPLAT_VECTOR,
  G_STEP_VECTOR,
  G_VECTOR_COMPRESS,
  G_CTTZ,
  G_CTTZ_ZERO_UNDEF,
  G_CTLZ,
  G_CTLZ_ZERO_UNDEF,
  G_CTPOP,
  G_BSWAP,
  G_BITREVERSE,
  G_FCEIL,
  G_FCOS,
  G_FSIN,
  G_FSINCOS,
  G_FTAN,
  G_FACOS,
  G_FASIN,
  G_FATAN,
  G_FATAN2,
  G_FCOSH,
  G_FSINH,
  G_FTANH,
  G_FSQRT,
  G_FFLOOR,
  G_FRINT,
  G_FNEARBYINT,
  G_ADDRSPACE_CAST,
  G_BLOCK_ADDR,
  G_JUMP_TABLE,
  G_DYN_STACKALLOC,
  G_STACKSAVE,
  G_STACKRESTORE,
  G_STRICT_FADD,
  G_STRICT_FSUB,
  G_STRICT_FMUL,
  G_STRICT_FDIV,
  G_STRICT_FREM,
  G_STRICT_FMA,
  G_STRICT_FSQRT,
  G_STRICT_FLDEXP,
  G_READ_REGISTER,
  G_WRITE_REGISTER,
  G_MEMCPY,
  G_MEMCPY_INLINE,
  G_MEMMOVE,
  G_MEMSET,
  G_BZERO,
  G_TRAP,
  G_DEBUGTRAP,
  G_UBSANTRAP,
  G_VECREDUCE_SEQ_FADD,
  G_VECREDUCE_SEQ_FMUL,
  G_VECREDUCE_FADD,
  G_VECREDUCE_FMUL,
  G_VECREDUCE_FMAX,
  G_VECREDUCE_FMIN,
  G_VECREDUCE_FMAXIMUM,
  G_VECREDUCE_FMINIMUM,
  G_VECREDUCE_ADD,
  G_VECREDUCE_MUL,
  G_VECREDUCE_AND,
  G_VECREDUCE_OR,
  G_VECREDUCE_XOR,
  G_VECREDUCE_SMAX,
  G_VECREDUCE_SMIN,
  G_VECREDUCE_UMAX,
  G_VECREDUCE_UMIN,
  G_SBFX,
  G_UBFX,
  ADJCALLSTACKDOWN,
  ADJCALLSTACKUP,
  FI_ri,
  MEMCPY,
  Select,
  Select_32,
  Select_32_64,
  Select_64_32,
  Select_Ri,
  Select_Ri_32,
  Select_Ri_32_64,
  Select_Ri_64_32,
  ADDR_SPACE_CAST,
  ADD_ri,
  ADD_ri_32,
  ADD_rr,
  ADD_rr_32,
  AND_ri,
  AND_ri_32,
  AND_rr,
  AND_rr_32,
  BE16,
  BE32,
  BE64,
  BSWAP16,
  BSWAP32,
  BSWAP64,
  CMPXCHGD,
  CMPXCHGW32,
  CORE_LD32,
  CORE_LD64,
  CORE_SHIFT,
  CORE_ST,
  DIV_ri,
  DIV_ri_32,
  DIV_rr,
  DIV_rr_32,
  JAL,
  JALX,
  JCOND,
  JEQ_ri,
  JEQ_ri_32,
  JEQ_rr,
  JEQ_rr_32,
  JMP,
  JMPL,
  JNE_ri,
  JNE_ri_32,
  JNE_rr,
  JNE_rr_32,
  JSET_ri,
  JSET_ri_32,
  JSET_rr,
  JSET_rr_32,
  JSGE_ri,
  JSGE_ri_32,
  JSGE_rr,
  JSGE_rr_32,
  JSGT_ri,
  JSGT_ri_32,
  JSGT_rr,
  JSGT_rr_32,
  JSLE_ri,
  JSLE_ri_32,
  JSLE_rr,
  JSLE_rr_32,
  JSLT_ri,
  JSLT_ri_32,
  JSLT_rr,
  JSLT_rr_32,
  JUGE_ri,
  JUGE_ri_32,
  JUGE_rr,
  JUGE_rr_32,
  JUGT_ri,
  JUGT_ri_32,
  JUGT_rr,
  JUGT_rr_32,
  JULE_ri,
  JULE_ri_32,
  JULE_rr,
  JULE_rr_32,
  JULT_ri,
  JULT_ri_32,
  JULT_rr,
  JULT_rr_32,
  LDB,
  LDB32,
  LDBSX,
  LDD,
  LDH,
  LDH32,
  LDHSX,
  LDW,
  LDW32,
  LDWSX,
  LD_ABS_B,
  LD_ABS_H,
  LD_ABS_W,
  LD_IND_B,
  LD_IND_H,
  LD_IND_W,
  LD_imm64,
  LD_pseudo,
  LE16,
  LE32,
  LE64,
  MOD_ri,
  MOD_ri_32,
  MOD_rr,
  MOD_rr_32,
  MOVSX_rr_16,
  MOVSX_rr_32,
  MOVSX_rr_32_16,
  MOVSX_rr_32_8,
  MOVSX_rr_8,
  MOV_32_64,
  MOV_ri,
  MOV_ri_32,
  MOV_rr,
  MOV_rr_32,
  MUL_ri,
  MUL_ri_32,
  MUL_rr,
  MUL_rr_32,
  NEG_32,
  NEG_64,
  NOP,
  OR_ri,
  OR_ri_32,
  OR_rr,
  OR_rr_32,
  RET,
  SDIV_ri,
  SDIV_ri_32,
  SDIV_rr,
  SDIV_rr_32,
  SLL_ri,
  SLL_ri_32,
  SLL_rr,
  SLL_rr_32,
  SMOD_ri,
  SMOD_ri_32,
  SMOD_rr,
  SMOD_rr_32,
  SRA_ri,
  SRA_ri_32,
  SRA_rr,
  SRA_rr_32,
  SRL_ri,
  SRL_ri_32,
  SRL_rr,
  SRL_rr_32,
  STB,
  STB32,
  STB_imm,
  STD,
  STD_imm,
  STH,
  STH32,
  STH_imm,
  STW,
  STW32,
  STW_imm,
  SUB_ri,
  SUB_ri_32,
  SUB_rr,
  SUB_rr_32,
  XADDD,
  XADDW,
  XADDW32,
  XANDD,
  XANDW32,
  XCHGD,
  XCHGW32,
  XFADDD,
  XFADDW32,
  XFANDD,
  XFANDW32,
  XFORD,
  XFORW32,
  XFXORD,
  XFXORW32,
  XORD,
  XORW32,
  XOR_ri,
  XOR_ri_32,
  XOR_rr,
  XOR_rr_32,
  XXORD,
  XXORW32,
  INSTRUCTION_LIST_END,
  UNKNOWN(u64),
}

impl From<u64> for Opcode {
    fn from(value: u64) -> Self {
        match value {
          0 => Opcode::PHI,
          1 => Opcode::INLINEASM,
          2 => Opcode::INLINEASM_BR,
          3 => Opcode::CFI_INSTRUCTION,
          4 => Opcode::EH_LABEL,
          5 => Opcode::GC_LABEL,
          6 => Opcode::ANNOTATION_LABEL,
          7 => Opcode::KILL,
          8 => Opcode::EXTRACT_SUBREG,
          9 => Opcode::INSERT_SUBREG,
          10 => Opcode::IMPLICIT_DEF,
          11 => Opcode::INIT_UNDEF,
          12 => Opcode::SUBREG_TO_REG,
          13 => Opcode::COPY_TO_REGCLASS,
          14 => Opcode::DBG_VALUE,
          15 => Opcode::DBG_VALUE_LIST,
          16 => Opcode::DBG_INSTR_REF,
          17 => Opcode::DBG_PHI,
          18 => Opcode::DBG_LABEL,
          19 => Opcode::REG_SEQUENCE,
          20 => Opcode::COPY,
          21 => Opcode::BUNDLE,
          22 => Opcode::LIFETIME_START,
          23 => Opcode::LIFETIME_END,
          24 => Opcode::PSEUDO_PROBE,
          25 => Opcode::ARITH_FENCE,
          26 => Opcode::STACKMAP,
          27 => Opcode::FENTRY_CALL,
          28 => Opcode::PATCHPOINT,
          29 => Opcode::LOAD_STACK_GUARD,
          30 => Opcode::PREALLOCATED_SETUP,
          31 => Opcode::PREALLOCATED_ARG,
          32 => Opcode::STATEPOINT,
          33 => Opcode::LOCAL_ESCAPE,
          34 => Opcode::FAULTING_OP,
          35 => Opcode::PATCHABLE_OP,
          36 => Opcode::PATCHABLE_FUNCTION_ENTER,
          37 => Opcode::PATCHABLE_RET,
          38 => Opcode::PATCHABLE_FUNCTION_EXIT,
          39 => Opcode::PATCHABLE_TAIL_CALL,
          40 => Opcode::PATCHABLE_EVENT_CALL,
          41 => Opcode::PATCHABLE_TYPED_EVENT_CALL,
          42 => Opcode::ICALL_BRANCH_FUNNEL,
          43 => Opcode::FAKE_USE,
          44 => Opcode::MEMBARRIER,
          45 => Opcode::JUMP_TABLE_DEBUG_INFO,
          46 => Opcode::CONVERGENCECTRL_ENTRY,
          47 => Opcode::CONVERGENCECTRL_ANCHOR,
          48 => Opcode::CONVERGENCECTRL_LOOP,
          49 => Opcode::CONVERGENCECTRL_GLUE,
          50 => Opcode::G_ASSERT_SEXT,
          51 => Opcode::G_ASSERT_ZEXT,
          52 => Opcode::G_ASSERT_ALIGN,
          53 => Opcode::G_ADD,
          54 => Opcode::G_SUB,
          55 => Opcode::G_MUL,
          56 => Opcode::G_SDIV,
          57 => Opcode::G_UDIV,
          58 => Opcode::G_SREM,
          59 => Opcode::G_UREM,
          60 => Opcode::G_SDIVREM,
          61 => Opcode::G_UDIVREM,
          62 => Opcode::G_AND,
          63 => Opcode::G_OR,
          64 => Opcode::G_XOR,
          65 => Opcode::G_ABDS,
          66 => Opcode::G_ABDU,
          67 => Opcode::G_IMPLICIT_DEF,
          68 => Opcode::G_PHI,
          69 => Opcode::G_FRAME_INDEX,
          70 => Opcode::G_GLOBAL_VALUE,
          71 => Opcode::G_PTRAUTH_GLOBAL_VALUE,
          72 => Opcode::G_CONSTANT_POOL,
          73 => Opcode::G_EXTRACT,
          74 => Opcode::G_UNMERGE_VALUES,
          75 => Opcode::G_INSERT,
          76 => Opcode::G_MERGE_VALUES,
          77 => Opcode::G_BUILD_VECTOR,
          78 => Opcode::G_BUILD_VECTOR_TRUNC,
          79 => Opcode::G_CONCAT_VECTORS,
          80 => Opcode::G_PTRTOINT,
          81 => Opcode::G_INTTOPTR,
          82 => Opcode::G_BITCAST,
          83 => Opcode::G_FREEZE,
          84 => Opcode::G_CONSTANT_FOLD_BARRIER,
          85 => Opcode::G_INTRINSIC_FPTRUNC_ROUND,
          86 => Opcode::G_INTRINSIC_TRUNC,
          87 => Opcode::G_INTRINSIC_ROUND,
          88 => Opcode::G_INTRINSIC_LRINT,
          89 => Opcode::G_INTRINSIC_LLRINT,
          90 => Opcode::G_INTRINSIC_ROUNDEVEN,
          91 => Opcode::G_READCYCLECOUNTER,
          92 => Opcode::G_READSTEADYCOUNTER,
          93 => Opcode::G_LOAD,
          94 => Opcode::G_SEXTLOAD,
          95 => Opcode::G_ZEXTLOAD,
          96 => Opcode::G_INDEXED_LOAD,
          97 => Opcode::G_INDEXED_SEXTLOAD,
          98 => Opcode::G_INDEXED_ZEXTLOAD,
          99 => Opcode::G_STORE,
          100 => Opcode::G_INDEXED_STORE,
          101 => Opcode::G_ATOMIC_CMPXCHG_WITH_SUCCESS,
          102 => Opcode::G_ATOMIC_CMPXCHG,
          103 => Opcode::G_ATOMICRMW_XCHG,
          104 => Opcode::G_ATOMICRMW_ADD,
          105 => Opcode::G_ATOMICRMW_SUB,
          106 => Opcode::G_ATOMICRMW_AND,
          107 => Opcode::G_ATOMICRMW_NAND,
          108 => Opcode::G_ATOMICRMW_OR,
          109 => Opcode::G_ATOMICRMW_XOR,
          110 => Opcode::G_ATOMICRMW_MAX,
          111 => Opcode::G_ATOMICRMW_MIN,
          112 => Opcode::G_ATOMICRMW_UMAX,
          113 => Opcode::G_ATOMICRMW_UMIN,
          114 => Opcode::G_ATOMICRMW_FADD,
          115 => Opcode::G_ATOMICRMW_FSUB,
          116 => Opcode::G_ATOMICRMW_FMAX,
          117 => Opcode::G_ATOMICRMW_FMIN,
          118 => Opcode::G_ATOMICRMW_UINC_WRAP,
          119 => Opcode::G_ATOMICRMW_UDEC_WRAP,
          120 => Opcode::G_ATOMICRMW_USUB_COND,
          121 => Opcode::G_ATOMICRMW_USUB_SAT,
          122 => Opcode::G_FENCE,
          123 => Opcode::G_PREFETCH,
          124 => Opcode::G_BRCOND,
          125 => Opcode::G_BRINDIRECT,
          126 => Opcode::G_INVOKE_REGION_START,
          127 => Opcode::G_INTRINSIC,
          128 => Opcode::G_INTRINSIC_W_SIDE_EFFECTS,
          129 => Opcode::G_INTRINSIC_CONVERGENT,
          130 => Opcode::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS,
          131 => Opcode::G_ANYEXT,
          132 => Opcode::G_TRUNC,
          133 => Opcode::G_CONSTANT,
          134 => Opcode::G_FCONSTANT,
          135 => Opcode::G_VASTART,
          136 => Opcode::G_VAARG,
          137 => Opcode::G_SEXT,
          138 => Opcode::G_SEXT_INREG,
          139 => Opcode::G_ZEXT,
          140 => Opcode::G_SHL,
          141 => Opcode::G_LSHR,
          142 => Opcode::G_ASHR,
          143 => Opcode::G_FSHL,
          144 => Opcode::G_FSHR,
          145 => Opcode::G_ROTR,
          146 => Opcode::G_ROTL,
          147 => Opcode::G_ICMP,
          148 => Opcode::G_FCMP,
          149 => Opcode::G_SCMP,
          150 => Opcode::G_UCMP,
          151 => Opcode::G_SELECT,
          152 => Opcode::G_UADDO,
          153 => Opcode::G_UADDE,
          154 => Opcode::G_USUBO,
          155 => Opcode::G_USUBE,
          156 => Opcode::G_SADDO,
          157 => Opcode::G_SADDE,
          158 => Opcode::G_SSUBO,
          159 => Opcode::G_SSUBE,
          160 => Opcode::G_UMULO,
          161 => Opcode::G_SMULO,
          162 => Opcode::G_UMULH,
          163 => Opcode::G_SMULH,
          164 => Opcode::G_UADDSAT,
          165 => Opcode::G_SADDSAT,
          166 => Opcode::G_USUBSAT,
          167 => Opcode::G_SSUBSAT,
          168 => Opcode::G_USHLSAT,
          169 => Opcode::G_SSHLSAT,
          170 => Opcode::G_SMULFIX,
          171 => Opcode::G_UMULFIX,
          172 => Opcode::G_SMULFIXSAT,
          173 => Opcode::G_UMULFIXSAT,
          174 => Opcode::G_SDIVFIX,
          175 => Opcode::G_UDIVFIX,
          176 => Opcode::G_SDIVFIXSAT,
          177 => Opcode::G_UDIVFIXSAT,
          178 => Opcode::G_FADD,
          179 => Opcode::G_FSUB,
          180 => Opcode::G_FMUL,
          181 => Opcode::G_FMA,
          182 => Opcode::G_FMAD,
          183 => Opcode::G_FDIV,
          184 => Opcode::G_FREM,
          185 => Opcode::G_FPOW,
          186 => Opcode::G_FPOWI,
          187 => Opcode::G_FEXP,
          188 => Opcode::G_FEXP2,
          189 => Opcode::G_FEXP10,
          190 => Opcode::G_FLOG,
          191 => Opcode::G_FLOG2,
          192 => Opcode::G_FLOG10,
          193 => Opcode::G_FLDEXP,
          194 => Opcode::G_FFREXP,
          195 => Opcode::G_FNEG,
          196 => Opcode::G_FPEXT,
          197 => Opcode::G_FPTRUNC,
          198 => Opcode::G_FPTOSI,
          199 => Opcode::G_FPTOUI,
          200 => Opcode::G_SITOFP,
          201 => Opcode::G_UITOFP,
          202 => Opcode::G_FPTOSI_SAT,
          203 => Opcode::G_FPTOUI_SAT,
          204 => Opcode::G_FABS,
          205 => Opcode::G_FCOPYSIGN,
          206 => Opcode::G_IS_FPCLASS,
          207 => Opcode::G_FCANONICALIZE,
          208 => Opcode::G_FMINNUM,
          209 => Opcode::G_FMAXNUM,
          210 => Opcode::G_FMINNUM_IEEE,
          211 => Opcode::G_FMAXNUM_IEEE,
          212 => Opcode::G_FMINIMUM,
          213 => Opcode::G_FMAXIMUM,
          214 => Opcode::G_GET_FPENV,
          215 => Opcode::G_SET_FPENV,
          216 => Opcode::G_RESET_FPENV,
          217 => Opcode::G_GET_FPMODE,
          218 => Opcode::G_SET_FPMODE,
          219 => Opcode::G_RESET_FPMODE,
          220 => Opcode::G_PTR_ADD,
          221 => Opcode::G_PTRMASK,
          222 => Opcode::G_SMIN,
          223 => Opcode::G_SMAX,
          224 => Opcode::G_UMIN,
          225 => Opcode::G_UMAX,
          226 => Opcode::G_ABS,
          227 => Opcode::G_LROUND,
          228 => Opcode::G_LLROUND,
          229 => Opcode::G_BR,
          230 => Opcode::G_BRJT,
          231 => Opcode::G_VSCALE,
          232 => Opcode::G_INSERT_SUBVECTOR,
          233 => Opcode::G_EXTRACT_SUBVECTOR,
          234 => Opcode::G_INSERT_VECTOR_ELT,
          235 => Opcode::G_EXTRACT_VECTOR_ELT,
          236 => Opcode::G_SHUFFLE_VECTOR,
          237 => Opcode::G_SPLAT_VECTOR,
          238 => Opcode::G_STEP_VECTOR,
          239 => Opcode::G_VECTOR_COMPRESS,
          240 => Opcode::G_CTTZ,
          241 => Opcode::G_CTTZ_ZERO_UNDEF,
          242 => Opcode::G_CTLZ,
          243 => Opcode::G_CTLZ_ZERO_UNDEF,
          244 => Opcode::G_CTPOP,
          245 => Opcode::G_BSWAP,
          246 => Opcode::G_BITREVERSE,
          247 => Opcode::G_FCEIL,
          248 => Opcode::G_FCOS,
          249 => Opcode::G_FSIN,
          250 => Opcode::G_FSINCOS,
          251 => Opcode::G_FTAN,
          252 => Opcode::G_FACOS,
          253 => Opcode::G_FASIN,
          254 => Opcode::G_FATAN,
          255 => Opcode::G_FATAN2,
          256 => Opcode::G_FCOSH,
          257 => Opcode::G_FSINH,
          258 => Opcode::G_FTANH,
          259 => Opcode::G_FSQRT,
          260 => Opcode::G_FFLOOR,
          261 => Opcode::G_FRINT,
          262 => Opcode::G_FNEARBYINT,
          263 => Opcode::G_ADDRSPACE_CAST,
          264 => Opcode::G_BLOCK_ADDR,
          265 => Opcode::G_JUMP_TABLE,
          266 => Opcode::G_DYN_STACKALLOC,
          267 => Opcode::G_STACKSAVE,
          268 => Opcode::G_STACKRESTORE,
          269 => Opcode::G_STRICT_FADD,
          270 => Opcode::G_STRICT_FSUB,
          271 => Opcode::G_STRICT_FMUL,
          272 => Opcode::G_STRICT_FDIV,
          273 => Opcode::G_STRICT_FREM,
          274 => Opcode::G_STRICT_FMA,
          275 => Opcode::G_STRICT_FSQRT,
          276 => Opcode::G_STRICT_FLDEXP,
          277 => Opcode::G_READ_REGISTER,
          278 => Opcode::G_WRITE_REGISTER,
          279 => Opcode::G_MEMCPY,
          280 => Opcode::G_MEMCPY_INLINE,
          281 => Opcode::G_MEMMOVE,
          282 => Opcode::G_MEMSET,
          283 => Opcode::G_BZERO,
          284 => Opcode::G_TRAP,
          285 => Opcode::G_DEBUGTRAP,
          286 => Opcode::G_UBSANTRAP,
          287 => Opcode::G_VECREDUCE_SEQ_FADD,
          288 => Opcode::G_VECREDUCE_SEQ_FMUL,
          289 => Opcode::G_VECREDUCE_FADD,
          290 => Opcode::G_VECREDUCE_FMUL,
          291 => Opcode::G_VECREDUCE_FMAX,
          292 => Opcode::G_VECREDUCE_FMIN,
          293 => Opcode::G_VECREDUCE_FMAXIMUM,
          294 => Opcode::G_VECREDUCE_FMINIMUM,
          295 => Opcode::G_VECREDUCE_ADD,
          296 => Opcode::G_VECREDUCE_MUL,
          297 => Opcode::G_VECREDUCE_AND,
          298 => Opcode::G_VECREDUCE_OR,
          299 => Opcode::G_VECREDUCE_XOR,
          300 => Opcode::G_VECREDUCE_SMAX,
          301 => Opcode::G_VECREDUCE_SMIN,
          302 => Opcode::G_VECREDUCE_UMAX,
          303 => Opcode::G_VECREDUCE_UMIN,
          304 => Opcode::G_SBFX,
          305 => Opcode::G_UBFX,
          306 => Opcode::ADJCALLSTACKDOWN,
          307 => Opcode::ADJCALLSTACKUP,
          308 => Opcode::FI_ri,
          309 => Opcode::MEMCPY,
          310 => Opcode::Select,
          311 => Opcode::Select_32,
          312 => Opcode::Select_32_64,
          313 => Opcode::Select_64_32,
          314 => Opcode::Select_Ri,
          315 => Opcode::Select_Ri_32,
          316 => Opcode::Select_Ri_32_64,
          317 => Opcode::Select_Ri_64_32,
          318 => Opcode::ADDR_SPACE_CAST,
          319 => Opcode::ADD_ri,
          320 => Opcode::ADD_ri_32,
          321 => Opcode::ADD_rr,
          322 => Opcode::ADD_rr_32,
          323 => Opcode::AND_ri,
          324 => Opcode::AND_ri_32,
          325 => Opcode::AND_rr,
          326 => Opcode::AND_rr_32,
          327 => Opcode::BE16,
          328 => Opcode::BE32,
          329 => Opcode::BE64,
          330 => Opcode::BSWAP16,
          331 => Opcode::BSWAP32,
          332 => Opcode::BSWAP64,
          333 => Opcode::CMPXCHGD,
          334 => Opcode::CMPXCHGW32,
          335 => Opcode::CORE_LD32,
          336 => Opcode::CORE_LD64,
          337 => Opcode::CORE_SHIFT,
          338 => Opcode::CORE_ST,
          339 => Opcode::DIV_ri,
          340 => Opcode::DIV_ri_32,
          341 => Opcode::DIV_rr,
          342 => Opcode::DIV_rr_32,
          343 => Opcode::JAL,
          344 => Opcode::JALX,
          345 => Opcode::JCOND,
          346 => Opcode::JEQ_ri,
          347 => Opcode::JEQ_ri_32,
          348 => Opcode::JEQ_rr,
          349 => Opcode::JEQ_rr_32,
          350 => Opcode::JMP,
          351 => Opcode::JMPL,
          352 => Opcode::JNE_ri,
          353 => Opcode::JNE_ri_32,
          354 => Opcode::JNE_rr,
          355 => Opcode::JNE_rr_32,
          356 => Opcode::JSET_ri,
          357 => Opcode::JSET_ri_32,
          358 => Opcode::JSET_rr,
          359 => Opcode::JSET_rr_32,
          360 => Opcode::JSGE_ri,
          361 => Opcode::JSGE_ri_32,
          362 => Opcode::JSGE_rr,
          363 => Opcode::JSGE_rr_32,
          364 => Opcode::JSGT_ri,
          365 => Opcode::JSGT_ri_32,
          366 => Opcode::JSGT_rr,
          367 => Opcode::JSGT_rr_32,
          368 => Opcode::JSLE_ri,
          369 => Opcode::JSLE_ri_32,
          370 => Opcode::JSLE_rr,
          371 => Opcode::JSLE_rr_32,
          372 => Opcode::JSLT_ri,
          373 => Opcode::JSLT_ri_32,
          374 => Opcode::JSLT_rr,
          375 => Opcode::JSLT_rr_32,
          376 => Opcode::JUGE_ri,
          377 => Opcode::JUGE_ri_32,
          378 => Opcode::JUGE_rr,
          379 => Opcode::JUGE_rr_32,
          380 => Opcode::JUGT_ri,
          381 => Opcode::JUGT_ri_32,
          382 => Opcode::JUGT_rr,
          383 => Opcode::JUGT_rr_32,
          384 => Opcode::JULE_ri,
          385 => Opcode::JULE_ri_32,
          386 => Opcode::JULE_rr,
          387 => Opcode::JULE_rr_32,
          388 => Opcode::JULT_ri,
          389 => Opcode::JULT_ri_32,
          390 => Opcode::JULT_rr,
          391 => Opcode::JULT_rr_32,
          392 => Opcode::LDB,
          393 => Opcode::LDB32,
          394 => Opcode::LDBSX,
          395 => Opcode::LDD,
          396 => Opcode::LDH,
          397 => Opcode::LDH32,
          398 => Opcode::LDHSX,
          399 => Opcode::LDW,
          400 => Opcode::LDW32,
          401 => Opcode::LDWSX,
          402 => Opcode::LD_ABS_B,
          403 => Opcode::LD_ABS_H,
          404 => Opcode::LD_ABS_W,
          405 => Opcode::LD_IND_B,
          406 => Opcode::LD_IND_H,
          407 => Opcode::LD_IND_W,
          408 => Opcode::LD_imm64,
          409 => Opcode::LD_pseudo,
          410 => Opcode::LE16,
          411 => Opcode::LE32,
          412 => Opcode::LE64,
          413 => Opcode::MOD_ri,
          414 => Opcode::MOD_ri_32,
          415 => Opcode::MOD_rr,
          416 => Opcode::MOD_rr_32,
          417 => Opcode::MOVSX_rr_16,
          418 => Opcode::MOVSX_rr_32,
          419 => Opcode::MOVSX_rr_32_16,
          420 => Opcode::MOVSX_rr_32_8,
          421 => Opcode::MOVSX_rr_8,
          422 => Opcode::MOV_32_64,
          423 => Opcode::MOV_ri,
          424 => Opcode::MOV_ri_32,
          425 => Opcode::MOV_rr,
          426 => Opcode::MOV_rr_32,
          427 => Opcode::MUL_ri,
          428 => Opcode::MUL_ri_32,
          429 => Opcode::MUL_rr,
          430 => Opcode::MUL_rr_32,
          431 => Opcode::NEG_32,
          432 => Opcode::NEG_64,
          433 => Opcode::NOP,
          434 => Opcode::OR_ri,
          435 => Opcode::OR_ri_32,
          436 => Opcode::OR_rr,
          437 => Opcode::OR_rr_32,
          438 => Opcode::RET,
          439 => Opcode::SDIV_ri,
          440 => Opcode::SDIV_ri_32,
          441 => Opcode::SDIV_rr,
          442 => Opcode::SDIV_rr_32,
          443 => Opcode::SLL_ri,
          444 => Opcode::SLL_ri_32,
          445 => Opcode::SLL_rr,
          446 => Opcode::SLL_rr_32,
          447 => Opcode::SMOD_ri,
          448 => Opcode::SMOD_ri_32,
          449 => Opcode::SMOD_rr,
          450 => Opcode::SMOD_rr_32,
          451 => Opcode::SRA_ri,
          452 => Opcode::SRA_ri_32,
          453 => Opcode::SRA_rr,
          454 => Opcode::SRA_rr_32,
          455 => Opcode::SRL_ri,
          456 => Opcode::SRL_ri_32,
          457 => Opcode::SRL_rr,
          458 => Opcode::SRL_rr_32,
          459 => Opcode::STB,
          460 => Opcode::STB32,
          461 => Opcode::STB_imm,
          462 => Opcode::STD,
          463 => Opcode::STD_imm,
          464 => Opcode::STH,
          465 => Opcode::STH32,
          466 => Opcode::STH_imm,
          467 => Opcode::STW,
          468 => Opcode::STW32,
          469 => Opcode::STW_imm,
          470 => Opcode::SUB_ri,
          471 => Opcode::SUB_ri_32,
          472 => Opcode::SUB_rr,
          473 => Opcode::SUB_rr_32,
          474 => Opcode::XADDD,
          475 => Opcode::XADDW,
          476 => Opcode::XADDW32,
          477 => Opcode::XANDD,
          478 => Opcode::XANDW32,
          479 => Opcode::XCHGD,
          480 => Opcode::XCHGW32,
          481 => Opcode::XFADDD,
          482 => Opcode::XFADDW32,
          483 => Opcode::XFANDD,
          484 => Opcode::XFANDW32,
          485 => Opcode::XFORD,
          486 => Opcode::XFORW32,
          487 => Opcode::XFXORD,
          488 => Opcode::XFXORW32,
          489 => Opcode::XORD,
          490 => Opcode::XORW32,
          491 => Opcode::XOR_ri,
          492 => Opcode::XOR_ri_32,
          493 => Opcode::XOR_rr,
          494 => Opcode::XOR_rr_32,
          495 => Opcode::XXORD,
          496 => Opcode::XXORW32,
          497 => Opcode::INSTRUCTION_LIST_END,
          _ => Opcode::UNKNOWN(value),
        }
    }
}
