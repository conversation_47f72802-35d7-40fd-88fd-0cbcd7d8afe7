import enum
from typing import Iterator, Optional, Union

import lief.assembly


class OPCODE(enum.Enum):
    PHI = 0

    INLINEASM = 1

    INLINEASM_BR = 2

    CFI_INSTRUCTION = 3

    EH_LABEL = 4

    GC_LABEL = 5

    ANNOTATION_LABEL = 6

    KILL = 7

    EXTRACT_SUBREG = 8

    INSERT_SUBREG = 9

    IMPLICIT_DEF = 10

    INIT_UNDEF = 11

    SUBREG_TO_REG = 12

    COPY_TO_REGCLASS = 13

    DBG_VALUE = 14

    DBG_VALUE_LIST = 15

    DBG_INSTR_REF = 16

    DBG_PHI = 17

    DBG_LABEL = 18

    REG_SEQUENCE = 19

    COPY = 20

    BUNDLE = 21

    LIFETIME_START = 22

    LIFETIME_END = 23

    PSEUDO_PROBE = 24

    ARITH_FENCE = 25

    STACKMAP = 26

    FENTRY_CALL = 27

    PATCHPOINT = 28

    LOAD_STACK_GUARD = 29

    PREALLOCATED_SETUP = 30

    PREALLOCATED_ARG = 31

    STATEPOINT = 32

    LOCAL_ESCAPE = 33

    FAULTING_OP = 34

    PATCHABLE_OP = 35

    PATCHABLE_FUNCTION_ENTER = 36

    PATCHABLE_RET = 37

    PATCHABLE_FUNCTION_EXIT = 38

    PATCHABLE_TAIL_CALL = 39

    PATCHABLE_EVENT_CALL = 40

    PATCHABLE_TYPED_EVENT_CALL = 41

    ICALL_BRANCH_FUNNEL = 42

    FAKE_USE = 43

    MEMBARRIER = 44

    JUMP_TABLE_DEBUG_INFO = 45

    CONVERGENCECTRL_ENTRY = 46

    CONVERGENCECTRL_ANCHOR = 47

    CONVERGENCECTRL_LOOP = 48

    CONVERGENCECTRL_GLUE = 49

    G_ASSERT_SEXT = 50

    G_ASSERT_ZEXT = 51

    G_ASSERT_ALIGN = 52

    G_ADD = 53

    G_SUB = 54

    G_MUL = 55

    G_SDIV = 56

    G_UDIV = 57

    G_SREM = 58

    G_UREM = 59

    G_SDIVREM = 60

    G_UDIVREM = 61

    G_AND = 62

    G_OR = 63

    G_XOR = 64

    G_ABDS = 65

    G_ABDU = 66

    G_IMPLICIT_DEF = 67

    G_PHI = 68

    G_FRAME_INDEX = 69

    G_GLOBAL_VALUE = 70

    G_PTRAUTH_GLOBAL_VALUE = 71

    G_CONSTANT_POOL = 72

    G_EXTRACT = 73

    G_UNMERGE_VALUES = 74

    G_INSERT = 75

    G_MERGE_VALUES = 76

    G_BUILD_VECTOR = 77

    G_BUILD_VECTOR_TRUNC = 78

    G_CONCAT_VECTORS = 79

    G_PTRTOINT = 80

    G_INTTOPTR = 81

    G_BITCAST = 82

    G_FREEZE = 83

    G_CONSTANT_FOLD_BARRIER = 84

    G_INTRINSIC_FPTRUNC_ROUND = 85

    G_INTRINSIC_TRUNC = 86

    G_INTRINSIC_ROUND = 87

    G_INTRINSIC_LRINT = 88

    G_INTRINSIC_LLRINT = 89

    G_INTRINSIC_ROUNDEVEN = 90

    G_READCYCLECOUNTER = 91

    G_READSTEADYCOUNTER = 92

    G_LOAD = 93

    G_SEXTLOAD = 94

    G_ZEXTLOAD = 95

    G_INDEXED_LOAD = 96

    G_INDEXED_SEXTLOAD = 97

    G_INDEXED_ZEXTLOAD = 98

    G_STORE = 99

    G_INDEXED_STORE = 100

    G_ATOMIC_CMPXCHG_WITH_SUCCESS = 101

    G_ATOMIC_CMPXCHG = 102

    G_ATOMICRMW_XCHG = 103

    G_ATOMICRMW_ADD = 104

    G_ATOMICRMW_SUB = 105

    G_ATOMICRMW_AND = 106

    G_ATOMICRMW_NAND = 107

    G_ATOMICRMW_OR = 108

    G_ATOMICRMW_XOR = 109

    G_ATOMICRMW_MAX = 110

    G_ATOMICRMW_MIN = 111

    G_ATOMICRMW_UMAX = 112

    G_ATOMICRMW_UMIN = 113

    G_ATOMICRMW_FADD = 114

    G_ATOMICRMW_FSUB = 115

    G_ATOMICRMW_FMAX = 116

    G_ATOMICRMW_FMIN = 117

    G_ATOMICRMW_UINC_WRAP = 118

    G_ATOMICRMW_UDEC_WRAP = 119

    G_ATOMICRMW_USUB_COND = 120

    G_ATOMICRMW_USUB_SAT = 121

    G_FENCE = 122

    G_PREFETCH = 123

    G_BRCOND = 124

    G_BRINDIRECT = 125

    G_INVOKE_REGION_START = 126

    G_INTRINSIC = 127

    G_INTRINSIC_W_SIDE_EFFECTS = 128

    G_INTRINSIC_CONVERGENT = 129

    G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS = 130

    G_ANYEXT = 131

    G_TRUNC = 132

    G_CONSTANT = 133

    G_FCONSTANT = 134

    G_VASTART = 135

    G_VAARG = 136

    G_SEXT = 137

    G_SEXT_INREG = 138

    G_ZEXT = 139

    G_SHL = 140

    G_LSHR = 141

    G_ASHR = 142

    G_FSHL = 143

    G_FSHR = 144

    G_ROTR = 145

    G_ROTL = 146

    G_ICMP = 147

    G_FCMP = 148

    G_SCMP = 149

    G_UCMP = 150

    G_SELECT = 151

    G_UADDO = 152

    G_UADDE = 153

    G_USUBO = 154

    G_USUBE = 155

    G_SADDO = 156

    G_SADDE = 157

    G_SSUBO = 158

    G_SSUBE = 159

    G_UMULO = 160

    G_SMULO = 161

    G_UMULH = 162

    G_SMULH = 163

    G_UADDSAT = 164

    G_SADDSAT = 165

    G_USUBSAT = 166

    G_SSUBSAT = 167

    G_USHLSAT = 168

    G_SSHLSAT = 169

    G_SMULFIX = 170

    G_UMULFIX = 171

    G_SMULFIXSAT = 172

    G_UMULFIXSAT = 173

    G_SDIVFIX = 174

    G_UDIVFIX = 175

    G_SDIVFIXSAT = 176

    G_UDIVFIXSAT = 177

    G_FADD = 178

    G_FSUB = 179

    G_FMUL = 180

    G_FMA = 181

    G_FMAD = 182

    G_FDIV = 183

    G_FREM = 184

    G_FPOW = 185

    G_FPOWI = 186

    G_FEXP = 187

    G_FEXP2 = 188

    G_FEXP10 = 189

    G_FLOG = 190

    G_FLOG2 = 191

    G_FLOG10 = 192

    G_FLDEXP = 193

    G_FFREXP = 194

    G_FNEG = 195

    G_FPEXT = 196

    G_FPTRUNC = 197

    G_FPTOSI = 198

    G_FPTOUI = 199

    G_SITOFP = 200

    G_UITOFP = 201

    G_FPTOSI_SAT = 202

    G_FPTOUI_SAT = 203

    G_FABS = 204

    G_FCOPYSIGN = 205

    G_IS_FPCLASS = 206

    G_FCANONICALIZE = 207

    G_FMINNUM = 208

    G_FMAXNUM = 209

    G_FMINNUM_IEEE = 210

    G_FMAXNUM_IEEE = 211

    G_FMINIMUM = 212

    G_FMAXIMUM = 213

    G_GET_FPENV = 214

    G_SET_FPENV = 215

    G_RESET_FPENV = 216

    G_GET_FPMODE = 217

    G_SET_FPMODE = 218

    G_RESET_FPMODE = 219

    G_PTR_ADD = 220

    G_PTRMASK = 221

    G_SMIN = 222

    G_SMAX = 223

    G_UMIN = 224

    G_UMAX = 225

    G_ABS = 226

    G_LROUND = 227

    G_LLROUND = 228

    G_BR = 229

    G_BRJT = 230

    G_VSCALE = 231

    G_INSERT_SUBVECTOR = 232

    G_EXTRACT_SUBVECTOR = 233

    G_INSERT_VECTOR_ELT = 234

    G_EXTRACT_VECTOR_ELT = 235

    G_SHUFFLE_VECTOR = 236

    G_SPLAT_VECTOR = 237

    G_STEP_VECTOR = 238

    G_VECTOR_COMPRESS = 239

    G_CTTZ = 240

    G_CTTZ_ZERO_UNDEF = 241

    G_CTLZ = 242

    G_CTLZ_ZERO_UNDEF = 243

    G_CTPOP = 244

    G_BSWAP = 245

    G_BITREVERSE = 246

    G_FCEIL = 247

    G_FCOS = 248

    G_FSIN = 249

    G_FSINCOS = 250

    G_FTAN = 251

    G_FACOS = 252

    G_FASIN = 253

    G_FATAN = 254

    G_FATAN2 = 255

    G_FCOSH = 256

    G_FSINH = 257

    G_FTANH = 258

    G_FSQRT = 259

    G_FFLOOR = 260

    G_FRINT = 261

    G_FNEARBYINT = 262

    G_ADDRSPACE_CAST = 263

    G_BLOCK_ADDR = 264

    G_JUMP_TABLE = 265

    G_DYN_STACKALLOC = 266

    G_STACKSAVE = 267

    G_STACKRESTORE = 268

    G_STRICT_FADD = 269

    G_STRICT_FSUB = 270

    G_STRICT_FMUL = 271

    G_STRICT_FDIV = 272

    G_STRICT_FREM = 273

    G_STRICT_FMA = 274

    G_STRICT_FSQRT = 275

    G_STRICT_FLDEXP = 276

    G_READ_REGISTER = 277

    G_WRITE_REGISTER = 278

    G_MEMCPY = 279

    G_MEMCPY_INLINE = 280

    G_MEMMOVE = 281

    G_MEMSET = 282

    G_BZERO = 283

    G_TRAP = 284

    G_DEBUGTRAP = 285

    G_UBSANTRAP = 286

    G_VECREDUCE_SEQ_FADD = 287

    G_VECREDUCE_SEQ_FMUL = 288

    G_VECREDUCE_FADD = 289

    G_VECREDUCE_FMUL = 290

    G_VECREDUCE_FMAX = 291

    G_VECREDUCE_FMIN = 292

    G_VECREDUCE_FMAXIMUM = 293

    G_VECREDUCE_FMINIMUM = 294

    G_VECREDUCE_ADD = 295

    G_VECREDUCE_MUL = 296

    G_VECREDUCE_AND = 297

    G_VECREDUCE_OR = 298

    G_VECREDUCE_XOR = 299

    G_VECREDUCE_SMAX = 300

    G_VECREDUCE_SMIN = 301

    G_VECREDUCE_UMAX = 302

    G_VECREDUCE_UMIN = 303

    G_SBFX = 304

    G_UBFX = 305

    ATOMIC_CMP_SWAP_I128 = 306

    ATOMIC_LOAD_ADD_I128 = 307

    ATOMIC_LOAD_AND_I128 = 308

    ATOMIC_LOAD_NAND_I128 = 309

    ATOMIC_LOAD_OR_I128 = 310

    ATOMIC_LOAD_SUB_I128 = 311

    ATOMIC_LOAD_XOR_I128 = 312

    ATOMIC_SWAP_I128 = 313

    BUILD_QUADWORD = 314

    BUILD_UACC = 315

    CFENCE = 316

    CFENCE8 = 317

    CLRLSLDI = 318

    CLRLSLDI_rec = 319

    CLRLSLWI = 320

    CLRLSLWI_rec = 321

    CLRRDI = 322

    CLRRDI_rec = 323

    CLRRWI = 324

    CLRRWI_rec = 325

    DCBFL = 326

    DCBFLP = 327

    DCBFPS = 328

    DCBFx = 329

    DCBSTPS = 330

    DCBTCT = 331

    DCBTDS = 332

    DCBTSTCT = 333

    DCBTSTDS = 334

    DCBTSTT = 335

    DCBTSTx = 336

    DCBTT = 337

    DCBTx = 338

    DFLOADf32 = 339

    DFLOADf64 = 340

    DFSTOREf32 = 341

    DFSTOREf64 = 342

    EXTLDI = 343

    EXTLDI_rec = 344

    EXTLWI = 345

    EXTLWI_rec = 346

    EXTRDI = 347

    EXTRDI_rec = 348

    EXTRWI = 349

    EXTRWI_rec = 350

    INSLWI = 351

    INSLWI_rec = 352

    INSRDI = 353

    INSRDI_rec = 354

    INSRWI = 355

    INSRWI_rec = 356

    KILL_PAIR = 357

    LAx = 358

    LIWAX = 359

    LIWZX = 360

    PPCLdFixedAddr = 361

    PSUBI = 362

    RLWIMIbm = 363

    RLWIMIbm_rec = 364

    RLWINMbm = 365

    RLWINMbm_rec = 366

    RLWNMbm = 367

    RLWNMbm_rec = 368

    ROTRDI = 369

    ROTRDI_rec = 370

    ROTRWI = 371

    ROTRWI_rec = 372

    SLDI = 373

    SLDI_rec = 374

    SLWI = 375

    SLWI_rec = 376

    SPILLTOVSR_LD = 377

    SPILLTOVSR_LDX = 378

    SPILLTOVSR_ST = 379

    SPILLTOVSR_STX = 380

    SRDI = 381

    SRDI_rec = 382

    SRWI = 383

    SRWI_rec = 384

    STIWX = 385

    SUBI = 386

    SUBIC = 387

    SUBIC_rec = 388

    SUBIS = 389

    SUBPCIS = 390

    XFLOADf32 = 391

    XFLOADf64 = 392

    XFSTOREf32 = 393

    XFSTOREf64 = 394

    ADD4 = 395

    ADD4O = 396

    ADD4O_rec = 397

    ADD4TLS = 398

    ADD4_rec = 399

    ADD8 = 400

    ADD8O = 401

    ADD8O_rec = 402

    ADD8TLS = 403

    ADD8_rec = 405

    ADDC = 406

    ADDC8 = 407

    ADDC8O = 408

    ADDC8O_rec = 409

    ADDC8_rec = 410

    ADDCO = 411

    ADDCO_rec = 412

    ADDC_rec = 413

    ADDE = 414

    ADDE8 = 415

    ADDE8O = 416

    ADDE8O_rec = 417

    ADDE8_rec = 418

    ADDEO = 419

    ADDEO_rec = 420

    ADDEX = 421

    ADDEX8 = 422

    ADDE_rec = 423

    ADDG6S = 424

    ADDG6S8 = 425

    ADDI = 426

    ADDI8 = 427

    ADDIC = 428

    ADDIC8 = 429

    ADDIC_rec = 430

    ADDIS = 431

    ADDIS8 = 432

    ADDISdtprelHA = 433

    ADDISdtprelHA32 = 434

    ADDISgotTprelHA = 435

    ADDIStlsgdHA = 436

    ADDIStlsldHA = 437

    ADDIStocHA = 438

    ADDIStocHA8 = 439

    ADDIdtprelL = 440

    ADDIdtprelL32 = 441

    ADDItlsgdL = 442

    ADDItlsgdL32 = 443

    ADDItlsgdLADDR = 444

    ADDItlsgdLADDR32 = 445

    ADDItlsldL = 446

    ADDItlsldL32 = 447

    ADDItlsldLADDR = 448

    ADDItlsldLADDR32 = 449

    ADDItoc = 450

    ADDItoc8 = 451

    ADDItocL = 452

    ADDItocL8 = 453

    ADDME = 454

    ADDME8 = 455

    ADDME8O = 456

    ADDME8O_rec = 457

    ADDME8_rec = 458

    ADDMEO = 459

    ADDMEO_rec = 460

    ADDME_rec = 461

    ADDPCIS = 462

    ADDZE = 463

    ADDZE8 = 464

    ADDZE8O = 465

    ADDZE8O_rec = 466

    ADDZE8_rec = 467

    ADDZEO = 468

    ADDZEO_rec = 469

    ADDZE_rec = 470

    ADJCALLSTACKDOWN = 471

    ADJCALLSTACKUP = 472

    AND = 473

    AND8 = 474

    AND8_rec = 475

    ANDC = 476

    ANDC8 = 477

    ANDC8_rec = 478

    ANDC_rec = 479

    ANDI8_rec = 480

    ANDIS8_rec = 481

    ANDIS_rec = 482

    ANDI_rec = 483

    ANDI_rec_1_EQ_BIT = 484

    ANDI_rec_1_EQ_BIT8 = 485

    ANDI_rec_1_GT_BIT = 486

    ANDI_rec_1_GT_BIT8 = 487

    AND_rec = 488

    ATOMIC_CMP_SWAP_I16 = 489

    ATOMIC_CMP_SWAP_I32 = 490

    ATOMIC_CMP_SWAP_I64 = 491

    ATOMIC_CMP_SWAP_I8 = 492

    ATOMIC_LOAD_ADD_I16 = 493

    ATOMIC_LOAD_ADD_I32 = 494

    ATOMIC_LOAD_ADD_I64 = 495

    ATOMIC_LOAD_ADD_I8 = 496

    ATOMIC_LOAD_AND_I16 = 497

    ATOMIC_LOAD_AND_I32 = 498

    ATOMIC_LOAD_AND_I64 = 499

    ATOMIC_LOAD_AND_I8 = 500

    ATOMIC_LOAD_MAX_I16 = 501

    ATOMIC_LOAD_MAX_I32 = 502

    ATOMIC_LOAD_MAX_I64 = 503

    ATOMIC_LOAD_MAX_I8 = 504

    ATOMIC_LOAD_MIN_I16 = 505

    ATOMIC_LOAD_MIN_I32 = 506

    ATOMIC_LOAD_MIN_I64 = 507

    ATOMIC_LOAD_MIN_I8 = 508

    ATOMIC_LOAD_NAND_I16 = 509

    ATOMIC_LOAD_NAND_I32 = 510

    ATOMIC_LOAD_NAND_I64 = 511

    ATOMIC_LOAD_NAND_I8 = 512

    ATOMIC_LOAD_OR_I16 = 513

    ATOMIC_LOAD_OR_I32 = 514

    ATOMIC_LOAD_OR_I64 = 515

    ATOMIC_LOAD_OR_I8 = 516

    ATOMIC_LOAD_SUB_I16 = 517

    ATOMIC_LOAD_SUB_I32 = 518

    ATOMIC_LOAD_SUB_I64 = 519

    ATOMIC_LOAD_SUB_I8 = 520

    ATOMIC_LOAD_UMAX_I16 = 521

    ATOMIC_LOAD_UMAX_I32 = 522

    ATOMIC_LOAD_UMAX_I64 = 523

    ATOMIC_LOAD_UMAX_I8 = 524

    ATOMIC_LOAD_UMIN_I16 = 525

    ATOMIC_LOAD_UMIN_I32 = 526

    ATOMIC_LOAD_UMIN_I64 = 527

    ATOMIC_LOAD_UMIN_I8 = 528

    ATOMIC_LOAD_XOR_I16 = 529

    ATOMIC_LOAD_XOR_I32 = 530

    ATOMIC_LOAD_XOR_I64 = 531

    ATOMIC_LOAD_XOR_I8 = 532

    ATOMIC_SWAP_I16 = 533

    ATOMIC_SWAP_I32 = 534

    ATOMIC_SWAP_I64 = 535

    ATOMIC_SWAP_I8 = 536

    ATTN = 537

    B = 538

    BA = 539

    BC = 540

    BCC = 541

    BCCA = 542

    BCCCTR = 543

    BCCCTR8 = 544

    BCCCTRL = 545

    BCCCTRL8 = 546

    BCCL = 547

    BCCLA = 548

    BCCLR = 549

    BCCLRL = 550

    BCCTR = 551

    BCCTR8 = 552

    BCCTR8n = 553

    BCCTRL = 554

    BCCTRL8 = 555

    BCCTRL8n = 556

    BCCTRLn = 557

    BCCTRn = 558

    BCDADD_rec = 559

    BCDCFN_rec = 560

    BCDCFSQ_rec = 561

    BCDCFZ_rec = 562

    BCDCPSGN_rec = 563

    BCDCTN_rec = 564

    BCDCTSQ_rec = 565

    BCDCTZ_rec = 566

    BCDSETSGN_rec = 567

    BCDSR_rec = 568

    BCDSUB_rec = 569

    BCDS_rec = 570

    BCDTRUNC_rec = 571

    BCDUS_rec = 572

    BCDUTRUNC_rec = 573

    BCL = 574

    BCLR = 575

    BCLRL = 576

    BCLRLn = 577

    BCLRn = 578

    BCLalways = 579

    BCLn = 580

    BCTR = 581

    BCTR8 = 582

    BCTRL = 583

    BCTRL8 = 584

    BCTRL8_LDinto_toc = 585

    BCTRL8_LDinto_toc_RM = 586

    BCTRL8_RM = 587

    BCTRL_LWZinto_toc = 588

    BCTRL_LWZinto_toc_RM = 589

    BCTRL_RM = 590

    BCn = 591

    BDNZ = 592

    BDNZ8 = 593

    BDNZA = 594

    BDNZAm = 595

    BDNZAp = 596

    BDNZL = 597

    BDNZLA = 598

    BDNZLAm = 599

    BDNZLAp = 600

    BDNZLR = 601

    BDNZLR8 = 602

    BDNZLRL = 603

    BDNZLRLm = 604

    BDNZLRLp = 605

    BDNZLRm = 606

    BDNZLRp = 607

    BDNZLm = 608

    BDNZLp = 609

    BDNZm = 610

    BDNZp = 611

    BDZ = 612

    BDZ8 = 613

    BDZA = 614

    BDZAm = 615

    BDZAp = 616

    BDZL = 617

    BDZLA = 618

    BDZLAm = 619

    BDZLAp = 620

    BDZLR = 621

    BDZLR8 = 622

    BDZLRL = 623

    BDZLRLm = 624

    BDZLRLp = 625

    BDZLRm = 626

    BDZLRp = 627

    BDZLm = 628

    BDZLp = 629

    BDZm = 630

    BDZp = 631

    BL = 632

    BL8 = 633

    BL8_NOP = 634

    BL8_NOP_RM = 635

    BL8_NOP_TLS = 636

    BL8_NOTOC = 637

    BL8_NOTOC_RM = 638

    BL8_NOTOC_TLS = 639

    BL8_RM = 640

    BL8_TLS = 641

    BLA = 643

    BLA8 = 644

    BLA8_NOP = 645

    BLA8_NOP_RM = 646

    BLA8_RM = 647

    BLA_RM = 648

    BLR = 649

    BLR8 = 650

    BLRL = 651

    BL_NOP = 652

    BL_NOP_RM = 653

    BL_RM = 654

    BL_TLS = 655

    BPERMD = 656

    BRD = 657

    BRH = 658

    BRH8 = 659

    BRINC = 660

    BRW = 661

    BRW8 = 662

    CBCDTD = 663

    CBCDTD8 = 664

    CDTBCD = 665

    CDTBCD8 = 666

    CFUGED = 667

    CLRBHRB = 668

    CMPB = 669

    CMPB8 = 670

    CMPD = 671

    CMPDI = 672

    CMPEQB = 673

    CMPLD = 674

    CMPLDI = 675

    CMPLW = 676

    CMPLWI = 677

    CMPRB = 678

    CMPRB8 = 679

    CMPW = 680

    CMPWI = 681

    CNTLZD = 682

    CNTLZDM = 683

    CNTLZD_rec = 684

    CNTLZW = 685

    CNTLZW8 = 686

    CNTLZW8_rec = 687

    CNTLZW_rec = 688

    CNTTZD = 689

    CNTTZDM = 690

    CNTTZD_rec = 691

    CNTTZW = 692

    CNTTZW8 = 693

    CNTTZW8_rec = 694

    CNTTZW_rec = 695

    CP_ABORT = 696

    CP_COPY = 697

    CP_COPY8 = 698

    CP_PASTE8_rec = 699

    CP_PASTE_rec = 700

    CR6SET = 701

    CR6UNSET = 702

    CRAND = 703

    CRANDC = 704

    CREQV = 705

    CRNAND = 706

    CRNOR = 707

    CRNOT = 708

    CROR = 709

    CRORC = 710

    CRSET = 711

    CRUNSET = 712

    CRXOR = 713

    CTRL_DEP = 714

    DADD = 715

    DADDQ = 716

    DADDQ_rec = 717

    DADD_rec = 718

    DARN = 719

    DCBA = 720

    DCBF = 721

    DCBFEP = 722

    DCBI = 723

    DCBST = 724

    DCBSTEP = 725

    DCBT = 726

    DCBTEP = 727

    DCBTST = 728

    DCBTSTEP = 729

    DCBZ = 730

    DCBZEP = 731

    DCBZL = 732

    DCBZLEP = 733

    DCCCI = 734

    DCFFIX = 735

    DCFFIXQ = 736

    DCFFIXQQ = 737

    DCFFIXQ_rec = 738

    DCFFIX_rec = 739

    DCMPO = 740

    DCMPOQ = 741

    DCMPU = 742

    DCMPUQ = 743

    DCTDP = 744

    DCTDP_rec = 745

    DCTFIX = 746

    DCTFIXQ = 747

    DCTFIXQQ = 748

    DCTFIXQ_rec = 749

    DCTFIX_rec = 750

    DCTQPQ = 751

    DCTQPQ_rec = 752

    DDEDPD = 753

    DDEDPDQ = 754

    DDEDPDQ_rec = 755

    DDEDPD_rec = 756

    DDIV = 757

    DDIVQ = 758

    DDIVQ_rec = 759

    DDIV_rec = 760

    DENBCD = 761

    DENBCDQ = 762

    DENBCDQ_rec = 763

    DENBCD_rec = 764

    DIEX = 765

    DIEXQ = 766

    DIEXQ_rec = 767

    DIEX_rec = 768

    DIVD = 769

    DIVDE = 770

    DIVDEO = 771

    DIVDEO_rec = 772

    DIVDEU = 773

    DIVDEUO = 774

    DIVDEUO_rec = 775

    DIVDEU_rec = 776

    DIVDE_rec = 777

    DIVDO = 778

    DIVDO_rec = 779

    DIVDU = 780

    DIVDUO = 781

    DIVDUO_rec = 782

    DIVDU_rec = 783

    DIVD_rec = 784

    DIVW = 785

    DIVWE = 786

    DIVWEO = 787

    DIVWEO_rec = 788

    DIVWEU = 789

    DIVWEUO = 790

    DIVWEUO_rec = 791

    DIVWEU_rec = 792

    DIVWE_rec = 793

    DIVWO = 794

    DIVWO_rec = 795

    DIVWU = 796

    DIVWUO = 797

    DIVWUO_rec = 798

    DIVWU_rec = 799

    DIVW_rec = 800

    DMMR = 801

    DMSETDMRZ = 802

    DMUL = 803

    DMULQ = 804

    DMULQ_rec = 805

    DMUL_rec = 806

    DMXOR = 807

    DMXXEXTFDMR256 = 808

    DMXXEXTFDMR512 = 809

    DMXXEXTFDMR512_HI = 810

    DMXXINSTFDMR256 = 811

    DMXXINSTFDMR512 = 812

    DMXXINSTFDMR512_HI = 813

    DQUA = 814

    DQUAI = 815

    DQUAIQ = 816

    DQUAIQ_rec = 817

    DQUAI_rec = 818

    DQUAQ = 819

    DQUAQ_rec = 820

    DQUA_rec = 821

    DRDPQ = 822

    DRDPQ_rec = 823

    DRINTN = 824

    DRINTNQ = 825

    DRINTNQ_rec = 826

    DRINTN_rec = 827

    DRINTX = 828

    DRINTXQ = 829

    DRINTXQ_rec = 830

    DRINTX_rec = 831

    DRRND = 832

    DRRNDQ = 833

    DRRNDQ_rec = 834

    DRRND_rec = 835

    DRSP = 836

    DRSP_rec = 837

    DSCLI = 838

    DSCLIQ = 839

    DSCLIQ_rec = 840

    DSCLI_rec = 841

    DSCRI = 842

    DSCRIQ = 843

    DSCRIQ_rec = 844

    DSCRI_rec = 845

    DSS = 846

    DSSALL = 847

    DST = 848

    DST64 = 849

    DSTST = 850

    DSTST64 = 851

    DSTSTT = 852

    DSTSTT64 = 853

    DSTT = 854

    DSTT64 = 855

    DSUB = 856

    DSUBQ = 857

    DSUBQ_rec = 858

    DSUB_rec = 859

    DTSTDC = 860

    DTSTDCQ = 861

    DTSTDG = 862

    DTSTDGQ = 863

    DTSTEX = 864

    DTSTEXQ = 865

    DTSTSF = 866

    DTSTSFI = 867

    DTSTSFIQ = 868

    DTSTSFQ = 869

    DXEX = 870

    DXEXQ = 871

    DXEXQ_rec = 872

    DXEX_rec = 873

    DYNALLOC = 874

    DYNALLOC8 = 875

    DYNAREAOFFSET = 876

    DYNAREAOFFSET8 = 877

    DecreaseCTR8loop = 878

    DecreaseCTRloop = 879

    EFDABS = 880

    EFDADD = 881

    EFDCFS = 882

    EFDCFSF = 883

    EFDCFSI = 884

    EFDCFSID = 885

    EFDCFUF = 886

    EFDCFUI = 887

    EFDCFUID = 888

    EFDCMPEQ = 889

    EFDCMPGT = 890

    EFDCMPLT = 891

    EFDCTSF = 892

    EFDCTSI = 893

    EFDCTSIDZ = 894

    EFDCTSIZ = 895

    EFDCTUF = 896

    EFDCTUI = 897

    EFDCTUIDZ = 898

    EFDCTUIZ = 899

    EFDDIV = 900

    EFDMUL = 901

    EFDNABS = 902

    EFDNEG = 903

    EFDSUB = 904

    EFDTSTEQ = 905

    EFDTSTGT = 906

    EFDTSTLT = 907

    EFSABS = 908

    EFSADD = 909

    EFSCFD = 910

    EFSCFSF = 911

    EFSCFSI = 912

    EFSCFUF = 913

    EFSCFUI = 914

    EFSCMPEQ = 915

    EFSCMPGT = 916

    EFSCMPLT = 917

    EFSCTSF = 918

    EFSCTSI = 919

    EFSCTSIZ = 920

    EFSCTUF = 921

    EFSCTUI = 922

    EFSCTUIZ = 923

    EFSDIV = 924

    EFSMUL = 925

    EFSNABS = 926

    EFSNEG = 927

    EFSSUB = 928

    EFSTSTEQ = 929

    EFSTSTGT = 930

    EFSTSTLT = 931

    EH_SjLj_LongJmp32 = 932

    EH_SjLj_LongJmp64 = 933

    EH_SjLj_SetJmp32 = 934

    EH_SjLj_SetJmp64 = 935

    EH_SjLj_Setup = 936

    EQV = 937

    EQV8 = 938

    EQV8_rec = 939

    EQV_rec = 940

    EVABS = 941

    EVADDIW = 942

    EVADDSMIAAW = 943

    EVADDSSIAAW = 944

    EVADDUMIAAW = 945

    EVADDUSIAAW = 946

    EVADDW = 947

    EVAND = 948

    EVANDC = 949

    EVCMPEQ = 950

    EVCMPGTS = 951

    EVCMPGTU = 952

    EVCMPLTS = 953

    EVCMPLTU = 954

    EVCNTLSW = 955

    EVCNTLZW = 956

    EVDIVWS = 957

    EVDIVWU = 958

    EVEQV = 959

    EVEXTSB = 960

    EVEXTSH = 961

    EVFSABS = 962

    EVFSADD = 963

    EVFSCFSF = 964

    EVFSCFSI = 965

    EVFSCFUF = 966

    EVFSCFUI = 967

    EVFSCMPEQ = 968

    EVFSCMPGT = 969

    EVFSCMPLT = 970

    EVFSCTSF = 971

    EVFSCTSI = 972

    EVFSCTSIZ = 973

    EVFSCTUF = 974

    EVFSCTUI = 975

    EVFSCTUIZ = 976

    EVFSDIV = 977

    EVFSMUL = 978

    EVFSNABS = 979

    EVFSNEG = 980

    EVFSSUB = 981

    EVFSTSTEQ = 982

    EVFSTSTGT = 983

    EVFSTSTLT = 984

    EVLDD = 985

    EVLDDX = 986

    EVLDH = 987

    EVLDHX = 988

    EVLDW = 989

    EVLDWX = 990

    EVLHHESPLAT = 991

    EVLHHESPLATX = 992

    EVLHHOSSPLAT = 993

    EVLHHOSSPLATX = 994

    EVLHHOUSPLAT = 995

    EVLHHOUSPLATX = 996

    EVLWHE = 997

    EVLWHEX = 998

    EVLWHOS = 999

    EVLWHOSX = 1000

    EVLWHOU = 1001

    EVLWHOUX = 1002

    EVLWHSPLAT = 1003

    EVLWHSPLATX = 1004

    EVLWWSPLAT = 1005

    EVLWWSPLATX = 1006

    EVMERGEHI = 1007

    EVMERGEHILO = 1008

    EVMERGELO = 1009

    EVMERGELOHI = 1010

    EVMHEGSMFAA = 1011

    EVMHEGSMFAN = 1012

    EVMHEGSMIAA = 1013

    EVMHEGSMIAN = 1014

    EVMHEGUMIAA = 1015

    EVMHEGUMIAN = 1016

    EVMHESMF = 1017

    EVMHESMFA = 1018

    EVMHESMFAAW = 1019

    EVMHESMFANW = 1020

    EVMHESMI = 1021

    EVMHESMIA = 1022

    EVMHESMIAAW = 1023

    EVMHESMIANW = 1024

    EVMHESSF = 1025

    EVMHESSFA = 1026

    EVMHESSFAAW = 1027

    EVMHESSFANW = 1028

    EVMHESSIAAW = 1029

    EVMHESSIANW = 1030

    EVMHEUMI = 1031

    EVMHEUMIA = 1032

    EVMHEUMIAAW = 1033

    EVMHEUMIANW = 1034

    EVMHEUSIAAW = 1035

    EVMHEUSIANW = 1036

    EVMHOGSMFAA = 1037

    EVMHOGSMFAN = 1038

    EVMHOGSMIAA = 1039

    EVMHOGSMIAN = 1040

    EVMHOGUMIAA = 1041

    EVMHOGUMIAN = 1042

    EVMHOSMF = 1043

    EVMHOSMFA = 1044

    EVMHOSMFAAW = 1045

    EVMHOSMFANW = 1046

    EVMHOSMI = 1047

    EVMHOSMIA = 1048

    EVMHOSMIAAW = 1049

    EVMHOSMIANW = 1050

    EVMHOSSF = 1051

    EVMHOSSFA = 1052

    EVMHOSSFAAW = 1053

    EVMHOSSFANW = 1054

    EVMHOSSIAAW = 1055

    EVMHOSSIANW = 1056

    EVMHOUMI = 1057

    EVMHOUMIA = 1058

    EVMHOUMIAAW = 1059

    EVMHOUMIANW = 1060

    EVMHOUSIAAW = 1061

    EVMHOUSIANW = 1062

    EVMRA = 1063

    EVMWHSMF = 1064

    EVMWHSMFA = 1065

    EVMWHSMI = 1066

    EVMWHSMIA = 1067

    EVMWHSSF = 1068

    EVMWHSSFA = 1069

    EVMWHUMI = 1070

    EVMWHUMIA = 1071

    EVMWLSMIAAW = 1072

    EVMWLSMIANW = 1073

    EVMWLSSIAAW = 1074

    EVMWLSSIANW = 1075

    EVMWLUMI = 1076

    EVMWLUMIA = 1077

    EVMWLUMIAAW = 1078

    EVMWLUMIANW = 1079

    EVMWLUSIAAW = 1080

    EVMWLUSIANW = 1081

    EVMWSMF = 1082

    EVMWSMFA = 1083

    EVMWSMFAA = 1084

    EVMWSMFAN = 1085

    EVMWSMI = 1086

    EVMWSMIA = 1087

    EVMWSMIAA = 1088

    EVMWSMIAN = 1089

    EVMWSSF = 1090

    EVMWSSFA = 1091

    EVMWSSFAA = 1092

    EVMWSSFAN = 1093

    EVMWUMI = 1094

    EVMWUMIA = 1095

    EVMWUMIAA = 1096

    EVMWUMIAN = 1097

    EVNAND = 1098

    EVNEG = 1099

    EVNOR = 1100

    EVOR = 1101

    EVORC = 1102

    EVRLW = 1103

    EVRLWI = 1104

    EVRNDW = 1105

    EVSEL = 1106

    EVSLW = 1107

    EVSLWI = 1108

    EVSPLATFI = 1109

    EVSPLATI = 1110

    EVSRWIS = 1111

    EVSRWIU = 1112

    EVSRWS = 1113

    EVSRWU = 1114

    EVSTDD = 1115

    EVSTDDX = 1116

    EVSTDH = 1117

    EVSTDHX = 1118

    EVSTDW = 1119

    EVSTDWX = 1120

    EVSTWHE = 1121

    EVSTWHEX = 1122

    EVSTWHO = 1123

    EVSTWHOX = 1124

    EVSTWWE = 1125

    EVSTWWEX = 1126

    EVSTWWO = 1127

    EVSTWWOX = 1128

    EVSUBFSMIAAW = 1129

    EVSUBFSSIAAW = 1130

    EVSUBFUMIAAW = 1131

    EVSUBFUSIAAW = 1132

    EVSUBFW = 1133

    EVSUBIFW = 1134

    EVXOR = 1135

    EXTSB = 1136

    EXTSB8 = 1137

    EXTSB8_32_64 = 1138

    EXTSB8_rec = 1139

    EXTSB_rec = 1140

    EXTSH = 1141

    EXTSH8 = 1142

    EXTSH8_32_64 = 1143

    EXTSH8_rec = 1144

    EXTSH_rec = 1145

    EXTSW = 1146

    EXTSWSLI = 1147

    EXTSWSLI_32_64 = 1148

    EXTSWSLI_32_64_rec = 1149

    EXTSWSLI_rec = 1150

    EXTSW_32 = 1151

    EXTSW_32_64 = 1152

    EXTSW_32_64_rec = 1153

    EXTSW_rec = 1154

    EnforceIEIO = 1155

    FABSD = 1156

    FABSD_rec = 1157

    FABSS = 1158

    FABSS_rec = 1159

    FADD = 1160

    FADDS = 1161

    FADDS_rec = 1162

    FADD_rec = 1163

    FADDrtz = 1164

    FCFID = 1165

    FCFIDS = 1166

    FCFIDS_rec = 1167

    FCFIDU = 1168

    FCFIDUS = 1169

    FCFIDUS_rec = 1170

    FCFIDU_rec = 1171

    FCFID_rec = 1172

    FCMPOD = 1173

    FCMPOS = 1174

    FCMPUD = 1175

    FCMPUS = 1176

    FCPSGND = 1177

    FCPSGND_rec = 1178

    FCPSGNS = 1179

    FCPSGNS_rec = 1180

    FCTID = 1181

    FCTIDU = 1182

    FCTIDUZ = 1183

    FCTIDUZ_rec = 1184

    FCTIDU_rec = 1185

    FCTIDZ = 1186

    FCTIDZ_rec = 1187

    FCTID_rec = 1188

    FCTIW = 1189

    FCTIWU = 1190

    FCTIWUZ = 1191

    FCTIWUZ_rec = 1192

    FCTIWU_rec = 1193

    FCTIWZ = 1194

    FCTIWZ_rec = 1195

    FCTIW_rec = 1196

    FDIV = 1197

    FDIVS = 1198

    FDIVS_rec = 1199

    FDIV_rec = 1200

    FENCE = 1201

    FMADD = 1202

    FMADDS = 1203

    FMADDS_rec = 1204

    FMADD_rec = 1205

    FMR = 1206

    FMR_rec = 1207

    FMSUB = 1208

    FMSUBS = 1209

    FMSUBS_rec = 1210

    FMSUB_rec = 1211

    FMUL = 1212

    FMULS = 1213

    FMULS_rec = 1214

    FMUL_rec = 1215

    FNABSD = 1216

    FNABSD_rec = 1217

    FNABSS = 1218

    FNABSS_rec = 1219

    FNEGD = 1220

    FNEGD_rec = 1221

    FNEGS = 1222

    FNEGS_rec = 1223

    FNMADD = 1224

    FNMADDS = 1225

    FNMADDS_rec = 1226

    FNMADD_rec = 1227

    FNMSUB = 1228

    FNMSUBS = 1229

    FNMSUBS_rec = 1230

    FNMSUB_rec = 1231

    FRE = 1232

    FRES = 1233

    FRES_rec = 1234

    FRE_rec = 1235

    FRIMD = 1236

    FRIMD_rec = 1237

    FRIMS = 1238

    FRIMS_rec = 1239

    FRIND = 1240

    FRIND_rec = 1241

    FRINS = 1242

    FRINS_rec = 1243

    FRIPD = 1244

    FRIPD_rec = 1245

    FRIPS = 1246

    FRIPS_rec = 1247

    FRIZD = 1248

    FRIZD_rec = 1249

    FRIZS = 1250

    FRIZS_rec = 1251

    FRSP = 1252

    FRSP_rec = 1253

    FRSQRTE = 1254

    FRSQRTES = 1255

    FRSQRTES_rec = 1256

    FRSQRTE_rec = 1257

    FSELD = 1258

    FSELD_rec = 1259

    FSELS = 1260

    FSELS_rec = 1261

    FSQRT = 1262

    FSQRTS = 1263

    FSQRTS_rec = 1264

    FSQRT_rec = 1265

    FSUB = 1266

    FSUBS = 1267

    FSUBS_rec = 1268

    FSUB_rec = 1269

    FTDIV = 1270

    FTSQRT = 1271

    GETtlsADDR = 1272

    GETtlsADDR32 = 1273

    GETtlsADDR32AIX = 1274

    GETtlsADDR64AIX = 1275

    GETtlsADDRPCREL = 1276

    GETtlsMOD32AIX = 1277

    GETtlsMOD64AIX = 1278

    GETtlsTpointer32AIX = 1279

    GETtlsldADDR = 1280

    GETtlsldADDR32 = 1281

    GETtlsldADDRPCREL = 1282

    HASHCHK = 1283

    HASHCHK8 = 1284

    HASHCHKP = 1285

    HASHCHKP8 = 1286

    HASHST = 1287

    HASHST8 = 1288

    HASHSTP = 1289

    HASHSTP8 = 1290

    HRFID = 1291

    ICBI = 1292

    ICBIEP = 1293

    ICBLC = 1294

    ICBLQ = 1295

    ICBT = 1296

    ICBTLS = 1297

    ICCCI = 1298

    ISEL = 1299

    ISEL8 = 1300

    ISYNC = 1301

    LA = 1302

    LA8 = 1303

    LBARX = 1304

    LBARXL = 1305

    LBEPX = 1306

    LBZ = 1307

    LBZ8 = 1308

    LBZCIX = 1309

    LBZU = 1310

    LBZU8 = 1311

    LBZUX = 1312

    LBZUX8 = 1313

    LBZX = 1314

    LBZX8 = 1315

    LBZXTLS = 1316

    LBZXTLS_32 = 1318

    LD = 1319

    LDARX = 1320

    LDARXL = 1321

    LDAT = 1322

    LDBRX = 1323

    LDCIX = 1324

    LDU = 1325

    LDUX = 1326

    LDX = 1327

    LDXTLS = 1328

    LDgotTprelL = 1330

    LDgotTprelL32 = 1331

    LDtoc = 1332

    LDtocBA = 1333

    LDtocCPT = 1334

    LDtocJTI = 1335

    LDtocL = 1336

    LFD = 1337

    LFDEPX = 1338

    LFDU = 1339

    LFDUX = 1340

    LFDX = 1341

    LFDXTLS = 1342

    LFIWAX = 1344

    LFIWZX = 1345

    LFS = 1346

    LFSU = 1347

    LFSUX = 1348

    LFSX = 1349

    LFSXTLS = 1350

    LHA = 1352

    LHA8 = 1353

    LHARX = 1354

    LHARXL = 1355

    LHAU = 1356

    LHAU8 = 1357

    LHAUX = 1358

    LHAUX8 = 1359

    LHAX = 1360

    LHAX8 = 1361

    LHAXTLS = 1362

    LHAXTLS_32 = 1364

    LHBRX = 1365

    LHBRX8 = 1366

    LHEPX = 1367

    LHZ = 1368

    LHZ8 = 1369

    LHZCIX = 1370

    LHZU = 1371

    LHZU8 = 1372

    LHZUX = 1373

    LHZUX8 = 1374

    LHZX = 1375

    LHZX8 = 1376

    LHZXTLS = 1377

    LHZXTLS_32 = 1379

    LI = 1380

    LI8 = 1381

    LIS = 1382

    LIS8 = 1383

    LMW = 1384

    LQ = 1385

    LQARX = 1386

    LQARXL = 1387

    LQX_PSEUDO = 1388

    LSWI = 1389

    LVEBX = 1390

    LVEHX = 1391

    LVEWX = 1392

    LVSL = 1393

    LVSR = 1394

    LVX = 1395

    LVXL = 1396

    LWA = 1397

    LWARX = 1398

    LWARXL = 1399

    LWAT = 1400

    LWAUX = 1401

    LWAX = 1402

    LWAXTLS = 1403

    LWAXTLS_32 = 1405

    LWAX_32 = 1406

    LWA_32 = 1407

    LWBRX = 1408

    LWBRX8 = 1409

    LWEPX = 1410

    LWZ = 1411

    LWZ8 = 1412

    LWZCIX = 1413

    LWZU = 1414

    LWZU8 = 1415

    LWZUX = 1416

    LWZUX8 = 1417

    LWZX = 1418

    LWZX8 = 1419

    LWZXTLS = 1420

    LWZXTLS_32 = 1422

    LWZtoc = 1423

    LWZtocL = 1424

    LXSD = 1425

    LXSDX = 1426

    LXSIBZX = 1427

    LXSIHZX = 1428

    LXSIWAX = 1429

    LXSIWZX = 1430

    LXSSP = 1431

    LXSSPX = 1432

    LXV = 1433

    LXVB16X = 1434

    LXVD2X = 1435

    LXVDSX = 1436

    LXVH8X = 1437

    LXVKQ = 1438

    LXVL = 1439

    LXVLL = 1440

    LXVP = 1441

    LXVPRL = 1442

    LXVPRLL = 1443

    LXVPX = 1444

    LXVRBX = 1445

    LXVRDX = 1446

    LXVRHX = 1447

    LXVRL = 1448

    LXVRLL = 1449

    LXVRWX = 1450

    LXVW4X = 1451

    LXVWSX = 1452

    LXVX = 1453

    MADDHD = 1454

    MADDHDU = 1455

    MADDLD = 1456

    MADDLD8 = 1457

    MBAR = 1458

    MCRF = 1459

    MCRFS = 1460

    MCRXRX = 1461

    MFBHRBE = 1462

    MFCR = 1463

    MFCR8 = 1464

    MFCTR = 1465

    MFCTR8 = 1466

    MFDCR = 1467

    MFFS = 1468

    MFFSCDRN = 1469

    MFFSCDRNI = 1470

    MFFSCE = 1471

    MFFSCRN = 1472

    MFFSCRNI = 1473

    MFFSL = 1474

    MFFS_rec = 1475

    MFLR = 1476

    MFLR8 = 1477

    MFMSR = 1478

    MFOCRF = 1479

    MFOCRF8 = 1480

    MFPMR = 1481

    MFSPR = 1482

    MFSPR8 = 1483

    MFSR = 1484

    MFSRIN = 1485

    MFTB = 1486

    MFTB8 = 1487

    MFUDSCR = 1488

    MFVRD = 1489

    MFVRSAVE = 1490

    MFVRSAVEv = 1491

    MFVRWZ = 1492

    MFVSCR = 1493

    MFVSRD = 1494

    MFVSRLD = 1495

    MFVSRWZ = 1496

    MODSD = 1497

    MODSW = 1498

    MODUD = 1499

    MODUW = 1500

    MSGSYNC = 1501

    MSYNC = 1502

    MTCRF = 1503

    MTCRF8 = 1504

    MTCTR = 1505

    MTCTR8 = 1506

    MTCTR8loop = 1507

    MTCTRloop = 1508

    MTDCR = 1509

    MTFSB0 = 1510

    MTFSB1 = 1511

    MTFSF = 1512

    MTFSFI = 1513

    MTFSFI_rec = 1514

    MTFSFIb = 1515

    MTFSF_rec = 1516

    MTFSFb = 1517

    MTLR = 1518

    MTLR8 = 1519

    MTMSR = 1520

    MTMSRD = 1521

    MTOCRF = 1522

    MTOCRF8 = 1523

    MTPMR = 1524

    MTSPR = 1525

    MTSPR8 = 1526

    MTSR = 1527

    MTSRIN = 1528

    MTUDSCR = 1529

    MTVRD = 1530

    MTVRSAVE = 1531

    MTVRSAVEv = 1532

    MTVRWA = 1533

    MTVRWZ = 1534

    MTVSCR = 1535

    MTVSRBM = 1536

    MTVSRBMI = 1537

    MTVSRD = 1538

    MTVSRDD = 1539

    MTVSRDM = 1540

    MTVSRHM = 1541

    MTVSRQM = 1542

    MTVSRWA = 1543

    MTVSRWM = 1544

    MTVSRWS = 1545

    MTVSRWZ = 1546

    MULHD = 1547

    MULHDU = 1548

    MULHDU_rec = 1549

    MULHD_rec = 1550

    MULHW = 1551

    MULHWU = 1552

    MULHWU_rec = 1553

    MULHW_rec = 1554

    MULLD = 1555

    MULLDO = 1556

    MULLDO_rec = 1557

    MULLD_rec = 1558

    MULLI = 1559

    MULLI8 = 1560

    MULLW = 1561

    MULLWO = 1562

    MULLWO_rec = 1563

    MULLW_rec = 1564

    MoveGOTtoLR = 1565

    MovePCtoLR = 1566

    MovePCtoLR8 = 1567

    NAND = 1568

    NAND8 = 1569

    NAND8_rec = 1570

    NAND_rec = 1571

    NAP = 1572

    NEG = 1573

    NEG8 = 1574

    NEG8O = 1575

    NEG8O_rec = 1576

    NEG8_rec = 1577

    NEGO = 1578

    NEGO_rec = 1579

    NEG_rec = 1580

    NOP = 1581

    NOP_GT_PWR6 = 1582

    NOP_GT_PWR7 = 1583

    NOR = 1584

    NOR8 = 1585

    NOR8_rec = 1586

    NOR_rec = 1587

    OR = 1588

    OR8 = 1589

    OR8_rec = 1590

    ORC = 1591

    ORC8 = 1592

    ORC8_rec = 1593

    ORC_rec = 1594

    ORI = 1595

    ORI8 = 1596

    ORIS = 1597

    ORIS8 = 1598

    OR_rec = 1599

    PADDI = 1600

    PADDI8 = 1601

    PADDI8pc = 1602

    PADDIdtprel = 1603

    PADDIpc = 1604

    PDEPD = 1605

    PEXTD = 1606

    PLA = 1607

    PLA8 = 1608

    PLA8pc = 1609

    PLApc = 1610

    PLBZ = 1611

    PLBZ8 = 1612

    PLBZ8nopc = 1613

    PLBZ8onlypc = 1614

    PLBZ8pc = 1615

    PLBZnopc = 1616

    PLBZonlypc = 1617

    PLBZpc = 1618

    PLD = 1619

    PLDnopc = 1620

    PLDonlypc = 1621

    PLDpc = 1622

    PLFD = 1623

    PLFDnopc = 1624

    PLFDonlypc = 1625

    PLFDpc = 1626

    PLFS = 1627

    PLFSnopc = 1628

    PLFSonlypc = 1629

    PLFSpc = 1630

    PLHA = 1631

    PLHA8 = 1632

    PLHA8nopc = 1633

    PLHA8onlypc = 1634

    PLHA8pc = 1635

    PLHAnopc = 1636

    PLHAonlypc = 1637

    PLHApc = 1638

    PLHZ = 1639

    PLHZ8 = 1640

    PLHZ8nopc = 1641

    PLHZ8onlypc = 1642

    PLHZ8pc = 1643

    PLHZnopc = 1644

    PLHZonlypc = 1645

    PLHZpc = 1646

    PLI = 1647

    PLI8 = 1648

    PLWA = 1649

    PLWA8 = 1650

    PLWA8nopc = 1651

    PLWA8onlypc = 1652

    PLWA8pc = 1653

    PLWAnopc = 1654

    PLWAonlypc = 1655

    PLWApc = 1656

    PLWZ = 1657

    PLWZ8 = 1658

    PLWZ8nopc = 1659

    PLWZ8onlypc = 1660

    PLWZ8pc = 1661

    PLWZnopc = 1662

    PLWZonlypc = 1663

    PLWZpc = 1664

    PLXSD = 1665

    PLXSDnopc = 1666

    PLXSDonlypc = 1667

    PLXSDpc = 1668

    PLXSSP = 1669

    PLXSSPnopc = 1670

    PLXSSPonlypc = 1671

    PLXSSPpc = 1672

    PLXV = 1673

    PLXVP = 1674

    PLXVPnopc = 1675

    PLXVPonlypc = 1676

    PLXVPpc = 1677

    PLXVnopc = 1678

    PLXVonlypc = 1679

    PLXVpc = 1680

    PMXVBF16GER2 = 1681

    PMXVBF16GER2NN = 1682

    PMXVBF16GER2NP = 1683

    PMXVBF16GER2PN = 1684

    PMXVBF16GER2PP = 1685

    PMXVBF16GER2W = 1686

    PMXVBF16GER2WNN = 1687

    PMXVBF16GER2WNP = 1688

    PMXVBF16GER2WPN = 1689

    PMXVBF16GER2WPP = 1690

    PMXVF16GER2 = 1691

    PMXVF16GER2NN = 1692

    PMXVF16GER2NP = 1693

    PMXVF16GER2PN = 1694

    PMXVF16GER2PP = 1695

    PMXVF16GER2W = 1696

    PMXVF16GER2WNN = 1697

    PMXVF16GER2WNP = 1698

    PMXVF16GER2WPN = 1699

    PMXVF16GER2WPP = 1700

    PMXVF32GER = 1701

    PMXVF32GERNN = 1702

    PMXVF32GERNP = 1703

    PMXVF32GERPN = 1704

    PMXVF32GERPP = 1705

    PMXVF32GERW = 1706

    PMXVF32GERWNN = 1707

    PMXVF32GERWNP = 1708

    PMXVF32GERWPN = 1709

    PMXVF32GERWPP = 1710

    PMXVF64GER = 1711

    PMXVF64GERNN = 1712

    PMXVF64GERNP = 1713

    PMXVF64GERPN = 1714

    PMXVF64GERPP = 1715

    PMXVF64GERW = 1716

    PMXVF64GERWNN = 1717

    PMXVF64GERWNP = 1718

    PMXVF64GERWPN = 1719

    PMXVF64GERWPP = 1720

    PMXVI16GER2 = 1721

    PMXVI16GER2PP = 1722

    PMXVI16GER2S = 1723

    PMXVI16GER2SPP = 1724

    PMXVI16GER2SW = 1725

    PMXVI16GER2SWPP = 1726

    PMXVI16GER2W = 1727

    PMXVI16GER2WPP = 1728

    PMXVI4GER8 = 1729

    PMXVI4GER8PP = 1730

    PMXVI4GER8W = 1731

    PMXVI4GER8WPP = 1732

    PMXVI8GER4 = 1733

    PMXVI8GER4PP = 1734

    PMXVI8GER4SPP = 1735

    PMXVI8GER4W = 1736

    PMXVI8GER4WPP = 1737

    PMXVI8GER4WSPP = 1738

    POPCNTB = 1739

    POPCNTB8 = 1740

    POPCNTD = 1741

    POPCNTW = 1742

    PPC32GOT = 1743

    PPC32PICGOT = 1744

    PREPARE_PROBED_ALLOCA_32 = 1745

    PREPARE_PROBED_ALLOCA_64 = 1746

    PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_32 = 1747

    PREPARE_PROBED_ALLOCA_NEGSIZE_SAME_REG_64 = 1748

    PROBED_ALLOCA_32 = 1749

    PROBED_ALLOCA_64 = 1750

    PROBED_STACKALLOC_32 = 1751

    PROBED_STACKALLOC_64 = 1752

    PSTB = 1753

    PSTB8 = 1754

    PSTB8nopc = 1755

    PSTB8onlypc = 1756

    PSTB8pc = 1757

    PSTBnopc = 1758

    PSTBonlypc = 1759

    PSTBpc = 1760

    PSTD = 1761

    PSTDnopc = 1762

    PSTDonlypc = 1763

    PSTDpc = 1764

    PSTFD = 1765

    PSTFDnopc = 1766

    PSTFDonlypc = 1767

    PSTFDpc = 1768

    PSTFS = 1769

    PSTFSnopc = 1770

    PSTFSonlypc = 1771

    PSTFSpc = 1772

    PSTH = 1773

    PSTH8 = 1774

    PSTH8nopc = 1775

    PSTH8onlypc = 1776

    PSTH8pc = 1777

    PSTHnopc = 1778

    PSTHonlypc = 1779

    PSTHpc = 1780

    PSTW = 1781

    PSTW8 = 1782

    PSTW8nopc = 1783

    PSTW8onlypc = 1784

    PSTW8pc = 1785

    PSTWnopc = 1786

    PSTWonlypc = 1787

    PSTWpc = 1788

    PSTXSD = 1789

    PSTXSDnopc = 1790

    PSTXSDonlypc = 1791

    PSTXSDpc = 1792

    PSTXSSP = 1793

    PSTXSSPnopc = 1794

    PSTXSSPonlypc = 1795

    PSTXSSPpc = 1796

    PSTXV = 1797

    PSTXVP = 1798

    PSTXVPnopc = 1799

    PSTXVPonlypc = 1800

    PSTXVPpc = 1801

    PSTXVnopc = 1802

    PSTXVonlypc = 1803

    PSTXVpc = 1804

    PseudoEIEIO = 1805

    RESTORE_ACC = 1806

    RESTORE_CR = 1807

    RESTORE_CRBIT = 1808

    RESTORE_QUADWORD = 1809

    RESTORE_UACC = 1810

    RESTORE_WACC = 1811

    RFCI = 1812

    RFDI = 1813

    RFEBB = 1814

    RFI = 1815

    RFID = 1816

    RFMCI = 1817

    RLDCL = 1818

    RLDCL_rec = 1819

    RLDCR = 1820

    RLDCR_rec = 1821

    RLDIC = 1822

    RLDICL = 1823

    RLDICL_32 = 1824

    RLDICL_32_64 = 1825

    RLDICL_32_rec = 1826

    RLDICL_rec = 1827

    RLDICR = 1828

    RLDICR_32 = 1829

    RLDICR_rec = 1830

    RLDIC_rec = 1831

    RLDIMI = 1832

    RLDIMI_rec = 1833

    RLWIMI = 1834

    RLWIMI8 = 1835

    RLWIMI8_rec = 1836

    RLWIMI_rec = 1837

    RLWINM = 1838

    RLWINM8 = 1839

    RLWINM8_rec = 1840

    RLWINM_rec = 1841

    RLWNM = 1842

    RLWNM8 = 1843

    RLWNM8_rec = 1844

    RLWNM_rec = 1845

    ReadTB = 1846

    SC = 1847

    SCV = 1848

    SELECT_CC_F16 = 1849

    SELECT_CC_F4 = 1850

    SELECT_CC_F8 = 1851

    SELECT_CC_I4 = 1852

    SELECT_CC_I8 = 1853

    SELECT_CC_SPE = 1854

    SELECT_CC_SPE4 = 1855

    SELECT_CC_VRRC = 1856

    SELECT_CC_VSFRC = 1857

    SELECT_CC_VSRC = 1858

    SELECT_CC_VSSRC = 1859

    SELECT_F16 = 1860

    SELECT_F4 = 1861

    SELECT_F8 = 1862

    SELECT_I4 = 1863

    SELECT_I8 = 1864

    SELECT_SPE = 1865

    SELECT_SPE4 = 1866

    SELECT_VRRC = 1867

    SELECT_VSFRC = 1868

    SELECT_VSRC = 1869

    SELECT_VSSRC = 1870

    SETB = 1871

    SETB8 = 1872

    SETBC = 1873

    SETBC8 = 1874

    SETBCR = 1875

    SETBCR8 = 1876

    SETFLM = 1877

    SETNBC = 1878

    SETNBC8 = 1879

    SETNBCR = 1880

    SETNBCR8 = 1881

    SETRND = 1882

    SETRNDi = 1883

    SLBFEE_rec = 1884

    SLBIA = 1885

    SLBIE = 1886

    SLBIEG = 1887

    SLBMFEE = 1888

    SLBMFEV = 1889

    SLBMTE = 1890

    SLBSYNC = 1891

    SLD = 1892

    SLD_rec = 1893

    SLW = 1894

    SLW8 = 1895

    SLW8_rec = 1896

    SLW_rec = 1897

    SPELWZ = 1898

    SPELWZX = 1899

    SPESTW = 1900

    SPESTWX = 1901

    SPILL_ACC = 1902

    SPILL_CR = 1903

    SPILL_CRBIT = 1904

    SPILL_QUADWORD = 1905

    SPILL_UACC = 1906

    SPILL_WACC = 1907

    SPLIT_QUADWORD = 1908

    SRAD = 1909

    SRADI = 1910

    SRADI_32 = 1911

    SRADI_rec = 1912

    SRAD_rec = 1913

    SRAW = 1914

    SRAW8 = 1915

    SRAW8_rec = 1916

    SRAWI = 1917

    SRAWI8 = 1918

    SRAWI8_rec = 1919

    SRAWI_rec = 1920

    SRAW_rec = 1921

    SRD = 1922

    SRD_rec = 1923

    SRW = 1924

    SRW8 = 1925

    SRW8_rec = 1926

    SRW_rec = 1927

    STB = 1928

    STB8 = 1929

    STBCIX = 1930

    STBCX = 1931

    STBEPX = 1932

    STBU = 1933

    STBU8 = 1934

    STBUX = 1935

    STBUX8 = 1936

    STBX = 1937

    STBX8 = 1938

    STBXTLS = 1939

    STBXTLS_32 = 1941

    STD = 1942

    STDAT = 1943

    STDBRX = 1944

    STDCIX = 1945

    STDCX = 1946

    STDU = 1947

    STDUX = 1948

    STDX = 1949

    STDXTLS = 1950

    STFD = 1952

    STFDEPX = 1953

    STFDU = 1954

    STFDUX = 1955

    STFDX = 1956

    STFDXTLS = 1957

    STFIWX = 1959

    STFS = 1960

    STFSU = 1961

    STFSUX = 1962

    STFSX = 1963

    STFSXTLS = 1964

    STH = 1966

    STH8 = 1967

    STHBRX = 1968

    STHCIX = 1969

    STHCX = 1970

    STHEPX = 1971

    STHU = 1972

    STHU8 = 1973

    STHUX = 1974

    STHUX8 = 1975

    STHX = 1976

    STHX8 = 1977

    STHXTLS = 1978

    STHXTLS_32 = 1980

    STMW = 1981

    STOP = 1982

    STQ = 1983

    STQCX = 1984

    STQX_PSEUDO = 1985

    STSWI = 1986

    STVEBX = 1987

    STVEHX = 1988

    STVEWX = 1989

    STVX = 1990

    STVXL = 1991

    STW = 1992

    STW8 = 1993

    STWAT = 1994

    STWBRX = 1995

    STWCIX = 1996

    STWCX = 1997

    STWEPX = 1998

    STWU = 1999

    STWU8 = 2000

    STWUX = 2001

    STWUX8 = 2002

    STWX = 2003

    STWX8 = 2004

    STWXTLS = 2005

    STWXTLS_32 = 2007

    STXSD = 2008

    STXSDX = 2009

    STXSIBX = 2010

    STXSIBXv = 2011

    STXSIHX = 2012

    STXSIHXv = 2013

    STXSIWX = 2014

    STXSSP = 2015

    STXSSPX = 2016

    STXV = 2017

    STXVB16X = 2018

    STXVD2X = 2019

    STXVH8X = 2020

    STXVL = 2021

    STXVLL = 2022

    STXVP = 2023

    STXVPRL = 2024

    STXVPRLL = 2025

    STXVPX = 2026

    STXVRBX = 2027

    STXVRDX = 2028

    STXVRHX = 2029

    STXVRL = 2030

    STXVRLL = 2031

    STXVRWX = 2032

    STXVW4X = 2033

    STXVX = 2034

    SUBF = 2035

    SUBF8 = 2036

    SUBF8O = 2037

    SUBF8O_rec = 2038

    SUBF8_rec = 2039

    SUBFC = 2040

    SUBFC8 = 2041

    SUBFC8O = 2042

    SUBFC8O_rec = 2043

    SUBFC8_rec = 2044

    SUBFCO = 2045

    SUBFCO_rec = 2046

    SUBFC_rec = 2047

    SUBFE = 2048

    SUBFE8 = 2049

    SUBFE8O = 2050

    SUBFE8O_rec = 2051

    SUBFE8_rec = 2052

    SUBFEO = 2053

    SUBFEO_rec = 2054

    SUBFE_rec = 2055

    SUBFIC = 2056

    SUBFIC8 = 2057

    SUBFME = 2058

    SUBFME8 = 2059

    SUBFME8O = 2060

    SUBFME8O_rec = 2061

    SUBFME8_rec = 2062

    SUBFMEO = 2063

    SUBFMEO_rec = 2064

    SUBFME_rec = 2065

    SUBFO = 2066

    SUBFO_rec = 2067

    SUBFUS = 2068

    SUBFUS_rec = 2069

    SUBFZE = 2070

    SUBFZE8 = 2071

    SUBFZE8O = 2072

    SUBFZE8O_rec = 2073

    SUBFZE8_rec = 2074

    SUBFZEO = 2075

    SUBFZEO_rec = 2076

    SUBFZE_rec = 2077

    SUBF_rec = 2078

    SYNC = 2079

    SYNCP10 = 2080

    TABORT = 2081

    TABORTDC = 2082

    TABORTDCI = 2083

    TABORTWC = 2084

    TABORTWCI = 2085

    TAILB = 2086

    TAILB8 = 2087

    TAILBA = 2088

    TAILBA8 = 2089

    TAILBCTR = 2090

    TAILBCTR8 = 2091

    TBEGIN = 2092

    TBEGIN_RET = 2093

    TCHECK = 2094

    TCHECK_RET = 2095

    TCRETURNai = 2096

    TCRETURNai8 = 2097

    TCRETURNdi = 2098

    TCRETURNdi8 = 2099

    TCRETURNri = 2100

    TCRETURNri8 = 2101

    TD = 2102

    TDI = 2103

    TEND = 2104

    TLBIA = 2105

    TLBIE = 2106

    TLBIEL = 2107

    TLBILX = 2108

    TLBIVAX = 2109

    TLBLD = 2110

    TLBLI = 2111

    TLBRE = 2112

    TLBRE2 = 2113

    TLBSX = 2114

    TLBSX2 = 2115

    TLBSX2D = 2116

    TLBSYNC = 2117

    TLBWE = 2118

    TLBWE2 = 2119

    TLSGDAIX = 2120

    TLSGDAIX8 = 2121

    TLSLDAIX = 2122

    TLSLDAIX8 = 2123

    TRAP = 2124

    TRECHKPT = 2125

    TRECLAIM = 2126

    TSR = 2127

    TW = 2128

    TWI = 2129

    UNENCODED_NOP = 2130

    UpdateGBR = 2131

    VABSDUB = 2132

    VABSDUH = 2133

    VABSDUW = 2134

    VADDCUQ = 2135

    VADDCUW = 2136

    VADDECUQ = 2137

    VADDEUQM = 2138

    VADDFP = 2139

    VADDSBS = 2140

    VADDSHS = 2141

    VADDSWS = 2142

    VADDUBM = 2143

    VADDUBS = 2144

    VADDUDM = 2145

    VADDUHM = 2146

    VADDUHS = 2147

    VADDUQM = 2148

    VADDUWM = 2149

    VADDUWS = 2150

    VAND = 2151

    VANDC = 2152

    VAVGSB = 2153

    VAVGSH = 2154

    VAVGSW = 2155

    VAVGUB = 2156

    VAVGUH = 2157

    VAVGUW = 2158

    VBPERMD = 2159

    VBPERMQ = 2160

    VCFSX = 2161

    VCFSX_0 = 2162

    VCFUGED = 2163

    VCFUX = 2164

    VCFUX_0 = 2165

    VCIPHER = 2166

    VCIPHERLAST = 2167

    VCLRLB = 2168

    VCLRRB = 2169

    VCLZB = 2170

    VCLZD = 2171

    VCLZDM = 2172

    VCLZH = 2173

    VCLZLSBB = 2174

    VCLZW = 2175

    VCMPBFP = 2176

    VCMPBFP_rec = 2177

    VCMPEQFP = 2178

    VCMPEQFP_rec = 2179

    VCMPEQUB = 2180

    VCMPEQUB_rec = 2181

    VCMPEQUD = 2182

    VCMPEQUD_rec = 2183

    VCMPEQUH = 2184

    VCMPEQUH_rec = 2185

    VCMPEQUQ = 2186

    VCMPEQUQ_rec = 2187

    VCMPEQUW = 2188

    VCMPEQUW_rec = 2189

    VCMPGEFP = 2190

    VCMPGEFP_rec = 2191

    VCMPGTFP = 2192

    VCMPGTFP_rec = 2193

    VCMPGTSB = 2194

    VCMPGTSB_rec = 2195

    VCMPGTSD = 2196

    VCMPGTSD_rec = 2197

    VCMPGTSH = 2198

    VCMPGTSH_rec = 2199

    VCMPGTSQ = 2200

    VCMPGTSQ_rec = 2201

    VCMPGTSW = 2202

    VCMPGTSW_rec = 2203

    VCMPGTUB = 2204

    VCMPGTUB_rec = 2205

    VCMPGTUD = 2206

    VCMPGTUD_rec = 2207

    VCMPGTUH = 2208

    VCMPGTUH_rec = 2209

    VCMPGTUQ = 2210

    VCMPGTUQ_rec = 2211

    VCMPGTUW = 2212

    VCMPGTUW_rec = 2213

    VCMPNEB = 2214

    VCMPNEB_rec = 2215

    VCMPNEH = 2216

    VCMPNEH_rec = 2217

    VCMPNEW = 2218

    VCMPNEW_rec = 2219

    VCMPNEZB = 2220

    VCMPNEZB_rec = 2221

    VCMPNEZH = 2222

    VCMPNEZH_rec = 2223

    VCMPNEZW = 2224

    VCMPNEZW_rec = 2225

    VCMPSQ = 2226

    VCMPUQ = 2227

    VCNTMBB = 2228

    VCNTMBD = 2229

    VCNTMBH = 2230

    VCNTMBW = 2231

    VCTSXS = 2232

    VCTSXS_0 = 2233

    VCTUXS = 2234

    VCTUXS_0 = 2235

    VCTZB = 2236

    VCTZD = 2237

    VCTZDM = 2238

    VCTZH = 2239

    VCTZLSBB = 2240

    VCTZW = 2241

    VDIVESD = 2242

    VDIVESQ = 2243

    VDIVESW = 2244

    VDIVEUD = 2245

    VDIVEUQ = 2246

    VDIVEUW = 2247

    VDIVSD = 2248

    VDIVSQ = 2249

    VDIVSW = 2250

    VDIVUD = 2251

    VDIVUQ = 2252

    VDIVUW = 2253

    VEQV = 2254

    VEXPANDBM = 2255

    VEXPANDDM = 2256

    VEXPANDHM = 2257

    VEXPANDQM = 2258

    VEXPANDWM = 2259

    VEXPTEFP = 2260

    VEXTDDVLX = 2261

    VEXTDDVRX = 2262

    VEXTDUBVLX = 2263

    VEXTDUBVRX = 2264

    VEXTDUHVLX = 2265

    VEXTDUHVRX = 2266

    VEXTDUWVLX = 2267

    VEXTDUWVRX = 2268

    VEXTRACTBM = 2269

    VEXTRACTD = 2270

    VEXTRACTDM = 2271

    VEXTRACTHM = 2272

    VEXTRACTQM = 2273

    VEXTRACTUB = 2274

    VEXTRACTUH = 2275

    VEXTRACTUW = 2276

    VEXTRACTWM = 2277

    VEXTSB2D = 2278

    VEXTSB2Ds = 2279

    VEXTSB2W = 2280

    VEXTSB2Ws = 2281

    VEXTSD2Q = 2282

    VEXTSH2D = 2283

    VEXTSH2Ds = 2284

    VEXTSH2W = 2285

    VEXTSH2Ws = 2286

    VEXTSW2D = 2287

    VEXTSW2Ds = 2288

    VEXTUBLX = 2289

    VEXTUBRX = 2290

    VEXTUHLX = 2291

    VEXTUHRX = 2292

    VEXTUWLX = 2293

    VEXTUWRX = 2294

    VGBBD = 2295

    VGNB = 2296

    VINSBLX = 2297

    VINSBRX = 2298

    VINSBVLX = 2299

    VINSBVRX = 2300

    VINSD = 2301

    VINSDLX = 2302

    VINSDRX = 2303

    VINSERTB = 2304

    VINSERTD = 2305

    VINSERTH = 2306

    VINSERTW = 2307

    VINSHLX = 2308

    VINSHRX = 2309

    VINSHVLX = 2310

    VINSHVRX = 2311

    VINSW = 2312

    VINSWLX = 2313

    VINSWRX = 2314

    VINSWVLX = 2315

    VINSWVRX = 2316

    VLOGEFP = 2317

    VMADDFP = 2318

    VMAXFP = 2319

    VMAXSB = 2320

    VMAXSD = 2321

    VMAXSH = 2322

    VMAXSW = 2323

    VMAXUB = 2324

    VMAXUD = 2325

    VMAXUH = 2326

    VMAXUW = 2327

    VMHADDSHS = 2328

    VMHRADDSHS = 2329

    VMINFP = 2330

    VMINSB = 2331

    VMINSD = 2332

    VMINSH = 2333

    VMINSW = 2334

    VMINUB = 2335

    VMINUD = 2336

    VMINUH = 2337

    VMINUW = 2338

    VMLADDUHM = 2339

    VMODSD = 2340

    VMODSQ = 2341

    VMODSW = 2342

    VMODUD = 2343

    VMODUQ = 2344

    VMODUW = 2345

    VMRGEW = 2346

    VMRGHB = 2347

    VMRGHH = 2348

    VMRGHW = 2349

    VMRGLB = 2350

    VMRGLH = 2351

    VMRGLW = 2352

    VMRGOW = 2353

    VMSUMCUD = 2354

    VMSUMMBM = 2355

    VMSUMSHM = 2356

    VMSUMSHS = 2357

    VMSUMUBM = 2358

    VMSUMUDM = 2359

    VMSUMUHM = 2360

    VMSUMUHS = 2361

    VMUL10CUQ = 2362

    VMUL10ECUQ = 2363

    VMUL10EUQ = 2364

    VMUL10UQ = 2365

    VMULESB = 2366

    VMULESD = 2367

    VMULESH = 2368

    VMULESW = 2369

    VMULEUB = 2370

    VMULEUD = 2371

    VMULEUH = 2372

    VMULEUW = 2373

    VMULHSD = 2374

    VMULHSW = 2375

    VMULHUD = 2376

    VMULHUW = 2377

    VMULLD = 2378

    VMULOSB = 2379

    VMULOSD = 2380

    VMULOSH = 2381

    VMULOSW = 2382

    VMULOUB = 2383

    VMULOUD = 2384

    VMULOUH = 2385

    VMULOUW = 2386

    VMULUWM = 2387

    VNAND = 2388

    VNCIPHER = 2389

    VNCIPHERLAST = 2390

    VNEGD = 2391

    VNEGW = 2392

    VNMSUBFP = 2393

    VNOR = 2394

    VOR = 2395

    VORC = 2396

    VPDEPD = 2397

    VPERM = 2398

    VPERMR = 2399

    VPERMXOR = 2400

    VPEXTD = 2401

    VPKPX = 2402

    VPKSDSS = 2403

    VPKSDUS = 2404

    VPKSHSS = 2405

    VPKSHUS = 2406

    VPKSWSS = 2407

    VPKSWUS = 2408

    VPKUDUM = 2409

    VPKUDUS = 2410

    VPKUHUM = 2411

    VPKUHUS = 2412

    VPKUWUM = 2413

    VPKUWUS = 2414

    VPMSUMB = 2415

    VPMSUMD = 2416

    VPMSUMH = 2417

    VPMSUMW = 2418

    VPOPCNTB = 2419

    VPOPCNTD = 2420

    VPOPCNTH = 2421

    VPOPCNTW = 2422

    VPRTYBD = 2423

    VPRTYBQ = 2424

    VPRTYBW = 2425

    VREFP = 2426

    VRFIM = 2427

    VRFIN = 2428

    VRFIP = 2429

    VRFIZ = 2430

    VRLB = 2431

    VRLD = 2432

    VRLDMI = 2433

    VRLDNM = 2434

    VRLH = 2435

    VRLQ = 2436

    VRLQMI = 2437

    VRLQNM = 2438

    VRLW = 2439

    VRLWMI = 2440

    VRLWNM = 2441

    VRSQRTEFP = 2442

    VSBOX = 2443

    VSEL = 2444

    VSHASIGMAD = 2445

    VSHASIGMAW = 2446

    VSL = 2447

    VSLB = 2448

    VSLD = 2449

    VSLDBI = 2450

    VSLDOI = 2451

    VSLH = 2452

    VSLO = 2453

    VSLQ = 2454

    VSLV = 2455

    VSLW = 2456

    VSPLTB = 2457

    VSPLTBs = 2458

    VSPLTH = 2459

    VSPLTHs = 2460

    VSPLTISB = 2461

    VSPLTISH = 2462

    VSPLTISW = 2463

    VSPLTW = 2464

    VSR = 2465

    VSRAB = 2466

    VSRAD = 2467

    VSRAH = 2468

    VSRAQ = 2469

    VSRAW = 2470

    VSRB = 2471

    VSRD = 2472

    VSRDBI = 2473

    VSRH = 2474

    VSRO = 2475

    VSRQ = 2476

    VSRV = 2477

    VSRW = 2478

    VSTRIBL = 2479

    VSTRIBL_rec = 2480

    VSTRIBR = 2481

    VSTRIBR_rec = 2482

    VSTRIHL = 2483

    VSTRIHL_rec = 2484

    VSTRIHR = 2485

    VSTRIHR_rec = 2486

    VSUBCUQ = 2487

    VSUBCUW = 2488

    VSUBECUQ = 2489

    VSUBEUQM = 2490

    VSUBFP = 2491

    VSUBSBS = 2492

    VSUBSHS = 2493

    VSUBSWS = 2494

    VSUBUBM = 2495

    VSUBUBS = 2496

    VSUBUDM = 2497

    VSUBUHM = 2498

    VSUBUHS = 2499

    VSUBUQM = 2500

    VSUBUWM = 2501

    VSUBUWS = 2502

    VSUM2SWS = 2503

    VSUM4SBS = 2504

    VSUM4SHS = 2505

    VSUM4UBS = 2506

    VSUMSWS = 2507

    VUPKHPX = 2508

    VUPKHSB = 2509

    VUPKHSH = 2510

    VUPKHSW = 2511

    VUPKLPX = 2512

    VUPKLSB = 2513

    VUPKLSH = 2514

    VUPKLSW = 2515

    VXOR = 2516

    V_SET0 = 2517

    V_SET0B = 2518

    V_SET0H = 2519

    V_SETALLONES = 2520

    V_SETALLONESB = 2521

    V_SETALLONESH = 2522

    WAIT = 2523

    WAITP10 = 2524

    WRTEE = 2525

    WRTEEI = 2526

    XOR = 2527

    XOR8 = 2528

    XOR8_rec = 2529

    XORI = 2530

    XORI8 = 2531

    XORIS = 2532

    XORIS8 = 2533

    XOR_rec = 2534

    XSABSDP = 2535

    XSABSQP = 2536

    XSADDDP = 2537

    XSADDQP = 2538

    XSADDQPO = 2539

    XSADDSP = 2540

    XSCMPEQDP = 2541

    XSCMPEQQP = 2542

    XSCMPEXPDP = 2543

    XSCMPEXPQP = 2544

    XSCMPGEDP = 2545

    XSCMPGEQP = 2546

    XSCMPGTDP = 2547

    XSCMPGTQP = 2548

    XSCMPODP = 2549

    XSCMPOQP = 2550

    XSCMPUDP = 2551

    XSCMPUQP = 2552

    XSCPSGNDP = 2553

    XSCPSGNQP = 2554

    XSCVDPHP = 2555

    XSCVDPQP = 2556

    XSCVDPSP = 2557

    XSCVDPSPN = 2558

    XSCVDPSXDS = 2559

    XSCVDPSXDSs = 2560

    XSCVDPSXWS = 2561

    XSCVDPSXWSs = 2562

    XSCVDPUXDS = 2563

    XSCVDPUXDSs = 2564

    XSCVDPUXWS = 2565

    XSCVDPUXWSs = 2566

    XSCVHPDP = 2567

    XSCVQPDP = 2568

    XSCVQPDPO = 2569

    XSCVQPSDZ = 2570

    XSCVQPSQZ = 2571

    XSCVQPSWZ = 2572

    XSCVQPUDZ = 2573

    XSCVQPUQZ = 2574

    XSCVQPUWZ = 2575

    XSCVSDQP = 2576

    XSCVSPDP = 2577

    XSCVSPDPN = 2578

    XSCVSQQP = 2579

    XSCVSXDDP = 2580

    XSCVSXDSP = 2581

    XSCVUDQP = 2582

    XSCVUQQP = 2583

    XSCVUXDDP = 2584

    XSCVUXDSP = 2585

    XSDIVDP = 2586

    XSDIVQP = 2587

    XSDIVQPO = 2588

    XSDIVSP = 2589

    XSIEXPDP = 2590

    XSIEXPQP = 2591

    XSMADDADP = 2592

    XSMADDASP = 2593

    XSMADDMDP = 2594

    XSMADDMSP = 2595

    XSMADDQP = 2596

    XSMADDQPO = 2597

    XSMAXCDP = 2598

    XSMAXCQP = 2599

    XSMAXDP = 2600

    XSMAXJDP = 2601

    XSMINCDP = 2602

    XSMINCQP = 2603

    XSMINDP = 2604

    XSMINJDP = 2605

    XSMSUBADP = 2606

    XSMSUBASP = 2607

    XSMSUBMDP = 2608

    XSMSUBMSP = 2609

    XSMSUBQP = 2610

    XSMSUBQPO = 2611

    XSMULDP = 2612

    XSMULQP = 2613

    XSMULQPO = 2614

    XSMULSP = 2615

    XSNABSDP = 2616

    XSNABSDPs = 2617

    XSNABSQP = 2618

    XSNEGDP = 2619

    XSNEGQP = 2620

    XSNMADDADP = 2621

    XSNMADDASP = 2622

    XSNMADDMDP = 2623

    XSNMADDMSP = 2624

    XSNMADDQP = 2625

    XSNMADDQPO = 2626

    XSNMSUBADP = 2627

    XSNMSUBASP = 2628

    XSNMSUBMDP = 2629

    XSNMSUBMSP = 2630

    XSNMSUBQP = 2631

    XSNMSUBQPO = 2632

    XSRDPI = 2633

    XSRDPIC = 2634

    XSRDPIM = 2635

    XSRDPIP = 2636

    XSRDPIZ = 2637

    XSREDP = 2638

    XSRESP = 2639

    XSRQPI = 2640

    XSRQPIX = 2641

    XSRQPXP = 2642

    XSRSP = 2643

    XSRSQRTEDP = 2644

    XSRSQRTESP = 2645

    XSSQRTDP = 2646

    XSSQRTQP = 2647

    XSSQRTQPO = 2648

    XSSQRTSP = 2649

    XSSUBDP = 2650

    XSSUBQP = 2651

    XSSUBQPO = 2652

    XSSUBSP = 2653

    XSTDIVDP = 2654

    XSTSQRTDP = 2655

    XSTSTDCDP = 2656

    XSTSTDCQP = 2657

    XSTSTDCSP = 2658

    XSXEXPDP = 2659

    XSXEXPQP = 2660

    XSXSIGDP = 2661

    XSXSIGQP = 2662

    XVABSDP = 2663

    XVABSSP = 2664

    XVADDDP = 2665

    XVADDSP = 2666

    XVBF16GER2 = 2667

    XVBF16GER2NN = 2668

    XVBF16GER2NP = 2669

    XVBF16GER2PN = 2670

    XVBF16GER2PP = 2671

    XVBF16GER2W = 2672

    XVBF16GER2WNN = 2673

    XVBF16GER2WNP = 2674

    XVBF16GER2WPN = 2675

    XVBF16GER2WPP = 2676

    XVCMPEQDP = 2677

    XVCMPEQDP_rec = 2678

    XVCMPEQSP = 2679

    XVCMPEQSP_rec = 2680

    XVCMPGEDP = 2681

    XVCMPGEDP_rec = 2682

    XVCMPGESP = 2683

    XVCMPGESP_rec = 2684

    XVCMPGTDP = 2685

    XVCMPGTDP_rec = 2686

    XVCMPGTSP = 2687

    XVCMPGTSP_rec = 2688

    XVCPSGNDP = 2689

    XVCPSGNSP = 2690

    XVCVBF16SPN = 2691

    XVCVDPSP = 2692

    XVCVDPSXDS = 2693

    XVCVDPSXWS = 2694

    XVCVDPUXDS = 2695

    XVCVDPUXWS = 2696

    XVCVHPSP = 2697

    XVCVSPBF16 = 2698

    XVCVSPDP = 2699

    XVCVSPHP = 2700

    XVCVSPSXDS = 2701

    XVCVSPSXWS = 2702

    XVCVSPUXDS = 2703

    XVCVSPUXWS = 2704

    XVCVSXDDP = 2705

    XVCVSXDSP = 2706

    XVCVSXWDP = 2707

    XVCVSXWSP = 2708

    XVCVUXDDP = 2709

    XVCVUXDSP = 2710

    XVCVUXWDP = 2711

    XVCVUXWSP = 2712

    XVDIVDP = 2713

    XVDIVSP = 2714

    XVF16GER2 = 2715

    XVF16GER2NN = 2716

    XVF16GER2NP = 2717

    XVF16GER2PN = 2718

    XVF16GER2PP = 2719

    XVF16GER2W = 2720

    XVF16GER2WNN = 2721

    XVF16GER2WNP = 2722

    XVF16GER2WPN = 2723

    XVF16GER2WPP = 2724

    XVF32GER = 2725

    XVF32GERNN = 2726

    XVF32GERNP = 2727

    XVF32GERPN = 2728

    XVF32GERPP = 2729

    XVF32GERW = 2730

    XVF32GERWNN = 2731

    XVF32GERWNP = 2732

    XVF32GERWPN = 2733

    XVF32GERWPP = 2734

    XVF64GER = 2735

    XVF64GERNN = 2736

    XVF64GERNP = 2737

    XVF64GERPN = 2738

    XVF64GERPP = 2739

    XVF64GERW = 2740

    XVF64GERWNN = 2741

    XVF64GERWNP = 2742

    XVF64GERWPN = 2743

    XVF64GERWPP = 2744

    XVI16GER2 = 2745

    XVI16GER2PP = 2746

    XVI16GER2S = 2747

    XVI16GER2SPP = 2748

    XVI16GER2SW = 2749

    XVI16GER2SWPP = 2750

    XVI16GER2W = 2751

    XVI16GER2WPP = 2752

    XVI4GER8 = 2753

    XVI4GER8PP = 2754

    XVI4GER8W = 2755

    XVI4GER8WPP = 2756

    XVI8GER4 = 2757

    XVI8GER4PP = 2758

    XVI8GER4SPP = 2759

    XVI8GER4W = 2760

    XVI8GER4WPP = 2761

    XVI8GER4WSPP = 2762

    XVIEXPDP = 2763

    XVIEXPSP = 2764

    XVMADDADP = 2765

    XVMADDASP = 2766

    XVMADDMDP = 2767

    XVMADDMSP = 2768

    XVMAXDP = 2769

    XVMAXSP = 2770

    XVMINDP = 2771

    XVMINSP = 2772

    XVMSUBADP = 2773

    XVMSUBASP = 2774

    XVMSUBMDP = 2775

    XVMSUBMSP = 2776

    XVMULDP = 2777

    XVMULSP = 2778

    XVNABSDP = 2779

    XVNABSSP = 2780

    XVNEGDP = 2781

    XVNEGSP = 2782

    XVNMADDADP = 2783

    XVNMADDASP = 2784

    XVNMADDMDP = 2785

    XVNMADDMSP = 2786

    XVNMSUBADP = 2787

    XVNMSUBASP = 2788

    XVNMSUBMDP = 2789

    XVNMSUBMSP = 2790

    XVRDPI = 2791

    XVRDPIC = 2792

    XVRDPIM = 2793

    XVRDPIP = 2794

    XVRDPIZ = 2795

    XVREDP = 2796

    XVRESP = 2797

    XVRSPI = 2798

    XVRSPIC = 2799

    XVRSPIM = 2800

    XVRSPIP = 2801

    XVRSPIZ = 2802

    XVRSQRTEDP = 2803

    XVRSQRTESP = 2804

    XVSQRTDP = 2805

    XVSQRTSP = 2806

    XVSUBDP = 2807

    XVSUBSP = 2808

    XVTDIVDP = 2809

    XVTDIVSP = 2810

    XVTLSBB = 2811

    XVTSQRTDP = 2812

    XVTSQRTSP = 2813

    XVTSTDCDP = 2814

    XVTSTDCSP = 2815

    XVXEXPDP = 2816

    XVXEXPSP = 2817

    XVXSIGDP = 2818

    XVXSIGSP = 2819

    XXBLENDVB = 2820

    XXBLENDVD = 2821

    XXBLENDVH = 2822

    XXBLENDVW = 2823

    XXBRD = 2824

    XXBRH = 2825

    XXBRQ = 2826

    XXBRW = 2827

    XXEVAL = 2828

    XXEXTRACTUW = 2829

    XXGENPCVBM = 2830

    XXGENPCVDM = 2831

    XXGENPCVHM = 2832

    XXGENPCVWM = 2833

    XXINSERTW = 2834

    XXLAND = 2835

    XXLANDC = 2836

    XXLEQV = 2837

    XXLEQVOnes = 2838

    XXLNAND = 2839

    XXLNOR = 2840

    XXLOR = 2841

    XXLORC = 2842

    XXLORf = 2843

    XXLXOR = 2844

    XXLXORdpz = 2845

    XXLXORspz = 2846

    XXLXORz = 2847

    XXMFACC = 2848

    XXMFACCW = 2849

    XXMRGHW = 2850

    XXMRGLW = 2851

    XXMTACC = 2852

    XXMTACCW = 2853

    XXPERM = 2854

    XXPERMDI = 2855

    XXPERMDIs = 2856

    XXPERMR = 2857

    XXPERMX = 2858

    XXSEL = 2859

    XXSETACCZ = 2860

    XXSETACCZW = 2861

    XXSLDWI = 2862

    XXSLDWIs = 2863

    XXSPLTI32DX = 2864

    XXSPLTIB = 2865

    XXSPLTIDP = 2866

    XXSPLTIW = 2867

    XXSPLTW = 2868

    XXSPLTWs = 2869

    gBC = 2870

    gBCA = 2871

    gBCAat = 2872

    gBCCTR = 2873

    gBCCTRL = 2874

    gBCL = 2875

    gBCLA = 2876

    gBCLAat = 2877

    gBCLR = 2878

    gBCLRL = 2879

    gBCLat = 2880

    gBCat = 2881

    INSTRUCTION_LIST_END = 2882

class Instruction(lief.assembly.Instruction):
    @property
    def opcode(self) -> OPCODE: ...
