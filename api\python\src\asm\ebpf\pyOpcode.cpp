#include "asm/ebpf/init.hpp"

#include "LIEF/asm/ebpf/opcodes.hpp"

namespace LIEF::assembly::ebpf::py {
template<>
void create<LIEF::assembly::ebpf::OPCODE>(nb::module_& m) {
  nb::enum_<LIEF::assembly::ebpf::OPCODE> opcodes(m, "OPCODE");
  opcodes.value("PHI", LIEF::assembly::ebpf::OPCODE::PHI)
  .value("INLINEASM", LIEF::assembly::ebpf::OPCODE::INLINEASM)
  .value("INLINEASM_BR", LIEF::assembly::ebpf::OPCODE::INLINEASM_BR)
  .value("CFI_INSTRUCTION", LIEF::assembly::ebpf::OPCODE::CFI_INSTRUCTION)
  .value("EH_LABEL", LIEF::assembly::ebpf::OPCODE::EH_LABEL)
  .value("GC_LABEL", LIEF::assembly::ebpf::OPCODE::GC_LABEL)
  .value("ANNOTATION_LABEL", LIEF::assembly::ebpf::OPCODE::ANNOTATION_LABEL)
  .value("KILL", LIEF::assembly::ebpf::OPCODE::KILL)
  .value("EXTRACT_SUBREG", LIEF::assembly::ebpf::OPCODE::EXTRACT_SUBREG)
  .value("INSERT_SUBREG", LIEF::assembly::ebpf::OPCODE::INSERT_SUBREG)
  .value("IMPLICIT_DEF", LIEF::assembly::ebpf::OPCODE::IMPLICIT_DEF)
  .value("INIT_UNDEF", LIEF::assembly::ebpf::OPCODE::INIT_UNDEF)
  .value("SUBREG_TO_REG", LIEF::assembly::ebpf::OPCODE::SUBREG_TO_REG)
  .value("COPY_TO_REGCLASS", LIEF::assembly::ebpf::OPCODE::COPY_TO_REGCLASS)
  .value("DBG_VALUE", LIEF::assembly::ebpf::OPCODE::DBG_VALUE)
  .value("DBG_VALUE_LIST", LIEF::assembly::ebpf::OPCODE::DBG_VALUE_LIST)
  .value("DBG_INSTR_REF", LIEF::assembly::ebpf::OPCODE::DBG_INSTR_REF)
  .value("DBG_PHI", LIEF::assembly::ebpf::OPCODE::DBG_PHI)
  .value("DBG_LABEL", LIEF::assembly::ebpf::OPCODE::DBG_LABEL)
  .value("REG_SEQUENCE", LIEF::assembly::ebpf::OPCODE::REG_SEQUENCE)
  .value("COPY", LIEF::assembly::ebpf::OPCODE::COPY)
  .value("BUNDLE", LIEF::assembly::ebpf::OPCODE::BUNDLE)
  .value("LIFETIME_START", LIEF::assembly::ebpf::OPCODE::LIFETIME_START)
  .value("LIFETIME_END", LIEF::assembly::ebpf::OPCODE::LIFETIME_END)
  .value("PSEUDO_PROBE", LIEF::assembly::ebpf::OPCODE::PSEUDO_PROBE)
  .value("ARITH_FENCE", LIEF::assembly::ebpf::OPCODE::ARITH_FENCE)
  .value("STACKMAP", LIEF::assembly::ebpf::OPCODE::STACKMAP)
  .value("FENTRY_CALL", LIEF::assembly::ebpf::OPCODE::FENTRY_CALL)
  .value("PATCHPOINT", LIEF::assembly::ebpf::OPCODE::PATCHPOINT)
  .value("LOAD_STACK_GUARD", LIEF::assembly::ebpf::OPCODE::LOAD_STACK_GUARD)
  .value("PREALLOCATED_SETUP", LIEF::assembly::ebpf::OPCODE::PREALLOCATED_SETUP)
  .value("PREALLOCATED_ARG", LIEF::assembly::ebpf::OPCODE::PREALLOCATED_ARG)
  .value("STATEPOINT", LIEF::assembly::ebpf::OPCODE::STATEPOINT)
  .value("LOCAL_ESCAPE", LIEF::assembly::ebpf::OPCODE::LOCAL_ESCAPE)
  .value("FAULTING_OP", LIEF::assembly::ebpf::OPCODE::FAULTING_OP)
  .value("PATCHABLE_OP", LIEF::assembly::ebpf::OPCODE::PATCHABLE_OP)
  .value("PATCHABLE_FUNCTION_ENTER", LIEF::assembly::ebpf::OPCODE::PATCHABLE_FUNCTION_ENTER)
  .value("PATCHABLE_RET", LIEF::assembly::ebpf::OPCODE::PATCHABLE_RET)
  .value("PATCHABLE_FUNCTION_EXIT", LIEF::assembly::ebpf::OPCODE::PATCHABLE_FUNCTION_EXIT)
  .value("PATCHABLE_TAIL_CALL", LIEF::assembly::ebpf::OPCODE::PATCHABLE_TAIL_CALL)
  .value("PATCHABLE_EVENT_CALL", LIEF::assembly::ebpf::OPCODE::PATCHABLE_EVENT_CALL)
  .value("PATCHABLE_TYPED_EVENT_CALL", LIEF::assembly::ebpf::OPCODE::PATCHABLE_TYPED_EVENT_CALL)
  .value("ICALL_BRANCH_FUNNEL", LIEF::assembly::ebpf::OPCODE::ICALL_BRANCH_FUNNEL)
  .value("FAKE_USE", LIEF::assembly::ebpf::OPCODE::FAKE_USE)
  .value("MEMBARRIER", LIEF::assembly::ebpf::OPCODE::MEMBARRIER)
  .value("JUMP_TABLE_DEBUG_INFO", LIEF::assembly::ebpf::OPCODE::JUMP_TABLE_DEBUG_INFO)
  .value("CONVERGENCECTRL_ENTRY", LIEF::assembly::ebpf::OPCODE::CONVERGENCECTRL_ENTRY)
  .value("CONVERGENCECTRL_ANCHOR", LIEF::assembly::ebpf::OPCODE::CONVERGENCECTRL_ANCHOR)
  .value("CONVERGENCECTRL_LOOP", LIEF::assembly::ebpf::OPCODE::CONVERGENCECTRL_LOOP)
  .value("CONVERGENCECTRL_GLUE", LIEF::assembly::ebpf::OPCODE::CONVERGENCECTRL_GLUE)
  .value("G_ASSERT_SEXT", LIEF::assembly::ebpf::OPCODE::G_ASSERT_SEXT)
  .value("G_ASSERT_ZEXT", LIEF::assembly::ebpf::OPCODE::G_ASSERT_ZEXT)
  .value("G_ASSERT_ALIGN", LIEF::assembly::ebpf::OPCODE::G_ASSERT_ALIGN)
  .value("G_ADD", LIEF::assembly::ebpf::OPCODE::G_ADD)
  .value("G_SUB", LIEF::assembly::ebpf::OPCODE::G_SUB)
  .value("G_MUL", LIEF::assembly::ebpf::OPCODE::G_MUL)
  .value("G_SDIV", LIEF::assembly::ebpf::OPCODE::G_SDIV)
  .value("G_UDIV", LIEF::assembly::ebpf::OPCODE::G_UDIV)
  .value("G_SREM", LIEF::assembly::ebpf::OPCODE::G_SREM)
  .value("G_UREM", LIEF::assembly::ebpf::OPCODE::G_UREM)
  .value("G_SDIVREM", LIEF::assembly::ebpf::OPCODE::G_SDIVREM)
  .value("G_UDIVREM", LIEF::assembly::ebpf::OPCODE::G_UDIVREM)
  .value("G_AND", LIEF::assembly::ebpf::OPCODE::G_AND)
  .value("G_OR", LIEF::assembly::ebpf::OPCODE::G_OR)
  .value("G_XOR", LIEF::assembly::ebpf::OPCODE::G_XOR)
  .value("G_ABDS", LIEF::assembly::ebpf::OPCODE::G_ABDS)
  .value("G_ABDU", LIEF::assembly::ebpf::OPCODE::G_ABDU)
  .value("G_IMPLICIT_DEF", LIEF::assembly::ebpf::OPCODE::G_IMPLICIT_DEF)
  .value("G_PHI", LIEF::assembly::ebpf::OPCODE::G_PHI)
  .value("G_FRAME_INDEX", LIEF::assembly::ebpf::OPCODE::G_FRAME_INDEX)
  .value("G_GLOBAL_VALUE", LIEF::assembly::ebpf::OPCODE::G_GLOBAL_VALUE)
  .value("G_PTRAUTH_GLOBAL_VALUE", LIEF::assembly::ebpf::OPCODE::G_PTRAUTH_GLOBAL_VALUE)
  .value("G_CONSTANT_POOL", LIEF::assembly::ebpf::OPCODE::G_CONSTANT_POOL)
  .value("G_EXTRACT", LIEF::assembly::ebpf::OPCODE::G_EXTRACT)
  .value("G_UNMERGE_VALUES", LIEF::assembly::ebpf::OPCODE::G_UNMERGE_VALUES)
  .value("G_INSERT", LIEF::assembly::ebpf::OPCODE::G_INSERT)
  .value("G_MERGE_VALUES", LIEF::assembly::ebpf::OPCODE::G_MERGE_VALUES)
  .value("G_BUILD_VECTOR", LIEF::assembly::ebpf::OPCODE::G_BUILD_VECTOR)
  .value("G_BUILD_VECTOR_TRUNC", LIEF::assembly::ebpf::OPCODE::G_BUILD_VECTOR_TRUNC)
  .value("G_CONCAT_VECTORS", LIEF::assembly::ebpf::OPCODE::G_CONCAT_VECTORS)
  .value("G_PTRTOINT", LIEF::assembly::ebpf::OPCODE::G_PTRTOINT)
  .value("G_INTTOPTR", LIEF::assembly::ebpf::OPCODE::G_INTTOPTR)
  .value("G_BITCAST", LIEF::assembly::ebpf::OPCODE::G_BITCAST)
  .value("G_FREEZE", LIEF::assembly::ebpf::OPCODE::G_FREEZE)
  .value("G_CONSTANT_FOLD_BARRIER", LIEF::assembly::ebpf::OPCODE::G_CONSTANT_FOLD_BARRIER)
  .value("G_INTRINSIC_FPTRUNC_ROUND", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_FPTRUNC_ROUND)
  .value("G_INTRINSIC_TRUNC", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_TRUNC)
  .value("G_INTRINSIC_ROUND", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_ROUND)
  .value("G_INTRINSIC_LRINT", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_LRINT)
  .value("G_INTRINSIC_LLRINT", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_LLRINT)
  .value("G_INTRINSIC_ROUNDEVEN", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_ROUNDEVEN)
  .value("G_READCYCLECOUNTER", LIEF::assembly::ebpf::OPCODE::G_READCYCLECOUNTER)
  .value("G_READSTEADYCOUNTER", LIEF::assembly::ebpf::OPCODE::G_READSTEADYCOUNTER)
  .value("G_LOAD", LIEF::assembly::ebpf::OPCODE::G_LOAD)
  .value("G_SEXTLOAD", LIEF::assembly::ebpf::OPCODE::G_SEXTLOAD)
  .value("G_ZEXTLOAD", LIEF::assembly::ebpf::OPCODE::G_ZEXTLOAD)
  .value("G_INDEXED_LOAD", LIEF::assembly::ebpf::OPCODE::G_INDEXED_LOAD)
  .value("G_INDEXED_SEXTLOAD", LIEF::assembly::ebpf::OPCODE::G_INDEXED_SEXTLOAD)
  .value("G_INDEXED_ZEXTLOAD", LIEF::assembly::ebpf::OPCODE::G_INDEXED_ZEXTLOAD)
  .value("G_STORE", LIEF::assembly::ebpf::OPCODE::G_STORE)
  .value("G_INDEXED_STORE", LIEF::assembly::ebpf::OPCODE::G_INDEXED_STORE)
  .value("G_ATOMIC_CMPXCHG_WITH_SUCCESS", LIEF::assembly::ebpf::OPCODE::G_ATOMIC_CMPXCHG_WITH_SUCCESS)
  .value("G_ATOMIC_CMPXCHG", LIEF::assembly::ebpf::OPCODE::G_ATOMIC_CMPXCHG)
  .value("G_ATOMICRMW_XCHG", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_XCHG)
  .value("G_ATOMICRMW_ADD", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_ADD)
  .value("G_ATOMICRMW_SUB", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_SUB)
  .value("G_ATOMICRMW_AND", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_AND)
  .value("G_ATOMICRMW_NAND", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_NAND)
  .value("G_ATOMICRMW_OR", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_OR)
  .value("G_ATOMICRMW_XOR", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_XOR)
  .value("G_ATOMICRMW_MAX", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_MAX)
  .value("G_ATOMICRMW_MIN", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_MIN)
  .value("G_ATOMICRMW_UMAX", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_UMAX)
  .value("G_ATOMICRMW_UMIN", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_UMIN)
  .value("G_ATOMICRMW_FADD", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_FADD)
  .value("G_ATOMICRMW_FSUB", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_FSUB)
  .value("G_ATOMICRMW_FMAX", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_FMAX)
  .value("G_ATOMICRMW_FMIN", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_FMIN)
  .value("G_ATOMICRMW_UINC_WRAP", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_UINC_WRAP)
  .value("G_ATOMICRMW_UDEC_WRAP", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_UDEC_WRAP)
  .value("G_ATOMICRMW_USUB_COND", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_USUB_COND)
  .value("G_ATOMICRMW_USUB_SAT", LIEF::assembly::ebpf::OPCODE::G_ATOMICRMW_USUB_SAT)
  .value("G_FENCE", LIEF::assembly::ebpf::OPCODE::G_FENCE)
  .value("G_PREFETCH", LIEF::assembly::ebpf::OPCODE::G_PREFETCH)
  .value("G_BRCOND", LIEF::assembly::ebpf::OPCODE::G_BRCOND)
  .value("G_BRINDIRECT", LIEF::assembly::ebpf::OPCODE::G_BRINDIRECT)
  .value("G_INVOKE_REGION_START", LIEF::assembly::ebpf::OPCODE::G_INVOKE_REGION_START)
  .value("G_INTRINSIC", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC)
  .value("G_INTRINSIC_W_SIDE_EFFECTS", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_W_SIDE_EFFECTS)
  .value("G_INTRINSIC_CONVERGENT", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_CONVERGENT)
  .value("G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS", LIEF::assembly::ebpf::OPCODE::G_INTRINSIC_CONVERGENT_W_SIDE_EFFECTS)
  .value("G_ANYEXT", LIEF::assembly::ebpf::OPCODE::G_ANYEXT)
  .value("G_TRUNC", LIEF::assembly::ebpf::OPCODE::G_TRUNC)
  .value("G_CONSTANT", LIEF::assembly::ebpf::OPCODE::G_CONSTANT)
  .value("G_FCONSTANT", LIEF::assembly::ebpf::OPCODE::G_FCONSTANT)
  .value("G_VASTART", LIEF::assembly::ebpf::OPCODE::G_VASTART)
  .value("G_VAARG", LIEF::assembly::ebpf::OPCODE::G_VAARG)
  .value("G_SEXT", LIEF::assembly::ebpf::OPCODE::G_SEXT)
  .value("G_SEXT_INREG", LIEF::assembly::ebpf::OPCODE::G_SEXT_INREG)
  .value("G_ZEXT", LIEF::assembly::ebpf::OPCODE::G_ZEXT)
  .value("G_SHL", LIEF::assembly::ebpf::OPCODE::G_SHL)
  .value("G_LSHR", LIEF::assembly::ebpf::OPCODE::G_LSHR)
  .value("G_ASHR", LIEF::assembly::ebpf::OPCODE::G_ASHR)
  .value("G_FSHL", LIEF::assembly::ebpf::OPCODE::G_FSHL)
  .value("G_FSHR", LIEF::assembly::ebpf::OPCODE::G_FSHR)
  .value("G_ROTR", LIEF::assembly::ebpf::OPCODE::G_ROTR)
  .value("G_ROTL", LIEF::assembly::ebpf::OPCODE::G_ROTL)
  .value("G_ICMP", LIEF::assembly::ebpf::OPCODE::G_ICMP)
  .value("G_FCMP", LIEF::assembly::ebpf::OPCODE::G_FCMP)
  .value("G_SCMP", LIEF::assembly::ebpf::OPCODE::G_SCMP)
  .value("G_UCMP", LIEF::assembly::ebpf::OPCODE::G_UCMP)
  .value("G_SELECT", LIEF::assembly::ebpf::OPCODE::G_SELECT)
  .value("G_UADDO", LIEF::assembly::ebpf::OPCODE::G_UADDO)
  .value("G_UADDE", LIEF::assembly::ebpf::OPCODE::G_UADDE)
  .value("G_USUBO", LIEF::assembly::ebpf::OPCODE::G_USUBO)
  .value("G_USUBE", LIEF::assembly::ebpf::OPCODE::G_USUBE)
  .value("G_SADDO", LIEF::assembly::ebpf::OPCODE::G_SADDO)
  .value("G_SADDE", LIEF::assembly::ebpf::OPCODE::G_SADDE)
  .value("G_SSUBO", LIEF::assembly::ebpf::OPCODE::G_SSUBO)
  .value("G_SSUBE", LIEF::assembly::ebpf::OPCODE::G_SSUBE)
  .value("G_UMULO", LIEF::assembly::ebpf::OPCODE::G_UMULO)
  .value("G_SMULO", LIEF::assembly::ebpf::OPCODE::G_SMULO)
  .value("G_UMULH", LIEF::assembly::ebpf::OPCODE::G_UMULH)
  .value("G_SMULH", LIEF::assembly::ebpf::OPCODE::G_SMULH)
  .value("G_UADDSAT", LIEF::assembly::ebpf::OPCODE::G_UADDSAT)
  .value("G_SADDSAT", LIEF::assembly::ebpf::OPCODE::G_SADDSAT)
  .value("G_USUBSAT", LIEF::assembly::ebpf::OPCODE::G_USUBSAT)
  .value("G_SSUBSAT", LIEF::assembly::ebpf::OPCODE::G_SSUBSAT)
  .value("G_USHLSAT", LIEF::assembly::ebpf::OPCODE::G_USHLSAT)
  .value("G_SSHLSAT", LIEF::assembly::ebpf::OPCODE::G_SSHLSAT)
  .value("G_SMULFIX", LIEF::assembly::ebpf::OPCODE::G_SMULFIX)
  .value("G_UMULFIX", LIEF::assembly::ebpf::OPCODE::G_UMULFIX)
  .value("G_SMULFIXSAT", LIEF::assembly::ebpf::OPCODE::G_SMULFIXSAT)
  .value("G_UMULFIXSAT", LIEF::assembly::ebpf::OPCODE::G_UMULFIXSAT)
  .value("G_SDIVFIX", LIEF::assembly::ebpf::OPCODE::G_SDIVFIX)
  .value("G_UDIVFIX", LIEF::assembly::ebpf::OPCODE::G_UDIVFIX)
  .value("G_SDIVFIXSAT", LIEF::assembly::ebpf::OPCODE::G_SDIVFIXSAT)
  .value("G_UDIVFIXSAT", LIEF::assembly::ebpf::OPCODE::G_UDIVFIXSAT)
  .value("G_FADD", LIEF::assembly::ebpf::OPCODE::G_FADD)
  .value("G_FSUB", LIEF::assembly::ebpf::OPCODE::G_FSUB)
  .value("G_FMUL", LIEF::assembly::ebpf::OPCODE::G_FMUL)
  .value("G_FMA", LIEF::assembly::ebpf::OPCODE::G_FMA)
  .value("G_FMAD", LIEF::assembly::ebpf::OPCODE::G_FMAD)
  .value("G_FDIV", LIEF::assembly::ebpf::OPCODE::G_FDIV)
  .value("G_FREM", LIEF::assembly::ebpf::OPCODE::G_FREM)
  .value("G_FPOW", LIEF::assembly::ebpf::OPCODE::G_FPOW)
  .value("G_FPOWI", LIEF::assembly::ebpf::OPCODE::G_FPOWI)
  .value("G_FEXP", LIEF::assembly::ebpf::OPCODE::G_FEXP)
  .value("G_FEXP2", LIEF::assembly::ebpf::OPCODE::G_FEXP2)
  .value("G_FEXP10", LIEF::assembly::ebpf::OPCODE::G_FEXP10)
  .value("G_FLOG", LIEF::assembly::ebpf::OPCODE::G_FLOG)
  .value("G_FLOG2", LIEF::assembly::ebpf::OPCODE::G_FLOG2)
  .value("G_FLOG10", LIEF::assembly::ebpf::OPCODE::G_FLOG10)
  .value("G_FLDEXP", LIEF::assembly::ebpf::OPCODE::G_FLDEXP)
  .value("G_FFREXP", LIEF::assembly::ebpf::OPCODE::G_FFREXP)
  .value("G_FNEG", LIEF::assembly::ebpf::OPCODE::G_FNEG)
  .value("G_FPEXT", LIEF::assembly::ebpf::OPCODE::G_FPEXT)
  .value("G_FPTRUNC", LIEF::assembly::ebpf::OPCODE::G_FPTRUNC)
  .value("G_FPTOSI", LIEF::assembly::ebpf::OPCODE::G_FPTOSI)
  .value("G_FPTOUI", LIEF::assembly::ebpf::OPCODE::G_FPTOUI)
  .value("G_SITOFP", LIEF::assembly::ebpf::OPCODE::G_SITOFP)
  .value("G_UITOFP", LIEF::assembly::ebpf::OPCODE::G_UITOFP)
  .value("G_FPTOSI_SAT", LIEF::assembly::ebpf::OPCODE::G_FPTOSI_SAT)
  .value("G_FPTOUI_SAT", LIEF::assembly::ebpf::OPCODE::G_FPTOUI_SAT)
  .value("G_FABS", LIEF::assembly::ebpf::OPCODE::G_FABS)
  .value("G_FCOPYSIGN", LIEF::assembly::ebpf::OPCODE::G_FCOPYSIGN)
  .value("G_IS_FPCLASS", LIEF::assembly::ebpf::OPCODE::G_IS_FPCLASS)
  .value("G_FCANONICALIZE", LIEF::assembly::ebpf::OPCODE::G_FCANONICALIZE)
  .value("G_FMINNUM", LIEF::assembly::ebpf::OPCODE::G_FMINNUM)
  .value("G_FMAXNUM", LIEF::assembly::ebpf::OPCODE::G_FMAXNUM)
  .value("G_FMINNUM_IEEE", LIEF::assembly::ebpf::OPCODE::G_FMINNUM_IEEE)
  .value("G_FMAXNUM_IEEE", LIEF::assembly::ebpf::OPCODE::G_FMAXNUM_IEEE)
  .value("G_FMINIMUM", LIEF::assembly::ebpf::OPCODE::G_FMINIMUM)
  .value("G_FMAXIMUM", LIEF::assembly::ebpf::OPCODE::G_FMAXIMUM)
  .value("G_GET_FPENV", LIEF::assembly::ebpf::OPCODE::G_GET_FPENV)
  .value("G_SET_FPENV", LIEF::assembly::ebpf::OPCODE::G_SET_FPENV)
  .value("G_RESET_FPENV", LIEF::assembly::ebpf::OPCODE::G_RESET_FPENV)
  .value("G_GET_FPMODE", LIEF::assembly::ebpf::OPCODE::G_GET_FPMODE)
  .value("G_SET_FPMODE", LIEF::assembly::ebpf::OPCODE::G_SET_FPMODE)
  .value("G_RESET_FPMODE", LIEF::assembly::ebpf::OPCODE::G_RESET_FPMODE)
  .value("G_PTR_ADD", LIEF::assembly::ebpf::OPCODE::G_PTR_ADD)
  .value("G_PTRMASK", LIEF::assembly::ebpf::OPCODE::G_PTRMASK)
  .value("G_SMIN", LIEF::assembly::ebpf::OPCODE::G_SMIN)
  .value("G_SMAX", LIEF::assembly::ebpf::OPCODE::G_SMAX)
  .value("G_UMIN", LIEF::assembly::ebpf::OPCODE::G_UMIN)
  .value("G_UMAX", LIEF::assembly::ebpf::OPCODE::G_UMAX)
  .value("G_ABS", LIEF::assembly::ebpf::OPCODE::G_ABS)
  .value("G_LROUND", LIEF::assembly::ebpf::OPCODE::G_LROUND)
  .value("G_LLROUND", LIEF::assembly::ebpf::OPCODE::G_LLROUND)
  .value("G_BR", LIEF::assembly::ebpf::OPCODE::G_BR)
  .value("G_BRJT", LIEF::assembly::ebpf::OPCODE::G_BRJT)
  .value("G_VSCALE", LIEF::assembly::ebpf::OPCODE::G_VSCALE)
  .value("G_INSERT_SUBVECTOR", LIEF::assembly::ebpf::OPCODE::G_INSERT_SUBVECTOR)
  .value("G_EXTRACT_SUBVECTOR", LIEF::assembly::ebpf::OPCODE::G_EXTRACT_SUBVECTOR)
  .value("G_INSERT_VECTOR_ELT", LIEF::assembly::ebpf::OPCODE::G_INSERT_VECTOR_ELT)
  .value("G_EXTRACT_VECTOR_ELT", LIEF::assembly::ebpf::OPCODE::G_EXTRACT_VECTOR_ELT)
  .value("G_SHUFFLE_VECTOR", LIEF::assembly::ebpf::OPCODE::G_SHUFFLE_VECTOR)
  .value("G_SPLAT_VECTOR", LIEF::assembly::ebpf::OPCODE::G_SPLAT_VECTOR)
  .value("G_STEP_VECTOR", LIEF::assembly::ebpf::OPCODE::G_STEP_VECTOR)
  .value("G_VECTOR_COMPRESS", LIEF::assembly::ebpf::OPCODE::G_VECTOR_COMPRESS)
  .value("G_CTTZ", LIEF::assembly::ebpf::OPCODE::G_CTTZ)
  .value("G_CTTZ_ZERO_UNDEF", LIEF::assembly::ebpf::OPCODE::G_CTTZ_ZERO_UNDEF)
  .value("G_CTLZ", LIEF::assembly::ebpf::OPCODE::G_CTLZ)
  .value("G_CTLZ_ZERO_UNDEF", LIEF::assembly::ebpf::OPCODE::G_CTLZ_ZERO_UNDEF)
  .value("G_CTPOP", LIEF::assembly::ebpf::OPCODE::G_CTPOP)
  .value("G_BSWAP", LIEF::assembly::ebpf::OPCODE::G_BSWAP)
  .value("G_BITREVERSE", LIEF::assembly::ebpf::OPCODE::G_BITREVERSE)
  .value("G_FCEIL", LIEF::assembly::ebpf::OPCODE::G_FCEIL)
  .value("G_FCOS", LIEF::assembly::ebpf::OPCODE::G_FCOS)
  .value("G_FSIN", LIEF::assembly::ebpf::OPCODE::G_FSIN)
  .value("G_FSINCOS", LIEF::assembly::ebpf::OPCODE::G_FSINCOS)
  .value("G_FTAN", LIEF::assembly::ebpf::OPCODE::G_FTAN)
  .value("G_FACOS", LIEF::assembly::ebpf::OPCODE::G_FACOS)
  .value("G_FASIN", LIEF::assembly::ebpf::OPCODE::G_FASIN)
  .value("G_FATAN", LIEF::assembly::ebpf::OPCODE::G_FATAN)
  .value("G_FATAN2", LIEF::assembly::ebpf::OPCODE::G_FATAN2)
  .value("G_FCOSH", LIEF::assembly::ebpf::OPCODE::G_FCOSH)
  .value("G_FSINH", LIEF::assembly::ebpf::OPCODE::G_FSINH)
  .value("G_FTANH", LIEF::assembly::ebpf::OPCODE::G_FTANH)
  .value("G_FSQRT", LIEF::assembly::ebpf::OPCODE::G_FSQRT)
  .value("G_FFLOOR", LIEF::assembly::ebpf::OPCODE::G_FFLOOR)
  .value("G_FRINT", LIEF::assembly::ebpf::OPCODE::G_FRINT)
  .value("G_FNEARBYINT", LIEF::assembly::ebpf::OPCODE::G_FNEARBYINT)
  .value("G_ADDRSPACE_CAST", LIEF::assembly::ebpf::OPCODE::G_ADDRSPACE_CAST)
  .value("G_BLOCK_ADDR", LIEF::assembly::ebpf::OPCODE::G_BLOCK_ADDR)
  .value("G_JUMP_TABLE", LIEF::assembly::ebpf::OPCODE::G_JUMP_TABLE)
  .value("G_DYN_STACKALLOC", LIEF::assembly::ebpf::OPCODE::G_DYN_STACKALLOC)
  .value("G_STACKSAVE", LIEF::assembly::ebpf::OPCODE::G_STACKSAVE)
  .value("G_STACKRESTORE", LIEF::assembly::ebpf::OPCODE::G_STACKRESTORE)
  .value("G_STRICT_FADD", LIEF::assembly::ebpf::OPCODE::G_STRICT_FADD)
  .value("G_STRICT_FSUB", LIEF::assembly::ebpf::OPCODE::G_STRICT_FSUB)
  .value("G_STRICT_FMUL", LIEF::assembly::ebpf::OPCODE::G_STRICT_FMUL)
  .value("G_STRICT_FDIV", LIEF::assembly::ebpf::OPCODE::G_STRICT_FDIV)
  .value("G_STRICT_FREM", LIEF::assembly::ebpf::OPCODE::G_STRICT_FREM)
  .value("G_STRICT_FMA", LIEF::assembly::ebpf::OPCODE::G_STRICT_FMA)
  .value("G_STRICT_FSQRT", LIEF::assembly::ebpf::OPCODE::G_STRICT_FSQRT)
  .value("G_STRICT_FLDEXP", LIEF::assembly::ebpf::OPCODE::G_STRICT_FLDEXP)
  .value("G_READ_REGISTER", LIEF::assembly::ebpf::OPCODE::G_READ_REGISTER)
  .value("G_WRITE_REGISTER", LIEF::assembly::ebpf::OPCODE::G_WRITE_REGISTER)
  .value("G_MEMCPY", LIEF::assembly::ebpf::OPCODE::G_MEMCPY)
  .value("G_MEMCPY_INLINE", LIEF::assembly::ebpf::OPCODE::G_MEMCPY_INLINE)
  .value("G_MEMMOVE", LIEF::assembly::ebpf::OPCODE::G_MEMMOVE)
  .value("G_MEMSET", LIEF::assembly::ebpf::OPCODE::G_MEMSET)
  .value("G_BZERO", LIEF::assembly::ebpf::OPCODE::G_BZERO)
  .value("G_TRAP", LIEF::assembly::ebpf::OPCODE::G_TRAP)
  .value("G_DEBUGTRAP", LIEF::assembly::ebpf::OPCODE::G_DEBUGTRAP)
  .value("G_UBSANTRAP", LIEF::assembly::ebpf::OPCODE::G_UBSANTRAP)
  .value("G_VECREDUCE_SEQ_FADD", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_SEQ_FADD)
  .value("G_VECREDUCE_SEQ_FMUL", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_SEQ_FMUL)
  .value("G_VECREDUCE_FADD", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FADD)
  .value("G_VECREDUCE_FMUL", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FMUL)
  .value("G_VECREDUCE_FMAX", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FMAX)
  .value("G_VECREDUCE_FMIN", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FMIN)
  .value("G_VECREDUCE_FMAXIMUM", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FMAXIMUM)
  .value("G_VECREDUCE_FMINIMUM", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_FMINIMUM)
  .value("G_VECREDUCE_ADD", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_ADD)
  .value("G_VECREDUCE_MUL", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_MUL)
  .value("G_VECREDUCE_AND", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_AND)
  .value("G_VECREDUCE_OR", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_OR)
  .value("G_VECREDUCE_XOR", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_XOR);
  opcodes.value("G_VECREDUCE_SMAX", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_SMAX)
  .value("G_VECREDUCE_SMIN", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_SMIN)
  .value("G_VECREDUCE_UMAX", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_UMAX)
  .value("G_VECREDUCE_UMIN", LIEF::assembly::ebpf::OPCODE::G_VECREDUCE_UMIN)
  .value("G_SBFX", LIEF::assembly::ebpf::OPCODE::G_SBFX)
  .value("G_UBFX", LIEF::assembly::ebpf::OPCODE::G_UBFX)
  .value("ADJCALLSTACKDOWN", LIEF::assembly::ebpf::OPCODE::ADJCALLSTACKDOWN)
  .value("ADJCALLSTACKUP", LIEF::assembly::ebpf::OPCODE::ADJCALLSTACKUP)
  .value("FI_ri", LIEF::assembly::ebpf::OPCODE::FI_ri)
  .value("MEMCPY", LIEF::assembly::ebpf::OPCODE::MEMCPY)
  .value("Select", LIEF::assembly::ebpf::OPCODE::Select)
  .value("Select_32", LIEF::assembly::ebpf::OPCODE::Select_32)
  .value("Select_32_64", LIEF::assembly::ebpf::OPCODE::Select_32_64)
  .value("Select_64_32", LIEF::assembly::ebpf::OPCODE::Select_64_32)
  .value("Select_Ri", LIEF::assembly::ebpf::OPCODE::Select_Ri)
  .value("Select_Ri_32", LIEF::assembly::ebpf::OPCODE::Select_Ri_32)
  .value("Select_Ri_32_64", LIEF::assembly::ebpf::OPCODE::Select_Ri_32_64)
  .value("Select_Ri_64_32", LIEF::assembly::ebpf::OPCODE::Select_Ri_64_32)
  .value("ADDR_SPACE_CAST", LIEF::assembly::ebpf::OPCODE::ADDR_SPACE_CAST)
  .value("ADD_ri", LIEF::assembly::ebpf::OPCODE::ADD_ri)
  .value("ADD_ri_32", LIEF::assembly::ebpf::OPCODE::ADD_ri_32)
  .value("ADD_rr", LIEF::assembly::ebpf::OPCODE::ADD_rr)
  .value("ADD_rr_32", LIEF::assembly::ebpf::OPCODE::ADD_rr_32)
  .value("AND_ri", LIEF::assembly::ebpf::OPCODE::AND_ri)
  .value("AND_ri_32", LIEF::assembly::ebpf::OPCODE::AND_ri_32)
  .value("AND_rr", LIEF::assembly::ebpf::OPCODE::AND_rr)
  .value("AND_rr_32", LIEF::assembly::ebpf::OPCODE::AND_rr_32)
  .value("BE16", LIEF::assembly::ebpf::OPCODE::BE16)
  .value("BE32", LIEF::assembly::ebpf::OPCODE::BE32)
  .value("BE64", LIEF::assembly::ebpf::OPCODE::BE64)
  .value("BSWAP16", LIEF::assembly::ebpf::OPCODE::BSWAP16)
  .value("BSWAP32", LIEF::assembly::ebpf::OPCODE::BSWAP32)
  .value("BSWAP64", LIEF::assembly::ebpf::OPCODE::BSWAP64)
  .value("CMPXCHGD", LIEF::assembly::ebpf::OPCODE::CMPXCHGD)
  .value("CMPXCHGW32", LIEF::assembly::ebpf::OPCODE::CMPXCHGW32)
  .value("CORE_LD32", LIEF::assembly::ebpf::OPCODE::CORE_LD32)
  .value("CORE_LD64", LIEF::assembly::ebpf::OPCODE::CORE_LD64)
  .value("CORE_SHIFT", LIEF::assembly::ebpf::OPCODE::CORE_SHIFT)
  .value("CORE_ST", LIEF::assembly::ebpf::OPCODE::CORE_ST)
  .value("DIV_ri", LIEF::assembly::ebpf::OPCODE::DIV_ri)
  .value("DIV_ri_32", LIEF::assembly::ebpf::OPCODE::DIV_ri_32)
  .value("DIV_rr", LIEF::assembly::ebpf::OPCODE::DIV_rr)
  .value("DIV_rr_32", LIEF::assembly::ebpf::OPCODE::DIV_rr_32)
  .value("JAL", LIEF::assembly::ebpf::OPCODE::JAL)
  .value("JALX", LIEF::assembly::ebpf::OPCODE::JALX)
  .value("JCOND", LIEF::assembly::ebpf::OPCODE::JCOND)
  .value("JEQ_ri", LIEF::assembly::ebpf::OPCODE::JEQ_ri)
  .value("JEQ_ri_32", LIEF::assembly::ebpf::OPCODE::JEQ_ri_32)
  .value("JEQ_rr", LIEF::assembly::ebpf::OPCODE::JEQ_rr)
  .value("JEQ_rr_32", LIEF::assembly::ebpf::OPCODE::JEQ_rr_32)
  .value("JMP", LIEF::assembly::ebpf::OPCODE::JMP)
  .value("JMPL", LIEF::assembly::ebpf::OPCODE::JMPL)
  .value("JNE_ri", LIEF::assembly::ebpf::OPCODE::JNE_ri)
  .value("JNE_ri_32", LIEF::assembly::ebpf::OPCODE::JNE_ri_32)
  .value("JNE_rr", LIEF::assembly::ebpf::OPCODE::JNE_rr)
  .value("JNE_rr_32", LIEF::assembly::ebpf::OPCODE::JNE_rr_32)
  .value("JSET_ri", LIEF::assembly::ebpf::OPCODE::JSET_ri)
  .value("JSET_ri_32", LIEF::assembly::ebpf::OPCODE::JSET_ri_32)
  .value("JSET_rr", LIEF::assembly::ebpf::OPCODE::JSET_rr)
  .value("JSET_rr_32", LIEF::assembly::ebpf::OPCODE::JSET_rr_32)
  .value("JSGE_ri", LIEF::assembly::ebpf::OPCODE::JSGE_ri)
  .value("JSGE_ri_32", LIEF::assembly::ebpf::OPCODE::JSGE_ri_32)
  .value("JSGE_rr", LIEF::assembly::ebpf::OPCODE::JSGE_rr)
  .value("JSGE_rr_32", LIEF::assembly::ebpf::OPCODE::JSGE_rr_32)
  .value("JSGT_ri", LIEF::assembly::ebpf::OPCODE::JSGT_ri)
  .value("JSGT_ri_32", LIEF::assembly::ebpf::OPCODE::JSGT_ri_32)
  .value("JSGT_rr", LIEF::assembly::ebpf::OPCODE::JSGT_rr)
  .value("JSGT_rr_32", LIEF::assembly::ebpf::OPCODE::JSGT_rr_32)
  .value("JSLE_ri", LIEF::assembly::ebpf::OPCODE::JSLE_ri)
  .value("JSLE_ri_32", LIEF::assembly::ebpf::OPCODE::JSLE_ri_32)
  .value("JSLE_rr", LIEF::assembly::ebpf::OPCODE::JSLE_rr)
  .value("JSLE_rr_32", LIEF::assembly::ebpf::OPCODE::JSLE_rr_32)
  .value("JSLT_ri", LIEF::assembly::ebpf::OPCODE::JSLT_ri)
  .value("JSLT_ri_32", LIEF::assembly::ebpf::OPCODE::JSLT_ri_32)
  .value("JSLT_rr", LIEF::assembly::ebpf::OPCODE::JSLT_rr)
  .value("JSLT_rr_32", LIEF::assembly::ebpf::OPCODE::JSLT_rr_32)
  .value("JUGE_ri", LIEF::assembly::ebpf::OPCODE::JUGE_ri)
  .value("JUGE_ri_32", LIEF::assembly::ebpf::OPCODE::JUGE_ri_32)
  .value("JUGE_rr", LIEF::assembly::ebpf::OPCODE::JUGE_rr)
  .value("JUGE_rr_32", LIEF::assembly::ebpf::OPCODE::JUGE_rr_32)
  .value("JUGT_ri", LIEF::assembly::ebpf::OPCODE::JUGT_ri)
  .value("JUGT_ri_32", LIEF::assembly::ebpf::OPCODE::JUGT_ri_32)
  .value("JUGT_rr", LIEF::assembly::ebpf::OPCODE::JUGT_rr)
  .value("JUGT_rr_32", LIEF::assembly::ebpf::OPCODE::JUGT_rr_32)
  .value("JULE_ri", LIEF::assembly::ebpf::OPCODE::JULE_ri)
  .value("JULE_ri_32", LIEF::assembly::ebpf::OPCODE::JULE_ri_32)
  .value("JULE_rr", LIEF::assembly::ebpf::OPCODE::JULE_rr)
  .value("JULE_rr_32", LIEF::assembly::ebpf::OPCODE::JULE_rr_32)
  .value("JULT_ri", LIEF::assembly::ebpf::OPCODE::JULT_ri)
  .value("JULT_ri_32", LIEF::assembly::ebpf::OPCODE::JULT_ri_32)
  .value("JULT_rr", LIEF::assembly::ebpf::OPCODE::JULT_rr)
  .value("JULT_rr_32", LIEF::assembly::ebpf::OPCODE::JULT_rr_32)
  .value("LDB", LIEF::assembly::ebpf::OPCODE::LDB)
  .value("LDB32", LIEF::assembly::ebpf::OPCODE::LDB32)
  .value("LDBSX", LIEF::assembly::ebpf::OPCODE::LDBSX)
  .value("LDD", LIEF::assembly::ebpf::OPCODE::LDD)
  .value("LDH", LIEF::assembly::ebpf::OPCODE::LDH)
  .value("LDH32", LIEF::assembly::ebpf::OPCODE::LDH32)
  .value("LDHSX", LIEF::assembly::ebpf::OPCODE::LDHSX)
  .value("LDW", LIEF::assembly::ebpf::OPCODE::LDW)
  .value("LDW32", LIEF::assembly::ebpf::OPCODE::LDW32)
  .value("LDWSX", LIEF::assembly::ebpf::OPCODE::LDWSX)
  .value("LD_ABS_B", LIEF::assembly::ebpf::OPCODE::LD_ABS_B)
  .value("LD_ABS_H", LIEF::assembly::ebpf::OPCODE::LD_ABS_H)
  .value("LD_ABS_W", LIEF::assembly::ebpf::OPCODE::LD_ABS_W)
  .value("LD_IND_B", LIEF::assembly::ebpf::OPCODE::LD_IND_B)
  .value("LD_IND_H", LIEF::assembly::ebpf::OPCODE::LD_IND_H)
  .value("LD_IND_W", LIEF::assembly::ebpf::OPCODE::LD_IND_W)
  .value("LD_imm64", LIEF::assembly::ebpf::OPCODE::LD_imm64)
  .value("LD_pseudo", LIEF::assembly::ebpf::OPCODE::LD_pseudo)
  .value("LE16", LIEF::assembly::ebpf::OPCODE::LE16)
  .value("LE32", LIEF::assembly::ebpf::OPCODE::LE32)
  .value("LE64", LIEF::assembly::ebpf::OPCODE::LE64)
  .value("MOD_ri", LIEF::assembly::ebpf::OPCODE::MOD_ri)
  .value("MOD_ri_32", LIEF::assembly::ebpf::OPCODE::MOD_ri_32)
  .value("MOD_rr", LIEF::assembly::ebpf::OPCODE::MOD_rr)
  .value("MOD_rr_32", LIEF::assembly::ebpf::OPCODE::MOD_rr_32)
  .value("MOVSX_rr_16", LIEF::assembly::ebpf::OPCODE::MOVSX_rr_16)
  .value("MOVSX_rr_32", LIEF::assembly::ebpf::OPCODE::MOVSX_rr_32)
  .value("MOVSX_rr_32_16", LIEF::assembly::ebpf::OPCODE::MOVSX_rr_32_16)
  .value("MOVSX_rr_32_8", LIEF::assembly::ebpf::OPCODE::MOVSX_rr_32_8)
  .value("MOVSX_rr_8", LIEF::assembly::ebpf::OPCODE::MOVSX_rr_8)
  .value("MOV_32_64", LIEF::assembly::ebpf::OPCODE::MOV_32_64)
  .value("MOV_ri", LIEF::assembly::ebpf::OPCODE::MOV_ri)
  .value("MOV_ri_32", LIEF::assembly::ebpf::OPCODE::MOV_ri_32)
  .value("MOV_rr", LIEF::assembly::ebpf::OPCODE::MOV_rr)
  .value("MOV_rr_32", LIEF::assembly::ebpf::OPCODE::MOV_rr_32)
  .value("MUL_ri", LIEF::assembly::ebpf::OPCODE::MUL_ri)
  .value("MUL_ri_32", LIEF::assembly::ebpf::OPCODE::MUL_ri_32)
  .value("MUL_rr", LIEF::assembly::ebpf::OPCODE::MUL_rr)
  .value("MUL_rr_32", LIEF::assembly::ebpf::OPCODE::MUL_rr_32)
  .value("NEG_32", LIEF::assembly::ebpf::OPCODE::NEG_32)
  .value("NEG_64", LIEF::assembly::ebpf::OPCODE::NEG_64)
  .value("NOP", LIEF::assembly::ebpf::OPCODE::NOP)
  .value("OR_ri", LIEF::assembly::ebpf::OPCODE::OR_ri)
  .value("OR_ri_32", LIEF::assembly::ebpf::OPCODE::OR_ri_32)
  .value("OR_rr", LIEF::assembly::ebpf::OPCODE::OR_rr)
  .value("OR_rr_32", LIEF::assembly::ebpf::OPCODE::OR_rr_32)
  .value("RET", LIEF::assembly::ebpf::OPCODE::RET)
  .value("SDIV_ri", LIEF::assembly::ebpf::OPCODE::SDIV_ri)
  .value("SDIV_ri_32", LIEF::assembly::ebpf::OPCODE::SDIV_ri_32)
  .value("SDIV_rr", LIEF::assembly::ebpf::OPCODE::SDIV_rr)
  .value("SDIV_rr_32", LIEF::assembly::ebpf::OPCODE::SDIV_rr_32)
  .value("SLL_ri", LIEF::assembly::ebpf::OPCODE::SLL_ri)
  .value("SLL_ri_32", LIEF::assembly::ebpf::OPCODE::SLL_ri_32)
  .value("SLL_rr", LIEF::assembly::ebpf::OPCODE::SLL_rr)
  .value("SLL_rr_32", LIEF::assembly::ebpf::OPCODE::SLL_rr_32)
  .value("SMOD_ri", LIEF::assembly::ebpf::OPCODE::SMOD_ri)
  .value("SMOD_ri_32", LIEF::assembly::ebpf::OPCODE::SMOD_ri_32)
  .value("SMOD_rr", LIEF::assembly::ebpf::OPCODE::SMOD_rr)
  .value("SMOD_rr_32", LIEF::assembly::ebpf::OPCODE::SMOD_rr_32)
  .value("SRA_ri", LIEF::assembly::ebpf::OPCODE::SRA_ri)
  .value("SRA_ri_32", LIEF::assembly::ebpf::OPCODE::SRA_ri_32)
  .value("SRA_rr", LIEF::assembly::ebpf::OPCODE::SRA_rr)
  .value("SRA_rr_32", LIEF::assembly::ebpf::OPCODE::SRA_rr_32)
  .value("SRL_ri", LIEF::assembly::ebpf::OPCODE::SRL_ri)
  .value("SRL_ri_32", LIEF::assembly::ebpf::OPCODE::SRL_ri_32)
  .value("SRL_rr", LIEF::assembly::ebpf::OPCODE::SRL_rr)
  .value("SRL_rr_32", LIEF::assembly::ebpf::OPCODE::SRL_rr_32)
  .value("STB", LIEF::assembly::ebpf::OPCODE::STB)
  .value("STB32", LIEF::assembly::ebpf::OPCODE::STB32)
  .value("STB_imm", LIEF::assembly::ebpf::OPCODE::STB_imm)
  .value("STD", LIEF::assembly::ebpf::OPCODE::STD)
  .value("STD_imm", LIEF::assembly::ebpf::OPCODE::STD_imm)
  .value("STH", LIEF::assembly::ebpf::OPCODE::STH)
  .value("STH32", LIEF::assembly::ebpf::OPCODE::STH32)
  .value("STH_imm", LIEF::assembly::ebpf::OPCODE::STH_imm)
  .value("STW", LIEF::assembly::ebpf::OPCODE::STW)
  .value("STW32", LIEF::assembly::ebpf::OPCODE::STW32)
  .value("STW_imm", LIEF::assembly::ebpf::OPCODE::STW_imm)
  .value("SUB_ri", LIEF::assembly::ebpf::OPCODE::SUB_ri)
  .value("SUB_ri_32", LIEF::assembly::ebpf::OPCODE::SUB_ri_32)
  .value("SUB_rr", LIEF::assembly::ebpf::OPCODE::SUB_rr)
  .value("SUB_rr_32", LIEF::assembly::ebpf::OPCODE::SUB_rr_32)
  .value("XADDD", LIEF::assembly::ebpf::OPCODE::XADDD)
  .value("XADDW", LIEF::assembly::ebpf::OPCODE::XADDW)
  .value("XADDW32", LIEF::assembly::ebpf::OPCODE::XADDW32)
  .value("XANDD", LIEF::assembly::ebpf::OPCODE::XANDD)
  .value("XANDW32", LIEF::assembly::ebpf::OPCODE::XANDW32)
  .value("XCHGD", LIEF::assembly::ebpf::OPCODE::XCHGD)
  .value("XCHGW32", LIEF::assembly::ebpf::OPCODE::XCHGW32)
  .value("XFADDD", LIEF::assembly::ebpf::OPCODE::XFADDD)
  .value("XFADDW32", LIEF::assembly::ebpf::OPCODE::XFADDW32)
  .value("XFANDD", LIEF::assembly::ebpf::OPCODE::XFANDD)
  .value("XFANDW32", LIEF::assembly::ebpf::OPCODE::XFANDW32)
  .value("XFORD", LIEF::assembly::ebpf::OPCODE::XFORD)
  .value("XFORW32", LIEF::assembly::ebpf::OPCODE::XFORW32)
  .value("XFXORD", LIEF::assembly::ebpf::OPCODE::XFXORD)
  .value("XFXORW32", LIEF::assembly::ebpf::OPCODE::XFXORW32)
  .value("XORD", LIEF::assembly::ebpf::OPCODE::XORD)
  .value("XORW32", LIEF::assembly::ebpf::OPCODE::XORW32)
  .value("XOR_ri", LIEF::assembly::ebpf::OPCODE::XOR_ri)
  .value("XOR_ri_32", LIEF::assembly::ebpf::OPCODE::XOR_ri_32)
  .value("XOR_rr", LIEF::assembly::ebpf::OPCODE::XOR_rr)
  .value("XOR_rr_32", LIEF::assembly::ebpf::OPCODE::XOR_rr_32)
  .value("XXORD", LIEF::assembly::ebpf::OPCODE::XXORD)
  .value("XXORW32", LIEF::assembly::ebpf::OPCODE::XXORW32)
  .value("INSTRUCTION_LIST_END", LIEF::assembly::ebpf::OPCODE::INSTRUCTION_LIST_END)
  ;
}
}
